{"name": "searchat-builder-refactor", "version": "0.1.0", "private": true, "scripts": {"start:dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@amcharts/amcharts5": "^5.3.12", "@amcharts/amcharts5-geodata": "^5.1.0", "@microsoft/signalr": "^7.0.14", "@monaco-editor/react": "^4.6.0", "@popperjs/core": "2.9.1", "@radix-ui/react-alert-dialog": "^1.0.3", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-context-menu": "^2.1.3", "@radix-ui/react-dialog": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.4", "@radix-ui/react-hover-card": "^1.0.5", "@radix-ui/react-menubar": "^1.0.3", "@radix-ui/react-popover": "^1.0.5", "@radix-ui/react-select": "^1.2.1", "@radix-ui/react-separator": "^1.0.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-tabs": "^1.0.3", "@react-google-maps/api": "^2.18.1", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.15", "@types/node": "18.15.11", "@types/react": "18.0.32", "@types/react-dom": "18.0.11", "@wavesurfer/react": "^1.0.4", "360dialog-connect-button": "^0.8.0", "ace-builds": "^1.23.1", "apexcharts": "^4.5.0", "axios": "^1.3.5", "class-variance-authority": "^0.5.1", "clsx": "^1.2.1", "cmdk": "^0.2.0", "csstype": "^3.0.10", "date-fns": "^2.30.0", "eslint": "8.37.0", "eslint-config-next": "13.2.4", "highcharts": "^11.1.0", "highcharts-react-official": "^3.2.0", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "lucide-react": "^0.439.0", "next": "13.2.4", "openai": "^3.2.1", "pathfinding": "^0.4.18", "react": "18.2.0", "react-apexcharts": "^1.4.0", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-day-picker": "^8.7.1", "react-dom": "18.2.0", "react-flow": "^1.0.3", "react-grid-layout": "^1.3.4", "react-hot-toast": "^2.4.1", "react-json-formatter": "^0.3.2", "react-mentions": "^4.4.10", "react-papaparse": "^4.1.0", "react-quill": "^2.0.0", "react-select": "^5.7.3", "react-top-loading-bar": "^2.3.1", "reactflow": "^11.7.0", "reactjs-timezone-select": "^2.0.0", "showdown": "^2.1.0", "swiper": "^9.1.0", "tailwind-merge": "^1.12.0", "tailwindcss-animate": "^1.0.5", "textarea-caret": "^3.1.0", "turndown": "^7.1.2", "typescript": "5.0.3", "typewriter-effect": "^2.19.0", "uuid": "^9.0.0", "xlsx": "^0.18.5", "zod": "^3.21.4", "zustand": "^4.3.7"}, "devDependencies": {"@types/react-mentions": "^4.1.8", "autoprefixer": "^10.4.14", "eslint-config-next": "13.4.4", "postcss": "^8.4.21", "tailwindcss": "^3.3.1"}}