/* * {
  font-family: "Source Sans Pro", sans-serif;
} */
.shake {
  animation: 10s shake infinite;
}

.flip {
  animation: 10s flip infinite;
}

.swing {
  animation: swing ease-in-out 0.6s infinite alternate;
}

@keyframes shake {
  0% {
    transform: skewX(-15deg);
  }
  2.5% {
    transform: skewX(15deg);
  }
  5% {
    transform: skewX(-15deg);
  }
  7.5% {
    transform: skewX(15deg);
  }
  10% {
    transform: skewX(0deg);
  }
  100% {
    transform: skewX(0deg);
  }
}

@keyframes swing {
  0% {
    transform: rotate(5deg);
  }
  100% {
    transform: rotate(-5deg);
  }
}

@keyframes bounce {
  0%,
  2%,
  5%,
  8%,
  10% {
    transform: translateY(0);
  }
  4% {
    transform: translateY(-30px);
  }
  6% {
    transform: translateY(-15px);
  }
}

@keyframes flip {
  0% {
    transform: perspective(400px) rotateY(0);
    animation-timing-function: ease-out;
  }
  4% {
    transform: perspective(400px) translateZ(150px) rotateY(170deg);
    animation-timing-function: ease-out;
  }
  5% {
    transform: perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
    animation-timing-function: ease-in;
  }
  8% {
    transform: perspective(400px) rotateY(360deg) scale(0.95);
    animation-timing-function: ease-in;
  }
  10% {
    transform: perspective(400px) scale(1);
    animation-timing-function: ease-in;
  }
}

.floating {
  animation-name: floating;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}

@keyframes floating {
  0% {
    transform: translate(0, 0px);
  }

  50% {
    transform: translate(0, 15px);
  }

  100% {
    transform: translate(0, -0px);
  }
}

@keyframes chatbot {
  0% {
    transform: translateY(-10px);
  }
  50% {
    transform: translateY(5px);
  }
  100% {
    transform: translateY(-10px);
  }
}

.will-change-transform {
  will-change: transform;
}

.cursor::after {
  display: block;
  content: "";
  position: absolute;
  width: 4px;
  height: 100%;
  background-color: #fff;
  animation: cursor 0.6s linear infinite alternate;
  will-change: opacity;
}

@keyframes cursor {
  0%,
  40% {
    opacity: 1;
  }

  60%,
  100% {
    opacity: 0;
  }
}

.glass {
  /* From https://css.glass */
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  /* border: 1px solid rgba(255, 255, 255, 0.3); */
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #4d4c4c;
}

::-webkit-scrollbar {
  width: 5px;
  background-color: #4d4c4c;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #141416;
}

input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
  height: 15px;
  width: 15px;
  background-color: white;
  -webkit-mask-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23777'><path d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/></svg>");
  background-size: 10px 10px;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  filter: invert(100%) sepia(13%) saturate(3207%) hue-rotate(90deg)
    brightness(95%) contrast(80%);
}

.ql-container {
  min-height: 10em;
  border-bottom-left-radius: 0.5em;
  border-bottom-right-radius: 0.5em;
  background: #9ca3af20;
}

.ql-toolbar {
  background: #eaececb6;
  border-top-left-radius: 0.5em;
  border-top-right-radius: 0.5em;
}

.tooltip-bot {
  position: absolute;
  display: block;
  padding: 20px 10px;
  box-shadow: 2px 4px 25px -4px rgba(0, 0, 0, 0.3);
  background: #fff;
  /* float: left; */
  min-width: auto;
  width: 190px;
  max-width: 400px;
  left: 25px;
  border-radius: 50px 50px 0;
  text-align: center;
  min-height: 20px;
  -webkit-animation: fadeInUp2 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55)
    forwards;
  animation: fadeInUp2 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}
.tooltip-bot p {
  /* display: inline-block; */
  margin: 0;
  line-height: 20px;
  color: #000;
  padding: 0;
  float: right;
  width: 100%;
  font-size: 13px;
  text-align: center;
}

.ace_editor, .ace_editor *{
  font-family: "Monaco", "Menlo", "Ubuntu Mono", "Droid Sans Mono", "Consolas", monospace !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  letter-spacing: 0 !important;
  }

  #react-select-2-input {
    --tw-ring-shadow: 0 0 #000 !important;
  }