import { toInteger } from "./utils";
import { gridToGraphPoint } from "./pointConversion";
import { svgDrawSmoothLinePath } from "./svgDrawSmoothLinePath";
import { pathfindingJumpPointNoDiagonal } from "./generatePath";
import getBoundingBoxes from "./getBoundingBoxes";
import createGrid from "./createGrid";

export function getSmartEdge({
  options = {},
  nodes = [],
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  edgesPath = []
}) {
  try {
    const {
      drawEdge = svgDrawSmoothLinePath,
      generatePath = pathfindingJumpPointNoDiagonal
    }:any = options;

    let { gridRatio = 10, nodePadding = 10, edgePadding = 5 }:any = options;
    gridRatio = toInteger(gridRatio);
    nodePadding = toInteger(nodePadding);

    const { graphBox, nodeBoxes } = getBoundingBoxes(
      nodes,
      nodePadding,
      gridRatio
    );

    const source = {
      x: sourceX,
      y: sourceY,
      position: sourcePosition
    };

    const target = {
      x: targetX,
      y: targetY,
      position: targetPosition
    };

   
    const { grid, start, end } = createGrid(
      graphBox,
      nodeBoxes,
      source,
      target,
      gridRatio,
      edgesPath,
      edgePadding
    );


    const generatePathResult = generatePath(grid, start, end);

    if (generatePathResult === null) {
      return null;
    }

    const { fullPath, smoothedPath } = generatePathResult;

  
    const graphPath = smoothedPath.map((gridPoint) => {
      const [x, y] = gridPoint;
      const graphPoint = gridToGraphPoint(
        { x, y },
        graphBox.xMin,
        graphBox.yMin,
        gridRatio
      );
      return [graphPoint.x, graphPoint.y];
    });

    const svgPathString = drawEdge(source, target, graphPath);

    const index = Math.floor(fullPath.length / 2);
    const middlePoint = fullPath[index];
    const [middleX, middleY] = middlePoint;
    const { x: edgeCenterX, y: edgeCenterY } = gridToGraphPoint(
      { x: middleX, y: middleY },
      graphBox.xMin,
      graphBox.yMin,
      gridRatio
    );

    return { svgPathString, edgeCenterX, edgeCenterY, smoothedPath, graphPath };
  } catch {
    return null;
  }
}
