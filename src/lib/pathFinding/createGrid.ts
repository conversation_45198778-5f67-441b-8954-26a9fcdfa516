import { Grid } from "pathfinding";
import { roundUp, round } from "./utils";
import { graphToGridPoint } from "./pointConversion";
import {
  guaranteeWalkablePath,
  getNextPointFromPosition
} from "./guaranteeWalkablePath";

const createGrid = (
  graph,
  nodes = [],
  source,
  target,
  gridRatio = 2,
  edgesPath = [],
  edgePadding
) => {
  const { xMin, yMin, width, height } = graph;


  const mapColumns = roundUp(width, gridRatio) / gridRatio + 1;
  const mapRows = roundUp(height, gridRatio) / gridRatio + 1;
  const grid = new Grid(mapColumns, mapRows);


  nodes.forEach((node) => {
    const nodeStart = graphToGridPoint(node.topLeft, xMin, yMin, gridRatio);
    const nodeEnd = graphToGridPoint(node.bottomRight, xMin, yMin, gridRatio);

    for (let x = nodeStart.x; x < nodeEnd.x; x++) {
      for (let y = nodeStart.y; y < nodeEnd.y; y++) {
        grid.setWalkableAt(x, y, false);
      }
    }
  });
  const setAsObstacle = (x, y) => {
    if (grid.isWalkableAt(x, y)) {
      grid.setWalkableAt(x, y, false);
    }
  };
  edgesPath.forEach((path) => {
    let { smoothedPath = [] } = path || {};

    smoothedPath.forEach((sp) => {
      let [x, y] = sp || [];
  
      setAsObstacle(x, y);
      setAsObstacle(x - edgePadding, y);
      setAsObstacle(x + edgePadding, y);
      setAsObstacle(x, y - edgePadding);
      setAsObstacle(x, y + edgePadding);
    });
  });


  const startGrid = graphToGridPoint(
    {
      x: round(source.x, gridRatio),
      y: round(source.y, gridRatio)
    },
    xMin,
    yMin,
    gridRatio
  );

  const endGrid = graphToGridPoint(
    {
      x: round(target.x, gridRatio),
      y: round(target.y, gridRatio)
    },
    xMin,
    yMin,
    gridRatio
  );


  const startingNode = grid.getNodeAt(startGrid.x, startGrid.y);
  guaranteeWalkablePath(grid, startingNode, source.position);
  const endingNode = grid.getNodeAt(endGrid.x, endGrid.y);
  guaranteeWalkablePath(grid, endingNode, target.position);

 
  const start = getNextPointFromPosition(startingNode, source.position);
  const end = getNextPointFromPosition(endingNode, target.position);

  return { grid, start, end };
};

export default createGrid;
