import { IDialogConfigurationStep } from "store/dialog/dialog.types";
import getConfigs from "data/composer/configs";
import { INodeContent, IPromptForm } from "types/composer.types";
import {
  SendMessageContent,
  SendCarouselContent,
  RequestUserDataContent,
  SendItemsContent,
  SendSuggestionsContent,
  SendCategoriesContent,
  SendPromotionsContent,
  TriggerDialogContent,
  JsonAPIContent,
  JumpToWidgetContent,
  DynamicCardContent,
  DynamicTableContent,
  DynamicFormContent,
  DynamicSuggestionsContent,
  PromptReportsContent,
  PromptLeadsContent,
  CreateStoreContent,
  StoreLookupContent,
  ChartContent,
  TextractionContent,
  JsEvalContent,
  NoContent,
  RepeatContent,
  SendEmailContent,
  LiveChatContent,
  HtmlContent,
  ConversationIDContent,
  WhatsappNumberContent,
  ChannelIDContent,
  MapCardContent,
  Condition<PERSON>ontent,
  CheckpointContent,
  <PERSON><PERSON>ttachmentContent,
  Request<PERSON>ttachment<PERSON>ontent,
  Requet<PERSON>serLocationContent,
  TicketingSystemContent,
  AgentContent
} from "common/components/composer/nodeContent/elements";

import {
  PromptTextForm,
  PromptSuggestionsForm,
  RequestUserDataForm,
  SendItemsForm,
  SendCategoriesForm,
  SendPromotionsForm,
  SendCarouselForm,
  TriggerDialogForm,
  JsonAPIForm,
  JumpToWidgetForm,
  DynamicCardForm,
  DynamicTableForm,
  DynamicSuggestionsForm,
  StarterNodeForm,
  PromptReportsForm,
  PromptLeadsForm,
  StoreLookupForm,
  CreateStoreForm,
  ChartForm,
  TextractionForm,
  JsEvalForm,
  RepeatForm,
  ConditionsForm,
  SendEmailForm,
  WhatsappNumberForm,
  LiveChatForm,
  HtmlForm,
  ChannelIdForm,
  ConversationIdForm,
  MapCardForm,
  SendAttachmentForm,
  RequestAttachmentForm,
  RequetUserLocationForm,
  TicketingSystemForm,
  DynamicFormForm,
  AgentForm,
} from "common/components/composer/sheet/forms";
import checkpointForm from "common/components/composer/sheet/forms/checkpoint.form";
class Block {
  steps: IDialogConfigurationStep[];
  content: INodeContent;
  form: IPromptForm;

  constructor(block_type: string) {
    const config = getConfigs()[block_type];
    this.steps = [...config];
  }
  getConfig(): IDialogConfigurationStep[] {
    return [...this.steps];
  }
  getConent(): INodeContent {
    return this.content;
  }
  getFrom(): IPromptForm {
    return this.form;
  }
}

class SendEmailBlock extends Block {
  constructor() {
    super("Send Email");
    this.content = SendEmailContent;
    this.form = SendEmailForm;
  }
}

class RequestUserDataBlock extends Block {
  constructor() {
    super("Request User Data");
    this.content = RequestUserDataContent;
    this.form = RequestUserDataForm;
  }
}

class SendMessageBlock extends Block {
  constructor() {
    super("Send Message");
    this.content = SendMessageContent;
    this.form = PromptTextForm;
  }
}

class SendSuggestionsBlock extends Block {
  constructor() {
    super("Send Suggestions");
    this.content = SendSuggestionsContent;
    this.form = PromptSuggestionsForm;
  }
}

class SendCarouselBlock extends Block {
  constructor() {
    super("Send Carousel");
    this.content = SendCarouselContent;
    this.form = SendCarouselForm;
  }
}

class SendItemsBlock extends Block {
  constructor() {
    super("Send Items");
    this.content = SendItemsContent;
    this.form = SendItemsForm;
  }
}

class SendCategoriesBlock extends Block {
  constructor() {
    super("Send Categories");
    this.content = SendCategoriesContent;
    this.form = SendCategoriesForm;
  }
}

class SendPromotionsBlock extends Block {
  constructor() {
    super("Send Promotions");
    this.content = SendPromotionsContent;
    this.form = SendPromotionsForm;
  }
}

class TriggerDialogBlock extends Block {
  constructor() {
    super("Trigger Dialog");
    this.content = TriggerDialogContent;
    this.form = TriggerDialogForm;
  }
}

class JSONAPIBlock extends Block {
  constructor() {
    super("JSON API");
    this.content = JsonAPIContent;
    this.form = JsonAPIForm;
  }
}

class JumpToWidgetBlock extends Block {
  constructor() {
    super("Jump To Widget");
    this.content = JumpToWidgetContent;
    this.form = JumpToWidgetForm;
  }
}

class KnowledgeBaseBlock extends Block {
  constructor() {
    super("Knowledge Base Lookup");
    this.content = NoContent;
  }
}

class DynamicCardBlock extends Block {
  constructor() {
    super("Dynamic Card");
    this.content = DynamicCardContent;
    this.form = DynamicCardForm;
  }
}

class DynamicTableBlock extends Block {
  constructor() {
    super("Dynamic Table");
    this.content = DynamicTableContent;
    this.form = DynamicTableForm;
  }
}

class DynamicFormBlock extends Block {
  constructor() {
    super("Dynamic Report");
    this.content = DynamicFormContent;
    this.form = DynamicFormForm;
  }
}

class PromptDynamicSuggestionsBlock extends Block {
  constructor() {
    super("Prompt Dynamic Suggestions");
    this.content = DynamicSuggestionsContent;
    this.form = DynamicSuggestionsForm;
  }
}

class PromptReportBlock extends Block {
  constructor() {
    super("Prompt Reports");
    this.content = PromptReportsContent;
    this.form = PromptReportsForm;
  }
}

class PromptLeadsBlock extends Block {
  constructor() {
    super("Lead_Generation");
    this.content = PromptLeadsContent;
    this.form = PromptLeadsForm;
  }
}

class EndDialogBlock extends Block {
  constructor() {
    super("end_dialog");
    this.content = NoContent;
  }
}

class CreateStoreBlock extends Block {
  constructor() {
    super("Create Store");
    this.content = CreateStoreContent;
    this.form = CreateStoreForm;
  }
}

class StoreLookupBlock extends Block {
  constructor() {
    super("Store Lookup");
    this.content = StoreLookupContent;
    this.form = StoreLookupForm;
  }
}
class ChartBlock extends Block {
  constructor() {
    super("Chart");
    this.content = ChartContent;
    this.form = ChartForm;
  }
}

class TextractionBlock extends Block {
  constructor() {
    super("Textraction");
    this.content = TextractionContent;
    this.form = TextractionForm;
  }
}

class CodeBlock extends Block {
  constructor() {
    super("JS Code");
    this.content = JsEvalContent;
    this.form = JsEvalForm;
  }
}

class LiveChatBlock extends Block {
  constructor() {
    super("Live Chat");
    this.content = LiveChatContent;
    this.form = LiveChatForm;
  }
}
class RepeatBlock extends Block {
  constructor() {
    super("Repeat");
    this.content = RepeatContent;
    this.form = RepeatForm;
  }
}
class Conditions extends Block {
  constructor() {
    super("Conditions");
    this.content = ConditionContent;
    this.form = ConditionsForm;
  }
}
class WhatsappNumber extends Block {
  constructor() {
    super("Whatsapp Number");
    this.content = WhatsappNumberContent;
    this.form = WhatsappNumberForm;
  }
}
class HtmlBlock extends Block {
  constructor() {
    super("Html Block");
    this.content = HtmlContent;
    this.form = HtmlForm;
  }
}

class TriggerForm extends Block {
  constructor() {
    super("Starter Node");
    this.content = NoContent;
    this.form = StarterNodeForm;
  }
}

class ChannelIdBlock extends Block {
  constructor() {
    super("Get Channel Id");
    this.content = ChannelIDContent;
    this.form = ChannelIdForm;
  }
}

class ConversationIdBlock extends Block {
  constructor() {
    super("Get Unique Conversation Id");
    this.content = ConversationIDContent;
    this.form = ConversationIdForm;
  }
}

class MapCardBlock extends Block {
  constructor() {
    super("Send Map Card");
    this.content = MapCardContent;
    this.form = MapCardForm;
  }
}

class CheckpointBlock extends Block {
  constructor() {
    super("Checkpoint");
    this.content = CheckpointContent;
    this.form = checkpointForm;
  }
}

class SendAttachmentBlock extends Block {
  constructor() {
    super("Send Attachment");
    this.content = SendAttachmentContent;
    this.form = SendAttachmentForm;
  }
}
class RequestAttachmentBlock extends Block {
  constructor() {
    super("Request Attachment");
    this.content = RequestAttachmentContent;
    this.form = RequestAttachmentForm;
  }
}

class RequestLocationBlock extends Block {
  constructor() {
    super("Request Location");
    this.content = RequetUserLocationContent;
    this.form = RequetUserLocationForm;
  }
}

class TicketingSystemBlock extends Block {
  constructor() {
    super("Ticketing system");
    this.content = TicketingSystemContent;
    this.form = TicketingSystemForm;
  }
}

class AgentBlock extends Block {
  constructor() {
    super("LLM Agent");
    this.content = AgentContent;
    this.form = AgentForm;
  }
}

class BlockConfigFactory {
  static createBlock(blockType: string): Block {
    switch (blockType) {
      case "Starter Node":
        return new TriggerForm();
      case "Request User Data":
        return new RequestUserDataBlock();
      case "Request Attachment":
        return new RequestAttachmentBlock();
      case "Send Message":
        return new SendMessageBlock();
      case "Send Suggestions":
        return new SendSuggestionsBlock();
      case "Send Carousel":
        return new SendCarouselBlock();
      case "Send Items":
        return new SendItemsBlock();
      case "Send Categories":
        return new SendCategoriesBlock();
      case "Send Promotions":
        return new SendPromotionsBlock();
      case "Trigger Dialog":
        return new TriggerDialogBlock();
      case "JSON API":
        return new JSONAPIBlock();
      case "Jump To Widget":
        return new JumpToWidgetBlock();
      case "Knowledge Base Lookup":
        return new KnowledgeBaseBlock();
      case "Dynamic Card":
        return new DynamicCardBlock();
      case "Dynamic Table":
        return new DynamicTableBlock();
      case "Prompt Dynamic Suggestions":
        return new PromptDynamicSuggestionsBlock();
      case "Prompt Reports":
        return new PromptReportBlock();
      case "Lead_Generation":
        return new PromptLeadsBlock();
      case "end_dialog":
        return new EndDialogBlock();
      case "Create Store":
        return new CreateStoreBlock();
      case "Store Lookup":
        return new StoreLookupBlock();
      case "Chart":
        return new ChartBlock();
      case "Textraction":
        return new TextractionBlock();
      case "JS Code":
        return new CodeBlock();
      case "Live Chat":
        return new LiveChatBlock();
      case "Repeat":
        return new RepeatBlock();
      case "Conditions":
        return new Conditions();
      case "Whatsapp Number":
        return new WhatsappNumber();
      case "Send Email":
        return new SendEmailBlock();
      case "Send Attachment":
        return new SendAttachmentBlock();
      case "Html Block":
        return new HtmlBlock();
      case "Get Channel Id":
        return new ChannelIdBlock();
      case "Get Unique Conversation Id":
        return new ConversationIdBlock();
      case "Send Map Card":
        return new MapCardBlock();
      case "Checkpoint":
        return new CheckpointBlock();
      case "Request Location":
        return new RequestLocationBlock();
      case "Ticketing system":
        return new TicketingSystemBlock();
      case "Dynamic Report":
        return new DynamicFormBlock();
      case "LLM Agent":
        return new AgentBlock();
      default:
        throw new Error("Invalid block type: " + blockType);
    }
  }
}

export default BlockConfigFactory;
