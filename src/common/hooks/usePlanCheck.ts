import planFunction<PERSON><PERSON><PERSON> from "helpers/planFunctionChecker";
import { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";

export const usePlanCheck = (function_name, current_number?) => {
  const planfunction = useBotStore((state) => state.planfunction);

  const [isAllowed, setIsAllowed] = useState(false);

  useEffect(() => {
    const res = planFunctionChecker(
      planfunction,
      function_name,
      current_number
    );
    setIsAllowed(res);
    return () => {};
  }, []);

  return isAllowed;
};
