"use client";
import { useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import constant from "constant";

export const useTestingBot = () => {
  const file_name = useBotStore((state) => state.bot.file_name);

  useEffect(() => {
    if (typeof window !== "undefined") {
      var script = document.createElement("script");
      script.src = constant.MEDIA_STORAGE_URL.concat(
        `Bots/${file_name}/newWebchat.js`
      );
      script.id = "testing-chatbot-script";
      document.getElementsByTagName("body")[0].appendChild(script);
    }
    return () => {
      const elementsToRemove = [
        "searchat-chatbot-iframe",
        "searchat-open-chatbot-button",
        "searchat-chatbot-bubble-tooltip",
        "testing-chatbot-script",
      ];

      for (var i = 0; i < elementsToRemove.length; i++) {
        const element = document.getElementById(elementsToRemove[i]);
        if (element) {
          element.parentNode.removeChild(element);
        }
      }
    };
  }, []);

  return null;
};
