import { ar } from "date-fns/locale";
import { isArabic } from "helpers/helper";
import { useEffect, useState } from "react";
import { useLangStore } from "store/language/lang.store";

const useMultiLanguage = (text: string = "") => {
  const { lang } = useLangStore();
  const template = (ar: string, en: string) =>
    `__ar__:##${ar}## __en__:##${en}##`;
  const [decodedText, setDecodedText] = useState({ ar: "", en: "" });
  const [encodedText, setEncodedText] = useState("");
  useEffect(() => {
    if (!text.includes("__ar__")) {
      const arText = isArabic(text);
      if (arText) {
        setDecodedText({ ar: text, en: "" });
      } else {
        setDecodedText({ ar: "", en: text });
      }
    } else {
      // Define regular expressions to match Arabic and English questions within curly braces
      // Update the regular expressions to include line breaks
const arabicRegexOld = /__ar__:{([\s\S]*?)}\s+/;
const arabicRegexNew = /__ar__:##([\s\S]*?)##\s+/;
const englishRegexOld = /__en__:{([\s\S]*?)}(\s*__|$)/;
const englishRegexNew = /__en__:##([\s\S]*?)##(\s*__|$)/;

      // Use regular expressions to extract questions
      const arabicMatchOld = text.match(arabicRegexOld);
      const arabicMatchNew = text.match(arabicRegexNew);
      const englishMatchOld = text.match(englishRegexOld);
      const englishMatchNew = text.match(englishRegexNew);
      const arabicMatch = arabicMatchOld || arabicMatchNew;
      const englishMatch = englishMatchOld || englishMatchNew;
      // Extract the questions from the matches
      const arabicQuestion = arabicMatch ? arabicMatch[1].trim() : "";
      const englishQuestion = englishMatch ? englishMatch[1].trim() : "";
      setDecodedText({ ar: arabicQuestion, en: englishQuestion });
    }
  }, [text]);
  useEffect(() => {
    setEncodedText(template(decodedText.ar, decodedText.en));
  }, [decodedText]);

  const setValue = (text: string) => {
    setDecodedText({ ...decodedText, [lang]: text });
  };
  const transformTextToLanguageObj = (text: string) => {
    if (!text.includes("__ar__")) {
      if (isArabic(text)) return { ar: text, en: "" };
      return { ar: "", en: text };
    } else {
      // Define regular expressions to match Arabic and English questions within curly braces
    // Update the regular expressions to include line breaks
const arabicRegexOld = /__ar__:{([\s\S]*?)}\s+/;
const arabicRegexNew = /__ar__:##([\s\S]*?)##\s+/;
const englishRegexOld = /__en__:{([\s\S]*?)}(\s*__|$)/;
const englishRegexNew = /__en__:##([\s\S]*?)##(\s*__|$)/;

      // Use regular expressions to extract questions
      const arabicMatchOld = text.match(arabicRegexOld);
      const arabicMatchNew = text.match(arabicRegexNew);
      const englishMatchOld = text.match(englishRegexOld);
      const englishMatchNew = text.match(englishRegexNew);
      const arabicMatch = arabicMatchOld || arabicMatchNew;
      const englishMatch = englishMatchOld || englishMatchNew;
      // Extract the questions from the matches
      const arabicQuestion = arabicMatch ? arabicMatch[1].trim() : "";
      const englishQuestion = englishMatch ? englishMatch[1].trim() : "";
      return { ar: arabicQuestion, en: englishQuestion };
    }
  };
  return {
    result: encodedText,
    value: decodedText[lang],
    setValue,
    transformTextToLanguageObj,
  };
};

export default useMultiLanguage;
