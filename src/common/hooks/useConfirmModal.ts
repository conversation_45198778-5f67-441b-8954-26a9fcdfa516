import { create } from "zustand";

interface ConfirmModalStore {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  type: "delete" | "without_saving" | "send";
  text?: string;
  subtext?: string;
  btntext?: string;
  setType: (type) => void;
  onConfirm: () => void;
  setOnConfirm: (onConfirm: () => void) => void | Promise<void>;
  setText: (text: string) => void;
  setSubtext: (subtext: string) => void;
  setBtntext: (btntext: string) => void;
}

const useConfirmModal = create<ConfirmModalStore>((set) => ({
  isOpen: false,
  type: "delete",
  text: "",
  subtext: "",
  btntext: "",
  onConfirm: () => {},
  onOpen: () => set({ isOpen: true }),
  onClose: () => set({ isOpen: false }),
  setType: (type) => set({ type: type }),
  setOnConfirm: (onConfirm: () => void) => set({ onConfirm }),
  setText: (text: string) => set({ text }),
  setSubtext: (subtext: string) => set({ subtext }),
  setBtntext: (btntext: string) => set({ btntext }),
}));



export default useConfirmModal;
