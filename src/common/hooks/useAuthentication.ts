import auth from "helpers/auth";
import { useState, useEffect } from "react";

function useAuthentication(token, loaded) {
  const [isAuth, setIsAuth] = useState(auth.isAuthenticated());

  useEffect(() => {
    if (token) {
      auth.setToken(token);
      setIsAuth(auth.isAuthenticated());
    } else if (!isAuth && loaded) {
      window.location.pathname = "/login";
    }

    return () => {
      //NOTE In case of Unmount
    };
  });

  return isAuth;
}
export default useAuthentication;
