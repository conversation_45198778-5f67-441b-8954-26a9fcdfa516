import React from "react";
import { CopyIcon, Trash2 } from "lucide-react";

const Tooltip = ({ title, children }) => (
  <div className="relative flex flex-col items-center group">
    {children}
    <div
      role="tooltip"
      className="absolute bottom-0 mb-8 hidden group-hover:block p-2 text-xs text-white bg-gray-700 rounded-md transition-opacity duration-300 opacity-0 group-hover:opacity-100 whitespace-nowrap shadow-md"
    >
      {title}
    </div>
  </div>
);

const SelectMenu = ({ onSelectAll, onUnselectAll, onCopy, onDelete, selectedCount }) => {
  return (
    <div className="flex space-x-3 bg-themeSecondary text-white rounded-full p-2 shadow-lg items-center">
      <span className="text-gray-400 ml-3">({selectedCount}) Selected</span>
      <button
        className="px-3 py-1 rounded-md text-white transition-colors duration-200 hover:shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 active:bg-gray-700"
        onClick={onSelectAll}
        aria-label="Select All"
      >
        Select All
      </button>
      <button
        className="px-3 py-1 rounded-md text-white transition-colors duration-200 hover:shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 active:bg-gray-700"
        onClick={onUnselectAll}
        aria-label="Select None"
      >
        Select None
      </button>
      <div className="w-px h-6 bg-gray-500 mx-2"></div>
      <Tooltip title="Duplicate All">
        <button
          className="p-3 rounded-md hover:bg-gray-500/25 text-white transition-colors duration-200 hover:shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 active:bg-gray-800"
          onClick={onCopy}
          aria-label="Duplicate"
        >
          <CopyIcon size={22} />
        </button>
      </Tooltip>
      <Tooltip title="Delete All">
        <button
          className="p-3 mr-3 rounded-md hover:bg-gray-500/25 text-white transition-colors duration-200 hover:shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 active:bg-gray-800"
          onClick={onDelete}
          aria-label="Delete"
        >
          <Trash2 size={22} />
        </button>
      </Tooltip>
    </div>
  );
};

export default SelectMenu;
