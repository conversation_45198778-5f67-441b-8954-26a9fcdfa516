import React from "react";
import { usePagination, DOTS } from "common/hooks/usePagination";
import { ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
interface PaginationProps {
  onPageChange: (page: number) => void;
  totalCount: number;
  siblingCount?: number;
  currentPage: number;
  pageSize: number;
}

const Pagination: React.FC<PaginationProps> = ({
  onPageChange,
  totalCount,
  siblingCount = 1,
  currentPage,
  pageSize,
}) => {
  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  });

  // If there are less than 2 times in pagination range we shall not render the component
  if (currentPage === 0 || paginationRange?.length < 2) {
    return null;
  }

  const onNext = () => {
    onPageChange(currentPage + 1);
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
  };

  let lastPage = currentPage;
  if (paginationRange && paginationRange?.length) {
    lastPage = paginationRange[paginationRange.length - 1];
  }
  return (
    <>
      <div className="flex items-center justify-center py-1">
        <div className=" w-full  flex items-center justify-between border-t border-white/25">
          <button
            disabled={currentPage === 1}
            onClick={onPrevious}
            className="flex items-center pt-3 text-white/50 hover:text-primary cursor-pointer disabled:cursor-default disabled:text-white/10 disabled:hover:text-white/10"
          >
            <ChevronsLeft className="w-4 h-4" />
            <p className="text-lg ml-3 font-medium leading-none ">Previous</p>
          </button>
          <div className="sm:flex hidden items-end">
            {paginationRange?.map((pageNumber, i) => {
              if (pageNumber === DOTS) {
                return (
                  <span
                    key={i}
                    className="text-lg font-medium text-white/50  mr-4 px-2"
                  >
                    &#8230;
                  </span>
                );
              }
              return (
                <p
                  key={i}
                  className={`text-lg font-medium leading-none cursor-pointer text-white/50  border-t-2  ${
                    pageNumber === currentPage
                      ? "text-primary border-primary"
                      : "hover:text-primary border-transparent hover:border-primary"
                  } pt-3 mr-4 px-2`}
                  onClick={() => onPageChange(pageNumber)}
                >
                  {pageNumber}
                </p>
              );
            })}
          </div>
          <button
            disabled={currentPage === lastPage}
            onClick={onNext}
            className="flex items-center pt-3 text-white/50 hover:text-primary cursor-pointer  disabled:cursor-default disabled:text-white/10 disabled:hover:text-white/10"
          >
            <p className="text-lg font-medium leading-none mr-3">Next</p>
            <ChevronsRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </>
  );
};

export default Pagination;
