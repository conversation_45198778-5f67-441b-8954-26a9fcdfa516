import { cn } from "lib/utils";
import { LucideIcon } from "lucide-react";
import React from "react";

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  border?: boolean;
  hr?: boolean;
  headerIcon?: LucideIcon;
  children: React.ReactNode;
  className?: string;
  svg?: React.FC;
}

export const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  children,
  border,
  hr,
  headerIcon: HeaderIcon,
  className,
  svg: Svg,
  ...props
}) => {
  return (
    <div
      className={cn(
        `flex flex-col shadow-lg shadow-accent hover:shadow-2xl ${
          border ? "border-t-2 border-primary rounded" : ""
        }`,
        className
      )}
      {...props}
    >
      {(<PERSON>olean(title) || Boolean(subtitle)) && (
        <>
          <div className="flex items-center rounded-t-sm justify-between sticky top-0 z-20 bg-[#1A1A1D] p-5">
            <div className={`flex flex-col   gap-1  `}>
              {title && <div className="text-lg">{title}</div>}
              {subtitle && (
                <span className="text-sm text-white/60 capitalize">
                  {subtitle}
                </span>
              )}
            </div>
            {HeaderIcon && (
              <HeaderIcon
                size={subtitle ? 35 : 25}
                className=" text-white/60"
              />
            )}
            {Svg && <Svg />}
          </div>
          {hr && <hr className="text-white/25  w-full" />}
        </>
      )}
      <div
        className={` bg-accent p-5 space-y-6 grow ${
          !Boolean(title) && !Boolean(subtitle) ? "rounded-sm" : "rounded-b-sm"
        }  `}
      >
        {/* <div className="flex flex-col"> */}
        {children}
        {/* </div> */}
      </div>
    </div>
  );
};
