import React, { useState, useRef } from 'react';
import Editor from '@monaco-editor/react';
import DropdownEntities from './dropdownEntities';

export interface CustomEditorProps {
  headers: Array<{ value: string }>; 
  onChangeRequestBodyXML?: (value: string) => void;
  onChangeRequestBody?: (value: string) => void;
  onChangeJS?: (value: string) => void;
  onChangeHTML?: (value: string) => void;
  onChangeMD?: (value: string) => void;
  stringifyBodyValue: string;
  entities?: { [key: string]: any };
  globals?: { [key: string]: any };
  isConditions?: boolean;
  isMin?: boolean;
  className?: string
}

const CustomEditor: React.FC<CustomEditorProps> = ({ 
  headers, 
  onChangeRequestBodyXML, 
  onChangeRequestBody, 
  onChangeJS, 
  onChangeHTML,
  onChangeMD,
  stringifyBodyValue, 
  entities, 
  globals,
  isConditions = false,
  isMin = false,
  className = ''
}) => {
  const editorRef = useRef(null);
  const [monacoInstance, setMonacoInstance] = useState(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [caretPosition, setCaretPosition] = useState({ top: 0, left: 0 });
  const [filteredGlobals, setFilteredGlobals] = useState({});
  const [filteredEntities, setFilteredEntities] = useState({});
  const [highlightedIndex, setHighlightedIndex] = useState(null);

  const defaultLanguage = headers[0]?.value === "application/xml" ? "xml" : headers[0]?.value === "application/json" ? "json": headers[0]?.value === "html" ? 'html' : headers[0]?.value === "markdown" ? 'markdown'  : "javascript" ;
 
  const handleEditorChange = (value) => {
    if (headers[0]?.value === "application/xml" && onChangeRequestBodyXML) {
      onChangeRequestBodyXML(value);
    } else if (headers[0]?.value === "application/json" && onChangeRequestBody) {
      onChangeRequestBody(value);
    } else if (headers[0]?.value === "javascript" && onChangeJS) {
      onChangeJS(value);
    } else if (headers[0]?.value === "html" && onChangeHTML) {
      onChangeHTML(value);
    }else if (headers[0]?.value === "markdown" && onChangeMD) {
      onChangeMD(value);
    }
  };


  const handleEditorMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    setMonacoInstance(monaco);

    const updateSuggestions = (enable) => {
      editor.updateOptions({
        quickSuggestions: enable,
        suggestOnTriggerCharacters: enable,
      });
    };

    editor.onDidChangeCursorPosition((e) => {
      const model = editor.getModel();
      const position = editor.getPosition();
      const textBeforeCursor = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column,
      });

      const match = textBeforeCursor.match(/\${[^}]*$/) ||
                    textBeforeCursor.match(/\$[^{]*$/) ||
                    textBeforeCursor.match(/entities\.[^\s]*$/) ||
                    textBeforeCursor.match(/globals\.[^\s]*$/);

      const inBraces = match ? match[0] : '';

      if (inBraces) {
        setShowDropdown(true);
        updateSuggestions(false);

        if (inBraces.startsWith('entities.') || inBraces.startsWith('globals.')) {
          setFilteredEntities(entities ? Object.keys(entities)
            .filter(key => key.toLowerCase().startsWith(inBraces.split('.')[1].toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = entities[key];
              return obj;
            }, {}) : {});

          setFilteredGlobals(globals ? Object.keys(globals)
            .filter(key => key.toLowerCase().startsWith(inBraces.split('.')[1].toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = globals[key];
              return obj;
            }, {}) : {});
        } else {
          const isGlobal = inBraces.startsWith('${globals.') || inBraces.startsWith('${globals') || inBraces.startsWith('${global');
          const searchStr = isGlobal ? inBraces.slice(10) : inBraces.slice(2);

          setFilteredEntities(entities ? Object.keys(entities)
            .filter(key => key.toLowerCase().startsWith(searchStr.toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = entities[key];
              return obj;
            }, {}) : {});

          setFilteredGlobals(globals ? Object.keys(globals)
            .filter(key => key.toLowerCase().startsWith(searchStr.toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = globals[key];
              return obj;
            }, {}) : {});
        }

        const cursorCoords = editor.getScrolledVisiblePosition(position);
        setCaretPosition({
          top: cursorCoords.top + 20,
          left: cursorCoords.left,
        });
      } else {
        setShowDropdown(false);
        updateSuggestions(true);
      }
    });
  };

  const handleEntitySelect = (entity, type) => {
    if (editorRef.current && monacoInstance) {
      const editor = editorRef.current;
      const model = editor.getModel();
      const position = editor.getPosition();

      const textBeforeCursor = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column,
      });

      const match = textBeforeCursor.match(/\${[^}]*$/) || 
                    textBeforeCursor.match(/\$[^{]*$/) || 
                    textBeforeCursor.match(/entities\.[^\s]*$/) || 
                    textBeforeCursor.match(/globals\.[^\s]*$/);

      const inBraces = match ? match[0] : '';

      const beforeCaret = textBeforeCursor.slice(0, -inBraces.length);
      const afterCaret = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: position.column,
        endLineNumber: position.lineNumber,
        endColumn: model.getLineMaxColumn(position.lineNumber),
      });

      const entityValue = type === 'global' ? globals[entity] : entities[entity];
      const isObject = entityValue === 'object';

      let newValue;
      if (defaultLanguage !== "javascript") {
        if (type === 'global') {
          newValue = isObject
            ? `${beforeCaret}\${globals.${entity}.${afterCaret.replace(/^}/, '')}`
            : `${beforeCaret}\${globals.${entity}}${afterCaret.startsWith('}') ? afterCaret.slice(1) : afterCaret}`;
        } else {
          newValue = isObject
            ? `${beforeCaret}\${${entity}.${afterCaret.replace(/^}/, '')}`
            : `${beforeCaret}\${${entity}}${afterCaret.startsWith('}') ? afterCaret.slice(1) : afterCaret}`;
        }
      } else {
        if (type === 'global') {
          newValue = isObject
            ? `${beforeCaret.replace(/^{/, '')}globals.${entity}.${afterCaret.replace(/^}/, '')}`
            : `${beforeCaret.replace(/^{/, '')}globals.${entity} ${afterCaret.replace(/^}/, '')}`;
        } else {
          newValue = isObject
            ? `${beforeCaret.replace(/^{/, '')}entities.${entity}.${afterCaret.replace(/^}/, '')}`
            : `${beforeCaret.replace(/^{/, '')}entities.${entity} ${afterCaret.replace(/^}/, '')}`;
        }
      }

      const newCursorPosition = beforeCaret.length + (type === 'global' ? `globals.${entity}`.length : entity.length);

      editor.executeEdits("", [{
        range: new monacoInstance.Range(
          position.lineNumber,
          1,
          position.lineNumber,
          model.getLineMaxColumn(position.lineNumber)
        ),
        text: newValue,
        forceMoveMarkers: true,
      }]);
  
      editor.setPosition({
        lineNumber: position.lineNumber,
        column: newCursorPosition + 1,
      });

      setShowDropdown(false);
      setHighlightedIndex(null);
    }
  };

  return (
    <div style={{ position: "relative" }}>
      <div className={`relative ${ isConditions ? 'h-[30rem]' : isMin ? 'h-80' : 'h-[50rem]'} w-full ${className}`}>
        <Editor
          defaultLanguage={defaultLanguage}
          defaultValue=""
          theme="vs-dark"
          onMount={handleEditorMount}
          onChange={handleEditorChange}
          value={stringifyBodyValue}
          options={
            isConditions
              ? { renderValidationDecorations: "off" }
              : {}
          }
        />
      </div>
      {showDropdown && (
        <div
          style={{
            position: "absolute",
            top: caretPosition.top,
            left: caretPosition.left >= 400 ? caretPosition.left - 200 : caretPosition.left,
            zIndex: 1000,
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
            backgroundColor: "#fff",
            borderRadius: "4px",
            overflow: "hidden",
          }}
        >
          <DropdownEntities
            onSelect={handleEntitySelect}
            entities={filteredEntities}
            globals={filteredGlobals}
            highlightedIndex={highlightedIndex}
            onHighlight={setHighlightedIndex}
          />
        </div>
      )}
    </div>
  );
};

export default CustomEditor;
