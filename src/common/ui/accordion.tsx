import { ChevronDown } from 'lucide-react'
import { FC, memo } from 'react'
import { IChildren } from 'types/common.types'
import { cn } from "lib/utils";

interface AccordionProps extends IChildren {
  title?: string
  className?: string
}

const Accordion: FC<AccordionProps> = ({children, title, className}) => {
  return <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
  <summary className={cn("flex cursor-pointer items-center justify-between gap-2 p-4 bg-secondary transition", className)}>
    <span className="text-sm font-medium"> {title} </span>

    <span className="transition group-open:-rotate-180">
      <ChevronDown className="h-4 w-4" />
    </span>
  </summary>
  {children}
  </details>
}

export default memo(Accordion)