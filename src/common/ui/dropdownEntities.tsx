import React from "react";
import { Separator } from "./separator";

interface DropdownEntitiesProps {
  onSelect: (entity: string, type: 'entity' | 'global') => void;
  entities: { [key: string]: any };
  globals: { [key: string]: any };
  highlightedIndex: number | null;
  onHighlight: (index: number) => void;
}

const DropdownEntities: React.FC<DropdownEntitiesProps> = ({ onSelect, entities, globals, highlightedIndex, onHighlight }) => {
  const handleEntityClick = (entityName: string) => {
    onSelect(entityName, 'entity');
  };

  const handleGlobalClick = (globalName: string) => {
    onSelect(globalName, 'global');
  };

  const handleMouseOver = (index: number) => {
    onHighlight(index);
  };

  const entityKeys = Object.keys(entities);
  const globalKeys = Object.keys(globals);
  const allItems = [...entityKeys, ...globalKeys];

  return (
    <div className="relative inline-block text-left">
      <div className="w-56 rounded-md shadow-lg bg-themeSecondary ring-1 ring-black ring-opacity-5 dark:bg-slate-800">
        <div className="px-4 py-2 text-sm text-gray-400">Entities</div>
        {entityKeys.length === 0 ? (
          <div className="py-1">
            <div className="block px-4 py-2 text-sm text-gray-400 cursor-not-allowed w-full text-left">
              No entities available
            </div>
          </div>
        ) : (
          <div className="py-1">
            {entityKeys.map((key, index) => (
              <button
                key={key}
                className={`block px-4 py-2 text-sm text-white w-full text-left focus:outline-none dark:hover:bg-slate-700 ${
                  highlightedIndex === index ? 'bg-white/25' : 'hover:bg-white/25'
                }`}
                onClick={() => handleEntityClick(key)}
                onMouseOver={() => handleMouseOver(index)}
              >
                {key}
              </button>
            ))}
          </div>
        )}
        <Separator className="-mx-1 my-1 bg-white/20 dark:bg-slate-700/20 h-[0.5px]" />
        <div className="px-4 py-2 text-sm text-gray-400">Globals</div>
        {globalKeys.length === 0 ? (
          <div className="py-1">
            <div className="block px-4 py-2 text-sm text-gray-400 cursor-not-allowed w-full text-left">
              No globals available
            </div>
          </div>
        ) : (
          <div>
            {globalKeys.map((key, index) => (
              <button
                key={key}
                className={`block px-4 py-2 text-sm text-white w-full text-left focus:outline-none dark:hover:bg-slate-700 ${
                  highlightedIndex === index + entityKeys.length ? 'bg-white/25' : 'hover:bg-white/25'
                }`}
                onClick={() => handleGlobalClick(key)}
                onMouseOver={() => handleMouseOver(index + entityKeys.length)}
              >
                {key}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DropdownEntities;
