import * as React from "react";
import { useRef, useState } from "react";
import DropdownEntities from "../dropdownEntities";
import { cn } from "lib/utils";
import getCaretCoordinates from "textarea-caret";
import { Button } from "../button";

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  value: string;
  entities?: { [key: string]: any };
  globals?: { [key: string]: any };
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, value, entities, globals, onChange, ...props }, ref) => {
    const [showDropdown, setShowDropdown] = useState(false);
    const [caretPosition, setCaretPosition] = useState({ top: 0, left: 0 });
    const [filteredGlobals, setFilteredGlobals] = useState<{ [key: string]: any }>({});
    const [filteredEntities, setFilteredEntities] = useState<{ [key: string]: any }>({});
    const [highlightedIndex, setHighlightedIndex] = useState<number | null>(null);
    const textareaRef = useRef<HTMLTextAreaElement | null>(null);
    const dropdownRef = useRef<HTMLDivElement | null>(null);
    const buttonRef = useRef<HTMLButtonElement | null>(null);

    const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;
      const caretIndex = e.target.selectionStart;
    
      const inBracesMatch = value.slice(0, caretIndex).match(/\${[^}]*$/) ||
                            value.slice(0, caretIndex).match(/\$[^{]*$/) ||
                            value.slice(0, caretIndex).match(/entities\.[^\s]*$/) ||
                            value.slice(0, caretIndex).match(/globals\.[^\s]*$/);
    
      const inBraces = inBracesMatch ? inBracesMatch[0] : '';
    
      if (inBraces) {
        setShowDropdown(true);

        if (inBraces.startsWith('entities.') || inBraces.startsWith('globals.')) {
          setFilteredEntities(entities ? Object.keys(entities)
            .filter(key => key.toLowerCase().startsWith(inBraces.split('.')[1].toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = entities[key];
              return obj;
            }, {}) : {});

          setFilteredGlobals(globals ? Object.keys(globals)
            .filter(key => key.toLowerCase().startsWith(inBraces.split('.')[1].toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = globals[key];
              return obj;
            }, {}) : {});
        } else {
          const isGlobal = inBraces.startsWith('${globals.') || inBraces.startsWith('${globals') || inBraces.startsWith('${global');
          const searchStr = isGlobal ? inBraces.slice(10) : inBraces.slice(2);

          setFilteredEntities(entities ? Object.keys(entities)
            .filter(key => key.toLowerCase().startsWith(searchStr.toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = entities[key];
              return obj;
            }, {}) : {});

          setFilteredGlobals(globals ? Object.keys(globals)
            .filter(key => key.toLowerCase().startsWith(searchStr.toLowerCase()))
            .reduce((obj, key) => {
              obj[key] = globals[key];
              return obj;
            }, {}) : {});
        }
        
        setCaretPosition(getCaretCoordinates(e.target, caretIndex));
      } else {
        setShowDropdown(false);
      }
    };
    

    const handleEntitySelect = (entity: string, type: 'entity' | 'global') => {
      if (textareaRef.current) {
        const currentValue = textareaRef.current.value;
        const caretIndex = textareaRef.current.selectionStart;
    
        const inBracesMatch = currentValue.slice(0, caretIndex).match(/\${[^}]*$/) || 
                              currentValue.slice(0, caretIndex).match(/\$[^{]*$/) || 
                              currentValue.slice(0, caretIndex).match(/entities\.[^\s]*$/) || 
                              currentValue.slice(0, caretIndex).match(/globals\.[^\s]*$/);
        
        const inBraces = inBracesMatch ? inBracesMatch[0] : '';
    
        const beforeCaret = currentValue.slice(0, caretIndex - inBraces.length);
        const afterCaret = currentValue.slice(caretIndex);
    
        const entityValue = type === 'global' ? globals?.[entity] : entities?.[entity];
        const isObject = typeof entityValue === 'object';
    
        let newValue;
        if (type === 'global') {
          newValue = isObject
            ? `${beforeCaret}\${globals.${entity}.${afterCaret}`
            : `${beforeCaret}\${globals.${entity}}${afterCaret.startsWith('}') ? afterCaret.slice(1) : afterCaret}`;
        } else {
          newValue = isObject
            ? `${beforeCaret}\${${entity}.${afterCaret}`
            : `${beforeCaret}\${${entity}}${afterCaret.startsWith('}') ? afterCaret.slice(1) : afterCaret}`;
        }
    
        const newCaretIndex = beforeCaret.length + (type === 'global' ? `globals.${entity}` : entity).length + 3;
    
        textareaRef.current.value = newValue;
        textareaRef.current.setSelectionRange(newCaretIndex, newCaretIndex);
    
        onChange({ target: { value: newValue } } as React.ChangeEvent<HTMLTextAreaElement>);
        setShowDropdown(false);
        setHighlightedIndex(null);
      }
    };  

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (showDropdown) {
        const entityCount = Object.keys(filteredEntities).length;
        const globalCount = Object.keys(filteredGlobals).length;
        const totalItemCount = entityCount + globalCount;

        if (e.key === 'ArrowDown') {
          e.preventDefault();
          setHighlightedIndex((prevIndex) =>
            prevIndex === null || prevIndex === totalItemCount - 1 ? 0 : prevIndex + 1
          );
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          setHighlightedIndex((prevIndex) =>
            prevIndex === null || prevIndex === 0 ? totalItemCount - 1 : prevIndex - 1
          );
        } else if (e.key === 'Enter' && highlightedIndex !== null) {
          e.preventDefault();
          const allItems = [...Object.keys(filteredEntities), ...Object.keys(filteredGlobals)];
          const selectedItem = allItems[highlightedIndex];
          const isGlobal = Object.keys(filteredGlobals).includes(selectedItem);
          handleEntitySelect(selectedItem, isGlobal ? 'global' : 'entity');
        }
      }
    };

    

    if (!entities && !globals) {
      return (
        <textarea
          className={cn(
            "flex h-20 w-full rounded-md border border-white/25 bg-transparent py-2 px-3 text-sm placeholder:text-slate-400 focus:outline-none focus:border-white/70 focus:ring-slate-400 focus:ring-0 disabled:cursor-default disabled:opacity-50 dark:border-slate-700 dark:text-slate-50 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900",
            className
          )}
          ref={ref}
          value={value}
          onInput={handleInput}
          onChange={onChange}
          onKeyDown={handleKeyDown}
          {...props}
        />
      );
    }

    return (
      <div style={{ position: "relative", display: "flex", flexDirection: "column" }}>
        <textarea
          className={cn(
            "flex-grow h-20 rounded-md border border-white/25 bg-transparent py-2 px-3 text-sm placeholder:text-slate-400 focus:outline-none focus:border-white/70 focus:ring-slate-400 focus:ring-0 disabled:cursor-default disabled:opacity-50 dark:border-slate-700 dark:text-slate-50 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900",
            className
          )}
          ref={(el) => {
            textareaRef.current = el;
            if (ref) {
              if (typeof ref === 'function') {
                ref(el);
              } else {
                (ref as React.MutableRefObject<HTMLTextAreaElement | null>).current = el;
              }
            }
          }}
          value={value}
          onInput={handleInput}
          onChange={onChange}
          onKeyDown={handleKeyDown}
          {...props}
        />
        {showDropdown && (
          <div
            ref={dropdownRef}
            tabIndex={-1}
            style={{
              position: "absolute",
              top: caretPosition.top, 
              left: caretPosition.left,
              zIndex: 1000,
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)"
            }}
          >
            <DropdownEntities
              onSelect={handleEntitySelect}
              entities={filteredEntities}
              globals={filteredGlobals}
              highlightedIndex={highlightedIndex}
              onHighlight={setHighlightedIndex}
            />
          </div>
        )}
      </div>
    );    
  }
);

Textarea.displayName = "Textarea";

export { Textarea };
