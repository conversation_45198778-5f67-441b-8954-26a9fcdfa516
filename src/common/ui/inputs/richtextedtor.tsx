"use client";
import React, { useEffect, useState } from "react";
import "react-quill/dist/quill.snow.css";
import TurndownService from "turndown";
import showdown from "showdown";
import dynamic from "next/dynamic";

const QuillNoSSRWrapper = dynamic(import("react-quill"), {
  ssr: false,
  loading: () => <p>Loading ...</p>,
});

const modules = {
  toolbar: [
    ["bold", "italic", "underline", "strike", "blockquote"],
    [{ list: "ordered" }, { list: "bullet" }, "link"],
    [
      { align: "" },
      { align: "center" },
      { align: "right" },
      { align: "justify" },
    ],
  ],
};

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list",
  "bullet",
  "indent",
  "link",
  "align",
];

const RichTextEditor = ({ onChangeRichText, value, lang }) => {
  const [richText, setRichText] = useState("");

  // useEffect(() => {
  //   setRichText(value);
  // }, [value]);

  const onChange = (value) => {
    setRichText(value);
    var turndownService = new TurndownService();
    var markdown = turndownService.turndown(value);
    onChangeRichText(markdown);
  };

  const markdownToHtmlConverter = (value) => {
    var converter = new showdown.Converter();
    return converter.makeHtml(value);
  };

  return (
    <QuillNoSSRWrapper
      value={richText || markdownToHtmlConverter(value) || ""}
      onChange={onChange}
      modules={modules}
      formats={formats}
      style={{ direction: lang === "ar" ? "rtl" : "ltr", color: "white" }}
    />
  );
};

export default RichTextEditor;
