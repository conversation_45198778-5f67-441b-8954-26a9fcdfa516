import { ImageIcon, UploadCloud, VideoIcon, X } from "lucide-react";
import React from "react";

type TImageInput = {
    imgSrc?: string;
    uploadImg?: (img: File) => void;
    onDeleteImage?: (image_index: number) => void;
  }

const ImageInput: React.FC<TImageInput> = ({
    imgSrc,
    uploadImg,
    onDeleteImage
}) => {
  return (
    <label
    htmlFor={`dropzone-file`}
    className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer  hover:bg-gray-800 dark:border-gray-600 overflow-hidden"
    onDragEnter={(event) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.add("bg-gray-200");
    }}
    onDragOver={(event) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.add("bg-gray-200");
    }}
    onDragLeave={(event) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.remove("bg-gray-200");
    }}
    onDrop={(event) => {
      event.preventDefault();
      event.stopPropagation();
      event.currentTarget.classList.remove("bg-gray-200");
      const files = event.dataTransfer.files;
       uploadImg(files[0]);
    }}
  >
    <div
      className={`flex flex-col items-center justify-center text-center pb-5`}
    >
      <>
        <img
          id={`card-poster`}
          src={imgSrc}
          alt="Preview"
          className={`w-28 h-28 object-cover ${imgSrc ? "block" : "hidden"}`}
        />
      </>
      {!imgSrc && (
        <>
        <UploadCloud className="w-8 h-8 mb-3 text-gray-400" />
          <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
            <span className="font-semibold">Click to upload</span> or drag and
            drop
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 flex gap-2 items-center">
            <ImageIcon size={15} />{" "}

          </p>
        </>
      )}
    </div>
    <input
      id={`dropzone-file`}
      type="file"
      className="hidden"
      onChange={(e) => {
      uploadImg(e.target.files[0]);
      }}
      accept={"image/*"}
      name="img"
    />
  </label>
  )
}

export default ImageInput