import CheckedSVG from "common/icons/CheckedSVG";
import UncheckedSVG from "common/icons/UncheckedSVG";
import React from "react";

interface SwitchProps {
  label?: string;
  checked: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name: string;
}

export const Switch: React.FC<SwitchProps> = ({
  label = "",
  checked,
  onChange,
  name,
}) => {
  
  return (
    <div className="flex gap-3 items-center">
      <label htmlFor={name} className="relative h-8 w-14 cursor-pointer">
        <input
          type="checkbox"
          id={name}
          checked={checked}
          onChange={onChange}
          className="peer sr-only [&:checked_+_span_svg[data-checked-icon]]:block [&:checked_+_span_svg[data-unchecked-icon]]:hidden"
        />

        <span className="absolute inset-y-0 start-0 z-10 m-1 inline-flex h-6 w-6 items-center justify-center rounded-full bg-white/70 text-gray-800 transition-all peer-checked:start-6 peer-checked:text-primary">
          <UncheckedSVG />
          <CheckedSVG />
        </span>

        <span className="absolute inset-0 rounded-full bg-gray-400 transition peer-checked:bg-primary"></span>
      </label>
      {label && <p>{label}</p>}
    </div>
  );
};
