import FilledCheckSVG from "common/icons/FilledCheckSVG";
import React from "react";

interface RadioProps {
  label: string;
  value: string;
  name: string;
  checked: boolean;
  checkMark?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const Radio: React.FC<RadioProps> = ({
  label,
  value,
  name,
  checked,
  onChange,
  checkMark,
}) => {
  return (
    <div>
      <input
        type="radio"
        name={name}
        value={value}
        id={value}
        className="peer hidden [&:checked_+_label_svg]:block"
        checked={checked}
        onChange={onChange}
      />

      <label
        htmlFor={value}
        className="flex cursor-pointer items-center justify-center gap-2 rounded-md border border-white/50 py-1 px-3 text-white hover:border-primary hover:backdrop-brightness-200 peer-checked:border-primary peer-checked:bg-primary peer-checked:text-white"
      >
        {checkMark && <FilledCheckSVG />}
        <p className="text-sm font-medium">{label}</p>
      </label>
    </div>
  );
};
