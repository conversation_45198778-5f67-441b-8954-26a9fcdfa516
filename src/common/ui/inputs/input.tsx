import { cn } from "lib/utils";
import { Check, ShieldClose } from "lucide-react";
import React from "react";
interface InputProps {
  title?: string | React.ReactNode;
  subtitle?: string;
  value?: string | number;
  type?: "text" | "email" | "password" | "number" | "time";
  onChange?: any;
  error?: string;
  name: string;
  placeholder?: string;
  disabled?: boolean;
  bold?: boolean;
  message?: string;
  readOnly?: boolean;
  className?: string;
  onBlur?: any;

}

export const Input: React.FC<InputProps> = ({
  title,
  subtitle,
  value,
  type = "text",
  onChange,
  error,
  name,
  placeholder,
  disabled,
  bold,
  message,
  readOnly,
  className,
  onBlur,
}) => {
  return (
    <>
      <div className="space-y-1 relative w-full">
        <label className={`block ${bold ? "font-bold" : ""}`} htmlFor={name}>
          {title}{" "}
          {subtitle ? (
            <span className="text-xs text-white/50">- {subtitle}</span>
          ) : null}
        </label>
        <input
          id={name}
          name={name}
          className={cn(
            "py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25",
            className
          )}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly || false}
          onBlur={onBlur}
        />
        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
          {error ? (
            <>
              <ShieldClose size={12} />
              {error}
            </>
          ) : null}
        </span>
        <span className="text-green-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
          {message ? (
            <>
              <Check size={12} />
              {message}
            </>
          ) : null}
        </span>
      </div>
    </>
  );
};
