import { Search } from "lucide-react";
import React, { useCallback, useEffect } from "react";
import debounce from "helpers/debouncer";

interface CustomSearchProps {
  placeholder?: string;
  onChange: (keySearch: string) => void;
}

export const CustomSearch: React.FC<CustomSearchProps> = ({
  placeholder,
  onChange,
}) => {
  const handleOnChange = useCallback(
    debounce((e) => onChange(e.target.value)),
    []
  );
  useEffect(() => {
    return () => {
      onChange("");
    };
  }, []);

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
      }}
      className="flex relative w-full text-white/50"
    >
      <button
        title="Custom-Search-Button"
        disabled
        className="absolute inset-y-0 left-2 text-white/25 "
      >
        <Search size={20} />
      </button>
      <input
        type="search"
        className="peer py-2 pl-8 w-full bg-transparent border-2 border-white/25 rounded-lg focus:border-white/70 focus:ring-0"
        placeholder={placeholder}
        onChange={handleOnChange}
      />
    </form>
  );
};
