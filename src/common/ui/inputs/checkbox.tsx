import React from "react";

interface CheckboxProps {
  label?: string;
  name: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  label,
  name,
  onChange,
  checked,
}) => {
  return (
    <label htmlFor={name} className="inline-flex items-center gap-2">
      <input
        type="checkbox"
        id={name}
        className="h-4 w-4 rounded border-gray-300 text-primary  focus:ring-0 focus:border-gray-300"
        name={name}
        onChange={onChange}
        checked={checked}
      />

      {label && <span className="text-sm font-medium ">{label}</span>}
    </label>
  );
};
