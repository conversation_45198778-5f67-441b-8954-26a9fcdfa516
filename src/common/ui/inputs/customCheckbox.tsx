import React from "react";

interface CustomCheckboxProps {
  name: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
  color?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  name,
  onChange,
  checked,
  color,

}) => {
  return (
    <label htmlFor={name} className="inline-flex items-center gap-2">
      <input
        type="checkbox"
        id={name}
        className="h-5 w-5 rounded cursor-pointer focus:ring-0 focus:border-gray-300"
        name={name}
        onChange={onChange}
        checked={checked}
        style={{ color, accentColor: color, borderColor: color}}
      />
    </label>
  );
};
export default CustomCheckbox;