import React, { memo, useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "common/ui/popover";
import { SketchPicker } from "react-color";

interface ColorPickerProps {
  onChange?: (color: string) => void;
  colorField?: string;
  value?: string;
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  onChange,
  colorField,
  value = "#ffffff",
}) => {
  const [color, setColor] = useState(value);
  return (
    <div className="space-y-2">
      {colorField && <div className="font-bold capitalize">{colorField}</div>}
      <Popover>
        <PopoverTrigger asChild>
          <div className="hover:backdrop-brightness-75 w-36 h-10 border rounded flex justify-around items-center text-md tracking-wider overflow-hidden">
            <div className="w-7 h-7 border rounded border-white/25 flex justify-center items-center">
              <div
                className="w-5 h-5 rounded"
                style={{
                  backgroundColor: color,
                }}
              ></div>
            </div>
            {color}
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-fit p-0">
          <SketchPicker
            onChangeComplete={(color) => {
              setColor(color.hex);
              onChange && onChange(color.hex);
            }}
            color={color}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default memo(ColorPicker);
