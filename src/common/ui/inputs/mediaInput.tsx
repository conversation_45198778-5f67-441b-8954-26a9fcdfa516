import { ImageIcon, UploadCloud, VideoIcon, X } from "lucide-react";
import React from "react";

interface MediaInputProps {
  card_index?: number;
  imgSrc?: string;
  uploadImg?: (img: File, card_index: number) => void;
  videoSrc?: string;
  video?: boolean;
  isMultiple?: boolean;
  uploadMultipleImg?: (imgs) => void;
  images?: any[];
  onDeleteImage?: (image_index: number) => void;
  accept?: string;
}

export const MediaInput: React.FC<MediaInputProps> = ({
  uploadImg,
  imgSrc,
  video,
  videoSrc,
  card_index,
  isMultiple,
  uploadMultipleImg,
  images,
  onDeleteImage,
  accept,
}) => {

  return (
    <label
      htmlFor={`dropzone-file-${card_index}`}
      className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer  hover:bg-gray-800 dark:border-gray-600 overflow-hidden"
      onDragEnter={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.add("bg-gray-200");
      }}
      onDragOver={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.add("bg-gray-200");
      }}
      onDragLeave={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.remove("bg-gray-200");
      }}
      onDrop={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.remove("bg-gray-200");
        const files = event.dataTransfer.files;

        const imageFiles = video
          ? Array.from(files).filter(
              (file) =>
                file.type.startsWith("image/") || file.type.startsWith("video/")
            )
          : Array.from(files).filter((file) => file.type.startsWith("image/"));

        isMultiple
          ? uploadMultipleImg(imageFiles)
          : uploadImg(files[0], card_index);
      }}
    >
      <div
        className={`flex flex-col items-center justify-center text-center pb-5 ${
          isMultiple ? "scale-75 p-0" : "pt-4"
        }  `}
      >
        <>
          <img
            id={`card-poster-${card_index}`}
            src={imgSrc}
            alt="Preview"
            className={`w-28 h-28 object-cover ${imgSrc ? "block" : "hidden"}`}
          />
          {video && (
            <video
              id={`card-video-${card_index}`}
              src={videoSrc}
              className={`w-28 h-28 object-cover ${
                videoSrc ? "block" : "hidden"
              }`}
              controls
            ></video>
          )}{" "}
        </>
        {!imgSrc && !videoSrc && !images && (
          <>
          <UploadCloud className="w-8 h-8 mb-3 text-gray-400" />
            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              <span className="font-semibold">Click to upload</span> or drag and
              drop
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 flex gap-2 items-center">
              <ImageIcon size={15} />{" "}
              {video && (
                <>
                  {" "}
                  or <VideoIcon size={15} />
                </>
              )}
            </p>
          </>
        )}
      </div>
      <input
        id={`dropzone-file-${card_index}`}
        type="file"
        className="hidden"
        onChange={(e) => {
          isMultiple
            ? uploadMultipleImg(e.target.files)
            : uploadImg(e.target.files[0], card_index);
        }}
        accept={accept ? accept : video ? "image/*, video/*" : "image/*"}
        name="img"
        multiple={isMultiple}
      />
    </label>
  );
};
