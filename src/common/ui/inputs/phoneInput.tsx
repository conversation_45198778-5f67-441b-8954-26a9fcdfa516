import { Input } from "common/ui/inputs/input";
import { useEffect, useState } from "react";
import countryCodes from "data/countryCodes.json";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { getCountryCode } from "helpers/phone";

interface PhoneInputProps {
  disabled?: boolean;
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({
  disabled,
  value,
  onChange,
  error,
}) => {
  const [countryCode, setCountryCode] = useState("");
  const [phone, setPhone] = useState("");

  useEffect(() => {
    const country = getCountryCode(value);
    setCountryCode(country?.dial_code || "");
    setPhone(value);
  }, [value]);

  const handleCodeChange = (code) => {
    const number = phone.replace(countryCode, code);

    onChange(number);
    setPhone(number);
  };

  return (
    <div className="space-y-1 relative w-full">
      <label className="block">Phone</label>
      <div className="flex gap-2 items-end">
        <Select
          value={countryCode}
          onValueChange={(value) => {
            handleCodeChange(value);
          }}
          disabled={disabled}
        >
          <SelectTrigger className="w-1/2 ">
            <SelectValue placeholder="Country Code" />
          </SelectTrigger>
          <SelectContent>
            {countryCodes.map((country, i) => (
              <SelectItem key={i} value={country.dial_code}>
                {country.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Input
          name="phone"
          placeholder="Phone"
          disabled={disabled}
          value={phone}
          error={error}
          onChange={(e) => {
            const sanitizedValue = e.target.value.replace(/[^\d-]/g, ""); // Remove non-digit and non-hyphen characters
            onChange(sanitizedValue);
          }}
        />
      </div>
    </div>
  );
};
