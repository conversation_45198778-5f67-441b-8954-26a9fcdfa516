import React from "react";

type TooltipType = {
  children: React.ReactNode;
  text: string;
};

export const Tooltip = ({ children, text }: TooltipType) => {
  return (
    <div className="group cursor-pointer relative  z-50">
      {" "}
      {children}
      <div className="opacity-0 bg-black text-white text-center text-xs rounded-lg py-2 absolute  group-hover:opacity-100 left-1/2 -translate-x-1/2 bottom-full px-3 mb-1 pointer-events-none whitespace-nowrap z-50">
        {text}
        <svg
          className="absolute text-black h-2 w-full left-0 top-full"
          x="0px"
          y="0px"
          viewBox="0 0 255 255"
          xmlSpace="preserve"
        >
          <polygon className="fill-current" points="0,0 127.5,127.5 255,0" />
        </svg>
      </div>
    </div>
  );
};
