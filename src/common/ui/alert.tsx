import { InfoIcon } from "lucide-react";
import React from "react";
import { cn } from "lib/utils";

interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  setShowAlert?: (showAlert: boolean) => void;
  title?: string;
  type?: string;
  content?: string;
}

export const Alert: React.FC<AlertProps> = ({
  className,
  setShowAlert,
  title,
  type = "info",
  content,
}) => {
  return (
    <div
      className={cn(
        "text-black  bg-amber-300 px-6 py-4 border-0 rounded relative mb-4 ",
        className
      )}
    >
      <span className="text-xl inline-block mr-5 align-middle">
        <InfoIcon />
      </span>
      <span className="inline-block align-middle mr-8">
        <b className="capitalize">{type}!</b> {title && title}
      </span>
      {content && <div className="pl-11 py-3">{content}</div>}

      {setShowAlert && (
        <button
          className="absolute bg-transparent text-2xl font-semibold leading-none right-0 top-0 mt-4 mr-6 outline-none focus:outline-none"
          onClick={() => setShowAlert(false)}
        >
          <span>×</span>
        </button>
      )}
    </div>
  );
};
