import React from "react";

const GenesysSVG = ({ width = "44px", height = "44px" }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.0"
      width={width}
      height={height}
      viewBox="0 0 121.000000 121.000000"
      preserveAspectRatio="xMidYMid meet"
    >
      <metadata>
        Created by potrace 1.16, written by <PERSON> 2001-2019
      </metadata>
      <g
        transform="translate(0.000000,121.000000) scale(0.100000,-0.100000)"
        fill="#ff4f1f"
        stroke="none"
      >
        <path d="M775 1203 c-38 -10 -65 -36 -76 -73 -24 -79 21 -140 101 -140 49 0 67 10 94 50 51 76 -28 185 -119 163z" />
        <path d="M355 911 c-48 -22 -79 -54 -100 -103 -41 -99 18 -218 123 -248 48 -13 346 -13 394 0 76 22 138 102 138 180 0 46 -26 104 -62 137 -49 46 -86 53 -275 53 -152 0 -184 -3 -218 -19z m425 -66 c55 -28 76 -105 45 -165 -28 -53 -55 -60 -250 -60 -195 0 -222 7 -250 60 -30 59 -14 125 41 161 26 17 48 19 207 19 140 0 184 -3 207 -15z" />
        <path d="M514 455 c-59 -30 -92 -74 -100 -138 -9 -61 15 -126 61 -166 54 -47 174 -64 285 -40 188 40 203 295 21 357 -19 7 -75 12 -126 12 -77 0 -99 -4 -141 -25z m266 -61 c80 -48 80 -168 -1 -210 -39 -20 -200 -19 -239 1 -77 40 -79 168 -2 209 40 22 205 22 242 0z" />
      </g>
    </svg>
  );
};

export default GenesysSVG;
