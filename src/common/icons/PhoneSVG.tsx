const PhoneSVG = () => {
  return (
    <svg
      fill="#fff"
      width="40px"
      height="40px"
      viewBox="-3.2 -3.2 38.40 38.40"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      stroke="#fff"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0">
        <path
          transform="translate(-3.2, -3.2), scale(1.2)"
          d="M16,28.508699864149094C18.36859268109893,28.46738038901815,20.892212997474715,30.41739523760397,22.96253822589114,29.266008232048065C25.044950454012593,28.107899183884793,24.56611893003984,24.802749808938202,26.03595665433358,22.927320706169883C27.636494450757116,20.885125765952466,31.44196660262093,20.486875325422044,31.851127463566684,17.92467533293511C32.2544559791249,15.398997981625525,29.272892295362833,13.63424283804162,27.860494893138977,11.50190760347556C26.731076683787606,9.79679384542948,25.75554007276484,8.062237332390675,24.34044214555393,6.585586975706377C22.83486898493338,5.014525989823607,21.37093934226043,3.2708859227201073,19.308296249598648,2.577714489639858C17.194618838115055,1.8673924733880254,14.93297178813777,2.433260694543284,12.716750041593732,2.6793312875310384C10.233258510643703,2.955077265862813,7.0389620388972745,2.1693486038152234,5.470092966951398,4.114192007269176C3.8104731093846587,6.171534324704581,5.776529815605752,9.334101773050385,5.257659995710249,11.925967817571578C4.845881094918611,13.982891750875403,3.1291529801255127,15.539933904195264,2.7690403143437745,17.606529364966146C2.340381778119326,20.066492230362975,1.8373749861056419,22.766452126879045,2.9819461341261224,24.985713789443544C4.138110148209897,27.22745339342567,6.474048338492707,28.85227488727936,8.908789813670804,29.51117216954104C11.247112248010842,30.143976232208708,13.577933210997823,28.55095218100048,16,28.508699864149094"
          fill="#9C6DF7"
          strokeWidth="0"
        ></path>
      </g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        {" "}
        <path d="M8.194 1.156c1.169 1.612 2.563 3.694 4.175 6.237 0.406 0.688 0.344 1.512-0.181 2.481-0.2 0.406-0.706 1.331-1.512 2.787 0.887 1.25 2.238 2.787 4.056 4.6s3.331 3.169 4.538 4.056c1.45-0.85 2.381-1.369 2.788-1.575 0.525-0.281 1.031-0.425 1.512-0.425 0.363 0 0.688 0.081 0.969 0.244 1.856 1.131 3.956 2.525 6.294 4.175 0.444 0.325 0.694 0.769 0.756 1.331 0.063 0.569-0.113 1.169-0.512 1.819-0.2 0.281-0.525 0.694-0.969 1.244-0.444 0.544-1.113 1.231-2 2.056s-1.613 1.244-2.181 1.244h-0.063c-4.269-0.169-9.531-3.369-15.762-9.6-6.237-6.238-9.438-11.494-9.6-15.769 0-0.563 0.412-1.3 1.244-2.212 0.825-0.906 1.506-1.563 2.025-1.969 0.525-0.4 0.969-0.725 1.331-0.969 0.444-0.325 0.95-0.481 1.513-0.481 0.694 0 1.212 0.244 1.581 0.725zM6.194 2.425c-0.85 0.606-1.644 1.287-2.394 2.031-0.744 0.75-1.181 1.3-1.3 1.662 0.163 3.756 3.156 8.537 8.988 14.35s10.625 8.819 14.375 9.019c0.325-0.119 0.856-0.563 1.606-1.331s1.425-1.575 2.025-2.419c0.119-0.163 0.163-0.3 0.119-0.425-2.419-1.694-4.438-3.044-6.056-4.056-0.163 0-0.363 0.063-0.606 0.181-0.363 0.2-1.269 0.706-2.725 1.512l-1.031 0.606-1.031-0.669c-1.331-0.925-2.944-2.363-4.844-4.3-1.894-1.894-3.306-3.512-4.238-4.844l-0.725-0.969 0.606-1.088c0.806-1.45 1.313-2.363 1.512-2.725 0.119-0.244 0.181-0.444 0.181-0.606-1.438-2.294-2.769-4.313-3.981-6.050h-0.063c-0.156 0-0.3 0.044-0.419 0.119z"></path>{" "}
      </g>
    </svg>
  );
};

export default PhoneSVG;
