import { FC, memo } from "react";

const GoogleDriveSVG: FC = () => {
  return (
    <svg
      fill="#ffffff"
      width="20px"
      height="20px"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      data-name="Layer 1"
      stroke="#ffffff"
      strokeWidth="0.0000000000000000000000000003"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
        stroke="#CCCCCC"
        strokeWidth="0.4800000000000001"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <path d="M22,14.44a.62.62,0,0,0,0-.13.61.61,0,0,1,0-.12l-.05-.12h0l-6-10.29A1,1,0,0,0,15,3.29H9a1,1,0,0,0-.5.13l-.11.08a.73.73,0,0,0-.09.08.58.58,0,0,0-.1.12s0,0-.06.08h0l-6,10.33a1,1,0,0,0,0,1l3,5.08h0a.83.83,0,0,0,.11.15l0,.06a1.1,1.1,0,0,0,.44.26h0a.83.83,0,0,0,.22,0H18a1,1,0,0,0,.86-.49l3-5.14h0l.05-.12a.61.61,0,0,1,0-.12.53.53,0,0,0,0-.13.51.51,0,0,0,0-.13A.59.59,0,0,0,22,14.44ZM6,17.73l-1.79-3.1L9,6.27l.87,1.5,1,1.66L7,15.91ZM12,11.41l1.26,2.16H10.72Zm5.43,7.3H7.7l1.84-3.14h9.72Zm-1.86-5.14L10.74,5.29h3.69l4.83,8.28Z"></path>
      </g>
    </svg>
  );
};

export default memo(GoogleDriveSVG);
