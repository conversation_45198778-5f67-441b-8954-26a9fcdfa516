const TrelloSVG = () => {
  return (
    <svg
      viewBox="0 0 256 256"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="xMidYMid"
      fill="#000000"
      width="44px"
      height="44px"
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <defs>
          <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="a">
            <stop stopColor="#0091E6" offset="0%"></stop>
            <stop stopColor="#0079BF" offset="100%"></stop>
          </linearGradient>
        </defs>
        <rect fill="url(#a)" width="256" height="256" rx="25"></rect>
        <rect
          fill="#FFF"
          x="144.64"
          y="33.28"
          width="78.08"
          height="112"
          rx="12"
        ></rect>
        <rect
          fill="#FFF"
          x="33.28"
          y="33.28"
          width="78.08"
          height="176"
          rx="12"
        ></rect>
      </g>
    </svg>
  );
};

export default TrelloSVG;
