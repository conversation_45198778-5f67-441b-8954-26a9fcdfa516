const LLMIntegrationSVG = ({ width = "44px", height = "44px", primaryColor = "rgb(0,122,255)", sparkleColor = "rgb(255,215,0)" }) => {
  return (
    <svg
      viewBox="0 0 64 64"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill="none"
    >
      {/* Message Bubble */}
      <path
        d="M12 52V16a4 4 0 0 1 4-4h32a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H20l-8 8z"
        fill={primaryColor}
        stroke={primaryColor}
        strokeWidth="2"
      />
      {/* Sparkles */}
      <path
        d="M46 10l2 4 4 2-4 2-2 4-2-4-4-2 4-2 2-4zm-8 12l1.5 3 3 1.5-3 1.5-1.5 3-1.5-3-3-1.5 3-1.5 1.5-3zm18 6l2 4 4 2-4 2-2 4-2-4-4-2 4-2 2-4z"
        fill={sparkleColor}
      />
    </svg>
  );
};

export default LLMIntegrationSVG;