import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";
import { HelpCircle } from "lucide-react";
import { FC, memo } from "react";
import { IChildren } from "types/common.types";

interface HelpHoverCardProps extends IChildren {}

const HelpHoverCard: FC<HelpHoverCardProps> = ({ children }) => {
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <HelpCircle size={25} className="cursor-pointer text-amber-500" />
      </HoverCardTrigger>
      <HoverCardContent className="w-60 bg-black ring-2 ring-gray-500/50 text-sm">
        {children}
      </HoverCardContent>
    </HoverCard>
  );
};

export default memo(HelpHoverCard);
