import { <PERSON><PERSON> } from "common/ui/button";
import { Card } from "common/ui/card";
import { LucideIcon, ToyBrick } from "lucide-react";
import React from "react";

interface ViewCardProps {
  title: string;
  description: string;
  onClick: () => void;
  icon: LucideIcon;
  btnText: string;
  btnIcon: LucideIcon;
}

export const ViewCard: React.FC<ViewCardProps> = ({
  title,
  description,
  onClick,
  icon: Icon,
  btnText,
  btnIcon: BtnIcon,
}) => {
  return (
    <Card>
      <div className="flex flex-col justify-between gap-3">
        <Icon size={45} className="text-white/40" />
        <div className="text-lg">{title}</div>
        <span className="text-white/70 text-sm">{description}</span>
        <Button onClick={onClick} variant="outline">
          <BtnIcon size={15} className="mr-2 h-4 w-4" /> {btnText}
        </Button>
      </div>
    </Card>
  );
};
