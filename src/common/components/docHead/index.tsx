import Head from "next/head";
import { memo } from "react";
import { NextPage } from "next";

interface Props {
    title: string;
    description: string;
    keywords: string;
}
const DocHead: NextPage<Props> = (props) => {
    const { title, description, keywords } = props;
    return (
        <>
            <Head>
                <title>{title}</title>
                <meta name="viewport" content="initial-scale=1.0, width=device-width" />
                <meta name="description" content={description} />
                <meta name="keywords" content={keywords} />
                <link
                    rel="icon"
                    type="image/png"
                    sizes="16x16"
                    href="../assets/backgrounds/logo.png"
                ></link>
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" />
                <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap" rel="stylesheet"  crossOrigin="anonymous"></link>
                <link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" />
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900;1000&display=swap" rel="stylesheet"  crossOrigin="anonymous"></link>
            </Head>
        </>
    );
};
export default memo(DocHead);