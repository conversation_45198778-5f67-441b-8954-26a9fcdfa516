import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { useLoaderContext } from "context/loaderContext";
import { Trash2, X } from "lucide-react";
import React, { useEffect, useState } from "react";

export const ConfirmModal = () => {
  const { isOpen, onClose, onConfirm, type, text, subtext, btntext } = useConfirmModal();

  const [loading, setLoading] = useState(false);
  const {loader} = useLoaderContext()

  const [showModal, setShowModal] = useState(false);
  useEffect(() => {
    setShowModal(isOpen);
  }, [isOpen]);

  const handleClose = () => {
    setShowModal(false);
    onClose();
  };
  const handleConfirm =async () => {
    setLoading(true);
    loader?.continuousStart();
  await  onConfirm();
    setLoading(false);
    loader?.complete();
    onClose();
  };

  const confirm_text = text ? text :
    type === "delete"
      ? "Are you sure you want to delete?"
      : type === "without_saving" ? "Are you sure you want to go back without saving?"
      : type === "send" ? "Are you sure you want to send" : ""
      ;
  const confirm_subtext = subtext ? subtext :
    type === "delete"
      ? "You will delete this permenantly"
      : type === "without_saving" ? "You will lose the data"
      : type === "send" ? "this will send"
      : "" ;
    
  return (
    <div
      className={`justify-center 
  items-center 
  flex 
  overflow-x-hidden 
  overflow-y-auto 
  fixed 
  inset-0 
  z-50
  h-full
  w-full
  outline-none 
  focus:outline-none
  bg-neutral-800/90  text-white ${showModal ? "block" : "hidden"}`}
    >
      <div
        className="
            relative 
            w-5/6
            md:w-4/6
            lg:w-3/6
            xl:w-2/5
            my-6
            mx-auto 
            h-auto 
            "
      >
        {/*content*/}
        <div
          className={`
            translate
            duration-300
            h-full
            ${showModal ? "-translate-y-52" : "translate-y-full"}
            ${showModal ? "opacity-100" : "opacity-0"}
        `}
        >
          <div
            className="
            translate
            h-full
            lg:h-auto
            md:h-auto
            border-0 
            rounded-lg 
            shadow-lg 
            relative 
            flex 
            flex-col 
            w-full 
            bg-secondary 
            outline-none 
            focus:outline-none
            "
          >
            {/*header*/}
            <div
              className=" flex 
                items-center 
                p-4
                rounded-t
                
                relative
                border-b-[1px]
                border-white/25
                "
            >
              <button
                title="close"
                className="p-1 border-0 hover:opacity-70 transition absolute right-9"
                onClick={handleClose}
              >
                <X size={18} />
              </button>
              <div className="">
                <div className="text-lg font-semibold">{confirm_text}</div>
                <div className="text-md text-white/25">{confirm_subtext}</div>
              </div>
            </div>
            {/*body*/}
            <div className="relative p-6 flex-auto max-h-[450px] overflow-y-auto">
              <div
                className="
                    flex 
                    flex-row 
                    items-center 
                    gap-4 
                    w-full
                    justify-end
                  "
              >
                <Button onClick={handleClose} variant="outline">
                  Cancel
                </Button>

                <Button
                  variant={type === "delete" ? "destructive" : "default"}
                  onClick={handleConfirm}
                  loading={loading}
                >
                  {btntext ? btntext : type === "delete" ? (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" /> Delete
                    </>
                  ) : type ==='send' ? "Send" 
                  : (
                    "Continue"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
