import { Button } from "common/ui/button";
import { Loader2, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { cn } from "lib/utils";
import { useLoaderContext } from "context/loaderContext";
interface ModalProps {
  isOpen?: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  onSave?: () => void | Promise<void>;
  footer?: boolean;
  className?: string;
  maxHeight?: string;
  saveButtonText?: string;
}

export const FullModal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  loading,
  disabled,
  onSave,
  footer,
  className,
  maxHeight,
  saveButtonText= "Save",
}) => {
  const [showModal, setShowModal] = useState(isOpen);
  const {loader} = useLoaderContext();
  useEffect(() => {
    setShowModal(isOpen);
  }, [isOpen]);

  const handleClose = () => {
    setShowModal(false);
    onClose();
  };

  const handleSave = async () => {
    loader?.continuousStart();
    await onSave();
    loader?.complete();
  }

  return (
    <div
      className={` justify-center 
      items-center 
      flex 
      overflow-x-hidden 
      overflow-y-auto 
      fixed 
      inset-0 
      z-50 
      outline-none 
      focus:outline-none
      bg-neutral-800/90
      ${showModal ? "visible" : "invisible"}`}
    >
      <div
        className={cn(
          " relative w-5/6 md:w-4/6 lg:w-3/6 xl:w-2/5 my-6 mx-auto h-auto ",
          className
        )}
      >
        {/*content*/}
        <div
          className={`
        translate
        duration-300
        h-full
        ${showModal ? "translate-y-0" : "translate-y-full"}
        ${showModal ? "opacity-100" : "opacity-0"}
      `}
        >
          <div
            className="
          translate
          h-full
          lg:h-auto
          md:h-auto
          border-0 
          rounded-lg 
          shadow-lg 
          relative 
          flex 
          flex-col 
          w-full 
          bg-secondary 
          outline-none 
          focus:outline-none
        "
          >
            {/*header*/}
            <div
              className="
            flex 
            items-center 
            p-4
            rounded-t
            
            relative
            border-b-[1px]
            border-white/25
            "
            >
              <button
              title="closeButton"
              className="
              p-1
              border-0 
              hover:opacity-70
              transition
              absolute
              right-3
              "
                onClick={handleClose}
              >
                <X size={18} />
              </button>
              <div className="text-lg font-semibold">{title}</div>
            </div>

            <div className="space-y-3">
              <div
                className={`relative p-6 flex-auto max-h-[500px] overflow-y-auto ${maxHeight}`}
              >
                {children}
              </div>
              {footer && (
                <>
                  <hr className="text-white/25" />
                  <div className="flex justify-end p-3">
                    <Button variant="outline" onClick={handleClose}>
                      Cancel
                    </Button>
                    <Button
                      loading={loading}
                      disabled={disabled}
                      onClick={handleSave}
                      className="ml-2"
                    >
                      {saveButtonText}
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
