"use client";

import { useLoaderContext } from "context/loaderContext";
import React, { useState, useEffect } from "react";
import LoadingBar from "react-top-loading-bar";

interface ClientOnlyProps {
  children: React.ReactNode;
}

const ClientOnly: React.FC<ClientOnlyProps> = ({ children }) => {
  const [hasMounted, setHasMounted] = useState(false);
  const { loaderRef } = useLoaderContext();

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted)
    return (
      <>
        <div className="w-screen h-screen bg-secondary flex items-center justify-center">
          <img
            className="h-20 animate-bounce"
            src="./assets/backgrounds/logo.png"
          ></img>
        </div>
      </>
    );

  return (
    <>
      <LoadingBar color={"#7950ED"} ref={loaderRef} />
      {children}
    </>
  );
};

export default ClientOnly;
