"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "common/ui/sheet";
import { useState } from "react";
import { IFAQ } from "store/faq/faq.types";
import { VoiceEditor } from "./voiceEditor";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";
import { IItem } from "store/item/item.types";

interface UpdateVoiceSheetProps {
  children: React.ReactNode;
  item: IFAQ | ISmallTalk | IItem;
  answer: string;
  handleUpdate: (
    data: { voice_path_female: string; voice_path_male: string },
    item?: IFAQ | ISmallTalk | IItem 
  ) => void;
}

export const UpdateVoiceSheet: React.FC<UpdateVoiceSheetProps> = ({
  children,
  item,
  answer,
  handleUpdate,
}) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto z-40" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Update Voice</SheetTitle>
          <SheetDescription>
            Modify your voice here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <VoiceEditor
          handleUpdate={handleUpdate}
          item={item}
          voice_path_male={item?.voice_path_male || ""}
          voice_path_female={item?.voice_path_female || ""}
          answer={answer}
        />
      </SheetContent>
    </Sheet>
  );
};
