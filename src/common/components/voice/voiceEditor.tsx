import { <PERSON><PERSON> } from "common/ui/button";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useUserStore from "store/user/user.store";
import { IFAQ } from "store/faq/faq.types";
import { getAudio, tashkeelCall } from "apis/tts.api";
import { extractMultiLangFromString, handleMultiVoice,removeBetweenSymbols } from "store/voice/voice.helper";
import toast from "react-hot-toast";
import VoiceWavePlayer from "./VoiceWavePlayer";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";
import useVoiceStore from "store/voice/voice.store";
import { Loader } from "lucide-react";
import { BotVoice } from "views/knowledgeBase/components/botVoice";
import { SheetFooter } from "common/ui/sheet";
import { IItem } from "store/item/item.types";
import { useLangStore } from "store/language/lang.store";
import { TVoice } from "store/voice/voice.types";
export type TVoiceBody = {
  bot_id: number;
  user_id: number;
  voice_path_male?: string;
  voice_path_female?: string;
};
interface VoiceEditorProps {
  voice_path_male: string;
  voice_path_female: string;
  answer: string;
  isMultiLang?: boolean;
  handleUpdate: (data: unknown, item?: IFAQ | ISmallTalk | IItem) => void;
  item?: IFAQ | ISmallTalk | IItem ;
}

export const VoiceEditor: React.FC<VoiceEditorProps> = ({
  voice_path_male,
  voice_path_female,
  answer,
  handleUpdate,
  isMultiLang,
  item,
}) => {
  const selectedVoices  = useVoiceStore((state)=>state.selectedVoices);
  const botVoice  = useVoiceStore((state)=>state.botVoice);

  const bot = useBotStore((state) => state.bot) as IBot;
  const user_id = useUserStore((state) => state.user).user_id;
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingAudio, setLoadingAudio] = useState<boolean>(false);
  const [selectedVoice, setSelectedVoice] = useState<TVoice | null>(null);
  const [audioUrl, setAudioUrl] = useState("");
  const [formData, setFormData] = useState({
    answer,
    voice_path_male,
    voice_path_female,
  });
  const lang = useLangStore()?.lang

  useEffect(() => {
    setFormData((data) => {
      return {
        answer,
        voice_path_male: isMultiLang
          ? voice_path_male || `ar:_en:`
          : voice_path_male || "",
        voice_path_female: isMultiLang
          ? voice_path_female || `ar:_en:`
          : voice_path_female || "",
      };
    });
  }, [answer, voice_path_female, voice_path_male,isMultiLang]);



  useEffect(() => {
    setSelectedVoice(()=>{
      const filteredVoice = selectedVoices?.find((voice) => {
        return (
          voice.gender.toLowerCase() === botVoice?.tts_gender.toLowerCase() &&
          voice.language.toLowerCase() === lang
        );
      });
      return filteredVoice
    });
  }, [selectedVoices,lang]);


  useEffect(() => {
    const genderKey =
      selectedVoice?.gender.toLowerCase() === "female"
        ? "voice_path_female"
        : "voice_path_male";
    const voicePaths = formData[genderKey];

    if (isMultiLang) {
      const extractedURLs = extractMultiLangFromString(voicePaths);
      const audioUrl = lang === 'ar' ? extractedURLs.ar : extractedURLs.en;
      setAudioUrl(audioUrl || "");
    } else {
      setAudioUrl(voicePaths);
    }
  }, [formData, selectedVoice,answer,isMultiLang]);

  const onChangeHandler = (key, value) => {
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  const handleTashkeel = async () => {
    setLoading(true);
    const tashkeelContent = await tashkeelCall({
      text: formData.answer,
    });
    setLoading(false);
    setFormData((prev) => ({ ...prev, answer: tashkeelContent?.result }));
  };

  const handleGetAudio = async () => {
    try {
      setLoadingAudio(true);
      const generatedVoice = await getAudio(
        selectedVoice.value,
        removeBetweenSymbols(formData.answer)
      );
      setAudioUrl(generatedVoice.remote_path);
      if (isMultiLang) {
        handleSubmit(generatedVoice.remote_path);
      }
    } catch (error) {
    } finally {
      setLoadingAudio(false);
    }
  };


  const handleSubmit = async (updatedAudioUrl: string) => {
    let dataToUpdate = {
      bot_id: bot.bot_id,
      user_id,
    } as TVoiceBody;
    if (isMultiLang) {
      if (selectedVoice.gender.toLowerCase() === "male") {
        const voice_path_male = handleMultiVoice(
          formData,
          updatedAudioUrl,
          "male"
        );

        dataToUpdate = { ...dataToUpdate, voice_path_male: voice_path_male };
      } else {
        const voice_path_female = handleMultiVoice(
          formData,
          updatedAudioUrl,
          "female"
        );

        dataToUpdate = {
          ...dataToUpdate,
          voice_path_female: voice_path_female,
        };
      }
    } else {
      if (selectedVoice.gender.toLowerCase() === "male") {
        dataToUpdate = { ...dataToUpdate, voice_path_male: updatedAudioUrl };
      } else {
        dataToUpdate = { ...dataToUpdate, voice_path_female: updatedAudioUrl };
      }
    }
    try {
      setLoading(true);
      handleUpdate(dataToUpdate, item);
      setFormData({ ...formData, ...dataToUpdate });
      toast.success(
        `${
          dataToUpdate?.voice_path_female
            ? "female Voice updated"
            : "male voice updated"
        }`
      );
    } catch (error) {
      toast.error(`an error occurred while updating the voice`);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectGender = (gender: "male" | "female") => {
    setSelectedVoice(()=>{
      const filteredVoice = selectedVoices?.find((voice) => {
        return (
          voice.gender.toLowerCase() === gender &&
          voice.language.toLowerCase() === lang
        );
      });
      return filteredVoice
    });
  };

  return (
    <div className="flex flex-col   text-white/70">
      <div className=" relative">
        <BotVoice
          onChange={(e) => onChangeHandler("answer", e.target.value)}
          value={formData.answer}
          title="Bot Pronunciation"
          handleTashkeel={handleTashkeel}
          loading={loading}
        />
      </div>
      <div className="my-2 flex items-center justify-center ">
        <Button
          onClick={handleGetAudio}
          className="w-52 flex items-center justify-between "
          disabled={!formData.answer?.trim() || loadingAudio}
          type="button"
        >
          <p className="w-1">
            {" "}
            {loadingAudio ? <Loader className="animate-spin" /> : null}
          </p>{" "}
          <p className="w-full">Generate Audio</p>
        </Button>
      </div>
      <div className=" my-2">
        <VoiceWavePlayer
          handleSelectGender={handleSelectGender}
          selectedVoice={selectedVoice}
          audioUrl={audioUrl}
          isCopy={true}
          loadingAudio={loadingAudio}
        />
      </div>
      {!isMultiLang &&
      <SheetFooter>
        <Button
          loading={loadingAudio}
          disabled={loading || loadingAudio}
          onClick={() => handleSubmit(audioUrl)}
        >
          Save changes
        </Button>
      </SheetFooter>
       }
    </div>
  );
};
