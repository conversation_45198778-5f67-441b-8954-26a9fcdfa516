import React, { useState } from "react";
import WavesurferPlayer from "@wavesurfer/react";
import { Input } from "common/ui/inputs/input";
import { Copy, Pause, Play, RotateCcw, RotateCw } from "lucide-react";
import { Slider } from "common/ui/slider";
import { copyToClipboard, isArabic } from "helpers/helper";
import { Button } from "common/ui/button";
import { TVoice } from "store/voice/voice.types";
import VoiceLoader from "./VoiceLoader";
type TVoiceWavePlayerProps = {
  audioUrl: string;
  isCopy?: boolean;
  loadingAudio: boolean;
  handleSelectGender: (gender: "male" | "female") => void;
  selectedVoice: TVoice;
};
const VoiceWavePlayer: React.FC<TVoiceWavePlayerProps> = ({
  audioUrl,
  selectedVoice,
  isCopy,
  loadingAudio,
  handleSelectGender,
}) => {
  const [wavesurfer, setWavesurfer] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioRate, setAudioRate] = useState(1);
  const [zoomRate, setZoomRate] = useState(1);

  const onReady = (ws) => {
    setWavesurfer(ws);
    setIsPlaying(false);
  };

  const onPlayPause = () => {
    wavesurfer && wavesurfer.playPause();
  };

  const manipulateVoice = (rate: number) => {
    wavesurfer && wavesurfer.setPlaybackRate(rate);
    wavesurfer && wavesurfer.zoom(100);
  };
  const zoomVoice = (zoom: number) => {
    wavesurfer && wavesurfer.zoom(zoom);
  };

  const skipForward = () => {
    if (wavesurfer) {
      const currentPosition = wavesurfer.getCurrentTime();
      const newPosition = currentPosition + 10; 
      wavesurfer.seekTo(newPosition / wavesurfer.getDuration());
    } else {
      console.error("Wavesurfer instance is not available.");
    }
  };

  const skipBackward = () => {
    if (wavesurfer) {
      const currentPosition = wavesurfer.getCurrentTime();
      const newPosition = Math.max(currentPosition - 10, 0); 
      wavesurfer.seekTo(newPosition / wavesurfer.getDuration());
    } else {
      console.error("Wavesurfer instance is not available.");
    }
  };
  return (
    <>
      <div className=" border p-2 relative   flex flex-col border-gray-200 rounded shadow">
        <div className="flex items-center justify-between">
          <p>Voice {`(${selectedVoice?.value})`}</p>
          <div className="mt-2 flex flex-col">
            {/* <div>Select a gender</div> */}
            <div className="flex gap-5">
              <label>
                <input
                  type="radio"
                  name="gender"
                  value="male"
                  className="text-primary"
                  checked={selectedVoice?.gender?.toLowerCase() === "male"}
                  onChange={() => {
                    handleSelectGender("male");
                  }}
                />
                Male
              </label>
              <label>
                <input
                  type="radio"
                  name="gender"
                  value="female"
                  className="text-primary "
                  checked={selectedVoice?.gender.toLowerCase() === "female"}
                  onChange={() => {
                    handleSelectGender("female");
                  }}
                />
                Female
              </label>
            </div>
          </div>
        </div>

        {loadingAudio ? (
          <VoiceLoader />
        ) : !loadingAudio && !audioUrl ? (
          <p>
            No {selectedVoice?.gender?.toUpperCase()} Voice Found,{" "}
            <span className="text-primary">
              please click generate audio button
            </span>
          </p>
        ) : (
          <div className="my-2">
          {audioUrl &&
            <WavesurferPlayer
              height={100}
              waveColor="violet"
              url={audioUrl}
              // audioRate={audioRate}
              onReady={onReady}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              />
            }
          </div>
        )}
        {audioUrl && (
          <div className="flex gap-4 items-end ">
            <div className="flex gap-3 items-end  text-violet-500">
              <RotateCcw
                className="hover:scale-105 cursor-pointer "
                onClick={skipBackward}
              />
              {isPlaying ? (
                <Pause
                  name="Pause"
                  className="hover:scale-105 cursor-pointer"
                  onClick={onPlayPause}
                />
              ) : (
                <Play
                  name="Play"
                  className="hover:scale-105 cursor-pointer"
                  onClick={onPlayPause}
                />
              )}
              <RotateCw
                className="hover:scale-105 cursor-pointer"
                onClick={skipForward}
              />
            </div>
            <div className="flex flex-col gap-2 w-36">
              <label htmlFor="sim" className="flex justify-between">
                Audio Speed
                <span>{audioRate}</span>
              </label>
              <Slider
                defaultValue={[1]}
                max={2}
                min={0}
                step={0.25}
                onValueChange={(value) => {
                  manipulateVoice(value[0]);
                  setAudioRate(value[0]);
                }}
              />
            </div>
            <div className="flex flex-col gap-2 w-36">
              <label htmlFor="sim" className="flex justify-between">
                Audio Zoom
                <span>{zoomRate}</span>
              </label>
              <Slider
                defaultValue={[1]}
                max={3000}
                min={1}
                step={100}
                onValueChange={(value) => {
                  setZoomRate(value[0]);
                  zoomVoice(value[0]);
                }}
              />
            </div>
          </div>
        )}
      </div>
      {isCopy && audioUrl ? (
        <div className="flex gap-4 items-center justify-between">
          <div className="flex items-end justify-center">
            <Input
              onChange={() => {}}
              value={audioUrl}
              name="copy_path"
              title="Audio link"
            />
            <Copy
              onClick={(e) => copyToClipboard(e, audioUrl)}
              className="hover:scale-105 mb-3 ml-3 cursor-pointer"
            />
          </div>
        </div>
      ) : null}
    </>
  );
};

export default VoiceWavePlayer;
