import React, { useState } from "react";
import { FullModal } from "common/components/modals";
import { VoiceEditor } from "./voiceEditor";
import useVoiceStore from "store/voice/voice.store";

type TDialogVoiceModalProps = {
  text: string;
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  voice_path_male: string;
  voice_path_female: string;
  handleUpdate: (data: unknown) => void;
  handleSave: () => void;

};
const DialogVoiceModal: React.FC<TDialogVoiceModalProps> = ({
  showModal,
  setShowModal,
  text,
  voice_path_male,
  voice_path_female,
  handleUpdate,
  handleSave
}) => {

  return (
    <FullModal
      className="text-white  min-w-[80%] "
      title={"Voice Editor"}
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      onSave={handleSave}
      footer
    >
      <div className="flex flex-col justify-center items-center   w-full gap-2 text-white">
        <div className="w-[100%] mb-4">
          <VoiceEditor
            handleUpdate={handleUpdate}
            voice_path_male={voice_path_male}
            voice_path_female={voice_path_female}
            answer={text}
            isMultiLang={true}
          />
        </div>
      </div>
    </FullModal>
  );
};

export default DialogVoiceModal;
