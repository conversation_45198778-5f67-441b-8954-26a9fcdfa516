import React from 'react'

const VoiceLoader = () => {
  return (
    <div className="h-52">
    <div
      role="status"
      className=" p-4 rounded shadow animate-pulse"
    >
      <div className="flex items-baseline mt-4 relative h-full">
        <div className="w-full h-1 bg-gray-200 rounded-t-lg  dark:bg-gray-700"></div>
        <div className="w-full h-5 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
        <div className="w-full h-10 bg-gray-200 rounded-t-lg  ms-6 dark:bg-gray-700"></div>
        <div className="w-full h-14 ms-6 bg-gray-200 rounded-t-lg dark:bg-gray-700"></div>
        <div className="w-full h-16 bg-gray-200 rounded-t-lg  ms-6 dark:bg-gray-700"></div>
        <div className="w-full h-20 bg-gray-200 rounded-t-lg  ms-6 dark:bg-gray-700"></div>
        <div className="w-full h-24 bg-gray-200 rounded-t-lg  ms-6 dark:bg-gray-700"></div>
      </div>
      <span className="sr-only">Loading...</span>
    </div>
  </div>
  )
}

export default VoiceLoader