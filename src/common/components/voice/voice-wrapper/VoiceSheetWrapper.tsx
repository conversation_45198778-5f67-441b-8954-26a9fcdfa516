import React from "react";
import useVoiceStore from "store/voice/voice.store";
import { UpdateVoiceSheet } from "../UpdateVoiceSheet";
import { FileAudio } from "lucide-react";
import { IFAQ } from "store/faq/faq.types";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";
import { IItem } from "store/item/item.types";
type TVoiceSheetWrapperProps = {
  handleUpdate: (
    data: {
      voice_path_female: string;
      voice_path_male: string;
    },
    item: IFAQ | ISmallTalk | IItem
  ) => void;
  item: IFAQ | ISmallTalk | IItem ;
  answer:string;
};
const VoiceSheetWrapper: React.FC<TVoiceSheetWrapperProps> = ({
  handleUpdate,
  item,
  answer,
}) => {
  const { botVoice } = useVoiceStore();
  return (
    <>
      {botVoice?.custom_voice_active && botVoice?.voice_active ? (
        <UpdateVoiceSheet
          handleUpdate={handleUpdate}
          item={item}
          answer={answer || ""}
        >
          <FileAudio size={15} className="hover:text-primary cursor-pointer" />
        </UpdateVoiceSheet>
      ) : null}
    </>
  );
};

export default VoiceSheetWrapper;
