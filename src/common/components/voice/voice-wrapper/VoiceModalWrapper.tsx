import React from "react";
import useVoiceStore from "store/voice/voice.store";
import DialogVoiceModal from "../DialogVoiceModal";
import { FileAudio } from "lucide-react";
import { TVoiceBody } from "../voiceEditor";
import generateStorageId from "helpers/generateStorageId";
type TVoiceModalWrapperProps = {
  text: string;
  handleSave: any;
  handleUpdateVoice: (data: TVoiceBody) => void;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  showModal:boolean;
  card_index?:number;
  card_Key?:number;
  voice_path_male:string;
  voice_path_female:string;
};
const VoiceModalWrapper: React.FC<TVoiceModalWrapperProps> = ({
  text,
  handleSave,
  handleUpdateVoice,
  setShowModal,
  showModal,
  card_index,
  card_Key,
  voice_path_male,
  voice_path_female
}) => {
  const { botVoice } = useVoiceStore();
  return (
    <>
      {botVoice?.custom_voice_active && botVoice?.voice_active ? (
        <div>
          <DialogVoiceModal
            key={card_Key || generateStorageId()}
            handleSave={handleSave}
            handleUpdate={handleUpdateVoice}
            voice_path_female={voice_path_female}
            voice_path_male={voice_path_male}
            showModal={showModal}
            setShowModal={setShowModal}
            text={text}
          />
          <FileAudio
            key={card_index || generateStorageId()}
            onClick={() => setShowModal(true)}
            size={15}
            className="hover:text-primary cursor-pointer"
          />
        </div>
      ) : null}
    </>
  );
};

export default VoiceModalWrapper;
