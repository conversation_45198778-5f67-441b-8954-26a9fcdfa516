import EmptySVG from "common/icons/EmptySVG";
import { FC, memo } from "react";

interface EmptyProps {
  text?: string;
}

const Empty: FC<EmptyProps> = ({ text }) => {
  return (
    <div className="flex flex-col justify-center items-center h-full">
      <div className="w-32 h-32">
        <EmptySVG />
      </div>
      <div className="text-2xl font-bold">Looks Empty</div>
      <div className="text-white/50">{text}</div>
    </div>
  );
};

export default memo(Empty);
