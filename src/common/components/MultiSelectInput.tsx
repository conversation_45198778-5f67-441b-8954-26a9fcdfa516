import { useEffect, useState } from "react";
import Select, { MultiValue } from "react-select";

export const customStyles = {
  control: (provided, state) => ({
    ...provided,
    backgroundColor: "transparent",
    outline: state.isFocused ? "1.8px solid white" : "#b7b7b8",
    borderColor: "#b7b7b8",
    marginTop: "0px",
    marginBottom: "5px",
    padding: "1.5px",
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused ? "transparent" : "transparent",
  }),
  input: (provided, state) => ({
    ...provided,
    color: "white",
    border: state.focus ? "none" : "none",
  }),

  indicatorSeparator: (provided) => ({
    ...provided,
    color: "red",
  }),

  clearIndicator: (provided) => ({
    ...provided,
    color: "red",
  }),

  valueContainer: (provided) => ({
    ...provided,
    color: "red",
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: "black",
  }),
  multiValue: (provided) => ({
    ...provided,
    borderColor: "white",
  }),
  menu: (provided, state) => ({
    ...provided,
    backgroundColor: "black",
    borderRadius: "5px",
    boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
  }),
  singleValue: (provided) => ({
    ...provided,
    color: "white",
  }),
};

export type TMultiOptions = MultiValue<{
  label: string;
  options: {
    value: string;
    label: string;
  }[];
}>;
export type Toptions =
  | {
      value: string;
      label: string;
    }[]
  | TMultiOptions;

export type Toption = {
  value: string;
  label: string;
};

type MultiSelectInput = {
  title: string;
  setData: (Data: Toption[]) => void;
  data: Toptions;
  defualtData?: Toption[];
  onChange?: (Data: Toption[]) => void;
};

const MultiSelectTextInput: React.FC<MultiSelectInput> = ({
  title,
  setData,
  data,
  defualtData,
  onChange
}) => {
  const [value, setValue] = useState([]);
  useEffect(() => {
    setData(value);
  }, [value]);
  useEffect(() => {
    setValue(defualtData);
  }, [defualtData]);

  const handleChange = (newValue: MultiValue<Toption>) => {
    const updatedValue = newValue as Toption[];
    setValue(updatedValue); // Update local state
    if (onChange) {
      onChange(updatedValue); // Call external onChange if provided
    }
  };

  return (
    <div className="gap-1 space-y-1">
      <label htmlFor={title}>{title}:</label>
      <Select
        id={title}
        defaultValue={defualtData || []}
        value={value}
        isMulti
        name="colors"
        options={data}
        className="basic-multi-select "
        // onChange={(newValue) => setValue(newValue as Toption[])}
        onChange={handleChange}
        classNamePrefix="select"
        styles={customStyles}
      />
    </div>
  );
};

export default MultiSelectTextInput;
