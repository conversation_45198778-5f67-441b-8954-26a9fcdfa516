import { FC, useMemo } from "react";
import { Node } from "reactflow";
import {isArabic} from "helpers/helper";
import useMultiLanguage from "common/hooks/useMultiLanguage";

const AgentContent: FC<Node> = (node) => {
    const content = useMemo(() => {
        return node.data.config[0].handlerParams[0];
    },[node])

    return (
    <div>
      {
        content && (
          <div className='flex flex-col gap-1'>
            <span
              style={{
                color: node.data.block.color,
              }}
            >
             Agent Type: {content.agent_type}
            </span>
            <span
              style={{
                color: node.data.block.color,
              }}
            >
             Source: {content.input.type === 'user question' ? 'User Question' : content.input.source_entity}
            </span>
            <span
              style={{
                color: node.data.block.color,
              }}
            >
             Saved in entity: {content.output_entity}
            </span>
          </div>
        )
      }
      </div>
  );
};

export default AgentContent;
