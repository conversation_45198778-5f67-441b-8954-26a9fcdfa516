import React, { FC, memo, useMemo, useState, useEffect } from "react";
import { Node } from "reactflow";
import { transformTextToLanguageObj } from "helpers/multiLang";
import { useLangStore } from "store/language/lang.store";

const DynamicSuggestionsContent: FC<Node> = (node) => {
  const { lang } = useLangStore();
  const [mapper, setMapper] = useState({
    entity: "",
    objectPropertyLabel: { en: "", ar: "" },
    objectPropertyPostback: { en: "", ar: "" },
    objectEntity: ""
  });
  
  useEffect(() => {
    setMapper({
      entity: node.data.config[0].handlerParams[0].entity,
      objectPropertyLabel: transformTextToLanguageObj(node.data.config[0].handlerParams[0]?.objectPropertyLabel || ""),
      objectPropertyPostback: transformTextToLanguageObj(node.data.config[0].handlerParams[0]?.objectPropertyPostback || ""),
      objectEntity: node.data.config[0].handlerParams[0].objectEntity
    });
  }, [node]);

  const content = useMemo(() => {
    const entity = node.data.config[1].recieving;
    const pagiNum = node.data.config[0].handlerParams[1];
    return {
      mapper,
      entity,
      pagiNum,
    };
  }, [node]);

  return (
    <div className="space-y-2">
      {content.entity && (
        <div className="text-center">
          Data stored in:{" "}
          <span
            style={{
              color: node.data.block.color, 
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
      <hr />
      {content.mapper && (
        <div>
          <div>
            entity: <span>{mapper.entity}</span>
          </div>
          <div>
            objectPropertylabel: <span>{mapper.objectPropertyLabel[lang]}</span>
          </div>
          <div>
            objectPropertyPostback: <span>{mapper.objectPropertyPostback[lang]}</span>
          </div>
          <div>
            objectEntity: <span>{mapper.objectEntity}</span>
          </div>
        </div>
      )}
      {content.pagiNum && (
        <span> paginationNumber: <span>{content.pagiNum}</span> </span>
      )}
    </div>
  );
};

export default memo(DynamicSuggestionsContent);
