import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const JumpToWidgetContent: FC<Node> = (node) => {
  
  const content = useMemo(() => {
    const redirectTo = node.data.config[0].handlerParams[0];

    return {
      redirectTo,
    };
  }, [node]);
  return (
   typeof content.redirectTo === "string" && (
      <div>
        Redirect to:{" "}
        <span
          style={{
            color: node.data.block.color,
          }}
        >
          {content.redirectTo}
        </span>
      </div>
    )
  );
};

export default memo(JumpToWidgetContent);
