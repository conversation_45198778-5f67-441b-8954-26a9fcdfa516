import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const JsonAPIContent: FC<Node> = (node) => {

  const content = useMemo(() => {
    const entity = node.data.config[0].handlerParams[0].entity;
    const method = node.data.config[0].handlerParams[0].method;
    const url = node.data.config[0].handlerParams[0].url;

    return {
      entity,
      method,
      url,
    };
  }, [node]);
  return (
    <div className="max-w-[200px] break-words">
      {content.method && (
        <div>
          Method: <small>{content.method}</small>
        </div>
      )}
      {content.url && (
        <div>
          URL: <small>{content.url}</small>
        </div>
      )}
      {content.entity && (
        <div>
          Data stored in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(JsonAPIContent);
