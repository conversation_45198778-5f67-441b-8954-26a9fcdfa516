import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const CreateStoreContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const entity = node.data.config[0].handlerParams[0].entity;

    return {
      entity,
    };
  }, [node]);
  return (
    <>
      {" "}
      {content.entity && (
        <div className="text-center">
          Data stored in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
    </>
  );
};

export default memo(CreateStoreContent);
