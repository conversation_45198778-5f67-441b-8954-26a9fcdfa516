import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const Checkpoint: FC<Node> = (node) => {
  const content = useMemo(() => {
    return {
      tag: node.data.config[0].handlerParams[0],
    };
  }, [node]);
  return (
    <div>
      {content.tag && (
        <div>
          Tag :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.tag}
          </span>
        </div>
      )}
    </div>
  );
};

export default Checkpoint;
