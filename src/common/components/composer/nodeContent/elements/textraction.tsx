import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const TextractionContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const entity = node.data.config[0].handlerParams[0].entity;
    const type = node.data.config[0].handlerParams[0].type;
    const sourceType = node.data.config[0].handlerParams[0].sourceType;
    const sourceEntity = node.data.config[0].handlerParams[0].sourceEntity;
    return {
      entity,
      type,
      sourceType,
      sourceEntity,
    };
  }, [node]);
  return (
    <div>
      {content.sourceType && (
        <div>
          Source Type :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
            className="capitalize"
          >
            {content.sourceType.replace("_", " ")}
          </span>
        </div>
      )}
      {content.sourceEntity && (
        <div>
          Source Entity :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.sourceEntity}
          </span>
        </div>
      )}

      {content.entity && (
        <div>
          Entity stored in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
      {content.type && (
        <div>
          Type :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.type}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(TextractionContent);
