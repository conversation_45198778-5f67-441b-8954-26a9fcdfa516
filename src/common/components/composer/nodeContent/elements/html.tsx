import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const HtmlContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const html_code = node.data.config[0].handlerParams[0].html_code;

    return {
      html_code,
    };
  }, [node]);

  return (
    <div>
      {content.html_code && (
        <div>
          Html code run:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            <div dangerouslySetInnerHTML={{ __html: content.html_code }} />

            {/* {content.html_code} */}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(HtmlContent);
