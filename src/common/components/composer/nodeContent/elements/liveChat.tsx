import { FC, useMemo } from "react";
import { Node } from "reactflow";
import { isArabic } from "helpers/helper";
import useGenesysStore from "store/genesys/genesys.store";
import useLiveChatStore from "store/livechat/livechat.store";

const LiveChatContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    let text = "";

    if (node?.data?.config[0]?.handlerParams[0]) {
      text = node?.data?.config[0]?.handlerParams[0];
    } else {
      if (useGenesysStore) {
        text = "genesys integration";
      } else if (useLiveChatStore) {
        text = "livechat integration";
      }
    }
    return {
      text,
      isArabic: isArabic(text),
    };
  }, [node]);
  return (
    <div
      className={`${
        content.isArabic ? "text-right" : "text-left"
      } w-fit max-w-[300px] whitespace-break-spaces`}
    >
      {content.text}
    </div>
  );
};

export default LiveChatContent;
