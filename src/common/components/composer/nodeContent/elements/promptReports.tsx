import { transformTextToLanguageObj } from "helpers/multiLang";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { useLangStore } from "store/language/lang.store";

const PromptReportsContent: FC<Node> = (node) => {
  const { lang } = useLangStore();
  const [mapper, setMapper] = useState({
    nameQ: { en: "", ar: "" },
    emailQ: { en: "", ar: "" },
    phoneQ: { en: "", ar: "" },
    feedbackQ: { en: "", ar: "" },
    endMessageQ: { en: "", ar: "" },
  });

  useEffect(() => {
    if (node.data.config[0]?.handlerParams[0]?.username) return;
    setMapper({
      nameQ: transformTextToLanguageObj(node.data.config[0].handlerParams[0]),
      emailQ: transformTextToLanguageObj(node.data.config[1].handlerParams[0]),
      phoneQ: transformTextToLanguageObj(node.data.config[2].handlerParams[0]),
      feedbackQ: transformTextToLanguageObj(
        node.data.config[3].handlerParams[0].question
      ),
      endMessageQ: transformTextToLanguageObj(
        node.data.config[4].handlerParams.length === 2 &&
          typeof node.data.config[4].handlerParams[0] !== "string"
          ? node.data.config[4].handlerParams[1]
          : typeof node.data.config[4].handlerParams[0] === "string"
          ? node.data.config[4].handlerParams[0]
          : ""
      ),
    });
  }, [node]);

  return (
    <>
    {mapper.nameQ[lang] && (
      <div>
        Name Question: <span>{mapper.nameQ[lang]}</span>
      </div>
    )}
    {mapper.emailQ[lang] && (
        <div>
          Email Question: {mapper.emailQ[lang]}
        </div>
      )}
      {mapper.phoneQ[lang] && (
        <div>
          Phone Question: {mapper.phoneQ[lang]}
        </div>
      )}
      {mapper.feedbackQ[lang] && (
        <div>
          Feedback Question: {mapper.feedbackQ[lang]}
        </div>
      )}
      {mapper.endMessageQ[lang] && (
        <div>
          End Report Question: {mapper.endMessageQ[lang]}
        </div>
      )}
    </>
  );
};

export default memo(PromptReportsContent);