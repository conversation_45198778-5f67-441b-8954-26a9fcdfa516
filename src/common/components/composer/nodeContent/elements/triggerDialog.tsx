import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const TriggerDialogContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    return node.data.config[0].handlerParams[0];
  }, [node]);
  return (
    content.dialog_name && (
      <div>
        Redirect to dialog:{" "}
        <span
          style={{
            color: node.data.block.color,
          }}
        >
          {content.dialog_name}
        </span>
      </div>
    )
  );
};

export default memo(TriggerDialogContent);
