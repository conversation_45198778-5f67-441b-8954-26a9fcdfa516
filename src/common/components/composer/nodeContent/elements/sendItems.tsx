import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";
import useItemStore from "store/item/item.store";
import { IItem } from "store/item/item.types";

const SendItemsContent: FC<Node> = (node) => {
  const items = useItemStore((state) => state.items);

  const content = useMemo(() => {
    const itemIds = node.data.config[0].handlerParams[0].item_ids;
    const itemsToSend = itemIds
      .map((id: number) => {
        return items.find((item: IItem) => item.item_id === id) || null;
      })
      .filter((item) => item);

    return { itemIds, itemsToSend };
  }, [node]);

  return (
    <div>
      <div className="text-center text-lg">
        <span
          style={{
            color: node.data.block.color,
          }}
        >
          {content.itemIds.length}{" "}
        </span>
        Items to send
      </div>
      <div className="w-[200px] max-h-[300px] overflow-y-auto">
        {content.itemsToSend.map((item, i) => {
          return (
            <div
              key={i}
              className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
            >
              <div>{item.item_title}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default memo(SendItemsContent);
