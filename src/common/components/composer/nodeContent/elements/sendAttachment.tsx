import { FC, useMemo } from "react";
import { Node } from "reactflow";
import { isArabic } from "helpers/helper";
import { useLangStore } from "store/language/lang.store";
import { transformTextToLanguageObj } from "helpers/multiLang";

const SendAttachmentContent: FC<Node> = (node) => {
  const { lang } = useLangStore();
  const content = useMemo(() => {
    const text =
      transformTextToLanguageObj(
        node.data.config[0]?.handlerParams[0]?.mapper?.title
      ) || "";

    return {
      text,

      isArabic: isArabic(text[lang]),
    };
  }, [node, lang]);

  return (
    <div
      className={`${
        content.isArabic ? "text-right" : "text-left"
      } w-fit max-w-[300px] whitespace-break-spaces`}
    >

      {node.data.config[0]?.handlerParams[0]?.attachments?.length} files uploaded

    </div>
  );
};

export default SendAttachmentContent;
