import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const StoreLookupContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const searchStoreEntity =
      node.data.config[0].handlerParams[0].searchStoreEntity;
    const searchEntity = node.data.config[0].handlerParams[0].searchEntity;
    const searchIndex = node.data.config[0].handlerParams[0].searchIndex;
    const result = node.data.config[0].handlerParams[0].entity;
    return {
      searchStoreEntity,
      searchEntity,
      searchIndex,
      result,
    };
  }, [node]);
  return (
    <div>
      {content.searchStoreEntity && (
        <div>
          Data pulled from:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.searchStoreEntity}
          </span>
        </div>
      )}

      {content.searchEntity && (
        <div>
          Entity to search for:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.searchEntity}
          </span>
        </div>
      )}

      {content.searchIndex && (
        <div>
          Data Searched By:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.searchIndex}
          </span>
        </div>
      )}

      {content.result && (
        <div>
          Result saved in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.result}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(StoreLookupContent);
