import { FC, memo, use, useMemo } from "react";
import { Node } from "reactflow";

const DynamicCardContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const entity = node.data.config[0].handlerParams[0].entity;
    const mapper = node.data.config[0].handlerParams[0].mapper;

    return {
      entity,
      mapper,
    };
  }, [node]);

  return (
    <div className="space-y-2">
      {content.entity && (
        <div className="text-center">
          Data pulled from:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
      <hr />
      {content.mapper && (
        <div>
          {Object.keys(content.mapper).filter(
            (key) => Boolean(content.mapper[key])
          ).map((key, i) => {
            return (
              <div key={i}>
                {key}: <span>{content.mapper[key]?.slice(0,40)}</span><span>...</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default memo(DynamicCardContent);
