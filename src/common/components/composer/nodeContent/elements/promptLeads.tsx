import { FC, memo, useMemo } from 'react'
import { Node } from 'reactflow'

const PromptLeadsContent: FC<Node> = (node) => {
    const content = useMemo(() => {
        return node.data.config[0].handlerParams[0];
      }, [node]);
    
      return (
        <div>
          {Object.keys(content).map((key) => {
            return (
              <div key={key}>
                {key}:{" "}
                <span
                  style={{
                    color: node.data.block.color,
                  }}
                >
                  {content[key]}
                </span>
              </div>
            );
          })}
        </div>
      );
}

export default memo(PromptLeadsContent)