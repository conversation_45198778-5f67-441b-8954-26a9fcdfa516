import { FC, memo, use, useMemo } from "react";
import { Node } from "reactflow";

const ConditionContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const data = node.data.config[0].handlerParams[0];
        return {
            data
        }
  }, [node]);

  return (
    <div className="space-y-2">
      {content.data?.length && content.data.map((item, index) => {
        return (
          <div className="text-center" key={index}>
            <span
              style={{
                color: node.data.block.color,
              }}
            >
              {item[1]}
            </span>
          </div>
        )
      })
        
      }
    </div>
  );
};

export default memo(ConditionContent);
