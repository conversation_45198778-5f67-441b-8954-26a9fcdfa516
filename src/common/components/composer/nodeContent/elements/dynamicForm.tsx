import { transformTextToLanguageObj } from "helpers/multiLang";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { useLangStore } from "store/language/lang.store";

const DynamicFormContent: FC<Node> = (node) => {
  const { lang } = useLangStore();
  const [mapper, setMapper] = useState({
    data: {
      dynamic_form_schema_id: "",
      form: [{ question: "", label: "", ask_user: false, entity: "" }],
    },
  });

  useEffect(() => {
    setMapper({
      data: node.data?.config[0]?.handlerParams[0]?.data,
    });
  }, [node]);

  return <></>;
};

export default memo(DynamicFormContent);
