import { transformTextToLanguageObj } from "helpers/multiLang";
import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";
import { CardPreview } from "views/cardsAndSuggestions/components/cardPreview";

const SendCarouselContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    console.log(node.data.config[0].handlerParams[0]);
    const cardConfigClone = JSON.parse(
      JSON.stringify(node.data.config[0].handlerParams[0])
    );
    cardConfigClone?.attachments?.forEach((attachment) => {
      attachment.body = attachment?.body.map((body) => {
        if (body?.id === "video") {
          return {
            ...body,
            sources: [
              {
                mimeType: "video/mp4",
                url: transformTextToLanguageObj(body.sources[0].url),
              },
            ],
          };
        }
        if (body?.id === "image") {
          return {
            ...body,
            url: transformTextToLanguageObj(body?.url),
          };
        }
        if (body?.id === "heading" || body?.id === "subheading") {
          return {
            ...body,
            text: transformTextToLanguageObj(body?.text),
          };
        }
      });
      attachment.actions = attachment?.actions.map((action) => {
        if (action?.type === "Action.Submit") {
          return {
            ...action,
            title: transformTextToLanguageObj(action?.title),
            data: transformTextToLanguageObj(action?.data),
          };
        }
        if (action?.type === "Action.OpenUrl") {
          return {
            ...action,
            title: transformTextToLanguageObj(action?.title),
            url: transformTextToLanguageObj(action?.url),
          };
        }
      });
    });
    return cardConfigClone;
  }, [node]);
  console.log(content);

  return Object.keys(content || {}).length ? (
    <div className="w-[200px]" onClick={(e) => e.stopPropagation()}>
      <CardPreview isDialog dialogCard={content} />
    </div>
  ) : null;
};

export default memo(SendCarouselContent);
