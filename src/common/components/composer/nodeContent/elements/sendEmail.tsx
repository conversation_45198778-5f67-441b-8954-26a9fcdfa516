import { FC, useMemo } from "react";
import { Node } from "reactflow";
import { isArabic } from "helpers/helper";
import { useLangStore } from "store/language/lang.store";
import { transformTextToLanguageObj } from "helpers/multiLang";

const SendEmailContent: FC<Node> = (node) => {
  const { lang } = useLangStore();
  const content = useMemo(() => {
    const text = transformTextToLanguageObj(
      node.data.config[0].handlerParams[0]?.mapper.title
    );
    const body = transformTextToLanguageObj(
      node.data.config[0].handlerParams[0]?.mapper.body
    );
    const Integration = transformTextToLanguageObj(
      node.data.config[0]?.handlerParams[1] ||"Choose an integration"
    );
    return {
      Integration,
      text,
      body,
      isArabic: isArabic(text[lang]),
    };
  }, [node, lang]);

  return (
    <div
      className={`${
        content.isArabic ? "text-right" : "text-left"
      } w-fit max-w-[300px] whitespace-break-spaces`}
    >
      {content.Integration[lang]}
      <br />
      {content.text[lang]}
      <br />
      {content.body[lang]}
    </div>
  );
};

export default SendEmailContent;
