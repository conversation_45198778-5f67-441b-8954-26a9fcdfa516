import useMultiLanguage from "common/hooks/useMultiLanguage";
import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const MapCardContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const { lat, long, title, description } =
      node.data.config[0].handlerParams[0];

    return {
      lat,
      long,
      title,
      description,
    };
  }, [node]);

  const {value: title} = useMultiLanguage(content.title);
    const {value: description} = useMultiLanguage(content.description);

  return (
    <div className="space-y-2 max-w-xs">
      {content.lat ? (
        <div>
          Latitude:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.lat}
          </span>
        </div>
      ): null}
      {content.long ? (
        <div>
          Longitude:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.long}
          </span>
        </div>
      ) : null}
      {title && (
        <div>
          Title:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {title}
          </span>
        </div>
      )}
      {description && (
        <div>
          Description:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
            className="whitespace-break-spaces break-words"
          >
            {description}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(MapCardContent);
