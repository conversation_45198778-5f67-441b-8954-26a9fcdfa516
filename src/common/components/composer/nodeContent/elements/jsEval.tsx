import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const JsEvalContent: FC<Node> = (node) => {
    
  const content = useMemo(() => {
    const code = node.data.config[0].handlerParams[0]

    return {
      code
    };
  }, [node]);
  return <div>
        {content.code && (
        <div className="px-5 py-2 max-h-[200px] truncate max-w-[300px] bg-secondary whitespace-break-spaces ">
          <code className="">{content.code}</code>
        </div>
        )}
  </div>;
};

export default memo(JsEvalContent);
