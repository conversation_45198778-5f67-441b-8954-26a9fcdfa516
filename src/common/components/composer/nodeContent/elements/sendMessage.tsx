import { FC, useMemo } from "react";
import { Node } from "reactflow";
import {isArabic} from "helpers/helper";
import useMultiLanguage from "common/hooks/useMultiLanguage";

const SendMessageContent: FC<Node> = (node) => {
    const content = useMemo(() => {
        const text = node.data.config[0].handlerParams[0];
        return {
            text,
            isArabic: isArabic(text)
        }
    },[node])
   const {value} = useMultiLanguage(content.text);
  return (
    <div
      className={`${
        content.isArabic ? "text-right" : "text-left"
      } w-fit max-w-[300px] whitespace-break-spaces break-words`}
    >
      {value}
    </div>
  );
};

export default SendMessageContent;
