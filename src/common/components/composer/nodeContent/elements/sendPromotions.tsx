import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";
import useOfferStore from "store/offers/offers.store";
import { IOffer } from "store/offers/offers.types";

const SendPromotionsContent: FC<Node> = (node) => {
  const offers = useOfferStore((state) => state.OFFERS);

  const content = useMemo(() => {
    const offerIds = node.data.config[0].handlerParams[0].offer_ids;
    const offersToSend = offerIds
      .map((id: number) => {
        return offers.find((offer: IOffer) => offer.offer_id === id) || null;
      })
      .filter((offer) => offer);

    return { offerIds, offersToSend };
  }, [node]);
  return (
    <div>
      <div className="text-center text-lg">
        <span
          style={{
            color: node.data.block.color,
          }}
        >
          {content.offerIds.length}{" "}
        </span>
        Categories to send
      </div>
      <div className="w-[200px] max-h-[300px] overflow-y-auto">
        {content.offersToSend.map((offer: IOffer, i) => {
          return (
            <div
              key={i}
              className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
            >
              <div>{offer.offer_description}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default memo(SendPromotionsContent);
