import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const ChannelIDContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const entity = node.data.config[0].handlerParams[0];

    return {
      entity,
    };
  }, [node]);

  return (
    <div className="space-y-2">
      {content.entity && (
        <div className="text-center">
          Data saved in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(ChannelIDContent);
