import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const SendSuggestionsContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const entity = node.data.config[1].recieving;
    const pagiNum = node.data.config[0].handlerParams[2];

    return {
      entity,
      pagiNum
    };
  }, [node]);
  return (
    <div>
      {content.entity && (
        <div className="text-center">
          Data stored in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
      {content.pagiNum && (
        <div>
          <hr />
          <br />
          <div>
            paginationNumber: <span>{content.pagiNum}</span> 
          <br/>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(SendSuggestionsContent);
