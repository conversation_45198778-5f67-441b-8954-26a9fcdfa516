import { transformTextToLanguageObj } from "helpers/multiLang";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { useLangStore } from "store/language/lang.store";

const TicketingSystemContent: FC<Node> = (node) => {
  const { lang } = useLangStore();
  const [mapper, setMapper] = useState({
    nameQ: { en: "", ar: "" },
    emailQ: { en: "", ar: "" },
    phoneQ: {en: "", ar: ""},
    IssueTitle: { en: "", ar: "" },
    IssueDescription: { en: "", ar: "" },
    department: { en: "", ar: "" },
    endMessageQ: { en: "", ar: "" },
  });

  useEffect(() => {
      setMapper({
        department: transformTextToLanguageObj(node.data?.config[0]?.handlerParams[0]),
        nameQ: transformTextToLanguageObj(node.data?.config[1]?.handlerParams[0]),
        emailQ: transformTextToLanguageObj(node.data?.config[2]?.handlerParams[0]),
        phoneQ: transformTextToLanguageObj(node.data?.config[3]?.handlerParams[0]),
        IssueTitle: transformTextToLanguageObj(node.data?.config[4]?.handlerParams[0]),
        IssueDescription: transformTextToLanguageObj(node.data?.config[5]?.handlerParams[0]),
        endMessageQ: transformTextToLanguageObj(node.data?.config[6].handlerParams[0]),
      });
  }, [node]);

  return (
    <>
      {mapper.department[lang] && (
        <div>
          Category: {mapper.department[lang]}
        </div>
      )}
      {/* add specific categories (departments) */}
      {mapper.nameQ[lang] && (
        <div>
          Name Question: <span>{mapper.nameQ[lang]}</span>
        </div>
      )}
      {mapper.emailQ[lang] && (
        <div>
          Email Question: {mapper.emailQ[lang]}
        </div>
      )}
      {mapper.phoneQ[lang] && (
        <div>
          Phone Question: {mapper.phoneQ[lang]}
        </div>
      )}
      {mapper.IssueTitle[lang] && (
        <div>
          Issue Title: {mapper.IssueTitle[lang]}
        </div>
      )}
      {mapper.IssueDescription[lang] && (
        <div>
          Issue Description: {mapper.IssueDescription[lang]}
        </div>
      )}
      {mapper.endMessageQ[lang] && (
        <div>
          Ending Message: {mapper.endMessageQ[lang]}
        </div>
      )}
    </>
  );
};

export default memo(TicketingSystemContent);
