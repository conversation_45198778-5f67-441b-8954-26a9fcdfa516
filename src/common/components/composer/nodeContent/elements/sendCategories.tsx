import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";
import useCategoryStore from "store/category/category.store";
import { ICategory } from "store/category/category.types";

const SendCategoriesContent: FC<Node> = (node) => {
  const categories = useCategoryStore((state) => state.categories);

  const content = useMemo(() => {
    const categoryIds = node.data.config[0].handlerParams[0].category_ids;
    const categoriesToSend = categoryIds
      .map((id: number) => {
        return (
          categories.find(
            (category: ICategory) => category.category_id === id
          ) || null
        );
      })
      .filter((category) => category);

    return { categoryIds, categoriesToSend };
  }, [node]);

  return (
    <div>
      <div className="text-center text-lg">
        <span
          style={{
            color: node.data.block.color,
          }}
        >
          {content.categoryIds.length}{" "}
        </span>
        Categories to send
      </div>
      <div className="w-[200px] max-h-[300px] overflow-y-auto">
        {content.categoriesToSend.map((category:ICategory, i) => {
          return (
            <div
              key={i}
              className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
            >
              <div>{category.category_name}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default memo(SendCategoriesContent);
