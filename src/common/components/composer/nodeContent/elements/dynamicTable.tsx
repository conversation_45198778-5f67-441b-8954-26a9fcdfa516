import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const DynamicTableContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const entity = node.data.config[0].handlerParams[0];
    return {
      entity,
    };
  }, [node]);
  return (
    <div>
      {content.entity && (
        <div className="text-center">
          Data pulled from:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(DynamicTableContent);
