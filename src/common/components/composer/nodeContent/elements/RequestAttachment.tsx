import useMultiLanguage from "common/hooks/useMultiLanguage";
import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const RequestAttachmentContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const question = node.data.config[0].handlerParams[0];
    const entity = node.data.config[1].recieving;

    return {
      question,
      entity,
    };
  }, [node]);
  const { value } = useMultiLanguage(content.question);
  return (
    <div className="flex flex-col gap-2 divide-y-2 max-w-sm whitespace-break-spaces break-words">
      {content.question && (
        <div>
          Question: <span>{value}</span>
        </div>
      )}
      {content.entity && (
        <div className="text-center">
          Data stored in:{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.entity}
          </span>
        </div>
      )}
    </div>
  );
};

export default memo(RequestAttachmentContent);
