import { FC, memo, useMemo } from "react";
import { Node } from "reactflow";

const ChartContent: FC<Node> = (node) => {
  const content = useMemo(() => {
    const searchStoreEntity = node.data.config[0].handlerParams[0].entity;
    const xAxis = node.data.config[0].handlerParams[0].mapper.xAxis;
    const yAxis = node.data.config[0].handlerParams[0].mapper.yAxis;
    const chartType = node.data.config[0].handlerParams[0].mapper.chart_type;
    const x_title = node.data.config[0].handlerParams[0].mapper.x_title;
    const y_title = node.data.config[0].handlerParams[0].mapper.y_title;
    const graph_name = node.data.config[0].handlerParams[0].mapper.graph_name;
    const set_color = node.data.config[0].handlerParams[0].mapper.color_set;

    return {
      searchStoreEntity,
      yAxis,
      xAxis,
      chartType,
      y_title,
      x_title,
      graph_name,
      set_color,
    };
  }, [node]);

  return (
    <div>
      {content.searchStoreEntity && (
        <div>
          Data pulled from :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.searchStoreEntity}
          </span>
        </div>
      )}

      {content.chartType && (
        <div>
          Chart Type :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.chartType}
          </span>
        </div>
      )}

      {content.xAxis && (
        <div>
          {content.chartType !== "Pie" ?<> X-axis:{" "} </> : <> Label:{" "}</>}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.xAxis}
          </span>
        </div>
      )}

      {content.yAxis && (
        <div>
          {content.chartType !== "Pie" ?<> Y-axis:{" "} </> : <> Value:{" "}</>}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.yAxis}
          </span>
        </div>
      )}
      {content.chartType !== 'Pie' && (
        <>
        {content.y_title && (
          <div>
            Y-axis title :{" "}
            <span
              style={{
                color: node.data.block.color,
              }}
            >
              {content.y_title}
            </span>
          </div>
        )}
        {content.x_title && (
          <div>
            X-axis title :{" "}
            <span
              style={{
                color: node.data.block.color,
              }}
            >
              {content.x_title}
            </span>
          </div>
        )}
        </>
      )}
      {content.graph_name && (
        <div>
          Graph name :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            {content.graph_name}
          </span>
        </div>
      )}
      {/* {content.set_color && (
        <div>
          Color Set :{" "}
          <span
            style={{
              color: node.data.block.color,
            }}
          >
            <div className="relative w-full w-100 flex">
              {content.set_color.split(",").map((color: string, i: number) => (
                <div
                  style={{ backgroundColor: color, color: color }}
                  className={`flex-1 min-w-1/4 min-h-full`}
           
                >
                  dffffffffffff
                </div>
              ))}
            </div>
          </span>
        </div>
      )} */}
    </div>
  );
};

export default memo(ChartContent);
