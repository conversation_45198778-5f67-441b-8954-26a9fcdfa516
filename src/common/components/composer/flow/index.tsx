"use client";
import React, {
  useRef,
  useState,
  use<PERSON><PERSON>back,
  useEffect,
} from "react";
import React<PERSON>low, {
  MiniMap,
  Background,
  BackgroundVariant,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Node,
  addEdge,
  getRectOfNodes,
  getTransformForBounds,
  Panel,
} from "reactflow";
import { ToolBar } from "common/components/composer/index";
import {
  StarterNode,
  CustomEdge,
  CustomNode,
} from "common/components/composer/flow/elements";
import {
  nodes as initNodes,
  edge as customEdge,
} from "data/composer/initialElements";
import useDialogStore from "store/dialog/dialog.store";
import { v4 as uuidv4 } from "uuid";
import blocks from "data/composer/blocks";
import BlockConfigFactory from "lib/Factory/BlockFactory";
import Sheet from "../sheet";
import { distinct } from "helpers/helper";
import connectorsSteps from "data/composer/connectorsSteps";
import { getFile, uploadBotImages, uploadFile } from "apis/file.api";
import { IDialog, IDialogExpiryConfig } from "store/dialog/dialog.types";
import generateStorageId from "helpers/generateStorageId";
import useBotStore from "store/bot/bot.store";
import { createOne, duplicate } from "apis/dialog.api";
import constant from "constant";
import * as signalR from "@microsoft/signalr";
import { ITrigger } from "store/trigger/trigger.types";
import { toBlob } from "html-to-image";
import EntitiesSheet from "../entities.sheet";
import { BrainCircuit } from "lucide-react";
import { distinctMultiple } from "helpers/helper";
import { getConfig } from "apis/config.api";
import SelectMenu from "common/ui/selectMenu";
import { publish, update, getStagingFalse, getIsStagingTrue } from "apis/dialogversioncontrol.api";
import useConfirmModal from "common/hooks/useConfirmModal";

const nodeTypes = {
  starter: StarterNode,
  custom: CustomNode,
};

const edgeTypes = {
  custom: CustomEdge,
};

interface FlowCanvasProps {
  dialog: IDialog;
  setDialog: (dialog: IDialog) => void;
  dialogTriggers: ITrigger[];
  setDialogTriggers: (dialogTriggers: ITrigger[]) => void;
  previewVersions;
  setPreviewVersions;
  DVCToShow;
  setDVCToShow;
  live,
  staging,
  setLive,
  setStaging,
  refresh,
  setRefresh,
  showTest
}

const FlowCanvas: React.FC<FlowCanvasProps> = ({
  dialog,
  setDialog,
  dialogTriggers,
  setDialogTriggers,
  previewVersions,
  setPreviewVersions,
  DVCToShow,
  setDVCToShow,
  live,
  staging,
  setLive,
  setStaging,
  refresh,
  setRefresh,
  showTest

}) => {
  const reactFlowWrapper = useRef(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [entities, setEntities] = useState({});
  const [showEntitiesSheet, setShowEntitiesSheet] = useState(false);
  const [onPublishLoadings, setOnPublishLoadings] = useState(false);
  const [onPublishChanges, setOnPublishChanges] = useState(0);
  const [onUpdateChanges, setOnUpdateChanges] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const [selectedNodeIds, setSelectedNodeIds] = useState([]);
  const [numberOfSelectedNodes, setNumberOfSelectedNodes] = useState(0);

  
  const confirmModal = useConfirmModal();
  const [onDuplicateDialog, setOnDuplicateDialog] = useState(false);
  const [dialogToduplicateName, setDialogToduplicateName] = useState('');
  const { project, fitView, getNodes, zoomIn, zoomOut, zoomTo } =
    useReactFlow();
  const bot = useBotStore((state) => state.bot);
  const { updateOne } = useDialogStore();
  const onDeleteEdge = useCallback(
    (id: string) => {
      setOnUpdateChanges((change)=> change + 1);
      setEdges((eds) => eds.filter((e) => e.id !== id));
    },
    [setEdges]
  );
  const [globals, setGlobals] = useState({});
  const [isExpiry, setIsExpiry] = useState(false);
  const [dialogSettings, setDialogSettings] = useState<IDialogExpiryConfig>({
    expiry_min: 30,
    check_at: "each_message",
    action: {
      type: "send_message",
      config: {
        message: "__ar__:#### __en__:####",
        dialog: {
          path: "",
          lang: "__ar__:##ar## __en__:##en##",
        },
      },
    },
  });

  const onSelectNode = useCallback(
    (isSelected, id) => {
      // if node is selected: add node.id to selected node ids array, else: remove node.id from it
      setSelectedNodeIds((prevSelectedNodeIds) => {
        const updatedSelectedNodeIds = isSelected
          ? [...prevSelectedNodeIds, id]
          : prevSelectedNodeIds.filter(nodeId => nodeId !== id);
  
        setNumberOfSelectedNodes(updatedSelectedNodeIds.length); // update the count
        return updatedSelectedNodeIds;
      });
    }, []);
  
  // update nodes when selectedNodeIds or nodes change
  useEffect(() => {
    const updatedNodes = nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        onSelectNode: onSelectNode, 
        isSelected: selectedNodeIds.includes(node.id) 
      }
    }));
  
    setNodes(updatedNodes); 
  }, [selectedNodeIds]);
  
  // select all nodes except 'node_trigger'
  const selectAll = () => {
    const allNodeIds = nodes
      .filter((node) => node.id != 'node_trigger')
      .map((node) => node.id);

    setSelectedNodeIds(allNodeIds);
    setNodes((prevNodes) =>
      prevNodes.map((node) => ({
        ...node,
        selected: node.id !== 'node_trigger', 
      }))
    );
    // update numberOfSelectedNodes
    setNumberOfSelectedNodes(allNodeIds.length);
  };
  
  // unselect all nodes
  const unSelectAll = () => {
    setSelectedNodeIds([]);
    setNodes((prevNodes) =>
      prevNodes.map((node) => ({ ...node, selected: false }))
    );
  };
  
  const duplicateAll = () => {
  
    const newNodes = [];
    const newEdges = [];
    const nodeMapping = {};
  
    // duplicate selected nodes
    nodes.forEach((node) => {
      if (selectedNodeIds.includes(node.id)) {
        // new ID and update position for duplicated node
        const newID = `${node.id}_duplicate_${Math.random().toString(36).slice(2, 11)}`;
        const position = { x: node.position.x + 50, y: node.position.y + 200 };
        
        // update duplicated node config
        const newConfig = JSON.parse(JSON.stringify([...node.data.config]));
        newConfig.forEach((obj) => {
          delete obj.step;
          delete obj.path;
          delete obj.node;
          delete obj.position;
          obj.node_id = newID;
        });
        
  
        // create duplicated node object
        const newNode = {
          ...node,
          id: newID,
          position: position,
          data: {
            ...node.data,
            config: newConfig,
          },
          selected: false,
        };
  
        newNodes.push(newNode); 
        nodeMapping[node.id] = newNode.id; 
      }
    });
  
    // duplicate selected edges
    edges.forEach((edge) => {
      if (selectedNodeIds.includes(edge.source) && selectedNodeIds.includes(edge.target)) {
        newEdges.push({
          ...edge,
          id: `${edge.id}_duplicate_${Math.random().toString(36).slice(2, 11)}`,
          source: nodeMapping[edge.source],
          target: nodeMapping[edge.target],
        });
      }
    });
  

    setNodes([...nodes, ...newNodes]);
    setEdges([...edges, ...newEdges]);
    setOnUpdateChanges((change) => change + 1);
  };
  
  const deleteAll = () => {
    
    const handleDeleteAll = () => {
      // Filter nodes/edges to keep based on selection
      const nodesToKeep = nodes.filter(
        (node) => !selectedNodeIds.includes(node.id) 
      );

      const edgesToKeep = edges.filter(
        (edge) =>
          !selectedNodeIds.includes(edge.source) &&
          !selectedNodeIds.includes(edge.target)
      );
  
      // Update nodes and edges state with filtered lists
      setNodes(nodesToKeep);
      setEdges(edgesToKeep);
      setSelectedNodeIds([]); // Clear selection after deletion
      setOnUpdateChanges((change) => change + 1); 
    };
  
    // Open confirm modal for deletion
    confirmModal.setType("delete");
    confirmModal.setOnConfirm(handleDeleteAll); 
    confirmModal.onOpen(); 
  };
  
  // show/hide menu based on selected nodes
  useEffect(() => {

    setShowMenu(!(selectedNodeIds.length === 0)); 
  }, [selectedNodeIds]); 
  
  const onConnect = useCallback(
    (params) => {
      const isConnected = edges.find(
        (a) =>
          (a.source === params.source &&
            a.sourceHandle === params.sourceHandle) ||
          (a.target === params.target && a.sourceHandle === params.sourceHandle)
      );
      if (isConnected) {
        return false;
      }
      setEdges((eds) => {
        setOnUpdateChanges((change)=> change + 1);
        
        return addEdge(
          {
            ...customEdge,
            id: params.source + "-" + params.target,
            ...params,
            data: {
              onDelete: onDeleteEdge,
            },
          },
          eds
        );
      });
    },
    [edges, setEdges]
  );

  // Remove node.id from selected nodes array
  const removeNodeFromSelected = useCallback(
    (nodeId) => {
      setSelectedNodeIds((prevSelectedNodeIds) => {
        return prevSelectedNodeIds.filter(id => id !== nodeId);
      });
    },
    [setSelectedNodeIds]
  );
  
  const onDeleteNode = useCallback(
    (node) => {
      setOnUpdateChanges((change)=> change + 1);
      if (node === "node_trigger") return false;
      setEdges((prevEdges) => {
        var edgesToUpd = [...prevEdges];

        const incomers = prevEdges
          .filter((e) => {
            return e.id.startsWith(node.id);
          })
          .map((a) => a.id);

        const outgoers = prevEdges
          .filter((e) => {
            return e.id.endsWith(node.id);
          })
          .map((a) => a.id);

        edgesToUpd = edgesToUpd.filter(
          (e) => !incomers.includes(e.id) && !outgoers.includes(e.id)
        );

        return [...edgesToUpd];
      });
      setNodes((nds) => {
        const ndsClone = [...nds];
        return ndsClone.filter((nd) => nd.id !== node.id);
      });
      removeNodeFromSelected(node.id);
      return true;
    },
    [edges, setEdges, nodes, setNodes]
  );

  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const handleOnClickBlock = useCallback((node) => {
    return setSelectedNode(node);
  }, []);

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();
      setOnUpdateChanges((change)=> change + 1);
      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const block_type = event.dataTransfer.getData("application/reactflow");
      const block = blocks.find((a) => a.block_type === block_type);
      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const randomId = uuidv4();

      const config = BlockConfigFactory.createBlock(block_type).getConfig();

      const newNode = {
        id: randomId,
        type: "custom",
        position,
        data: {
          label: block.block_type,
          block,
          config,
          handleOnClickBlock,
          onDeleteNode,
          tracked: false,
          selected: false,
          onSelectNode,
          onDuplicateNode,
        },
      };
      setNodes((nds) => nds.concat(newNode));
    },
    [setNodes, nodes]
  );

  const handleSocketConnection = useCallback(() => {
    if (nodes.length > 1) {
      const negotiationURL = `${constant.SOCKET_SERVER}/api?userId=${dialog.dialog_id}&conversation_id=${dialog.dialog_id}&channel=webchat&bot_id=${dialog.bot_id}&type=server`;
      const _connection = new signalR.HubConnectionBuilder()
        .withUrl(negotiationURL)
        .build();
      _connection
        .start()
        .then(() => console.log("SHOULD BE WORKING"))
        .catch((err) => {});

      _connection.on("chatMessage", (message) => {
        const targetNode = nodes.find((a) => a.id === message.node_id);
        fitView({
          padding: 0.1,
          includeHiddenNodes: false,
          nodes: [targetNode],
          duration: 0.8,
        });
      });
    }
  }, [dialog, nodes.length]);

  useEffect(() => {
    if (
      dialog?.configuration &&
      Object.keys(dialog.configuration).length !== 0
    ) {
      const dialogBlocks = distinct(dialog.configuration.steps, "node_id");
      const dialogEntities = dialog.configuration.entities;
      const dialogSteps = dialog.configuration.steps;
      const dialogEdges = distinct(dialog.configuration.edges, "id");
      const dialogExpiryConfig = dialog.configuration.dialogExpiry;
      const dialogNodes = [];
      for (var i = 0; i < dialogBlocks.length; i++) {
        const block = blocks.find(
          (a) => a.block_type === dialogBlocks[i].block_type
        );
        dialogNodes.push({
          id: dialogBlocks[i].node_id || uuidv4(),
          type: "custom",
          position: dialogBlocks[i].position || { x: 250 * i + 1, y: 50 },
          data: {
            label: block.block_type,
            block,
            config: dialogSteps.filter(
              (a) => a.node_id === dialogBlocks[i].node_id
            ),
            handleOnClickBlock,
            onDeleteNode: previewVersions? () => {}  : onDeleteNode,
            tracked: false,
            selected: false,
            onSelectNode,
            onDuplicateNode: previewVersions? () => {} :onDuplicateNode,
            previewVersions
          },
        });
      }
      setNodes((nds) => {
        const ndsClone = []
        ndsClone.push(...initNodes,...dialogNodes);
        console.log(ndsClone, "clone")
        ndsClone[0].data.handleOnClickBlock = handleOnClickBlock;
        return ndsClone;
      });
      setEdges((eds) => {
        const edsClone=[];
        edsClone.push(
          ...dialogEdges.map((e) => {
            return {
              ...customEdge,
              ...e,
              data: {
                onDelete: onDeleteEdge,
                previewVersions
              },
            };
          })
        );
        return edsClone;
      });
      setEntities(dialogEntities);
      getConfig(dialog.bot_id).then((data) => {
        console.log(data);
        if (data) {
          setGlobals(data.globals || {});
        }
      });
      if (dialogExpiryConfig) {
        setIsExpiry(true);
        setDialogSettings(dialogExpiryConfig);
      } else {
        setIsExpiry(false);
        setDialogSettings({
          expiry_min: 30,
          check_at: "each_message",
          action: {
            type: "send_message",
            config: {
              message: "__ar__:#### __en__:####",
              dialog: {
                path: "",
                lang: "__ar__:##ar## __en__:##en##",
              },
            },
          },
        });
      }
    }
  }, [dialog]);

  useEffect(() => {
    if(showTest){
    handleSocketConnection();
    }
  }, [dialog, nodes.length, showTest]);

  const onSaveNodeData = useCallback(
    (node) => {
      setOnUpdateChanges((change)=> change + 1);
      const ndsClone = [...nodes];
      const targetNodeIndex = ndsClone.findIndex((nd) => node.id === nd.id);
      if (targetNodeIndex !== -1) {
        ndsClone[targetNodeIndex]["data"]["config"] = node.data.config;
      }
      setNodes([]);
      setTimeout(() => setNodes(ndsClone), 1);
      // setNodes(ndsClone);
      setSelectedNode(null);
    },
    [setNodes, nodes, setSelectedNode]
  );

  const extractStepsFromNodes = (
    node_id,
    edges,
    nodes,
    connectedNodesCount,
    steps,
    path,
    nodeOrder
  ) => {
    const sourceEdges = edges.filter((a) => a.source === node_id);
    var nodeCount = connectedNodesCount;

    var stepsToUpd = steps;
    if (sourceEdges.length) {
      nodeCount++;
    }
    const sourceNode = nodes.find((a) => a.id === node_id);

    const isMultiSourceNode =
      sourceNode?.data?.block?.block_type === "Prompt Reports"
        ? sourceNode?.data?.config?.[3]?.handlerParams?.[0]?.type === "rate"
        : sourceNode?.data?.block?.hasConnectors;
    if (isMultiSourceNode) {
      const stepsArr = [];
      stepsToUpd.forEach((a) => {
        const isConnectorStep = connectorsSteps.includes(a.handler);
        if (
          (a.node_id === sourceNode.id &&
            isConnectorStep &&
            a.handler !== "reportSaveHandler") ||
          (a.handler === "reportSaveHandler" && a.handlerParams?.length === 2)
        ) {
          const handlerParams = [];

          if (!sourceEdges.length) {
            stepsArr.push({
              ...a,
            });
          } else {
            sourceEdges.forEach((edge) => {
              const targetNode = nodes.find((a) => a.id === edge.target);
              if (targetNode) {
                const config =
                  a.handler === "entityStoreLookup" ||
                  a.handler === "textraction" ||
                  a.handler === "loopCounter" ||
                  a.handler === "callAPIJSON" ||
                  a.handler === "requestLocation" ||
                  a.handler === "conditions" ||
                  a.handler === "validateIncomingAttachment" ||
                  (a.handler === "reportSaveHandler" &&
                    a.handlerParams?.length > 1)
                    ? a.handlerParams[1].find(
                        (b) => b.connector_id === edge.sourceHandle
                      )
                    : a.handlerParams[0].find(
                        (b) => b.connector_id === edge.sourceHandle
                      );
                handlerParams.push({
                  ...config,
                  redirect: `${path}__${targetNode.id}`,
                });
              }
            });
            if (
              a.handler === "textraction" ||
              a.handler === "entityStoreLookup" ||
              a.handler === "loopCounter" ||
              a.handler === "callAPIJSON" ||
              a.handler === "conditions" ||
              a.handler === "reportSaveHandler" ||
              a.handler === "validateIncomingAttachment" ||
              a.handler === "requestLocation"
            ) {
              const connectedHandleparamsIDs = handlerParams.map(
                (a) => a.connector_id
              );
              const allParams = a.handlerParams[1].map((param) => {
                if (connectedHandleparamsIDs.includes(param.connector_id)) {
                  return handlerParams.find(
                    (a) => a.connector_id === param.connector_id
                  );
                }
                return param;
              });
              stepsArr.push({
                ...a,
                handlerParams: [a.handlerParams[0], [...allParams]],
              });
            } else {
              const connectedHandleparamsIDs = handlerParams.map(
                (a) => a.connector_id
              );
              const allParams = a.handlerParams[0].map((param) => {
                if (connectedHandleparamsIDs.includes(param.connector_id)) {
                  return handlerParams.find(
                    (a) => a.connector_id === param.connector_id
                  );
                }
                return param;
              });
              stepsArr.push({
                ...a,
                handlerParams: [[...allParams]],
              });
            }
          }
        } else {
          stepsArr.push({
            ...a,
          });
        }
      });
      stepsToUpd = stepsArr;
    }

    for (var i = 0; i < sourceEdges.length; i++) {
      const edge = sourceEdges[i];
      const targetNode = nodes.find((a) => a.id === edge.target);
      targetNode.data.config.forEach((step) => {
        stepsToUpd.push({
          ...step,
          position: targetNode.position,
          node_id: targetNode.id,
          path: isMultiSourceNode ? `${path}__${targetNode.id}` : path,
          node: isMultiSourceNode ? 1 : nodeOrder + 1,
          step: stepsToUpd.filter(
            (a) =>
              a.path ===
              (isMultiSourceNode ? `${path}__${targetNode.id}` : path)
          )?.length,
        });
      });

      const results = extractStepsFromNodes(
        targetNode.id,
        edges,
        nodes,
        nodeCount,
        stepsToUpd,
        isMultiSourceNode ? `${path}__${targetNode.id}` : path,
        isMultiSourceNode ? 1 : nodeOrder + 1
      );
      stepsToUpd = results.steps;
      nodeCount = results.connectedNodesCount;
    }

    return {
      steps: [...stepsToUpd],
      connectedNodesCount: nodeCount,
    };
  };

  const imageWidth = 1500;
  const imageHeight = 768;

  //update the dialog (dvc.is_staging=true , onUpdateChanges++)
  const onUpdate = useCallback(async()=>{
    setOnUpdateChanges((change) => change + 1);
    const { steps, connectedNodesCount } = extractStepsFromNodes(
      "node_trigger",
      edges,
      nodes,
      1,
      [],
      "1",
      1
    );
    const dialogToUpdate = {
      dialog_id: dialog.dialog_id,
      dialogExpiry: isExpiry ? dialogSettings : undefined,
      entities: entities,
      steps: distinctMultiple(steps, ["node_id", "handler"]),
      edges: distinct(edges, "id"),
    };
    update({dialog_id: dialog.dialog_id, bot_id:dialog.bot_id, user_id: bot.user_id}, {...dialogToUpdate});
    setOnUpdateChanges(0);


  }, [nodes, edges, isExpiry, dialogSettings, entities]);


  const captureScreenshot = useCallback(async () => {
    const nodesBounds = getRectOfNodes(getNodes());
    const transform = getTransformForBounds(
      nodesBounds,
      imageWidth,
      imageHeight,
      0.3,
      0.5,
      10
    );
    // FIXME if there is an image in the dialog an error will be thrown
    toBlob(document.querySelector(".react-flow__viewport") as HTMLElement, {
      backgroundColor: "#141416",
      width: imageWidth,
      height: imageHeight,
      style: {
        width: imageWidth.toString(),
        height: imageHeight.toString(),
        transform: `translate(${transform[0]}px, ${transform[1]}px) scale(${transform[2]})`,
      },
    })
      .then(async (dataUrl) => {
        const file = new File([dataUrl], `${dialog.dialog_id}.png`, {
          type: "image/png",
        });
        const formData = new FormData();
        formData.append("file", file);

        const path = `Bots/${bot.file_name}/assets/dialog_${dialog.dialog_id}_${bot.file_name}.png&image=true`;

        uploadBotImages({
          path: path,
          formData: formData,
        })
          .then((res) => {
            const imageURL = constant.MEDIA_STORAGE_URL + path;
            updateOne({ ...dialog, dialog_image: imageURL });
          })
          .catch((err) => console.log(err));
      })
      .catch((error) => {
        console.error("Error in Promise chain:", error);
      })
      .finally(() => {
        setOnPublishLoadings(false);
        setOnUpdateChanges(0);
      });
  }, []);

 //publish the dialog (DVC.is_live =true )
 const onPublish = useCallback(async () => {
  try {
    setOnPublishLoadings(true);

    const { steps, connectedNodesCount } = extractStepsFromNodes(
      "node_trigger",
      edges,
      nodes,
      1,
      [],
      "1",
      1
    );

    const dialogToPublish = {
      dialog_id: dialog.dialog_id,
      dialogExpiry: isExpiry ? dialogSettings : undefined,
      entities: entities,
      steps: distinctMultiple(steps, ["node_id", "handler"]),
      edges: distinct(edges, "id"),
    };

    const publishResult = await publish({dialog_id: dialog.dialog_id, bot_id: dialog.bot_id, user_id: bot.user_id}, {...dialogToPublish});

    setOnPublishLoadings(false);

    return publishResult;
  } catch (error) {
    console.error("Error publishing dialog:", error);
    setOnPublishLoadings(false);
    throw error;
  }
}, [nodes, edges, isExpiry, dialogSettings, entities]);


  const onDuplicate = useCallback(async () => {
    setOnUpdateChanges((change)=> change + 1);
    setOnDuplicateDialog(false);
    
    duplicate(dialog, dialogToduplicateName).then((data) => {
      if (data && !data.message) {
        window.open(`/composer/${data.dialog_id}`, "_blank");
      }
    });
    setDialogToduplicateName('');
  }, [nodes, edges, dialogToduplicateName]);

  const onDuplicateNode = useCallback(
    (node) => {
      setOnUpdateChanges((change)=> change + 1);
      const newID = uuidv4();
      const position = { x: node.xPos + 50, y: node.yPos + 50 };
      const newConfig = JSON.parse(JSON.stringify([...node.data.config]));
      newConfig.forEach((obj) => {
        delete obj.step;
        delete obj.path;
        delete obj.node;
        delete obj.position;
        obj.node_id = newID;
      });
      const newNode = {
        ...node,
        id: newID,
        position: position,
        xPos: position.x,
        yPos: position.y,
        data: {
          ...node.data,
          config: newConfig,
        },
        selected: false,
      };
      setNodes((nds) => nds.concat(newNode));
    },

    [nodes, setNodes]
  );

  const zoomToContent = useCallback(() => {
    fitView({
      padding: 0.1,
      includeHiddenNodes: false,
      nodes: [...nodes],
      duration: 0.8,
    });
  }, [nodes]);

  const resetZoom = useCallback(() => {
    zoomTo(0.1);
  }, []);

  return (
    <React.Fragment>
      <ToolBar
        onUpdate={onUpdate}
        onPublish={onPublish}
        onPublishLoadings={onPublishLoadings}
        onPublishChanges={onPublishChanges}
        onUpdateChanges={onUpdateChanges}
        onDuplicate={onDuplicate}
        dialog={dialog}
        setDialog={setDialog}
        zoomIn={zoomIn}
        zoomOut={zoomOut}
        zoomToContent={zoomToContent}
        resetZoom={resetZoom}
        isExpiry={isExpiry}
        setIsExpiry={setIsExpiry}
        dialogSettings={dialogSettings}
        setDialogSettings={setDialogSettings}
        setOnPublishChanges={setOnPublishChanges}
        setOnUpdateChanges={setOnUpdateChanges}
        previewVersions={previewVersions}
        setPreviewVersions={setPreviewVersions}
        DVCToShow={DVCToShow}
        setDVCToShow={setDVCToShow}
        live={live}
        staging={staging}
        setLive={setLive}
        setStaging={setStaging}
        refresh={refresh}
        setRefresh={setRefresh}
        dialogTriggers={dialogTriggers} 
        onDuplicateDialog={onDuplicateDialog} 
        setOnDuplicateDialog={setOnDuplicateDialog} 
        dialogToduplicateName={dialogToduplicateName} 
        setDialogToduplicateName={setDialogToduplicateName}
      />

      {/* if (previewVersions === true) -> disable editting for the flow (delete, dupicate, moving, adding nodes) */}
      <div className="h-[calc(100%-50px)] max-h-[calc(100%-50px)]">
        {!previewVersions && selectedNode && !selectedNode.data.block ?.noForm ? (
          <Sheet
            setEntities={setEntities}
            entities={entities}
            selectedNode={selectedNode}
            setSelectedNode={setSelectedNode}
            onSaveNodeData={onSaveNodeData}
            nodes={nodes.filter((a) => a.id !== "node_trigger")}
            dialog={dialog}
            dialogTriggers={dialogTriggers}
            setDialogTriggers={setDialogTriggers}
            globals={globals}
          />
        ) : null}

        {
          <EntitiesSheet
            open={showEntitiesSheet}
            setOpen={setShowEntitiesSheet}
            setEntities={setEntities}
            entities={entities}
            globals={globals}
            dialog={dialog}
            setGlobals={setGlobals}
            setOnUpdateChanges={setOnUpdateChanges}
            onUpdateChanges={onUpdateChanges}
          />
        }

        <div
          className="reactflow-wrapper"
          style={{ height: "100%", position: "relative" }}
          ref={reactFlowWrapper}
          id="dialog-builder-new"
        >

          <ReactFlow
            edges={edges}
            nodes={nodes}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            onDragOver={previewVersions ? () => {} : onDragOver}
            onDrop={previewVersions ? () => {} : onDrop}
            onNodesChange={previewVersions ? () => {} : onNodesChange}
            onEdgesChange={previewVersions ? () => {} : onEdgesChange}
            onConnect={previewVersions ? () => {} : onConnect}
            deleteKeyCode={null}
            // arrowHeadColor="#16aedf"
          >
            <Background
              variant={BackgroundVariant.Lines}
              gap={50}
              size={5}
              color="#202024"
            />
            {/* <MiniMap/> */}
            {!previewVersions ?
            <Panel position="top-right">
              <div
                onClick={() => setShowEntitiesSheet(true)}
                className="flex text-xs items-center text-primary/90 cursor-pointer hover:drop-shadow-xl "
              >
                <BrainCircuit
                  size={35}
                  className=" text-primary  cursor-pointer "
                />
                Entities
              </div>
              </Panel>: null}
              <Panel position="top-center">
                {showMenu&& ( // Show select menu if any node is selected
                  <SelectMenu 
                  onSelectAll={selectAll}
                  onUnselectAll={unSelectAll}
                  onCopy={duplicateAll}
                  onDelete={deleteAll}
                  selectedCount={numberOfSelectedNodes}
                  />
                )}
            </Panel>
          </ReactFlow>
        </div>
      </div>
    </React.Fragment>
  );
};

export default FlowCanvas;
