import { <PERSON>, <PERSON>le, Node, Position } from "reactflow";
import connectorsSteps from "data/composer/connectorsSteps";
import React, { FC, useMemo, memo, useState, useEffect } from "react";
import { CopyIcon, Eye, Plug2, Trash2, X } from "lucide-react";
import { cn } from "lib/utils";
import Content from "common/components/composer/nodeContent";
import useConfirmModal from "common/hooks/useConfirmModal";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "common/ui/contextMenu";
import { getTextBasedOnLanguageForConnectors } from "helpers/multiLang";
import { useLangStore } from "store/language/lang.store";
import CustomCheckbox from "common/ui/inputs/customCheckbox";

const CustomNode = (node) => {
  const { lang } = useLangStore();
  const confirmModal = useConfirmModal();
  const [isChecked, setIsChecked] = useState(node.selected);

  const connectors = useMemo(() => {
    const hasConnectors = node.data.config.find((step) =>
      connectorsSteps.includes(step.handler)
    );
    if (
      hasConnectors &&
      (node.data.config[0].handler === "entityStoreLookup" ||
        node.data.config[0].handler === "textraction" ||
        node.data.config?.[1]?.handler === "validateIncomingAttachment" ||
        node.data.config[0].handler === "loopCounter" ||
        node.data.config[0].handler === "callAPIJSON" ||
        node.data.config?.[1]?.handler === "requestLocation" ||
        node.data.config[0].handler === "conditions" ||
        node.data.config?.[4]?.handler === "reportSaveHandler")
    ) {
      return hasConnectors.handlerParams[1] || null;
    } else if (hasConnectors && typeof hasConnectors.handlerParams[0]) {
      return hasConnectors.handlerParams[0];
    }
    return null;
  }, [node]);

  const handleDelete = (e) => {
    e.stopPropagation();
    confirmModal.onOpen();
    confirmModal.setOnConfirm(() => {
      node.data.onDeleteNode(node);
    });
  };

  const handleCheckboxChange = (e) => {
    const checked = e.target.checked;
    setIsChecked(checked);
    const ID = node.id;
    node.data.onSelectNode(checked, ID);
  };

  useEffect(() => {
    setIsChecked(node.data.isSelected);
  }, [node.data.isSelected]);
  console.log(connectors);

  return (
    <div
      className={`relative group z-2 grid grid-cols-12 gap-0 bg-themeSecondary min-w-[300px] w-auto  rounded-xl text-white shadow-xl hover:bg-gray-900`}
    >
      {/* if previewVersions === true enter editing mode  */}
      {!node.data.previewVersions && (
        <div className="absolute -top-2 -right-10 z-2 cursor-pointer hidden group-hover:flex flex-col gap-2 p-3  rounded-full">
          <CustomCheckbox
            name={`checkbox_${node.id}`}
            onChange={handleCheckboxChange}
            checked={isChecked ?? false}
            color={node.data.block.color}
          />
          <Trash2
            size={22}
            className="text-gray-900 rounded-full p-1"
            style={{
              background: node.data.block.color,
            }}
            onClick={handleDelete}
          />
          <CopyIcon
            size={22}
            className="text-gray-900 rounded-full p-1"
            style={{
              background: node.data.block.color,
            }}
            onClick={(e) => {
              e.stopPropagation();
              node.data.onDuplicateNode(node);
            }}
          />
        </div>
      )}
      <div
        className="col-span-1 h-full rounded-l-xl"
        style={{ background: node.data.block.color }}
      >
        <Handle
          type="target"
          position={Position.Left}
          id={"target_" + node.id}
          onConnect={(params) => {}}
          style={{
            position: "absolute",
            left: "0",
            top: "50%",
            background: "transparent",
            width: "20px",
            height: "100%",
            borderRadius: "10px",
            border: "none",
          }}
        ></Handle>
      </div>
      <ContextMenu>
        <ContextMenuTrigger asChild className="pointer-events-auto">
          <div
            className={cn(
              " grid grid-cols-1 gap-0 py-2  ",
              connectors || node?.data?.block?.lastNode
                ? "col-span-11 border-t-2 border-b-2  border-r-2 border-[#202024]  rounded-r-xl"
                : "col-span-10 border-t-2 border-b-2 border-[#202024] "
            )}
            onMouseOver={(e) => {
              e.currentTarget.style.borderColor = node.data.block.color;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = "";
            }}
            onClick={() => node.data.handleOnClickBlock(node)}
          >
            <div className="flex justify-between items-center border-b border-gray-700">
              <div className="flex flex-inline p-2 py-3 ">
                <node.data.block.icon color={node.data.block.color} />
                <span className="ml-2 capitalize">
                  {node.data.label.replaceAll("_", " ")}
                </span>
                {node.data.config[0].name ? (
                  <span className="ml-2 truncate max-w-[100px] text-sm text-gray-400">
                    {node.data.config[0].name}
                  </span>
                ) : null}
              </div>
            </div>
            <div className="p-2">
              <Content {...node} />
            </div>
            {connectors ? (
              <div className="p-2">
                <div className="grid grid-cols-1 gap-2">
                  {connectors.map((connector, i) => (
                    <div
                      className="relative w-full p-2 rounded-md bg-gray-700 border"
                      style={{ borderColor: node.data.block.color }}
                      key={i}
                    >
                      {getTextBasedOnLanguageForConnectors(
                        connector.title,
                        lang
                      )}

                      {connector.value}
                      <Handle
                        type="source"
                        position={Position.Right}
                        id={connector.connector_id}
                        // className='h-8 w-8 '
                        style={{
                          position: "absolute",
                          right: "-10px",
                          bottom: "25%",

                          background: node.data.block.color,
                          width: "20px",
                          height: "20px",

                          borderRadius: "30px",
                          border: "none",

                          // zIndex:1,
                        }}
                      ></Handle>
                    </div>
                  ))}
                </div>
              </div>
            ) : null}
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent className="w-64">
          <ContextMenuItem inset>Open</ContextMenuItem>
          <ContextMenuItem
            onClick={() => node.data.handleOnClickBlock(node)}
            inset
          >
            Edit
          </ContextMenuItem>
          <ContextMenuItem
            onClick={() => node.data.onDuplicateNode(node)}
            inset
          >
            Duplicate
          </ContextMenuItem>
          <ContextMenuItem onClick={handleDelete} inset>
            Delete
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>

      {!connectors && !node?.data?.block?.lastNode ? (
        <div
          className="col-span-1  h-full rounded-r-xl "
          style={{ background: node.data.block.color }}
        >
          <Handle
            type="source"
            position={Position.Right}
            id={"source_" + node.id}
            style={{
              position: "absolute",
              right: "0",
              top: "50%",
              background: "transparent",
              width: "20px",
              height: "100%",
              borderRadius: "10px",
              border: "none",
            }}
          ></Handle>
        </div>
      ) : null}
    </div>
  );
};

export default memo(CustomNode);
