import { memo } from "react";
import { ListStartIcon } from "lucide-react";
import { Handle, useReactFlow, useStore<PERSON><PERSON>, Position } from "reactflow";

const StarterNode = (node) => {    
  
  return (
    <div className="relative z-2 grid grid-rows-1 gap-1 bg-themeSecondary w-[230px]  rounded-md text-white shadow-xl hover:bg-gray-900">
      {/* <img
        className="absolute invert -top-[50px] -left-[90px] w-24 h-2w-24"
        src="https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/start-here.svg"
        alt=""
      /> */}
      <div className="grid grid-cols-12 gap-0">
        <div onClick={()=>node.data.handleOnClickBlock(node)} className="col-span-11 flex flex-inline item-center p-2 py-4 ">
          <ListStartIcon color="#9F6FFC" />
          <span className="ml-2">Dialog Triggered if...</span>
        </div>
        <div className="col-span-1  h-full rounded-r-xl bg-primary  rounded-b-md hover:bg-hover">
          <Handle
            type="source"
            position={Position.Right}
            id="starter"
            style={{
              position: "absolute",
              right: "0",
              top: "50%",
              background: "transparent",
              width: "20px",
              height: "100%",
              borderRadius: "10px",
              border: "none",
            }}
          ></Handle>
        </div>
      </div>
    </div>
  );
};

export default memo(StarterNode);
