import React, { useMemo, memo } from "react";
import {
  useN<PERSON>,
  SmoothStepEdge,
  EdgeText,
  EdgeLabelRenderer,
  BaseEdge,
  EdgeProps,
  getBezierPath,
} from "reactflow";

const SmartEdge = (props: any) => {
  const {
    id,
    sourcePosition,
    targetPosition,
    sourceX,
    sourceY,
    targetX,
    targetY,
    style,
    markerStart,
    markerEnd,
    // centerX,
    // centerY,
    label,
    labelStyle,
    labelShowBg,
    labelBgStyle,
    labelBgPadding,
    labelBgBorderRadius,
    data,
  } = props;

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // const nodes = useNodes();

  // const currentEdgePathIndex = useMemo(
  //   () => edgePaths.getEdgePathIndex(id),
  //   [id, edgePaths]
  // );
  // let currentEdgePaths = useMemo(() => {
  //   if (currentEdgePathIndex !== null) {
  //     return currentEdgePaths.slice(0, currentEdgePathIndex);
  //   }
  //   edgePaths.getEdgePaths()
  // },[edgePaths]) ;

  // const getSmartEdgeResponse = useMemo(() => {
  //   return getSmartEdge({
  //     sourcePosition,
  //     targetPosition,
  //     sourceX,
  //     sourceY,
  //     targetX,
  //     targetY,
  //     nodes,
  //     // edgesPath: currentEdgePaths,
  //     options: {
  //       nodePadding: 20,
  //       edgePadding: 3, //edgePadding => mutiple of gridRatio
  //       gridRatio: 5,
  //     },
  //   });
  // }, [
  //   sourcePosition,
  //   targetPosition,
  //   sourceX,
  //   sourceY,
  //   targetX,
  //   targetY,
  //   nodes,
  //   // currentEdgePaths
  // ]);

  // let { smoothedPath, graphPath } = getSmartEdgeResponse || {};

  // edgePaths.setEdgePaths({ id, smoothedPath, graphPath });

  // if (getSmartEdgeResponse === null) {
  //   return <SmoothStepEdge {...props} />;
  // }

  // const index = Math.floor(graphPath.length / 2);
  // const middlePoint = graphPath[index];
  // const [middleX, middleY] = middlePoint;

  // const { svgPathString } = getSmartEdgeResponse;

  return (
    <>
      {/* if previewVersions === true enter editing mode  */}
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      <foreignObject
        style={{
          transform: `translate(${labelX}px,${labelY - 10}px)`,

          borderRadius: 5,
          fontSize: 10,
          fontWeight: 700,
        }}
        className={`${
          data.previewVersions ?
           'h-6 w-8  p-1 align-middle cursor-default' 
          : 
           'h-6 w-8  p-1 align-middle bg-[#6CEAFF] hover:bg-red-500 before:w-fit before:content-["Next"] hover:before:content-["Delete"] hover:w-10'
        }`}

        onClick={() => !data.previewVersions && data?.onDelete(id)}
      />
    </>
  );
};

export default memo(SmartEdge);
