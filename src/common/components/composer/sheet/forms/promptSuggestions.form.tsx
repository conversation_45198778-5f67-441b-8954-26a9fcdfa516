import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import generateStorageId from "helpers/generateStorageId";
import { PlusCircle, Trash2 } from "lucide-react";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useRef, useState } from "react";
import { Node } from "reactflow";
import ImageInput from "common/ui/inputs/imageInput";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { uploadBotImages } from "apis/file.api";
import { useLangStore } from "store/language/lang.store";
import {
  transformTextToLanguageObj,
  setValuesBasedOnLanguage,
  transformLangObjToText,
} from "helpers/multiLang";
import SelectEntity from "../selectEntity";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";
type TviewType = "Buttons-View" | "Card-View";
const PromptSuggestionsSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  setEntities,
  globals,
}) => {
  const { lang } = useLangStore();
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [suggestions, setSuggestions] = useState([]);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [oldEntites, setOldEntiteis] = useState("");
  const [pagiNum, setPagiNum] = useState("")
  const bot = useBotStore((state) => state.bot) as IBot;
  const [viewType, setViewType] = useState<TviewType>("Buttons-View");
  const [cardData, setCardData] = useState({
    heading: { ar: "", en: "" },
    subheading: { ar: "", en: "" },
    imageSrc: { ar: "", en: "" },
  });
  const voiceDataRef = useRef({
    voice_path_male: "",
    voice_path_female: "",
  });
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    if (nodeData?.data?.config) {
      const prevViewType =
        nodeData.data.config[0]?.handlerParams[1]?.viewType || viewType;
      const prevCardData = {
        ...nodeData.data.config[0].handlerParams[1],
        heading: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[1]?.heading || ""
        ),
        subheading: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[1]?.subheading || ""
        ),
        imageSrc: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[1]?.imageSrc || ""
        ),
      };

      setViewType(prevViewType);
      setCardData(prevCardData);
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);

      const updatedSuggestions = nodeData.data.config[0].handlerParams[0].map((a) => {
        const altsEn = [];
        const altsAr = [];
        if (a.alternatives.en || a.alternatives.ar) {
          a.alternatives.forEach((alt) => {
            if (alt.en) {
              altsEn.push(alt.en);
            }
            if (alt.ar) {
              altsAr.push(alt.ar);
            }
          });
          return {
            ...a,
            trigger: transformTextToLanguageObj(a.trigger),
            altsEn,
            altsAr,
          };
        } 
        // handles old logic
        else {
          return {
            ...a,
            trigger: transformTextToLanguageObj(a.trigger),
            alternatives: a.alternatives?.map((alt) =>
              transformTextToLanguageObj(alt)
            ),
          };
        }
      });
  
      setSuggestions(updatedSuggestions);
      setPagiNum(nodeData.data.config[0]?.handlerParams[2]);
      setEntity(nodeData.data.config[1].recieving);
      setOldEntiteis(nodeData.data.config[1]?.recieving);
      voiceDataRef.current = nodeData.data.config[0]?.handlerParams[3];

    }
  }, [nodeData || oldEntites]);

  const addSuggestion = () => {
    const suggestionsClone = [...suggestions];

    const connector_id = generateStorageId();
    suggestionsClone.push({
      connector_id: connector_id,
      trigger: { ar: "", en: "" },
      altsEn: [],
      altsAr: [],
      // handles old logic
      alternatives: [],
    });
    setSuggestions(suggestionsClone);
  };

  const handleDelete = (connector) => {
    var suggestionsClone = [...suggestions];
    suggestionsClone = suggestionsClone.filter(
      (c, idx) => c.connector_id !== connector.connector_id
    );
    setSuggestions(suggestionsClone);
  };

  const handleSuggestionChange = (event, connector) => {
    const suggestionsClone = [...suggestions];
    const index = suggestionsClone.findIndex(
      (c) => c.connector_id === connector.connector_id
    );
    suggestionsClone[index] = {
      ...suggestionsClone[index],
      trigger: setValuesBasedOnLanguage(
        event.target.value,
        suggestionsClone[index].trigger,
        lang
      ),
    };

    setSuggestions(suggestionsClone);
  };

  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].handlerParams[0] = suggestions.map((a) => {
      var alternatives = []
      if (a.altsEn || a.altsAr) {
        alternatives = [
          ...a.altsEn.map(alt => ({ en: alt })),
          ...a.altsAr.map(alt => ({ ar: alt }))
        ]
      } 
      // handles old logic
      else {
        alternatives = a.alternatives?.map((alt) => transformLangObjToText(alt))
      }

      return {
        ...a,
        trigger: transformLangObjToText(a.trigger),
        alternatives
      };
    });
    nodeToEditClone.data.config[0].handlerParams[1] = {
      imageSrc: transformLangObjToText(cardData.imageSrc),
      heading: transformLangObjToText(cardData.heading),
      subheading: transformLangObjToText(cardData.subheading),
      viewType: viewType,
    };
    nodeToEditClone.data.config[0].name = widgetName;
    if (Boolean(entity)) {
      nodeToEditClone.data.config[1].recieving = entity;
    }
    nodeToEditClone.data.config[0].handlerParams[2] = pagiNum

    nodeToEditClone.data.config[1].handlerParams[0] = suggestions.map((a) => {
      var alternatives = []
      if (a.altsEn || a.altsAr) {
        alternatives = [
          ...a.altsEn.map(alt => ({ en: alt })),
          ...a.altsAr.map(alt => ({ ar: alt }))
        ]
      }
      // handles old logic 
      else {
        alternatives = a.alternatives?.map((alt) => transformLangObjToText(alt))
      }
      return {
        ...a,
        title: transformLangObjToText(a.trigger),
        trigger: transformLangObjToText(a.trigger),
        alternatives,
      };
    }); 

    nodeToEditClone.data.config[1].handlerParams[0].push({
      title: "not matched",
      redirect: "",
      connector_id: "0",
    });
    nodeToEditClone.data.config[0].handlerParams[3] = {voice_path_male:voiceDataRef.current?.voice_path_male || '',voice_path_female:voiceDataRef.current?.voice_path_female || ''};
    onSaveNodeData(nodeToEditClone);
  };

  const handleAddAlternative = (connector, lang) => {
    const suggestionsClone = [...suggestions];
    const index = suggestionsClone.findIndex(c => c.connector_id === connector.connector_id);
  
    if (lang === "en") {
      if (!suggestionsClone[index].altsEn) {
        suggestionsClone[index].altsEn = [];
      }
      suggestionsClone[index].altsEn.push("");
    } else if (lang === "ar") {
      if (!suggestionsClone[index].altsAr) {
        suggestionsClone[index].altsAr = [];
      }
      suggestionsClone[index].altsAr.push("");
    } else { // handles old logic
      if (!suggestionsClone[index].alternatives) {
        suggestionsClone[index].alternatives = [];
      }
      suggestionsClone[index].alternatives.push({ ar: "", en: "" });
    }
  
    setSuggestions(suggestionsClone);
  };
  
  const handleAlternativeOnChange = (e, connector, idx, language) => {
    const suggestionsClone = [...suggestions];
    const index = suggestionsClone.findIndex(c => c.connector_id === connector.connector_id);
  
    if (language === "en") {
      suggestionsClone[index].altsEn[idx] = e.target.value;
    } else if (language === "ar") {
      suggestionsClone[index].altsAr[idx] = e.target.value;
    } else { // handles old logic
      suggestionsClone[index].alternatives[idx] = setValuesBasedOnLanguage(
        e.target.value,
        suggestionsClone[index].alternatives[idx],
        lang
      );
    }
    setSuggestions(suggestionsClone);
  };
  
  const handleAlternativeDelete = (connector, idx, lang) => {
    const suggestionsClone = [...suggestions];
    const index = suggestionsClone.findIndex(c => c.connector_id === connector.connector_id);
  
    if (lang === "en") {
      suggestionsClone[index].altsEn.splice(idx, 1);
    } else if (lang === "ar") {
      suggestionsClone[index].altsAr.splice(idx, 1);
    } else { // handles old logic
      suggestionsClone[index].alternatives.splice(idx, 1);
    }
  
    setSuggestions(suggestionsClone);
  };    

  const uploadImg = async (file) => {
    const formData = new FormData();
    formData.append("file", file);
    if (file) {
      await uploadBotImages({
        path: `Bots/${bot.file_name}/Wa-suggestions/${file.name}&image=true`,
        formData,
      });
      const imageUrl =
        "https://infotointell.fra1.digitaloceanspaces.com/" +
        `Bots/${bot.file_name}/Wa-suggestions/${file.name}`;
      setCardData({
        ...cardData,
        imageSrc: { ...cardData.imageSrc, [lang]: imageUrl },
      });
    }
  };

  const handleChange = (e: { target: { name: string; value: string } }) => {
    setCardData({
      ...cardData,
      [e.target.name]: setValuesBasedOnLanguage(
        e.target.value,
        cardData[e.target.name],
        lang
      ),
    });
  };



  const handleUpdateVoice = (data: TVoiceBody) => {
    const updatedVoices = data;
    delete updatedVoices.bot_id;
    delete updatedVoices.user_id;
    voiceDataRef.current = {...voiceDataRef.current,...updatedVoices};
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <VoiceModalWrapper
       handleSave={handleSave}
       handleUpdateVoice={handleUpdateVoice}
       setShowModal={setShowModal}
       showModal={showModal}
       text={suggestions?.map((suggestion)=>{
        return  suggestion?.trigger[lang]
       })?.join(', ')}
       voice_path_female={nodeData?.data.config[0]?.handlerParams[3]?.voice_path_female || ''}     
       voice_path_male={nodeData?.data.config[0]?.handlerParams[3]?.voice_path_male || ''} 
         />

        <label htmlFor="entity">Entity Storage</label>
        <SelectEntity
          entities={entities}
          onValueChange={(value) => {
            setEntity(value);
          }}
          entity={entity}
          globals={globals}
        />
        <label htmlFor="view">View Type</label>
        <Select
          value={viewType}
          onValueChange={(value) => {
            setViewType(value as TviewType);
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="View type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={"Buttons-View"}>{"Buttons View"}</SelectItem>
            <SelectItem value={"Card-View"}>{"Card View"}</SelectItem>
          </SelectContent>
        </Select>
        {viewType === "Card-View" && (
          <div className="group overflow-hidden rounded border p-3 border-gray-300 [&_summary::-webkit-details-marker]:hidden">
            <ImageInput
              imgSrc={cardData?.imageSrc[lang]}
              uploadImg={uploadImg}
            />

            <div>
              <label htmlFor="view" className="flex gap-5 items-end">Heading:
              <small className="text-white/50">
            - Characters Limit: WhatsApp: 60, Facebook: 80

          </small>
              </label>
              <Textarea
                id="cardheading"
                name="heading"
                // maxLength={25}
                placeholder="Card Heading..."
                tabIndex={0}
                dir="auto"
                value={cardData.heading[lang]}
                className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 `}
                rows={1}
                onChange={handleChange}
              ></Textarea>
              <small className="float-right text-white/50">
            { cardData.heading[lang]?.length} characters
          </small>
            </div>
            <div>
              <label htmlFor="view" className="flex gap-5 items-end">Subheading:
              <small className="text-white/50">
            - Characters Limit: WhatsApp: 1024, Facebook: 80
          </small>
              </label>
              <Textarea
                id="cardsubheading"
                name="subheading"
                // maxLength={25}
                placeholder="Card Subheading..."
                tabIndex={0}
                dir="auto"
                value={cardData.subheading[lang]}
                className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 `}
                rows={1}
                onChange={handleChange}
              ></Textarea>
               <small className="float-right text-white/50">
            {cardData.subheading[lang]?.length} characters
          </small>
            </div>
          </div>
        )}
         <label htmlFor="pagiNum"><span className="text-gray-300">(Optional)</span> Set Button Limit for Web Channel</label>
          <Input
            name="pagiNum"
            placeholder="Enter buttons' number"
            type="number"
            onChange={(event) => {
              setPagiNum(event.target.value);
            }}
            value={pagiNum}
          />
        <div className="flex justify-between items-center pt-3">
          Add Suggestion 
          <small className="text-white/50">
            {viewType === "Card-View" ? "Buttons Limit: WhatsApp 3, Facebook 3" : "Buttons Limit: WhatsApp 10, Facebook 13"}
          </small>
          <PlusCircle
            className="cursor-pointer hover:text-primary"
            onClick={addSuggestion}
          />
        </div>
        <hr className="text-white/25" />
        <div className="flex flex-col gap-3 max-h-[300px] overflow-y-auto">
          {suggestions &&
            suggestions
              ?.filter((a) => a.connector_id !== "0")
              .map((connector, i) => (
              <div key={i} className="flex gap-3 items-center">
                <div className="p-4 self-stretch w-full bg-secondary rounded flex flex-col">
                  <Input
                    name="suggestions"
                    placeholder={`Enter Suggestion`}
                    value={connector.trigger[lang]}
                    onChange={(event) => handleSuggestionChange(event, connector)}
                  />
                  <span
                    className="text-primary cursor-pointer hover:text-primary/80 text-xs self-end"
                    onClick={() => handleAddAlternative(connector, lang)}
                  >
                    Add Alternative 
                  </span>
                  {connector?.altsEn || connector?.altsAr ?
                    <div>
                      {lang === "en" &&
                      connector?.altsEn?.map((alt, idx) => (
                        <div key={idx} className="flex items-center gap-3">
                        <Input
                        value={alt}
                        onChange={(e) => handleAlternativeOnChange(e, connector, idx, "en")}
                        name="suggestions_alts"
                        />
                        <Trash2
                        onClick={() => handleAlternativeDelete(connector, idx, "en")}
                        size={17}
                        className="mr-2 text-red-500 hover:text-red-800 cursor-pointer"
                        />
                        </div>
                        ))}
                      {lang === "ar" &&
                      connector?.altsAr?.map((alt, idx) => (
                        <div key={idx} className="flex items-center gap-3">
                        <Input
                        value={alt}
                        onChange={(e) => handleAlternativeOnChange(e, connector, idx, "ar")}
                        name="suggestions_alts"
                        />
                        <Trash2
                        onClick={() => handleAlternativeDelete(connector, idx, "ar")}
                        size={17}
                        className="mr-2 text-red-500 hover:text-red-800 cursor-pointer"
                        />
                        </div>
                        ))}
                    </div>
                    :
                    <div> 
                      {connector?.alternatives?.map((a, idx) => ( // handles old logic
                      <div key={idx} className="flex items-center gap-3">
                        <Input
                          value={a[lang]}
                          onChange={(e) =>
                            handleAlternativeOnChange(e, connector, idx, "")
                          }
                          name="suggestions_alts"
                        />
                        <Trash2
                          onClick={() =>
                            handleAlternativeDelete(connector, idx, "")
                          }
                          size={17}
                          className="mr-2 text-red-500 hover:text-red-800 cursor-pointer"
                        />
                      </div>
                    ))}
                    </div>
                  }
                </div>
                <Trash2
                onClick={() => handleDelete(connector)}
                size={17}
                className="mr-2 text-red-500 hover:text-red-800 cursor-pointer"
                />
              </div>
            ))}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(PromptSuggestionsSheet);
