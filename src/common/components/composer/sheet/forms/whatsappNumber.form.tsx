import { <PERSON><PERSON> } from "common/ui/button";

import { <PERSON><PERSON><PERSON>oot<PERSON> } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useState } from "react";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import { Node } from "reactflow";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import SelectEntity from "../selectEntity";

const WhatsappNumberForm: IPromptForm = ({
  nodeData,
  entities,
  onSaveNodeData,
  globals
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [entity, setEntity] = useState("");
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setEntity(nodeData.data.config[0].handlerParams[0]);
    }
  }, [nodeData]);

  const handleSave = () => {
    setLoading(true);
    const nodeDataClone = { ...nodeDataToEdit };
    nodeDataClone.data.config[0].handlerParams[0] = entity;
    onSaveNodeData(nodeDataClone);
    setLoading(false);
  };

  return (
    <>
      <div className="space-y-3 mb-5">
        <div>
        <div className="flex items-center gap-3 mb-4 mt-5">
          <label htmlFor="entity">Entity Storage</label>
          <HelpHoverCard>Choose an entity to save the Whatsapp number in. <br/><span className="italic text-gray-500">Only works in Whatsapp Channel.</span></HelpHoverCard>
        </div>
          <SelectEntity 
            entity={entity || undefined} 
            entities={entities} 
            onValueChange={(value) => {
              setEntity(value);
            }}            
            globals={globals}
          />
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit" loading={loading}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(WhatsappNumberForm);
