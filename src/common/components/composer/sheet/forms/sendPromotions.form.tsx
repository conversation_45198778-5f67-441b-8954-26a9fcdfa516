import Empty from "common/components/empty";
import { Button } from "common/ui/button";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";
import { CustomSearch } from "common/ui/inputs/search";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { SheetFooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useBotStore from "store/bot/bot.store";
import useOfferStore from "store/offers/offers.store";

const SendPromotionsForm: IPromptForm = ({ nodeData, onSaveNodeData, dialog }) => {
  const offers = useOfferStore((state) => state.OFFERS);
  const bot_id = useBotStore((state) => state.bot.bot_id);

  const [keySearch, setKeySearch] = useState("");
  const [offersData, setOffersData] = useState([]);
  const [offerToFilterBy, setOfferToFilterBy] = useState("All");
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [offerIds, setOfferIds] = useState([]);

  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setOffersData([...offers]);
      setOfferIds(nodeConfig.handlerParams[0].offer_ids);
      setWidgetName(nodeConfig.name);
    }
  }, [nodeData]);

  const offersToView = () => {
    var offer_to_view = [...offersData];
    var results = [];
    offer_to_view.map((offer) => {
      const description1 = offer.offer_description || "";
      const description2 = offer.offer_description
        ? offer.offer_description.replace(/أ|إ/g, "ا")
        : "";
      const match3 = description1.toLowerCase().search(keySearch.toLowerCase());
      const match4 = description2.toLowerCase().search(keySearch.toLowerCase());
      if (match3 !== -1 || match4 !== -1) results.push(offer);
    });

    if (offerToFilterBy !== "All") {
      results = results.filter((a) => a.offer_id === +offerToFilterBy);
    }
    return [...results];
  };

  const onSelectOffer = (offer_id, isSelected) => {
    var offerIdsToUpd = [...offerIds];
    if (isSelected) {
      offerIdsToUpd.push(offer_id);
    } else {
      offerIdsToUpd = offerIdsToUpd.filter((a) => a !== offer_id);
    }
    setOfferIds(offerIdsToUpd);
  };

  const handleSave = () => {
    const nodeDataToEditClone = {...nodeDataToEdit};
    const config = nodeDataToEditClone.data.config[0]
    ? nodeDataToEditClone.data.config[0]
    : nodeDataToEditClone.data.config;
    config.handlerParams[0].offer_ids = offerIds.map(a => a);
    config.name = widgetName;
    config.handlerParams[0].bot_id = dialog.bot_id;
    onSaveNodeData(nodeDataToEditClone);
  };
  return offers.length > 0 ? (
    <>
      <div className="flex flex-col gap-4 py-7 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />
        <div className="flex gap-5">
          <CustomSearch
            placeholder="Search for offers"
            onChange={(value) => setKeySearch(value)}
          />
          <Select
            value={offerToFilterBy}
            onValueChange={(value) => setOfferToFilterBy(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select an offer" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {offers?.map((offer, i) => {
                return (
                  <SelectItem key={i} value={offer.offer_id.toString()}>
                    {offer.offer_description}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        <div className="max-h-[400px] overflow-y-auto">
          <div className="flex items-center justify-between p-2 pb-3 bg-secondary sticky top-0">
            <Checkbox
              onChange={(event) => {
                if (!event.target.checked) {
                  setOfferIds([]);
                } else {
                  setOfferIds([...offersToView().map((b) => b.item_id)]);
                }
              }}
              checked={offersToView().length === offerIds.length}
              name={`select-all`}
            />
            <div>Offer</div>
          </div>
          {offersToView()?.map((offer, i) => {
            return (
              <div
                key={i}
                className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
              >
                <Checkbox
                  onChange={(event) =>
                    onSelectOffer(offer.offer_id, event.target.checked)
                  }
                  checked={offerIds?.includes(offer.offer_id)}
                  name={`select-${offer.offer_id}`}
                  key={offer.offer_id}
                />
                <div>{offer.offer_description}</div>
              </div>
            );
          })}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  ) : (
    <div className="flex flex-col items-center justify-center gap-4 py-7 min-h-[400px]">
      <Empty />
      <div className=" font-semibold text-white text-center">
        Please add offers to your bot so you can use the widget
      </div>
    </div>
  );
};

export default memo(SendPromotionsForm);
