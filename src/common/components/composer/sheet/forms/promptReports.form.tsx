import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Switch } from "common/ui/inputs/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { <PERSON><PERSON><PERSON>oot<PERSON> } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import { useLangStore } from "store/language/lang.store";
import {
  setValuesBasedOnLanguage,
  transformLangObjToText,
  transformTextToLanguageObj,
} from "helpers/multiLang";
const PromptReportsForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
}) => {
  const { lang } = useLangStore();
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [mapper, setMapper] = useState({
    nameQ: { en: "", ar: "" },
    emailQ: { en: "", ar: "" },
    phoneQ: { en: "", ar: "" },
    feedbackQ: { en: "", ar: "" },
    endMessageQ: { en: "", ar: "" },
    typeQ: "",
    style: "",
  });

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setMapper({
        nameQ: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[0]
        ),
        emailQ: transformTextToLanguageObj(
          nodeData.data.config[1].handlerParams[0]
        ),
        phoneQ: transformTextToLanguageObj(
          nodeData.data.config[2].handlerParams[0]
        ),
        feedbackQ: transformTextToLanguageObj(
          nodeData.data.config[3].handlerParams[0].question
        ),
        typeQ: nodeData.data.config[3].handlerParams[0].type,
        style: nodeData.data.config[3].handlerParams[0].style,
        endMessageQ: transformTextToLanguageObj(
          nodeData.data.config[4].handlerParams.length === 2 &&
            typeof nodeData.data.config[4].handlerParams[0] !== "string"
            ? nodeData.data.config[4].handlerParams[1]
            : typeof nodeData.data.config[4].handlerParams[0] === "string"
            ? nodeData.data.config[4].handlerParams[0]
            : ""
        ),
      });
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[0] = transformLangObjToText(
      mapper.nameQ
    );
    nodeToEditClone.data.config[1].handlerParams[0] = transformLangObjToText(
      mapper.emailQ
    );
    nodeToEditClone.data.config[2].handlerParams[0] = transformLangObjToText(
      mapper.phoneQ
    );
    nodeToEditClone.data.config[3].handlerParams[0].question =
      transformLangObjToText(mapper.feedbackQ);
    nodeToEditClone.data.config[3].handlerParams[0].type = mapper.typeQ;
    nodeToEditClone.data.config[3].handlerParams[0].style = mapper.style;
    nodeToEditClone.data.config[4].handlerParams[0] = transformLangObjToText(
      mapper.endMessageQ
    );
    nodeToEditClone.data.config[3].name = widgetName;
    onSaveNodeData(nodeToEditClone);
  };
  return (
    <>
        <div className="space-y-3 pb-5 mt-5">
          <div className="flex items-center justify-between">
            <Input
              title="Widget Name"
              name="widgetName"
              placeholder="Widget Name"
              type="text"
              onChange={(event) => {
                setWidgetName(event.target.value);
              }}
              value={widgetName}
            />
          </div>
          <hr className="text-white/25 pb-5"/>
          <div className="flex items-center justify-between">
            <Switch
              name="nameIncluded"
              label="Include Name Question"
              checked={mapper.nameQ[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  setMapper({
                    ...mapper,
                    nameQ: { ...mapper.nameQ, [lang]: "What is your name?" },
                  });
                } else {
                    setMapper({ ...mapper, nameQ: { ...mapper.nameQ, [lang]: "" } });
                  }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
              title=""
              name="nameQ"
              placeholder="What is your name?"
              type="text"
              onChange={(event) => {
                setMapper({
                  ...mapper,
                  nameQ: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.nameQ,
                  lang
                  ),
                });
              }}
              value={mapper.nameQ[lang]}
            />
          </div>
          <div className="flex items-center justify-between">
            <Switch
              name="emailIncluded"
              label="Include Email Question"
              checked={mapper.emailQ[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  setMapper({
                    ...mapper,
                    emailQ: { ...mapper.emailQ, [lang]: "what is your email?" },
                  });
                } else {
                  setMapper({ ...mapper, emailQ: { ...mapper.emailQ, [lang]: "" } });
                }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
            title=""
            name="emailQ"
            placeholder="What is your email?"
            type="text"
            onChange={(event) => {
              setMapper({
                ...mapper,
                emailQ: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.emailQ,
                  lang
                  ),
                });
              }}
              value={mapper.emailQ[lang]}
              />  
          </div>
          <div className="flex items-center justify-between">
            <Switch
              name="phoneIncluded"
              label="Include Phone Question"
              checked={mapper.phoneQ[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  setMapper({
                    ...mapper,
                    phoneQ: { ...mapper.phoneQ, [lang]: "what is your phone?" },
                  });
                } else {
                  setMapper({ ...mapper, phoneQ: { ...mapper.phoneQ, [lang]: "" } });
                }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
            title=""
            name="phoneQ"
            placeholder="What is your phone?"
            type="text"
            onChange={(event) => {
              setMapper({
                ...mapper,
                phoneQ: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.phoneQ,
                  lang
                  ),
                });
              }}
              value={mapper.phoneQ[lang]}
              />
          </div>
          <div className="flex items-center justify-between">
            <Input
              title="Feedback Question"
              name="widgetName"
              placeholder="what is your feedback?"
              type="text"
              onChange={(event) => {
                setMapper({ ...mapper, feedbackQ: setValuesBasedOnLanguage(event.target.value, mapper.feedbackQ, lang) });
              }}
              value={mapper.feedbackQ[lang]}
            />
          </div>
          <div className="space-y-1 w-full">
            <span>Feedback Type</span>
            <Select
              value={mapper.typeQ}
              onValueChange={(value) => {
                const clonedNode = JSON.parse(
                  JSON.stringify(nodeDataToEdit.data.config[4])
                );
                if (value === "rate" && clonedNode.handlerParams.length === 1) {
                  clonedNode.handlerParams.push([
                    {
                      connector_id: "0",
                      title: "Failed",
                      redirect: "",
                    },
                    {
                      connector_id: "1",
                      title: "Answered",
                      redirect: "",
                    },
                  ]);
                  setNodeDataToEdit({
                    ...nodeDataToEdit,
                    data: {
                      ...nodeDataToEdit.data,
                      config: [
                        ...nodeDataToEdit.data.config.slice(0, 4),
                        clonedNode,
                      ],
                    },
                  });
                } else if (
                  value === "text" &&
                  clonedNode.handlerParams.length === 2
                ) {
                  clonedNode.handlerParams.pop();
                  setNodeDataToEdit({
                    ...nodeDataToEdit,
                    data: {
                      ...nodeDataToEdit.data,
                      config: [
                        ...nodeDataToEdit.data.config.slice(0, 4),
                        clonedNode,
                      ],
                    },
                  });
                }
                setMapper({ ...mapper, typeQ: value });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select the feedback type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={"text"}>Text</SelectItem>
                <SelectItem value={"rate"}>Rate</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {mapper.typeQ === "rate" && (
            <div key={mapper.typeQ} className="space-y-1 w-full">
              <span>Feedback Style</span>
              <Select
                value={mapper.style}
                onValueChange={(value) => {
                  setMapper({ ...mapper, style: value });
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select the feedback style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={"stars"}>⭐️ ⭐️ ⭐️ ⭐️ ⭐️</SelectItem>
                  <SelectItem value={"emoji"}>😕 🙂 😊 😁 😍</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          <hr className="text-white/25 pb-5" />
          <div className="flex items-center justify-between">
            <Input
              title="Ending Message"
              name="endMessageQ"
              placeholder="Thank you for your feedback"
              type="text"
              onChange={(event) => {
                setMapper({
                  ...mapper,
                  endMessageQ: setValuesBasedOnLanguage(
                    event.target.value,
                    mapper.endMessageQ,
                    lang
                  ),
                });
              }}
              value={mapper.endMessageQ[lang]}
            />
          </div>
        </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(PromptReportsForm);
