import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import constant from "constant";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { Node } from "reactflow";
import SelectEntity from "../selectEntity";

const TextractionForm: IPromptForm = ({
  nodeData,
  entities,
  onSaveNodeData,
  globals
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [sourceType, setsourceType] = useState("");
  const [sourceEntity, setsourceEntity] = useState("");
  const [entity, setentity] = useState("");
  const [type, settype] = useState("");
  const [typeOption, settypeOption] = useState("");
  const [regexError, setRegexError] = useState("");

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0]?.name);
      setsourceType(nodeData.data.config[0]?.handlerParams[0].sourceType);
      setentity(nodeData.data.config[0]?.handlerParams[0].entity);
      setsourceEntity(nodeData.data.config[0]?.handlerParams[0].sourceEntity);
      settype(nodeData.data.config[0]?.handlerParams[0].type);
      settypeOption(nodeData.data.config[0]?.handlerParams[0].typeOption);
    }
  }, [nodeData]);

  const handleSourceTypeChange = (value) => {
    if (value === "user_question") {
      setsourceEntity("");
    }
    setsourceType(value);
  };

  const handleTypeChange = (value) => {
    settypeOption("");
    setRegexError("");
    settype(value);
  };

  const handleRegexChange = (value) => {
    setRegexError("");
    try {
      new RegExp(value);
    } catch (e) {      
      setRegexError("Please enter a valid regex");
    }

    settypeOption(value);
  };

  const handleSave = () => {
    if(regexError) {
      toast.error("Please enter a valid regex");
      return;
    }
    const nodeDataToEditClone = { ...nodeDataToEdit };

    nodeDataToEditClone.data.config[0].handlerParams[0].sourceEntity =
      sourceEntity;
    nodeDataToEditClone.data.config[0].handlerParams[0].sourceType = sourceType;
    nodeDataToEditClone.data.config[0].handlerParams[0].type = type;
    nodeDataToEditClone.data.config[0].handlerParams[0].entity = entity;
    nodeDataToEditClone.data.config[0].handlerParams[0].typeOption = typeOption;
    nodeDataToEditClone.data.config[0].name = widgetName;

    onSaveNodeData(nodeDataToEditClone);
  };
  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />
        <div className="flex flex-col gap-3">
          <label htmlFor="source-type">Source Type</label>
          <Select
            onValueChange={(value) => {
              handleSourceTypeChange(value);
            }}
            value={sourceType}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select an entity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="user_question">User Question</SelectItem>
              <SelectItem value="entity">Entity</SelectItem>
            </SelectContent>
          </Select>
          {sourceType === "entity" && (
            <>
              <label htmlFor="source-entity">Source Entity</label>
              <SelectEntity 
            entity={sourceEntity}
            onValueChange={(value) => {
              setsourceEntity(value);
            }}
            entities={entities}
            globals={globals}
          />
            </>
          )}
          <label htmlFor="entity">Entity to store in</label>
          <SelectEntity 
            entity={entity}
            onValueChange={(value) => {
              setentity(value);
            }}
            entities={entities}
            globals={globals}
          />
          <label htmlFor="type">Type</label>
          <Select
            onValueChange={(value) => {
              handleTypeChange(value);
            }}
            value={type}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="regex">Regex</SelectItem>
              <SelectItem value="number">Number</SelectItem>
              <SelectItem value="date">Date</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="phone">Phone Number</SelectItem>
            </SelectContent>
          </Select>
          {
            type === "regex" && (
              <Input
                name="regex"
                placeholder="Enter regex"
                type="text"
                onChange={(event) => {
                  handleRegexChange(event.target.value);
                }}
                value={typeOption}
                error={regexError}
              />
            )
          }
          {
            type === "date" && (
              <>
              <label htmlFor="date">Choose Date Format</label>
              <Select
              onValueChange={(value) => {
                settypeOption(value);
              }}
              value={typeOption}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a date format" />
              </SelectTrigger>
              <SelectContent>
                  {
                    constant.DATE_FORMATS.map((format, i) => {
                      return (
                        <SelectItem key={i} value={format}>
                          {format}
                        </SelectItem>
                      );
                    }
                    )
                  }
              </SelectContent>
            </Select>
            </>
            )
          }
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(TextractionForm);
