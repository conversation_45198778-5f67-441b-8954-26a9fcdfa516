import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "common/ui/tabs";
import { Minus, Plus, Trash2 } from "lucide-react";
import Accordion from "common/ui/accordion";
import { Button } from "common/ui/button";
import JsonFormatter from "react-json-formatter";
import { SheetFooter } from "common/ui/sheet";
import { Node } from "reactflow";
import helper from "views/configure/subviews/botInfo/helper";
import SelectEntity from "../selectEntity";
import dynamic from "next/dynamic";
import MultiSelectInput, {
  Toption,
  Toptions,
} from "common/components/MultiSelectInput";
import responseCodesJson from "../../../../../data/responseCodes.json";
import { Switch } from "common/ui/inputs/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import contentTypeJson from "../../../../../data/contentType.json";
import authTypeJson from "../../../../../data/authType.json";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { FilesInput } from "views/unstructured/components";
import AttachmentIcon from "common/components/attachmentIcon";
import MultiPartAttachment from "../../../../../common/components/mutlipart/MultiPartAttachment";
import { uploadSendMessageAttachments } from "apis/file.api";
import constant from "constant";
import { Expand, Minimize } from "lucide-react";
import BinaryAttachment from "common/components/mutlipart/BinaryAttachment";
import { Textarea } from "common/ui/inputs/textarea";
import CustomEditor from "common/ui/customEditor";
export type TField = {
  attachment: any;
  fieldName: string;
  value: string;
  type: "text" | "file";
};
const JsonStyle = {
  propertyStyle: { color: "red" },
  stringStyle: { color: "green" },
  numberStyle: { color: "darkorange" },
};

interface IConfig {
  method: any;
  headers: {};
  withCredentials: boolean;
  crossdomain: boolean;
  body?: string;
}

const JsonAPIForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  setEntities,
  globals,
  onExpandChange,
}) => {
  console.log(nodeData);
  
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [bodyValidationError, setBodyValidationError] = useState<
    string | undefined
  >();
  const [stringifyBodyValue, setStringifyBodyValue] = useState("");
  const [testResult, setTestResult] = useState("{}");
  const [headers, setHeaders] = useState([
    {
      key: "Content-Type",
      value: "application/json",
    },
  ]);
  const [params, setParams] = useState([
    {
      key: "",
      value: "",
    },
  ]);
  const [url, setUrl] = useState("");
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [method, setMethod] = useState("GET");
  const [body, setBody] = useState("{}");
  const [urlError, setUrlError] = useState("");
  const [expandAce, setExpandAce] = useState(false);
  const [responseCodes, setResponseCodes] = useState<Toption[]>([]);
  const [prevResponseCodes, setPrevResponseCodes] = useState<Toption[]>([]);
  const [isCustomResponse, setIsCustomResponse] = useState<boolean>(false);
  const [fields, setFields] = useState<TField[]>([
    { attachment: "", fieldName: "", value: "", type: "text" },
  ]);

  const bot = useBotStore((state) => state.bot) as IBot;

  useEffect(() => {
    if (nodeData?.data?.config) {
      const previousCodes = nodeData.data.config[0].handlerParams[1].filter(
        (CodeResponse, i) => {
          if (i > 1) return CodeResponse;
        }
      );
      const SelectedCodes = previousCodes?.map((value, i) => {
        return { value: value?.title, label: value?.title };
      });
      setPrevResponseCodes(SelectedCodes);
      setWidgetName(nodeData.data.config[0].name);
      setIsCustomResponse(
        nodeData.data.config[0].handlerParams[0]?.isCustomResponse || false
      );

      setNodeDataToEdit(nodeData);
      const headers_keys = Object.keys(
        nodeData.data.config[0].handlerParams[0].headers
      );
      const headers_arr = [];
      const params_arr = [];
      headers_keys.map((key) => {
        headers_arr.push({
          key,
          value: nodeData.data.config[0].handlerParams[0].headers[key],
        });
      });

      if (nodeData.data.config[0].handlerParams[0].url.includes("?")) {
        const params_string =
          nodeData.data.config[0].handlerParams[0].url.split("?")[1];
        if (params_string.includes("&")) {
          params_string.split("&").map((a) => {
            params_arr.push({
              key: a.split("=")[0],
              value: a.split("=")[1],
            });
          });
        } else {
          params_arr.push({
            key: params_string.split("=")[0],
            value: params_string.split("=")[1],
          });
        }

        setUrl(nodeData.data.config[0].handlerParams[0].url.split("?")[0]);
        setHeaders([...headers_arr]);
        setParams([...params_arr]);
        setEntity(nodeData.data.config[0].handlerParams[0].entity);
        if (
          headers_arr[0]?.value === "multipart/form-data" ||
          headers_arr[0]?.value === "application/x-www-form-urlencoded"
        ) {
          setFields(
            JSON.parse(nodeData.data.config[0].handlerParams[0].body) || []
          );
        }
        if (headers_arr[0]?.value === "application/xml") {
          const cleanedXmlString =
            nodeData.data.config[0].handlerParams[0].body.replace(/'/g, "");
          setBody(cleanedXmlString);
        } else if (headers_arr[0]?.value === "application/octet-stream") {
          setFields(
            JSON.parse(nodeData.data.config[0].handlerParams[0].body) || []
          );
        } else if (headers_arr[0]?.value === "text/plain") {
          setBody(nodeData.data.config[0].handlerParams[0].body);
        } else {
          setBody(nodeData.data.config[0].handlerParams[0].body);
        }
        setMethod(nodeData.data.config[0].handlerParams[0].method);
        if (!headers_keys.length) {
          setHeaders([
            {
              key: "Content-Type",
              value: "application/json",
            },
          ]);
        }
      } else {
        setUrl(nodeData.data.config[0].handlerParams[0].url);
        if (!headers_arr.length) {
          setHeaders([
            {
              key: "Content-Type",
              value: "application/json",
            },
          ]);
        } else {
          setHeaders([...headers_arr]);
        }

        setParams([
          {
            key: "",
            value: "",
          },
        ]);
        setEntity(nodeData.data.config[0].handlerParams[0].entity);
        if (headers_arr[0]?.value === "application/xml") {
          const cleanedXmlString =
            nodeData.data.config[0].handlerParams[0].body.replace(/'/g, "");

          setBody(cleanedXmlString);
        } else if (
          headers_arr[0]?.value === "application/octet-stream" ||
          headers_arr[0]?.value === "multipart/form-data" ||
          headers_arr[0]?.value === "application/x-www-form-urlencoded"
        ) {
          setFields(
            JSON.parse(nodeData.data.config[0].handlerParams[0].body) || []
          );
        } else if (headers_arr[0]?.value === "text/plain") {
          setBody(nodeData.data.config[0].handlerParams[0].body);
        } else {
          setBody(
            JSON.stringify(nodeData.data.config[0].handlerParams[0].body)
          );
        }
        setMethod(nodeData.data.config[0].handlerParams[0].method);
      }
      if (headers_arr[0]?.value === "application/xml") {
        const cleanedXmlString =
          nodeData.data.config[0].handlerParams[0].body.replace(/'/g, "");
        setStringifyBodyValue(cleanedXmlString);
      } else {
        setStringifyBodyValue(
          JSON.stringify(nodeData.data.config[0].handlerParams[0].body)
        );
      }
    }
  }, [nodeData]);

  const onChangeMethod = (value) => {
    if (value === "GET") {
      setBodyValidationError(undefined);
      setBody("{}");
      setStringifyBodyValue("{}");
    }
    setMethod(value);
  };

  const onChangeURL = (url_value) => {
    // if (!helper.isUrlValid(url_value) && url_value.trim().length > 0) {
    //   setUrlError("Please Enter a valid URL");
    // } else {
    //   setUrlError("");
    // }
    setUrl(url_value);
  };

  const onChangeRequestBody = (value) => {
    setStringifyBodyValue(value);
    if (IsJsonString(value)) {
      setBody(value);
      setBodyValidationError(undefined);
    } else if (!Boolean(value.length)) {
      setBodyValidationError(undefined);
    } else {
      setBodyValidationError("Please Enter a valid JSON Body");
    }
  };
  const onChangeRequestBodyXML = (value) => {
    setStringifyBodyValue(value);
    if (value) {
      setBody(value);
      setBodyValidationError(undefined);
    } else if (!Boolean(value.length)) {
      setBodyValidationError(undefined);
    } else {
      setBodyValidationError("Please Enter a valid JSON Body");
    }
  };

  const onAddHeader = () => {
    const headers_to_update = [...headers];
    headers_to_update.push({
      key: "",
      value: "",
    });
    setHeaders([...headers_to_update]);
  };

  const onRemoveHeader = (index) => {
    const headers_to_update = [...headers];
    delete headers_to_update[index];
    setHeaders([...headers_to_update.filter((a) => a)]);
  };

  const onChangeHeader = (header_index, entity, value) => {
    const headers_to_update = [...headers];
    headers_to_update[header_index][entity] = value;
    setHeaders([...headers_to_update]);
  };

  const onAddParam = () => {
    const params_to_update = [...params];
    params_to_update.push({
      key: "",
      value: "",
    });
    setParams([...params_to_update]);
  };

  const onRemoveParam = (index) => {
    const params_to_update = [...params];
    delete params_to_update[index];
    setParams([...params_to_update.filter((a) => a)]);
  };

  const onChangeParam = (param_index, entity, value) => {
    const params_to_update = [...params];
    params_to_update[param_index][entity] = value;
    setParams([...params_to_update]);
  };

  const onTestApi = () => {
    if (bodyValidationError) {
      return false;
    }
    const headers = {};
    const headerKeys = Object.keys(
      nodeData.data.config[0].handlerParams[0].headers
    ).filter((a) => Boolean(a));
    headerKeys.map((a) => {
      headers[a] = nodeData.data.config[0].handlerParams[0].headers[a];
    });
    const config: IConfig = {
      method: nodeData.data.config[0].handlerParams[0].method,
      headers: { ...headers },
      withCredentials: true,
      crossdomain: true,
    };
    if (config.method !== "GET") {
      config.body = JSON.stringify({
        ...nodeData.data.config[0].handlerParams[0].body,
      });
    }

    var testing_url = url;

    if (params[0].key.length > 0 && params[0].value.length > 0) {
      params.map((param, index) => {
        if (index) {
          testing_url = testing_url.concat(`&${param.key}=${param.value}`);
        } else {
          testing_url = testing_url.concat(`?${param.key}=${param.value}`);
        }
      });
    }

    fetch(`${testing_url}`, {
      ...config,
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        setTestResult(JSON.stringify(data));
      })
      .catch((err) => setTestResult(JSON.stringify(err)));
  };

  const IsJsonString = (str) => {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  };
  const handleSave = () => {
    if (urlError) {
      return;
    }

    const nodeDataToEditClone = { ...nodeDataToEdit };

    var urlToUp = `${url}`;

    if (params[0].key.length > 0 && params[0].value.length > 0) {
      params.forEach((param, index) => {
        if (index) {
          urlToUp = urlToUp.concat(`&${param.key}=${param.value}`);
        } else {
          urlToUp = urlToUp.concat(`?${param.key}=${param.value}`);
        }
      });
    }

    nodeDataToEditClone.data.config[0].handlerParams[0].url = urlToUp;

    if (
      headers.length &&
      Boolean(headers[0]?.key) &&
      Boolean(headers[0]?.value)
    ) {
      nodeDataToEditClone.data.config[0].handlerParams[0].headers = {};
      headers.map((header, index) => {
        nodeDataToEditClone.data.config[0].handlerParams[0].headers[
          header.key
        ] = header.value;
      });
    }

    nodeDataToEditClone.data.config[0].name = widgetName;
    nodeDataToEditClone.data.config[0].handlerParams[0].entity = entity;
    nodeDataToEditClone.data.config[0].handlerParams[0].method = method;
    if (
      nodeDataToEditClone.data.config[0].handlerParams[0]?.isCustomResponse !==
      undefined
    ) {
      nodeDataToEditClone.data.config[0].handlerParams[0].isCustomResponse =
        isCustomResponse || false;
    }
    // setEntities({
    //   ...entities,
    //   [entity]: "",
    // });
    if (IsJsonString(body) && headers[0].value === "application/json") {
      nodeDataToEditClone.data.config[0].handlerParams[0].body =
        JSON.parse(body);
    } else if (
      (fields.length > 0 && headers[0].value === "multipart/form-data") ||
      (fields.length > 0 &&
        headers[0].value === "application/x-www-form-urlencoded")
    ) {
      nodeDataToEditClone.data.config[0].handlerParams[0].body =
        JSON.stringify(fields);
    } else if (headers[0].value === "application/xml") {
      nodeDataToEditClone.data.config[0].handlerParams[0].body = body;
    } else if (
      headers[0].value === "application/octet-stream" &&
      fields[0]?.attachment?.url
    ) {
      nodeDataToEditClone.data.config[0].handlerParams[0].body =
        JSON.stringify(fields);
    } else if (headers[0].value === "text/plain") {
      nodeDataToEditClone.data.config[0].handlerParams[0].body = body;
    }

    const newHandlers = responseCodes.map((data, index) => {
      const id = index + 2;
      return {
        connector_id: id.toString(),
        title: data.label,
        redirect: "",
      };
    });

    const codesParams: string[] = [
      ...nodeDataToEditClone.data.config[0].handlerParams[1],
    ].map((code) => {
      return code.value;
    });
    codesParams.splice(0, 2);
    const codes: string[] = responseCodes.map((code) => {
      return code.value;
    });

    const initialHandler =
      [
        nodeDataToEditClone.data.config[0].handlerParams[1][0],
        nodeDataToEditClone.data.config[0].handlerParams[1][1],
      ] || [];

    const RemainingHandelrParams =
      [...nodeDataToEditClone.data.config[0].handlerParams[1]]?.filter(
        (handler) => {
          return codes?.includes(handler?.value);
        }
      ) || [];
    const addedHandelrParams =
      newHandlers?.filter((handler) => {
        return !codesParams?.includes(handler?.title);
      }) || [];
    if (isCustomResponse) {
      initialHandler[0].title = "200-299";
      nodeDataToEditClone.data.config[0].handlerParams[1] = [
        ...initialHandler,
        ...RemainingHandelrParams,
        ...addedHandelrParams,
      ];
    } else {
      initialHandler[0].title = "Success";
      nodeDataToEditClone.data.config[0].handlerParams[1] = [...initialHandler];
    }

    onSaveNodeData(nodeDataToEdit);
  };
  const handleSwitchChange = () => {
    setIsCustomResponse(!isCustomResponse);
  };

  const handleAttachments = async (files: File[] | FileList, index: number) => {
    if (!files) {
      return false;
    }
    const file = files[0];
    const formData = new FormData();
    formData.append("file", file);

    const media = await uploadSendMessageAttachments({
      path: `Bots/${bot.file_name}/Attachments/${file.name}`,
      formData,
    });
    const newField = {
      attachment: {
        url: constant.MEDIA_STORAGE_URL + media.path,
        type: file.type,
        filename: file.name,
      },
      fieldName: fields[index]?.fieldName || "",
      value: "",
      type: "file",
    } as TField;

    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = newField;
      return updatedFields;
    });
  };

  const handleFieldTypeChange = (index: number, type: "text" | "file") => {
    setFields((prev) => {
      const updatedFields = [...prev];
      updatedFields[index].type = type;
      updatedFields[index].value =
        type === "text" ? updatedFields[index].value : "";
      updatedFields[index].attachment =
        type === "file" ? updatedFields[index].attachment : "";
      return updatedFields;
    });
  };

  const handleDeleteField = (index: number) => {
    const filterdAttachments = [...fields];
    filterdAttachments.splice(index, 1);
    setFields(filterdAttachments);
  };
  const onChangeFieldsHandler = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const { name, value } = e.target;

    const updatedFields = [...fields];
    updatedFields[index] = { ...updatedFields[index], [name]: value };
    setFields(updatedFields);
  };
  const [authentication, setAuthentication] = useState({
    type: "No Auth",
    value: "",
  });
  const [basicToken, setBasicToken] = useState({ username: "", password: "" });
  const [bearerToken, setBearerToken] = useState({
    token: "",
    prefix: "Bearer",
  });

  useEffect(() => {
    if (authentication.type === "Basic Auth") {
      const basicAuthToken = Buffer.from(
        `${basicToken.username}:${basicToken.password}`
      ).toString("base64");
      const updatedHeaders = headers.map((value) => {
        if (value.key.toLowerCase().includes("authorization")) {
          return { ...value, value: `Basic ${basicAuthToken}` };
        } else {
          return value;
        }
      });
      setHeaders(updatedHeaders);
    }
  }, [basicToken]);
  useEffect(() => {
    if (authentication.type === "Bearer token") {
      const updatedHeaders = headers.map((value) => {
        if (value.key.toLowerCase().includes("authorization")) {
          return {
            ...value,
            value: `${bearerToken.prefix} ${bearerToken.token}`,
          };
        } else {
          return value;
        }
      });
      setHeaders(updatedHeaders);
    }
  }, [bearerToken]);

  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(expandAce);
    }
  }, [expandAce, onExpandChange]);

  return (
    <>
      {!expandAce ? (
        <div className="flex flex-col gap-4 py-7 min-h-[400px]">
          <Switch
            label={
              isCustomResponse
                ? `Disable ${"custom response"}`
                : `Enable ${"custom response"}`
            }
            name={`enable custom response`}
            checked={isCustomResponse}
            onChange={handleSwitchChange}
          />
          {isCustomResponse ? (
            <MultiSelectInput
              setData={setResponseCodes}
              title="Response codes"
              data={responseCodesJson}
              defualtData={prevResponseCodes}
              // type="email"
            />
          ) : null}

          <Input
            title="Widget Name"
            name="widgetName"
            placeholder="Enter widget name"
            type="text"
            onChange={(event) => {
              setWidgetName(event.target.value);
            }}
            value={widgetName}
          />
          <div>
            <span>Method</span>
            <div className="flex justify-between">
              <div className="flex items-center gap-2">
                <input
                  className="text-primary"
                  checked={method === "GET"}
                  onChange={() => onChangeMethod("GET")}
                  type="radio"
                  name="method"
                  id="get"
                />
                <label htmlFor="get">GET</label>
              </div>
              <div className="flex items-center gap-2">
                <input
                  className="text-primary"
                  checked={method === "POST"}
                  onChange={() => onChangeMethod("POST")}
                  type="radio"
                  name="method"
                  id="post"
                />
                <label htmlFor="post">POST</label>
              </div>
              <div className="flex items-center gap-2">
                <input
                  className="text-primary"
                  checked={method === "PUT"}
                  onChange={() => onChangeMethod("PUT")}
                  type="radio"
                  name="method"
                  id="put"
                />
                <label htmlFor="put">PUT</label>
              </div>
            </div>
          </div>
          <Input
            name="url"
            placeholder="Enter Request URL"
            type="text"
            onChange={(event) => {
              onChangeURL(event.target.value);
            }}
            value={url}
            error={urlError}
          />
          <Tabs defaultValue="params" className="">
            <TabsList
              className={`grid w-full ${
                method === "GET" ? "grid-cols-3" : "grid-cols-4"
              } `}
            >
              <TabsTrigger value="params">Params</TabsTrigger>
              <TabsTrigger value="headers">Headers</TabsTrigger>

              {method !== "GET" && <TabsTrigger value="body">Body</TabsTrigger>}

              <TabsTrigger value="auth">Auth</TabsTrigger>
            </TabsList>
            <TabsContent value="params" className="border-0 p-2">
              <div className="flex flex-col gap-2">
                <Plus
                  size={19}
                  onClick={() => onAddParam()}
                  className="bg-primary rounded-full self-end cursor-pointer"
                />
                <div className="space-y-1 max-h-[200px] overflow-y-auto">
                  {params.map((param, index) => {
                    return (
                      <div className="flex gap-2 items-center" key={index}>
                        <Input
                          name="key"
                          placeholder="Key"
                          type="text"
                          onChange={(event) => {
                            onChangeParam(index, "key", event.target.value);
                          }}
                          value={param.key}
                        />
                        <Input
                          name="value"
                          placeholder="Value"
                          type="text"
                          onChange={(event) => {
                            onChangeParam(index, "value", event.target.value);
                          }}
                          value={param.value}
                        />
                        {params.length > 1 && (
                          <Trash2
                            onClick={() => onRemoveParam(index)}
                            className="text-red-500 rounded-full cursor-pointer w-5 h-5"
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="headers" className="border-0 p-2">
              <div className="flex flex-col gap-2">
                <Plus
                  size={19}
                  onClick={() => onAddHeader()}
                  className="bg-primary rounded-full self-end cursor-pointer"
                />
                <div className="space-y-1 max-h-[200px] overflow-y-auto">
                  {headers.map((header, index) => {
                    return (
                      <div className="flex gap-2 items-center" key={index}>
                        <Input
                          name="key"
                          placeholder="Key"
                          type="text"
                          onChange={(event) => {
                            onChangeHeader(index, "key", event.target.value);
                          }}
                          value={header.key}
                        />
                        <Input
                          name="value"
                          placeholder="Value"
                          type="text"
                          onChange={(event) => {
                            onChangeHeader(index, "value", event.target.value);
                          }}
                          value={header.value}
                        />
                        {headers.length > 1 && (
                          <Trash2
                            onClick={() => onRemoveHeader(index)}
                            className="text-red-500 rounded-full cursor-pointer w-5 h-5"
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="auth" className="border-0 p-2">
              <div className="flex flex-col gap-2">
                <Select
                  value={authentication?.type}
                  onValueChange={(authValue) => {
                    const updatedAuth = authTypeJson?.filter((value) => {
                      return value.type === authValue;
                    });
                    setAuthentication(updatedAuth[0]);
                    if (authValue !== "No Auth") {
                      const filteredHeaders = headers.filter((value) => {
                        return !value.key
                          .toLowerCase()
                          .includes("authorization");
                      });
                      filteredHeaders.push({ key: "Authorization", value: "" });
                      setHeaders(filteredHeaders);
                    } else {
                      const filteredHeaders = headers.filter((value) => {
                        return !value.key
                          .toLowerCase()
                          .includes("authorization");
                      });
                      setHeaders(filteredHeaders);
                    }
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    {authTypeJson.map((type, i) => {
                      return (
                        <SelectItem key={i} value={type.type}>
                          {type.type}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>

                {authentication.type === "Basic Auth" ? (
                  <div className=" w-40 flex flex-col gap-3">
                    <form>
                      <Input
                        name="username"
                        title="Username"
                        value={basicToken.username}
                        onChange={(e) => {
                          setBasicToken((prev) => {
                            const updatedAuth = {
                              ...prev,
                              username: e.target.value,
                            };
                            return updatedAuth;
                          });
                        }}
                      />
                      <Input
                        name="password"
                        title="Password"
                        type="password"
                        value={basicToken.password}
                        onChange={(e) => {
                          setBasicToken((prev) => {
                            const updatedAuth = {
                              ...prev,
                              password: e.target.value,
                            };
                            return updatedAuth;
                          });
                        }}
                      />
                    </form>
                  </div>
                ) : null}
                {authentication.type === "Bearer token" ? (
                  <div className=" w-40 flex flex-col gap-3">
                    <form>
                      <Input
                        name="token"
                        title="Token"
                        value={bearerToken.token}
                        onChange={(e) => {
                          setBearerToken((prev) => {
                            const updatedAuth = {
                              ...prev,
                              token: e.target.value,
                            };
                            return updatedAuth;
                          });
                        }}
                      />
                      <Input
                        name="prefix"
                        title="Prefix"
                        value={bearerToken.prefix}
                        onChange={(e) => {
                          setBearerToken((prev) => {
                            const updatedAuth = {
                              ...prev,
                              prefix: e.target.value,
                            };
                            return updatedAuth;
                          });
                        }}
                      />
                    </form>
                  </div>
                ) : null}
              </div>
            </TabsContent>
            {method !== "GET" && (
              <TabsContent value="body" className="border-0 p-2">
                <Select
                  value={
                    contentTypeJson?.filter((type) => {
                      return type.Content_Type === headers[0]?.value;
                    })[0]?.body
                  }
                  onValueChange={(value) => {
                    setHeaders((prev) => {
                      const prevDate = [...prev];
                      prevDate[0].value = contentTypeJson.filter((type) => {
                        return type.body === value;
                      })[0].Content_Type;
                      return prevDate;
                    });
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    {contentTypeJson.map((type, i) => {
                      return (
                        <SelectItem key={i} value={type.body}>
                          {type.body}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                <div className="space-y-1 max-h-[200px] overflow-y-auto my-1"></div>
                <small className="text-red-500">{bodyValidationError}</small>

                <div className="relative h-[30rem] w-full overflow-y-auto">
                  {headers[0].value === "multipart/form-data" ||
                  headers[0].value === "application/x-www-form-urlencoded" ? (
                    <>
                      <div className="w-full flex justify-end">
                        <Plus
                          size={19}
                          onClick={() =>
                            setFields((prev) => [
                              ...prev,
                              {
                                attachment: "",
                                fieldName: "",
                                value: "",
                                type: "text",
                              },
                            ])
                          }
                          className="bg-primary rounded-full self-end cursor-pointer"
                        />
                      </div>
                      <div className="w-full ">
                        {fields?.map((field, index) => {
                          return (
                            <MultiPartAttachment
                              handleFieldTypeChange={handleFieldTypeChange}
                              key={index}
                              index={index}
                              handleDeleteField={handleDeleteField}
                              onChangeField={onChangeFieldsHandler}
                              field={field}
                              handleAttachments={handleAttachments}
                            />
                          );
                        })}
                      </div>
                    </>
                  ) : headers[0].value === "application/octet-stream" ? (
                    <>
                      <BinaryAttachment
                        onChangeField={onChangeFieldsHandler}
                        field={fields[0]}
                        handleAttachments={handleAttachments}
                      />
                    </>
                  ) : headers[0].value === "text/plain" ? (
                    <>
                      <Textarea
                        placeholder="your plain text ..."
                        onChange={(e) => setBody(e.target.value)}
                        value={body}
                        name="textPlain"
                        title="textPlain"
                        entities={entities}
                        globals={globals}
                      />
                    </>
                  ) : (
                    <>
                      <Expand
                        onClick={() => setExpandAce(!expandAce)}
                        className="absolute right-0 top-0 z-[99999] hover:scale-105"
                      />
                      <CustomEditor
                        headers={headers}
                        onChangeRequestBodyXML={onChangeRequestBodyXML}
                        onChangeRequestBody={onChangeRequestBody}
                        stringifyBodyValue={stringifyBodyValue}
                        entities={entities}
                        globals={globals}
                      />
                    </>
                  )}
                </div>
              </TabsContent>
            )}
          </Tabs>
          {url && (
            <Accordion title="Test API">
              <div className="p-5 flex flex-col max-h-[200px] overflow-y-auto">
                <Button
                  onClick={() => {
                    onTestApi();
                  }}
                  className="mx-auto"
                >
                  Test API
                </Button>
                <JsonFormatter
                  json={testResult}
                  tabWith={4}
                  jsonStyle={JsonStyle}
                />
              </div>
            </Accordion>
          )}
          <label htmlFor="entity">Entity Storage</label>
          <SelectEntity
            entities={entities}
            onValueChange={(value) => {
              setEntity(value);
            }}
            globals={globals}
            entity={entity}
          />
        </div>
      ) : (
        <>
          <div className="relative h-[50rem] w-full my-5">
            <Minimize
              onClick={() => setExpandAce(!expandAce)}
              className="absolute right-0 top-0 z-[99999] hover:scale-105"
            />
            <CustomEditor
              headers={headers}
              onChangeRequestBodyXML={onChangeRequestBodyXML}
              onChangeRequestBody={onChangeRequestBody}
              stringifyBodyValue={stringifyBodyValue}
              entities={entities}
              globals={globals}
            />
          </div>
        </>
      )}
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(JsonAPIForm);
