import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import CustomEditor from "common/ui/customEditor";
import SelectEntity from "../selectEntity";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import { ShieldClose, X } from "lucide-react";
import MultiSelectTextInput from "common/components/MultiSelectInput";
import { Switch } from "common/ui/inputs/switch";
import toast from "react-hot-toast";
import { ReactSelect } from "common/components/ReactSelect";
import { Textarea } from "common/ui/inputs/textarea";

const AgentForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  globals,
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [data, setData] = useState({
    agent_type: "textraction",
    input: {
      type: "user_question",
      source_entity: "",
    },
    custom_prompt: "",
    output_structure: [],
    output_entity: "",
    output_language: "__ar__:##arabic## __en__:##english##",
  });

  const [variables, setVariables] = useState([]);
  const [widgetName, setWidgetName] = useState("");

  // const [isStructured, setIsStructured] = useState(false);

  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      console.log(nodeConfig.handlerParams[0]);
      setNodeDataToEdit(nodeData);
      setData(nodeConfig.handlerParams[0]);
      setWidgetName(nodeConfig.name);
      if (nodeConfig.handlerParams[0].output_structure?.length)
        setVariables(nodeConfig.handlerParams[0].output_structure);
      // setIsStructured(
      //   nodeConfig.handlerParams[0].agent_type === "custom" &&
      //     nodeConfig.handlerParams[0].structured_output?.length
      // );
    }
  }, [nodeData]);

  const addVariable = () => {
    setVariables([...variables, { var_name: "", description: "", type: "" }]);
  };

  const updateVariable = (index, key, value) => {
    const updatedVars = [...variables];
    updatedVars[index][key] = value;
    setVariables(updatedVars);
  };

  const removeVariable = (index) => {
    if (variables.length > 1) {
      setVariables(variables.filter((_, i) => i !== index));
    }
  };

  const handleSave = () => {
    if (data.agent_type === "textraction" && variables.length < 1) {
      toast.error("Please add at least one variable");
      return;
    }
    if (
      variables?.length &&
      variables.some((variable) => {
        return !variable.var_name || !variable.description || !variable.type;
      })
    ) {
      toast.error("Please fill in all fields for all variables");
      return;
    }

    if (data.agent_type === "custom" && !data.custom_prompt) {
      toast.error("Please enter a prompt");
      return;
    }

    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0];

    config.handlerParams[0] = {
      ...data,
      output_structure: variables,
    };
    config.name = widgetName;

    onSaveNodeData({
      ...nodeDataToEditClone,
    });
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <div className="w-full">
          <label htmlFor="agent-type" className="text-xs">
            Agent Type
          </label>
          <Select
            onValueChange={(value) => {
              setData({
                ...data,
                agent_type: value,
                custom_prompt: "",
              });
              if (value === "textraction")
                return setVariables([
                  { var_name: "", description: "", type: "" },
                ]);
              setVariables([]);
            }}
            value={data?.agent_type}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a task" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="textraction">Textraction</SelectItem>
              {/* <SelectItem value="summarization">Summarization</SelectItem> */}
              <SelectItem value="sentiment">Sentiment Analysis</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-full flex gap-2">
          <div className="w-full">
            <label htmlFor="source-type" className="text-xs">
              Source Type
            </label>
            <Select
              onValueChange={(value) => {
                setData((prev) => ({
                  ...prev,
                  input: { ...prev.input, type: value },
                }));
              }}
              value={data?.input?.type}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user_question">User Question</SelectItem>
                <SelectItem value="entity">Entity</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {data?.input?.type === "entity" && (
            <div className="w-full">
              <label htmlFor="source-entity" className="text-xs">
                Source Entity
              </label>
              <SelectEntity
                entity={data?.input?.source_entity}
                onValueChange={(value) => {
                  setData((prev) => ({
                    ...prev,
                    input: { ...prev.input, source_entity: value },
                  }));
                }}
                entities={entities}
                globals={globals}
              />
            </div>
          )}
        </div>

        {data?.agent_type === "custom" && (
          <div>
            <div className="text-sm flex items-center gap-2 mb-2">
              Custom Prompt
            </div>
            <div className="relative h-[9rem] ">
              <CustomEditor
                headers={[{ value: "markdown" }]}
                onChangeMD={(value) => {
                  setData((prev) => ({ ...prev, custom_prompt: value }));
                }}
                stringifyBodyValue={data?.custom_prompt}
                entities={entities}
                globals={globals}
                className="h-[9rem]"
              />
            </div>
          </div>
        )}

        {data?.agent_type === "custom" && (
          <div>
            <label>Output Language</label>
            <Select
              value={data?.output_language}
              onValueChange={(value) => {
                setData((prev) => ({
                  ...prev,
                  output_language: value,
                }));
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Output Language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="arabic">Arabic</SelectItem>
                <SelectItem value="english">English</SelectItem>
                <SelectItem value="__ar__:##arabic## __en__:##english##">
                  Dynamic
                </SelectItem>
              </SelectContent>
            </Select>
            <div className="flex gap-3 mt-3 items-center">
              <Switch
                checked={!!variables?.length}
                name="structured"
                onChange={(e) => {
                  if (!variables.length) {
                    // setIsStructured(e.target.checked);
                    setVariables([{ var_name: "", description: "", type: "" }]);
                  } else {
                    setVariables([]);
                  }
                }}
              />
              <p>
                {variables?.length
                  ? "Structured Output"
                  : "Unstructured Output"}
              </p>
            </div>
          </div>
        )}

        {((data?.agent_type === "custom" && variables?.length) ||
          data?.agent_type === "textraction") && (
          <div className="p-1">
            <h2 className="text-sm mb-4">Structured Output</h2>
            <div className="relative max-h-[400px] overflow-y-auto">
              {variables.map((variable, index) => (
                <div
                  key={index}
                  className="flex relative flex-col gap-2 mb-2 border border-gray-600 rounded p-4"
                >
                  <div className="flex items-center justify-center gap-2">
                    <Input
                      name="var_name"
                      type="text"
                      placeholder="Variable Name"
                      value={variable.var_name}
                      onChange={(e) =>
                        updateVariable(index, "var_name", e.target.value)
                      }
                    />
                    <Select
                      onValueChange={(value) => {
                        updateVariable(index, "type", value);
                      }}
                      value={variable.type}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a type" />
                      </SelectTrigger>
                      <SelectContent>
                        {["string", "integer", "float", "boolean", "array"].map(
                          (item, i) => (
                            <SelectItem key={item} value={item}>
                              {item}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <Textarea
                    name="description"
                    // type="text"
                    placeholder="Description"
                    value={variable.description}
                    onChange={(e) =>
                      updateVariable(index, "description", e.target.value)
                    }
                  />
                  {variables?.length > 1 && (
                    <X
                      className="absolute top-1 right-1 z-50 cursor-pointer text-red-500"
                      size={20}
                      onClick={() => removeVariable(index)}
                    />
                  )}
                </div>
              ))}
            </div>
            <Button onClick={addVariable}>Add Variable</Button>
          </div>
        )}
        <div className="">
          <label className="mb-2 text-xs whitespace-nowrap  " htmlFor="entity">
            Save response to
          </label>
          <SelectEntity
            entities={entities}
            onValueChange={(value) => {
              setData((prev) => ({ ...prev, output_entity: value }));
            }}
            entity={data.output_entity}
            globals={globals}
          />
        </div>
      </div>
      <br />

      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(AgentForm);
