import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { But<PERSON> } from "common/ui/button";
import SelectEntity from "../selectEntity";
import { Editor } from "@monaco-editor/react";
import { Expand, Minimize } from "lucide-react";

const CreateStoreForm: IPromptForm = ({
  nodeData,
  entities,
  onSaveNodeData,
  globals,
  onExpandChange
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [data, setData] = useState("");
  const [stringifyDataValue, setStringifyDataValue] = useState("");
  const [bodyValidationError, setBodyValidationError] = useState<string | undefined>(undefined);
  const [expandAce, setExpandAce] = useState(false);
  const [code, setCode] = useState("");

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0]?.name);
      setEntity(nodeData.data.config[0]?.handlerParams[0].entity);
      setData(JSON.stringify(nodeData.data.config[0]?.handlerParams[0].data));
      setStringifyDataValue(
        JSON.stringify(nodeData.data.config[0]?.handlerParams[0].data, null, 2)
      );
    }
  }, [nodeData]);

  const IsJsonString = (str: string) => {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  };

  const onChangeData = (value: string | undefined) => {
    if (value) {
      setStringifyDataValue(value);
      if (IsJsonString(value)) {
        setData(value);
        setBodyValidationError(undefined);
      } else if (!Boolean(value.length)) {
        setBodyValidationError(undefined);
      } else {
        setBodyValidationError("Please Enter a valid JSON");
      }
    }
  };

  const handleSave = () => {
    if (bodyValidationError) return;
    if (nodeDataToEdit) {
      const nodeDataToEditClone = { ...nodeDataToEdit };
      nodeDataToEditClone.data.config[0].handlerParams[0].data = JSON.parse(data);
    nodeDataToEditClone.data.config[0].handlerParams[0].entity = entity;
    nodeDataToEditClone.data.config[0].handlerParams[0].entity = entity;

      nodeDataToEditClone.data.config[0].handlerParams[0].entity = entity;

    nodeDataToEditClone.data.config[0].name = widgetName;
    nodeDataToEditClone.data.config[0].name = widgetName;

      nodeDataToEditClone.data.config[0].name = widgetName;

      onSaveNodeData(nodeDataToEditClone);
    }
  };
  
  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(expandAce);
    }
  }, [expandAce, onExpandChange]);

  return (
    <>
      {!expandAce ? (
        <div className="flex flex-col gap-4 py-10 min-h-[400px]">
          <Input
            title="Widget Name"
            name="widgetName"
            placeholder="Enter widget name"
            type="text"
            onChange={(event) => {
              setWidgetName(event.target.value);
            }}
            value={widgetName}
          />
          <label htmlFor="entity">Entity Storage</label>
          <SelectEntity
            entity={entity}
            entities={entities}
            globals={globals}
            onValueChange={(value) => {
              setEntity(value);
            }}
          />
          <div>
            Write a valid JSON object for body if you want to change api response
            keys
          </div>
          <small className="text-red-500">{bodyValidationError}</small>

          <div className="relative h-[30rem] w-full">
          <Expand
              onClick={() => setExpandAce(!expandAce)}
              className="absolute right-0 top-0 z-[99999] hover:scale-105"
            />
            <Editor
              defaultLanguage="json"
              defaultValue=""
              theme="vs-dark"
              onChange={onChangeData}
              value={stringifyDataValue}
            />
          </div>
        </div>
      ) : (
        <>
          <div className="relative h-[50rem] w-full my-5">
            <Minimize
              onClick={() => setExpandAce(!expandAce)}
              className="absolute right-0 top-0 z-[99999] hover:scale-105"
            />

            <div className="relative h-[50rem] w-full">
              <Editor
                defaultLanguage="json"
                defaultValue=""
                theme="vs-dark"
                onChange={onChangeData}
                value={stringifyDataValue}
              />
            </div>
          </div>
        </>
      )}
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(CreateStoreForm);
