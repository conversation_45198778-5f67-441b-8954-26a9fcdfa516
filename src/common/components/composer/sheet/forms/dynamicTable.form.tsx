import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { <PERSON><PERSON><PERSON>oot<PERSON> } from "common/ui/sheet";
import { <PERSON><PERSON> } from "common/ui/button";
import SelectEntity from "../selectEntity";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import { Trash } from "lucide-react";
import { uploadBotImages } from "apis/file.api"; 
import { transformLangObjToText, transformTextToLanguageObj } from "helpers/multiLang";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import ImageInput from "common/ui/inputs/imageInput";
import { useLangStore } from "store/language/lang.store";
import generateStorageId from "helpers/generateStorageId";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Switch } from "common/ui/inputs/switch";

const DynamicTableForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  globals,
  onExpandChange
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [columns, setColumns] = useState([{ key: "" }]);
  const [pdfTitle, setPdfTitle] = useState({ ar: "", en: "" }); 
  const [imageToUpload, setImageToUpload] = useState({ ar: "", en: "" });  
  const [saveToEntity, setSaveToEntity] = useState<boolean>(false);
  const [selectedEntity, setSelectedEntity] = useState<string>("");
  const bot = useBotStore((state) => state.bot) as IBot;
  const { lang } = useLangStore();
  
  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setEntity(nodeData.data.config[0].handlerParams[0]);
      const existingColumns = nodeData.data.config[0].handlerParams[1];
      if (typeof existingColumns === "object") {
        setColumns(Object.keys(existingColumns).map((key) => ({ key })));
      }
      setImageToUpload(transformTextToLanguageObj(nodeData.data.config[0]?.handlerParams[2]?.img || ""));
      setPdfTitle(transformTextToLanguageObj(nodeData.data.config[0]?.handlerParams[2]?.pdfTitle || "")); 
      setSaveToEntity(nodeData.data.config[0]?.handlerParams[3]?.saveToEntity || false);
      setSelectedEntity(nodeData.data.config[0]?.handlerParams[3]?.selectedEntity || "");
    }
  }, [nodeData]);
  

  const handleColumnChange = (index, value) => {
    const newColumns = [...columns];
    newColumns[index].key = value;
    setColumns(newColumns);
  };

  const handleAddColumn = () => {
    setColumns([...columns, { key: "" }]);
  };

  const handleRemoveColumn = (index) => {
    const newColumns = columns.filter((_, i) => i !== index);  // remove column at index
    setColumns(newColumns);
  };

  const uploadImg = async (file) => {
    const formData = new FormData();
    formData.append("file", file);
    if (file) {
      const storageId = generateStorageId(5)

      const path = `Bots/${bot.file_name}/pdfTableAssets/${storageId}`
      
      await uploadBotImages({
        path: path + '&image=true',
        formData,
      });
      const imageUrl =
        "https://infotointell.fra1.digitaloceanspaces.com/" + 
        path;

      setImageToUpload((prevImages) => ({
        ...prevImages,
        [lang]: imageUrl,
      }));
    }
  };

  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[0] = entity;

    // convert columns to JSON
    const jsonBodyFromColumns = columns.reduce((acc, { key }) => {
      if (key) acc[key] = "";  // value always empty
      return acc;
    }, {});

    nodeToEditClone.data.config[0].handlerParams[1] = jsonBodyFromColumns;

    // Check if handlerParams[2] exists, if not, initialize it as an empty object
    if (!nodeToEditClone.data.config[0].handlerParams[2]) {
      nodeToEditClone.data.config[0].handlerParams[2] = {
        img: "",
        pdfTitle: "",
      };
    }

    // Check if handlerParams[3] exists, if not, initialize it as an empty object
    if (!nodeToEditClone.data.config[0].handlerParams[3]){
      nodeToEditClone.data.config[0].handlerParams[1] = {
        saveToEntity: false,
        selectedEntity: ""
      }
    }

    nodeToEditClone.data.config[0].handlerParams[2].img = transformLangObjToText(imageToUpload);
    nodeToEditClone.data.config[0].handlerParams[2].pdfTitle = transformLangObjToText(pdfTitle);
    nodeToEditClone.data.config[0].handlerParams[3].saveToEntity = saveToEntity;
    nodeToEditClone.data.config[0].handlerParams[3].selectedEntity = selectedEntity;
    onSaveNodeData(nodeToEditClone);
  };

  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(false);
    }
  }, [onExpandChange]);

  return (
    <>
      <div className="flex flex-col gap-4 py-7 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <span>Select an entity to get data from</span>
        <SelectEntity
          entities={entities}
          onValueChange={(value) => {
            setEntity(value);
          }}
          entity={entity}
          globals={globals}
          width="w-[180px]"
        />
        
        <div className="mb-2 flex items-center gap-4">
          <span>Upload logo for your table&apos;s PDF</span>
          <HelpHoverCard>
            <div>
              Logo and title will be used in the PDF for WhatsApp and Facebook only.
            </div>
          </HelpHoverCard>
        </div>
        <div className="group overflow-hidden rounded border p-3 border-gray-300 [&_summary::-webkit-details-marker]:hidden">
          <div className="flex gap-4">
            <ImageInput
              imgSrc={imageToUpload[lang]}
              uploadImg={uploadImg}
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
        <Input
          title="PDF Title"
          name="pdfTitle"
          placeholder="Enter PDF title"
          type="text"
          onChange={(event) => {
            setPdfTitle((prevTitle) => ({
              ...prevTitle,
              [lang]: event.target.value,
            }));
          }}
          value={pdfTitle[lang]}  
        />
        </div>

        <div className="text-lg font-semibold">Customize Table Column Titles:</div>
        <div className="mb-2 text-sm flex items-center gap-4">
          <span className="text-gray-500">Enter column titles:</span>
          <HelpHoverCard>
            <div>
              Column names are optional and only used on the web. If omitted, the table will use the JSON keys. 
              <br />
              <span className="text-amber-300">Ensure</span> column names are in the same order as the JSON keys.
            </div>
          </HelpHoverCard>
        </div>

        {columns.map((column, index) => (
          <div key={index} className="flex items-center gap-4">
            <Input
              placeholder="Column Name"
              name={`column-${index}`}
              value={column.key}
              onChange={(e) => handleColumnChange(index, e.target.value)}
            />
            <Trash className="cursor-pointer" onClick={() => handleRemoveColumn(index)} />
          </div>
        ))}
        <Button onClick={handleAddColumn} className="mt-2">
          Add Column
        </Button>

        <Switch
          label="Save PDF to an entity?"
          name="saveToEntity"
          checked={saveToEntity}
          onChange={(event) => {
            const isChecked = event.target.checked;
            setSaveToEntity(isChecked);
            if (!isChecked) {
              setSelectedEntity("");
            }
          }}
        />

        {saveToEntity && (
          <SelectEntity
            entities={entities}
            globals={globals}
            entity={selectedEntity}
            onValueChange={setSelectedEntity}
          />
        )}

      </div>

      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save Changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(DynamicTableForm);
