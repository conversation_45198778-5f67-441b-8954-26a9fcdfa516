import React, { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { She<PERSON>Footer } from "common/ui/sheet";
import { But<PERSON> } from "common/ui/button";
import { Textarea } from "common/ui/inputs/textarea";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import { Switch } from "common/ui/inputs/switch";
import SelectEntity from "../selectEntity";
import { transformLangObjToText, transformTextToLanguageObj } from "helpers/multiLang";
import { useLangStore } from "store/language/lang.store";
import CustomEditor from "common/ui/customEditor";
import { ShieldClose } from "lucide-react";

const HtmlForm: IPromptForm = ({ nodeData, entities, globals, onSaveNodeData }) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [htmlCode, setHtml_code] = useState("");
  const [isValid, setIsValid] = useState<boolean>(true);
  const [isPdf, setIsPdf] = useState<boolean>(true);
  const [pdfTitle, setPdfTitle] = useState({ ar: "", en: "" });
  const [saveToEntity, setSaveToEntity] = useState<boolean>(false);
  const [selectedEntity, setSelectedEntity] = useState<string>("");
  const { lang } = useLangStore();



  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0]?.name);
      setHtml_code(nodeData.data.config[0]?.handlerParams[0].html_code);
      setIsPdf(nodeData.data.config[0]?.handlerParams[1]?.isPdf || false);
      setPdfTitle(transformTextToLanguageObj(nodeData.data.config[0]?.handlerParams[1]?.pdfTitle || ''));
      setSaveToEntity(nodeData.data.config[0]?.handlerParams[2]?.saveToEntity || false);
      setSelectedEntity(nodeData.data.config[0]?.handlerParams[2]?.selectedEntity || "");
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };

    nodeDataToEditClone.data.config[0].handlerParams[0].html_code = htmlCode;

    if (!nodeDataToEditClone.data.config[0].handlerParams[1]) {
      nodeDataToEditClone.data.config[0].handlerParams[1] = {
        isPDF: false,
        PdfTitle: {},
      }
    }

    if (!nodeDataToEditClone.data.config[0].handlerParams[2]) {
      nodeDataToEditClone.data.config[0].handlerParams[2] = {
        saveToEntity: false,
        selectedEntity: ""
      }
    }

    nodeDataToEditClone.data.config[0].handlerParams[1].isPdf = isPdf;
    nodeDataToEditClone.data.config[0].handlerParams[1].pdfTitle = isPdf ? transformLangObjToText(pdfTitle) : "";
    nodeDataToEditClone.data.config[0].handlerParams[2].saveToEntity = saveToEntity;
    nodeDataToEditClone.data.config[0].handlerParams[2].selectedEntity = selectedEntity;

    onSaveNodeData(nodeDataToEditClone);
  };

  const preContent = `
<p style="color:blue">
This will be 
displayed to the user
as a message from the bot.
</p>
  `;

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">

        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />

        <div className="flex items-center gap-2">
          
          <Switch
            label="Generate HTML as PDF"
            name="isPdf"
            checked={isPdf}
            onChange={(event) => {
              setIsPdf(event.target.checked);
              if (!event.target.checked) {
                setPdfTitle({ ar: "", en: "" }); // clear pdf title if not a PDF
              }
            }}
          />
          <HelpHoverCard>
            Enabling this option will generate a PDF of the HTML content &#40;default is image&#41;. This feature is only for WhatsApp and Facebook.
          </HelpHoverCard>

        </div>
        {isPdf && (
          <div className="flex flex-col gap-2">
            <Input
              title="PDF Title"
              name="pdfTitle"
              placeholder="Enter PDF title"
              type="text"
              onChange={(event) => {
                setPdfTitle((prevTitle) => ({
                  ...prevTitle,
                  [lang]: event.target.value,
                }));
              }}
              value={pdfTitle[lang]}
            />
          </div>
        )}

        <div className="flex items-center gap-2">
          <span>Html Code</span>
          <HelpHoverCard>Place your <span className="text-amber-300">HTML</span> code here, and the content will be rendered for the user. You can only use internal CSS to style your content, as shown here:
            <pre> {preContent} </pre>
          </HelpHoverCard>
        </div>



        <div className="relative h-[20rem] w-full">
              <CustomEditor
              headers={[{value:"html"}]}
              onChangeHTML={(value) => setHtml_code(value)}
              stringifyBodyValue={htmlCode}
              entities={entities}
              globals={globals}
              isMin={true}
            />
          </div>

        {isPdf && 
        <>
            <Switch
            label={`Save generated ${isPdf ? 'PDF' : 'image'} to an entity?`}
            name="saveToEntity"
            checked={saveToEntity}
            onChange={(event) => {
              const isChecked = event.target.checked;
              setSaveToEntity(isChecked);
              if (!isChecked) {
                setSelectedEntity("");
              }
            }}
          />

          {saveToEntity && (
            <div className="relative">
            <SelectEntity
              entities={entities}
              globals={globals}
              entity={selectedEntity}
              onValueChange={setSelectedEntity}
            />
            {!selectedEntity && (
              <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                <ShieldClose size={12} />
                {"Please select an entity"}
              </span>
            )}
            </div>
          )}
          </>
    }
      </div>

      <SheetFooter>
        <Button onClick={handleSave} type="submit" disabled={!isValid}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(HtmlForm);
