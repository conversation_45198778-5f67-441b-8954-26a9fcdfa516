import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { memo, useEffect, useRef, useState } from "react";
import { Node } from "reactflow";
import { IPromptForm } from "types/composer.types";
import useMultiLanguage from "common/hooks/useMultiLanguage";
import SelectEntity from "../selectEntity";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

import { TAttachmentsConditions } from "constants/attachments.constants";

const RequestAttachmentSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  setEntities,
  globals,
}) => {
  const { value, setValue, result } = useMultiLanguage(
    nodeData?.data?.config[0]?.handlerParams[0] || ""
  );
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);

  const [attachmentsConditions, setAttachmentsConditions] =
    useState<TAttachmentsConditions>({
      size: 1,
      attachment_type: "file",
      entity: "",
      saveAs: "",
    });

  const [widgetName, setWidgetName] = useState("");

  const voiceDataRef = useRef({
    voice_path_male: "",
    voice_path_female: "",
  });
  
  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      const data = nodeData?.data?.config[1]?.handlerParams[0];
      setAttachmentsConditions(data as any);
      setWidgetName(nodeData?.data?.config[0]?.name);
      voiceDataRef.current = nodeData?.data?.config[0]?.handlerParams[2];
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = result; 
    nodeDataToEditClone.data.config[0].handlerParams[1] =
      "attachment-" + attachmentsConditions.attachment_type; 
    nodeDataToEditClone.data.config[1].handlerParams[0] = attachmentsConditions;
    nodeDataToEditClone.data.config[0].name = widgetName;
    nodeDataToEditClone.data.config[0].handlerParams[2] = {voice_path_male:voiceDataRef.current?.voice_path_male || '',voice_path_female:voiceDataRef.current?.voice_path_female || ''};
    onSaveNodeData(nodeDataToEditClone);
  };

  const handleUpdateVoice = (data: TVoiceBody) => {
    const updatedVoices = data;
    delete updatedVoices.bot_id;
    delete updatedVoices.user_id;
    voiceDataRef.current = {...voiceDataRef.current,...updatedVoices};
  };

  const [showModal, setShowModal] = useState(false);
  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <VoiceModalWrapper
          handleSave={handleSave}
          handleUpdateVoice={handleUpdateVoice}
          setShowModal={setShowModal}
          showModal={showModal}
          text={value}
          voice_path_female={nodeData?.data?.config[0]?.handlerParams[2]?.voice_path_female || ""}
          voice_path_male={nodeData?.data?.config[0]?.handlerParams[2]?.voice_path_male || ""}
        />

        <div className="space-y-1">
          <label htmlFor="text" className="flex gap-5 items-end">
            Text
            <small className="text-white/50">
              - Characters Limit: WhatsApp: 4096, Facebook: 2000
            </small>
          </label>
          <Textarea
            onChange={(event) => setValue(event.target.value)}
            value={value}
            rows={4}
            id="text"
            className="h-full"
            entities={entities}
            globals={globals}
          />
          <small className="float-right text-white/50">
            {value?.length || 0} characters
          </small>
        </div>
        <div className="grid grid-cols-3 items-end gap-2">
          <h1 className="col-span-3 font-bold">Attachment info</h1>
          <hr className="col-span-3" />
          <div className="flex items-center justify-start gap-2 col-span-3">
            <div className="w-full">
              <label htmlFor="entity">Entity Storage</label>
              <SelectEntity
                entities={entities}
                onValueChange={(value) => {
                  setAttachmentsConditions({
                    ...attachmentsConditions,
                    entity: value,
                  });
                }}
                entity={attachmentsConditions.entity}
                globals={globals}
              />
            </div>
          </div>

          <div className="col-span-3">
            <label htmlFor="check_at" className="text-md whitespace-nowrap">
              Attachment Type
            </label>
            <Select
              name="check_at"
              key={"storageType"}
              value={attachmentsConditions.attachment_type}
              onValueChange={(value: "file" | "image") => {
                setAttachmentsConditions({
                  ...attachmentsConditions,
                  attachment_type: value,
                });
              }}
            >
              <SelectTrigger className="">
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="image">Image</SelectItem>
                <SelectItem value="file">File</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {/* <div className="col-span-3">
            <label htmlFor="check_at" className="text-md whitespace-nowrap">
              Save attachment as
            </label>
            <Select
              name="check_at"
              key={"storageType"}
              value={attachmentsConditions.saveAs}
              onValueChange={(value: "file" | "image") => {
                setAttachmentsConditions({
                  ...attachmentsConditions,
                  saveAs: value,
                });
              }}
            >
              <SelectTrigger className="">
                <SelectValue placeholder="Select a one" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="url">URL</SelectItem>
                <SelectItem value="blob">Blob</SelectItem>
              </SelectContent>
            </Select>
          </div> */}
          {/* <Input
            value={attachmentsConditions.size || 1}
            onChange={(e) => {
              let number = 1;
              try {
                if (!isNaN(e.target.value)) {
                  number = Number(e.target.value);
                }
              } catch (error) {}
              if (number < 1 || number > 20) return;
              setAttachmentsConditions({
                ...attachmentsConditions,
                size: number,
              });
            }}
            className="col-span-3 w-full"
            type="number"
            name="size"
            title={"Attachment Max Size (MB)"}
          /> */}
        </div>
      </div>

      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(RequestAttachmentSheet);
