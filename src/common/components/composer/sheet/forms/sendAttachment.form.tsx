import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { uploadSendMessageAttachments } from "apis/file.api";
import { FilesInput } from "views/unstructured/components";
import AttachmentIcon from "common/components/attachmentIcon";

import {
  setValuesBasedOnLanguage,
  transformLangObjToText,
  transformTextToLanguageObj,
} from "helpers/multiLang";
import { useLangStore } from "store/language/lang.store";
import generateStorageId from "helpers/generateStorageId";

type IntegrationType = "sendGrid integration" | "SMTP integration";

const SendAttachmentSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);

  const { lang } = useLangStore();
  const [widgetName, setWidgetName] = useState("");
  const [attachments, setAttachments] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const bot = useBotStore((state) => state.bot) as IBot;

  useEffect(() => {
    if (nodeData?.data?.config) {
      setAttachments(nodeData.data.config[0].handlerParams[0].attachments);

      setNodeDataToEdit(nodeData);

      setWidgetName(nodeData?.data?.config[0]?.name);
    }
  }, [nodeData]);

  const handleSave = async () => {
    setIsLoading(true);

    const savedAttachments = await Promise.all(
      attachments.map(async (image) => {
        try {
          const result = await uploadSendMessageAttachments(image);
          return result;
        } catch (error) {
          console.error("Image upload failed:", error);
        }
      })
    );

    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
      ? nodeDataToEditClone.data.config[0]
      : nodeDataToEditClone.data.config;

    nodeDataToEditClone.data.config[0].handlerParams[0].attachments =
      savedAttachments;

    config.name = widgetName;

    onSaveNodeData({
      ...nodeDataToEditClone,
    });
    setIsLoading(false);
  };

  const handleAttachments = (files) => {
    if (!files) {
      return false;
    }

    const filesArr = [...files];

    const attachmentsArr = filesArr.map((file) => {
      const formData = new FormData();
      formData.append("file", file);
      return {
        path: `Bots/${bot.file_name}/sendMessageAttachments/${
          generateStorageId(5) + file.name
        }`,
        formData,
        filename: file.name,
        type: file.type,
        disposition: "attachment",
      };
    });
    setAttachments((prev) => [...prev, ...attachmentsArr]);
  };

  const handleDeleteAttachment = (index) => {
    const filterdAttachments = [...attachments];
    filterdAttachments.splice(index, 1);
    setAttachments(filterdAttachments);
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <FilesInput
          onChange={handleAttachments}
          ACCEPTED_FILE_TYPES={[
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/bmp",
          ]}
        />

        <div className="w-96 ">
          {attachments.map((attachment, index) => {
            return (
              <AttachmentIcon
                key={index}
                attachment={attachment}
                index={index}
                handleDeleteAttachment={handleDeleteAttachment}
              />
            );
          })}
        </div>
      </div>

      <SheetFooter>
        <Button onClick={handleSave} type="submit" loading={isLoading}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(SendAttachmentSheet);
