import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useDialogStore from "store/dialog/dialog.store";
import { IDialog } from "store/dialog/dialog.types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { SheetFooter } from "common/ui/sheet";
import { Button } from "common/ui/button";

const TriggerDialogForm: IPromptForm = ({ nodeData, onSaveNodeData }) => {
  const dialogs = useDialogStore((state) => state.dialogs);

  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [redirectTo, setRedirectTo] = useState({
    dialog_id: 0,
    dialog_name: "",
    path: "",
  });
  const [widgetName, setWidgetName] = useState("");

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setRedirectTo(nodeData.data.config[0].handlerParams[0]);
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = redirectTo;
    nodeDataToEditClone.data.config[0].name = widgetName;
    onSaveNodeData(nodeDataToEditClone);
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-7 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <Select
          value={redirectTo?.dialog_id?.toString()}
          onValueChange={(value) => {
            const dialog = dialogs.find(
              (dialog) => dialog.dialog_id === +value
            );
            setRedirectTo({
              dialog_id: +value,
              dialog_name: dialog?.dialog_name,
              path: dialog?.url,
            });
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select a dialog" />
          </SelectTrigger>
          <SelectContent>
            {dialogs?.map((dialog, i) => {
              return (
                <SelectItem key={i} value={dialog.dialog_id.toString()}>
                  {dialog.dialog_name || "Untitled"}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(TriggerDialogForm);
