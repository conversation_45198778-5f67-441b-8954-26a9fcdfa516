import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { But<PERSON> } from "common/ui/button";
import SelectEntity from "../selectEntity";
import { useLangStore } from "store/language/lang.store";
import { 
  setValuesBasedOnLanguage, 
  transformLangObjToText, 
  transformTextToLanguageObj 
} from "helpers/multiLang";
import HelpHoverCard from "common/components/cards/helpHoverCard";

const DynamicSuggestionsForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  globals
}) => {
  const { lang } = useLangStore();
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [entityToStore, setEntityToStore] = useState("");
  const [objectToStore, setObjectToStore] =  useState("");
  const [pagiNum, setPagiNum] = useState("")
  const [mapper, setMapper] = useState({
    objectPropertyLabel: { en: "", ar: "" },
    objectPropertyPostback: { en: "", ar: "" }
  });

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setMapper({
        objectPropertyLabel: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[0]?.objectPropertyLabel || ""
        ),
        objectPropertyPostback: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[0]?.objectPropertyPostback || ""
          )
      });
      setEntityToStore(nodeData.data.config[1].recieving);
      setObjectToStore(nodeData.data.config[0].handlerParams[0].objectEntity)
      setEntity(nodeData.data.config[0].handlerParams[0].entity);
      setPagiNum(nodeData.data.config[0]?.handlerParams[1])
    }
  }, [nodeData]);

  const handleSave = () => {
    if (!nodeDataToEdit) return;

    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[1] = pagiNum
    nodeToEditClone.data.config[0].handlerParams[0].entity = entity;
    nodeToEditClone.data.config[0].handlerParams[0].objectPropertyLabel = transformLangObjToText(
      mapper.objectPropertyLabel );
    nodeToEditClone.data.config[0].handlerParams[0].objectPropertyPostback = transformLangObjToText(
      mapper.objectPropertyPostback );
    nodeToEditClone.data.config[1].recieving = entityToStore;
    nodeToEditClone.data.config[0].handlerParams[0].objectEntity = objectToStore;
    onSaveNodeData(nodeToEditClone);
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <span>Select an entity to get data from</span>
        <SelectEntity 
        entity={entity}
        entities={entities}
        globals={globals}
        onValueChange={(value) => {
          setEntity(value);
        }}
        width="w-[180px]"
        />

        <hr className="text-white/25" />

        <div className="flex items-center gap-2">
          <span>Object Property Label Key</span>
          <HelpHoverCard>Enter the key from API response that corresponds to the label of the button.</HelpHoverCard>
        </div>
        <Input
          name="objectPropertyLabel"
          placeholder="Label key from API response"
          type="text"
          value={mapper.objectPropertyLabel[lang]}
          onChange={(event) => {
            setMapper({ ...mapper, objectPropertyLabel: setValuesBasedOnLanguage(event.target.value, mapper.objectPropertyLabel , lang) });
          }}
        />
        
        <div className="flex items-center gap-2">
          <span>Object Property Postback Key</span>
          <HelpHoverCard>Enter the key from API response that corresponds to the postback value of the button.</HelpHoverCard>
        </div>
        <Input
          name="objectPropertyPostback"
          placeholder="Postback key from API response"
          type="text"
          value={mapper.objectPropertyPostback[lang]}
          onChange={(event) => {
            setMapper({ ...mapper, objectPropertyPostback: setValuesBasedOnLanguage(event.target.value, mapper.objectPropertyPostback, lang) });
          }}
        />
        
        <div className="flex items-center gap-2">
          <label htmlFor="pagiNum"><span className="text-gray-300">(Optional)</span>{" "}Set Button Limit for Web Channel:</label>
          <HelpHoverCard>Set the maximum number of buttons to be displayed at once on the web channel.</HelpHoverCard>
        </div>
        <Input
          name="pagiNum"
          placeholder="Enter the number of buttons"
          type="number"
          onChange={(event) => {
            setPagiNum(event.target.value);
          }}
          value={pagiNum}
        />

        <hr className="text-white/25" />
        <div className="flex items-center gap-2">
          <label htmlFor="entity">Store Entity In:</label>
          <HelpHoverCard>Select where to store user{"'"}s selection postback value.</HelpHoverCard>
        </div>
        <SelectEntity 
          entity={entityToStore}
          entities={entities}
          globals={globals}
          onValueChange={(value) => {
            setEntityToStore(value);
          }}
        />

        <div className="flex items-center gap-2">
          <label htmlFor="storedObject"><span className="text-gray-300">(Optional)</span> Store Object Entity In:</label>
          <HelpHoverCard>Specify where to store the complete object of user{"'"}s selction entity, if needed.</HelpHoverCard>
        </div>
        <SelectEntity 
          entity={objectToStore}
          entities={entities}
          globals={globals}
          onValueChange={(value) => {
            setObjectToStore(value);
          }}
        />
        
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save Changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(DynamicSuggestionsForm);
