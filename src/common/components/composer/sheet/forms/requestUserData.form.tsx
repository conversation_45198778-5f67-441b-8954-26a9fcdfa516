import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON>Footer } from "common/ui/sheet";
import generateStorageId from "helpers/generateStorageId";
import { FC, memo, useEffect, useMemo, useRef, useState } from "react";
import { Node } from "reactflow";
import Conditions from "../conditions";
import { IPromptForm } from "types/composer.types";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel
} from "common/ui/inputs/select";
import useMultiLanguage from "common/hooks/useMultiLanguage";
import SelectEntity from "../selectEntity";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";

const RequestUserDataSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  setEntities,
  globals
}) => {
  const { value, setValue, result } = useMultiLanguage(
    nodeData?.data?.config[0]?.handlerParams[0] || ""
  );
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [text, setText] = useState("");
  const [conditions, setConditions] = useState([]);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [oldEntites, setOldEntiteis] = useState("");
 const voiceDataRef = useRef({
    voice_path_male: "",
    voice_path_female: "",
  });
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setText(nodeData?.data?.config[0].handlerParams[0]);
      setConditions(nodeData?.data?.config[1]?.handlerParams[0]);
      setWidgetName(nodeData?.data?.config[0]?.name);
      setEntity(nodeData?.data?.config[1]?.recieving);
      setOldEntiteis(nodeData?.data?.config[1]?.recieving);
      voiceDataRef.current = nodeData?.data?.config[0]?.handlerParams[2];

    }
  }, [nodeData || oldEntites]);

  const addCondition = () => {
    const conditionsClone = [...conditions];
    conditionsClone.push({
      connector_id: generateStorageId(),
      criteria: "isEmail",
      title: "email validation",
      redirect: "",
      label: "email validation",
    });
    setConditions(conditionsClone);
  };

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = result;
    nodeDataToEditClone.data.config[1].handlerParams[0] = conditions.map(
      (a) => a
    );
    nodeDataToEditClone.data.config[1].recieving = entity;
    nodeDataToEditClone.data.config[0].name = widgetName;

    nodeDataToEditClone.data.config[0].handlerParams[2] = {voice_path_male:voiceDataRef.current.voice_path_male,voice_path_female:voiceDataRef.current.voice_path_female};

    // if (oldEntites.length != 0) {
    //   entities[entity] = entities[oldEntites];
    //   delete entities[oldEntites];
    //   // delete Object.assign(obj, {newKey: obj.oldKey})['oldKey'];
    // } else {
    //   setEntities({
    //     ...entities,
    //     [entity]: "string",
    //   });
    // }
    // setOldEntiteis(entity);
    onSaveNodeData(nodeDataToEditClone);
  };

  const handleUpdateVoice = (data: TVoiceBody) => {
    const updatedVoices = data;
    delete updatedVoices.bot_id;
    delete updatedVoices.user_id;
    voiceDataRef.current = {...voiceDataRef.current,...updatedVoices};
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div className="space-y-1">

        <VoiceModalWrapper
       handleSave={handleSave}
       handleUpdateVoice={handleUpdateVoice}
       setShowModal={setShowModal}
       showModal={showModal}
       text={value}
       voice_path_female={nodeData?.data?.config[0]?.handlerParams[2]?.voice_path_female || ''}     
       voice_path_male={nodeData?.data?.config[0]?.handlerParams[2]?.voice_path_male || ''} 
         />

          <label htmlFor="text" className="flex gap-5 items-end">Text
          <small className="text-white/50">
            - Characters Limit: WhatsApp: 4096, Facebook: 2000

          </small>
          </label>
          <Textarea
            onChange={(event) => setValue(event.target.value)}
            value={value}
            rows={4}
            id="text"
            className="h-full"
            entities={entities}
            globals={globals}
          />
          <small className="float-right text-white/50">
            {value?.length || 0} characters
          </small>
        </div>
        <label htmlFor="entity">Entity Storage</label>
        <SelectEntity 
          entities={entities}
          onValueChange={(value) => {
            setEntity(value);
          }}
          entity={entity}
          globals={globals}
        />
        <Conditions
          addCondition={addCondition}
          conditions={conditions}
          setConditions={setConditions}
        />
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(RequestUserDataSheet);
