import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useState } from "react";

import { Node } from "reactflow";
import HelpHoverCard from "common/components/cards/helpHoverCard";

const CheckpointForm: IPromptForm = ({ nodeData, onSaveNodeData, dialog }) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [tag, setTag] = useState("");
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (nodeData?.data?.config) {
      setTag(nodeData.data.config[0]?.handlerParams[0]);
      setNodeDataToEdit(nodeData);
    }
  }, [nodeData]);

  const handleSave = () => {
    setLoading(true);
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = tag;
    nodeDataToEditClone.data.config[0].handlerParams[1] = dialog?.dialog_id;
    onSaveNodeData(nodeDataToEditClone);
    setLoading(false);
  };
  return (
    <>
      <div className="space-y-3 mb-5">
        <div className="flex items-center gap-2 mt-5">
          <span>Tag <span className="text-xs text-white/50">- Choose the name to mark the checkpoint. This will be used in the reports to identify users progress.</span></span>
          <HelpHoverCard>Your checkpoint&apos;s tag can be customized according to your preference. 
            It can range from a simple <span className="text-amber-300">&quot;tag&quot;</span> to a more detailed message per your liking. Additionally, you may include local and global entities.
          </HelpHoverCard>
        </div>
        <Input
          name="tag"
          value={tag}
          onChange={(e) => {
            setTag(e.target.value);
          }}
        />
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit" loading={loading}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(CheckpointForm);
