import { But<PERSON> } from "common/ui/button";

import { SheetFooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useState } from "react";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import { Node } from "reactflow";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Edit, Plus, X } from "lucide-react";
import { Editor } from "@monaco-editor/react"
import CustomEditor from "common/ui/customEditor";

const OperatorToggle = (props: {
  i: number;
  onClick: (type: "&&" | "||") => void;
  isAnd: boolean;
}) => {
  const { i, onClick, isAnd } = props;

  return (
    <div key={i} className="w-full flex justify-center gap-3">
      <Button
        onClick={() => {
          onClick("&&");
        }}
        variant={isAnd ? "default" : "ghost"}
        size="sm"
      >
        And
      </Button>
      <Button
        onClick={() => {
          onClick("||");
        }}
        variant={!isAnd ? "default" : "ghost"}
        size="sm"
      >
        Or
      </Button>
    </div>
  );
};
const ConditionsForm: IPromptForm = ({
  nodeData,
  entities,
  globals,
  onSaveNodeData,
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [conditionsToEdit, setConditionsToEdit] = useState([]);
  const [loading, setLoading] = useState(false);
  const [headers, setHeaders] = useState([
    {
      key: "Content-Type",
      value: "javascript",
    }
  ]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setConditionsToEdit(nodeData.data.config[0].handlerParams[0]);
    }
  }, [nodeData]);

  const handleEditorChange = (value: string | undefined, index: number) => {
    const condArrClone = [...conditionsToEdit];
    if (value !== undefined) {
      condArrClone[index][1] = value;
      setConditionsToEdit(condArrClone);
    }
  };
  
  const handleSave = () => {
    setLoading(true);
    const nodeDataClone = { ...nodeDataToEdit };
    nodeDataClone.data.config[0].handlerParams[0] = conditionsToEdit;
    onSaveNodeData(nodeDataClone);
    setLoading(false);
  };

  return (
    <>
      <div className="space-y-3 mb-5">
        <div className="mt-5">Add as much conditions as you like.</div>
        {conditionsToEdit.map(([key, val], i) => {
          switch (key) {
            case "channel":
              return (
                <div key={i}>
                  <div className="flex w-full justify-between items-center my-3">
                    <span>Validate based on channel</span>

                    <Edit
                      className="cursor-pointer"
                      onClick={() => {
                        const condArrClone = [...conditionsToEdit];
                        condArrClone[i] = ["", ""];
                        setConditionsToEdit(condArrClone);
                      }}
                      size={15}
                    />
                  </div>
                  <div className="flex items-center gap-3 ">
                    <div>Is</div>
                    <div className="flex-1">
                      <Select
                        value={val || undefined}
                        onValueChange={(value) => {
                          const condArrClone = [...conditionsToEdit];
                          condArrClone[i][1] = value;
                          setConditionsToEdit(condArrClone);
                        }}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Choose channel" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={'"${channel}" === "web"'}>
                            Web
                          </SelectItem>
                          <SelectItem value={'"${channel}" === "whatsapp"'}>
                            WhatsApp
                          </SelectItem>
                          <SelectItem value={'"${channel}" === "facebook"'}>
                            facebook
                          </SelectItem>
                          <SelectItem value={'"${channel}" === "slack"'}>
                            slack
                          </SelectItem>
                          <SelectItem value={'"${channel}" === "telegram"'}>
                            telegram
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>channel?</div>
                  </div>
                  {conditionsToEdit?.length > 1 &&
                    i !== conditionsToEdit?.length - 1 && (
                      <hr className="my-5" />
                    )}
                </div>
              );
            case "operator":
              return (
                <div key={i}>
                  <div className="flex w-full justify-between items-center my-3">
                    <span>Choose an operator</span>
                    <X
                      className="cursor-pointer text-red-500"
                      onClick={() => {
                        const condArrClone = [...conditionsToEdit];
                        condArrClone.splice(i, 2);
                        setConditionsToEdit(condArrClone);
                      }}
                    />
                  </div>
                  <OperatorToggle
                    onClick={(op) => {
                      const condArrClone = [...conditionsToEdit];
                      condArrClone[i][1] = op;
                      setConditionsToEdit(condArrClone);
                    }}
                    i={i}
                    isAnd={conditionsToEdit[i][1] === "&&"}
                  />
                </div>
              );
            case "code":
              return (
                <div key={i}>
                  <div className="flex w-full justify-between items-center my-3">
                    <div className="flex items-center gap-3">
                      <span>Wtite your own condition</span>
                      <HelpHoverCard>
                        <div>
                          Type some logic to figure out what {`it's`} boolean
                          value is.
                          <br />
                          <small className="leading-3 tracking-tighter text-amber-500">
                            Example: value1 {`(=== , !== , > , <)`} value2
                          </small>
                          <br />
                          You can even use JavaScript or the entities.
                        </div>

                        <small className="leading-3 tracking-tighter text-amber-500">
                          Example: ${"{email}"}.includes{`("gmail")`}
                        </small>
                      </HelpHoverCard>
                    </div>
                    <Edit
                      className="cursor-pointer"
                      onClick={() => {
                        const condArrClone = [...conditionsToEdit];
                        condArrClone[i] = ["", ""];
                        setConditionsToEdit(condArrClone);
                      }}
                      size={15}
                    />
                  </div>
                  <div className="flex items-center gap-3 ">
                    <div className="relative h-[30rem] w-full">
                      <CustomEditor
                        headers={headers}
                        onChangeJS={(value) => handleEditorChange(value, i)}
                        stringifyBodyValue={val}
                        entities={entities}
                        globals={globals}
                        isConditions={true}
                      />
                    </div>
                  </div>
                  {conditionsToEdit?.length > 1 &&
                    i !== conditionsToEdit?.length - 1 && (
                      <hr className="my-5" />
                    )}
                </div>
              );
            default:
              return (
                <div key={i}>
                  <div className="flex w-full justify-between items-center my-3">
                    <span>Choose A Condition</span>
                  </div>

                  <Select
                    key={i}
                    value={val || undefined}
                    onValueChange={(value) => {
                      const condArrClone = [...conditionsToEdit];
                      condArrClone[i][0] = value;
                      setConditionsToEdit(condArrClone);
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue
                        defaultChecked={true}
                        placeholder="Choose Condition"
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={"channel"}>
                        validate based on current channel
                      </SelectItem>
                      <SelectItem value={"code"}>
                        write you own condition
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {conditionsToEdit?.length > 1 &&
                    i !== conditionsToEdit?.length - 1 && (
                      <hr className="my-5" />
                    )}
                </div>
              );
          }
        })}

        <div className="w-full flex justify-center">
          <Button
            size="sm"
            onClick={() => {
              const condArrClone = [...conditionsToEdit];
              condArrClone.push(["operator", "&&"]);
              condArrClone.push(["", ""]);
              setConditionsToEdit(condArrClone);
            }}
          >
            <Plus />
          </Button>
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit" loading={loading}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(ConditionsForm);
