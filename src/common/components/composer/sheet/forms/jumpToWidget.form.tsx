import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { <PERSON><PERSON><PERSON>oot<PERSON> } from "common/ui/sheet";
import { Button } from "common/ui/button";
import { Node } from "reactflow";

const JumpToWidgetForm: IPromptForm = ({ nodeData, onSaveNodeData, nodes }) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [redirectDestination, setRedirectDestination] = useState("");
  const [widgetName, setWidgetName] = useState("");

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setRedirectDestination(
        Boolean(nodeData.data.config[0].handlerParams[0])
          ? nodeData.data.config[0].handlerParams[0]
          : nodes?.filter(
                (a) =>
                  a.data?.config?.length && Boolean(a.data?.config[0]?.name)
              ).length
            ? nodes?.filter(
                (a) =>
                  a.data?.config?.length && Boolean(a.data?.config[0]?.name)
              )[0].data?.config[0]?.name
            : ""
      );
      setWidgetName(nodeData.data.config[0].name);
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = redirectDestination;
    nodeDataToEditClone.data.config[0].name = widgetName;
    onSaveNodeData(nodeDataToEditClone);
  };
  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div>
          <p>Redirect to</p>
          <small className="text-white/50">
            Make sure to name the widget you want to redirect to
          </small>
        </div>

        <Select
          value={redirectDestination}
          onValueChange={(value) => {
            setRedirectDestination(value);
          }}
          disabled={
            !nodes?.filter(
              (a) => a.data?.config?.length && Boolean(a.data?.config[0]?.name)
            ).length
          }
        >
          <SelectTrigger className="w-[180px] ">
            <SelectValue placeholder="Select a widget" />
          </SelectTrigger>

          <SelectContent className="max-h-[500px]  ">
            {nodes
              ?.filter(
                (a) =>
                  a.data?.config?.length &&
                  Boolean(a.data?.config[0]?.name) &&
                  a.data?.config[0]?.name !== widgetName
              )
              .map((el, i) => {
                return (
                  <SelectItem key={i} value={el.data?.config[0]?.name}>
                    {el.data?.config[0]?.name}
                  </SelectItem>
                );
              })}
          </SelectContent>
        </Select>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(JumpToWidgetForm);
