import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useMultiLanguage from "common/hooks/useMultiLanguage";

const SendMapCardSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [text, setText] = useState("");
  const [title, setTitle] = useState("");
  const [mapData, setMapData] = useState({
    lat: "",
    long: ""
  });
  const [widgetName, setWidgetName] = useState("");

  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setText(nodeConfig.handlerParams[0].description);
        setMapData({
            lat: nodeConfig.handlerParams[0].lat,
            long: nodeConfig.handlerParams[0].long
        })
        setTitle(nodeConfig.handlerParams[0].title);
      setWidgetName(nodeConfig.name);
    }
  }, [nodeData]);


  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
      ? nodeDataToEditClone.data.config[0]
      : nodeDataToEditClone.data.config;
    config.handlerParams[0].description = result;
    config.handlerParams[0].lat = mapData.lat;
    config.handlerParams[0].long = mapData.long;
    config.handlerParams[0].title = titleRes;
    config.name = widgetName;
    console.log("config", result);
    onSaveNodeData({
      ...nodeDataToEditClone,
    });
  };
  const { result, value, setValue } = useMultiLanguage(text);
  const { result: titleRes, value: titleVal, setValue: setTitleVal } = useMultiLanguage(title);
  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div className="space-y-1">
        <Input
          title="Latitude"
          name="lat"
          placeholder="Enter latitude"
          type="text"
          onChange={(event) => {
            setMapData({...mapData, lat: event.target.value});
          }}
          value={mapData.lat}
        />
          <Input
          title="Longitude"
          name="long"
          placeholder="Enter longitude"
          type="text"
          onChange={(event) => {
            setMapData({...mapData, long: event.target.value});
          }}
          value={mapData.long}
        />
          <Input
          title="Title"
          name="title"
          placeholder="Enter title"
          type="text"
          onChange={(event) => {
            setTitleVal(event.target.value);
          }}
          value={titleVal}
        />
          <label htmlFor="text">Location Description</label>
          <Textarea
            placeholder="Write your text to send to the user, Use <br /> to make a new line"
            onChange={(event) => {
              setValue(event.target.value);
            }}
            className="h-64 px-3 py-2 "
            value={value}
          />
        </div>
      </div>
      <br />
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(SendMapCardSheet);
