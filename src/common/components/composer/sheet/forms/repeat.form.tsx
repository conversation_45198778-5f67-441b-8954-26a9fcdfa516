import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { <PERSON><PERSON>Footer } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useState } from "react";

import { Node } from "reactflow";

const RepeatForm: IPromptForm = ({ nodeData, entities, onSaveNodeData }) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [maxRetries, setMaxRetries] = useState(1);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setMaxRetries(nodeData.data.config[0]?.handlerParams[0].max);
    }
  }, [nodeData]);

  const handleSave = () => {
    setLoading(true);
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0].max = maxRetries;
    onSaveNodeData(nodeDataToEditClone);
    setLoading(false);
  };
  return (
    <>
      <div className="space-y-3 mt-5 mb-5">
        <span>
        In case the user didn&apos;t match, you get to repeat the question as much as you modify or fail.
        </span>
        <Input
          name="max"
          type="number"
          value={maxRetries}
          onChange={(e) => {
            setMaxRetries(parseInt(e.target.value));
          }}
        />
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit" loading={loading}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(RepeatForm);
