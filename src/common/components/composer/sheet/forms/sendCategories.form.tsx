import Empty from "common/components/empty";
import { But<PERSON> } from "common/ui/button";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";
import { CustomSearch } from "common/ui/inputs/search";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useBotStore from "store/bot/bot.store";
import useCategoryStore from "store/category/category.store";

const SendCategoriesForm: IPromptForm = ({ nodeData, onSaveNodeData, dialog }) => {
  const categories = useCategoryStore((state) => state.categories);
  const bot_id = useBotStore((state) => state.bot.bot_id);

  const [keySearch, setKeySearch] = useState("");
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [categoryIds, setcategoryIds] = useState([]);
  const [widgetName, setWidgetName] = useState("");

  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);
  
  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setcategoryIds(nodeConfig?.handlerParams[0].category_ids);
      setWidgetName(nodeConfig?.name);
    }
  }, [nodeData]);

  const categoriesToView = () => {
    var categories_to_view = [...categories];
    const results = [];
    categories_to_view.map((category) => {
      const description1 = category.category_name || "";
      const description2 = category.category_name
        ? category.category_name.replace(/أ|إ/g, "ا")
        : "";
      const match3 = description1.toLowerCase().search(keySearch.toLowerCase());
      const match4 = description2.toLowerCase().search(keySearch.toLowerCase());
      if (match3 !== -1 || match4 !== -1) results.push(category);
    });
    return [...results];
  };

  const onSelectCategory = (category_id, isSelected) => {
    var categoryIdsToUpd = [...categoryIds];
    if (isSelected) {
      categoryIdsToUpd.push(category_id);
    } else {
      categoryIdsToUpd = categoryIdsToUpd.filter((a) => a !== category_id);
    }
    setcategoryIds(categoryIdsToUpd);
  };

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
    ? nodeDataToEditClone.data.config[0]
    : nodeDataToEditClone.data.config;
    config.handlerParams[0].category_ids =
      categoryIds.map((a) => a);
    config.name = widgetName;
    config.handlerParams[0].bot_id = dialog.bot_id;
    onSaveNodeData(nodeDataToEditClone);
  };
  return categories && categories.length > 0 ? (
    <>
      <div className="flex flex-col gap-4 py-7 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />
        <CustomSearch
          placeholder="Search for categories"
          onChange={(value) => setKeySearch(value)}
        />
        <div className="max-h-[400px] overflow-y-auto">
          <div className="flex items-center justify-between p-2 pb-3 bg-secondary sticky top-0">
            <Checkbox
              onChange={(event) => {
                if (!event.target.checked) {
                  setcategoryIds([]);
                } else {
                  setcategoryIds([
                    ...categoriesToView().map((b) => b.category_id),
                  ]);
                }
              }}
              checked={categoriesToView().length === categoryIds?.length}
              name={`select-all`}
            />
            <div>Category</div>
          </div>
          {categoriesToView()?.map((category, i) => {
            return (
              <div
                key={i}
                className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
              >
                <Checkbox
                  onChange={(event) =>
                    onSelectCategory(category.category_id, event.target.checked)
                  }
                  checked={categoryIds?.includes(category.category_id)}
                  name={`select-${category.category_id}`}
                  key={category.category_id}
                />
                <div>{category.category_name}</div>
              </div>
            );
          })}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  ) : (
    <div className="flex flex-col items-center justify-center gap-4 py-7 min-h-[400px]">
      <Empty />
      <div className=" font-semibold text-white text-center">
        Please add categories to your bot so you can use the widget
      </div>
    </div>
  );
};

export default memo(SendCategoriesForm);
