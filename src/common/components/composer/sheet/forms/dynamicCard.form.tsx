import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { <PERSON><PERSON><PERSON>oot<PERSON> } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import SelectEntity from "../selectEntity";


const DynamicCardForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  globals
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [mapper, setMapper] = useState({
    image_url: "",
    heading: "",
    subheading: "",
  });

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setMapper(nodeData.data.config[0].handlerParams[0].mapper);
      setEntity(nodeData.data.config[0].handlerParams[0].entity);
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].handlerParams[0].mapper = {
      ...mapper,
    };
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[0].entity = entity;
    onSaveNodeData(nodeToEditClone);
  };
  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <span>Select an entity to get data from</span>
        <SelectEntity 
          entities={entities}
          onValueChange={(value) => {
            setEntity(value);
          }}
          entity={entity}
          globals={globals}
          width="w-[180px]"
        />

        <hr className="text-white/25" />
        <div className="flex flex-col gap-2">
          <small>
            Use keys from your api response to map the data to the card
          </small>
          {Object.keys(mapper).map((a, index) => (
            <Input
              key={index}
              title={a}
              name={a}
              placeholder={`Enter ${a} key from api response`}
              type="text"
              onChange={(event) => {
                setMapper({ ...mapper, [a]: event.target.value });
              }}
              value={mapper[a]}
            />
          ))}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(DynamicCardForm);
