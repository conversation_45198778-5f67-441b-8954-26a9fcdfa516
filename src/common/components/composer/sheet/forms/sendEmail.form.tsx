import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { uploadSendMessageAttachments } from "apis/file.api";
import { FilesInput } from "views/unstructured/components";
import MultiSelectTextInput from "common/components/MultiSelectTextInput";
import AttachmentIcon from "common/components/attachmentIcon";

import { Option } from "common/components/MultiSelectTextInput";
import {
  setValuesBasedOnLanguage,
  transformLangObjToText,
  transformTextToLanguageObj,
} from "helpers/multiLang";
import { useLangStore } from "store/language/lang.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import useSendGridStore from "store/sendgrid/sendgrid.store";
import useSmtpStore from "store/smtp/smtp.store";
type IntegrationType = "sendGrid integration" | "SMTP integration";

const SendEmailSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [emailInfo, setEmailInfo] = useState({
    title: { en: "", ar: "" },
    body: { en: "", ar: "" },
  });
  const { lang } = useLangStore();
  const [widgetName, setWidgetName] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [prevData, setPrevData] = useState({
    toData: [],
    ccData: [],
    bccData: [],
  });
  const [toData, setToData] = useState<readonly Option[]>([]);
  const [ccData, setCcData] = useState<readonly Option[]>([]);
  const [bccData, setBccData] = useState<readonly Option[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const bot = useBotStore((state) => state.bot) as IBot;
  const { sendgrid, get_one_sendgrid } = useSendGridStore();
  const { Smtp, get_one_Smtp } = useSmtpStore();

  const [integrationType, setIntegrationType] = useState<IntegrationType[]>([]);
  const [selectedIntegrationType, setSelectedIntegrationType] =
    useState<IntegrationType>(null);
  
    useEffect(() => {
      const integrations: IntegrationType[] = [];
      if (sendgrid.sendGrid_id) {
          integrations.push("sendGrid integration");
      }
      if (Smtp.smtp_id) {
          integrations.push("SMTP integration");
      }
      setIntegrationType(integrations);
    }, [sendgrid, Smtp]);
  


  useEffect(() => {
    if (nodeData?.data?.config) {
      const prevViewType = nodeData.data?.config?.[0]?.handlerParams?.[0]?.mapper?.integrationType|| selectedIntegrationType;
      setSelectedIntegrationType(prevViewType)
      setAttachments(nodeData.data.config[0].handlerParams[0].attachments);
      setPrevData({
        toData: nodeData.data.config[0].handlerParams[0].mapper.email,
        ccData: nodeData.data.config[0].handlerParams[0].mapper.cc,
        bccData: nodeData.data.config[0].handlerParams[0].mapper.bcc,
      });
      setToData(nodeData.data.config[0].handlerParams[0].mapper.email);
      setCcData(nodeData.data.config[0].handlerParams[0].mapper.cc);
      setBccData(nodeData.data.config[0].handlerParams[0].mapper.bcc);
      setNodeDataToEdit(nodeData);
      setEmailInfo({
        title: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[0].mapper.title
        ),
        body: transformTextToLanguageObj(
          nodeData.data.config[0].handlerParams[0].mapper.body
        ),
      });
      setWidgetName(nodeData?.data?.config[0]?.name);
    }
  }, [nodeData]);

  const handleSave = async () => {
    setIsLoading(true);
    const To = toData.map((email) => {
      return email.value;
    });
    const Cc = ccData.map((email) => {
      return email.value;
    });
    const Bcc = bccData.map((email) => {
      return email.value;
    });
    const savedAttachments = await Promise.all(
      attachments.map(async (image) => {
        try {
          const result = await uploadSendMessageAttachments(image);
          return result;
        } catch (error) {
          console.error("Image upload failed:", error);
        }
      })
    );

    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
      ? nodeDataToEditClone.data.config[0]
      : nodeDataToEditClone.data.config;

    nodeDataToEditClone.data.config[0].handlerParams[0].attachments =
      savedAttachments;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.body =
      transformLangObjToText(emailInfo.body);
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.title =
     transformLangObjToText (emailInfo.title);
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.email = To;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.cc = Cc;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.bcc = Bcc;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.integrationType = selectedIntegrationType;
    nodeDataToEditClone.data.config[0].handlerParams[1] = selectedIntegrationType;
    config.name = widgetName;
    onSaveNodeData({
      ...nodeDataToEditClone,
    });
    setIsLoading(false);
  };

  const handleChange = (e: { target: { name: string; value: string } }) => {
    setEmailInfo({
      ...emailInfo,
      [e.target.name]: setValuesBasedOnLanguage(
        e.target.value,
        emailInfo[e.target.name],
        lang
      ),
    });
  };

  const handleAttachments = (files) => {
    if (!files) {
      return false;
    }

    const filesArr = [...files];

    const attachmentsArr = filesArr.map((file) => {
      const formData = new FormData();
      formData.append("file", file);
      return {
        path: `Bots/${bot.file_name}/sendMessageAttachments/${file.name}`,
        formData,
        filename: file.name,
        type: file.type,
        disposition: "attachment",
      };
    });
    setAttachments((prev) => [...prev, ...attachmentsArr]);
  };

  const handleDeleteAttachment = (index) => {
    const filterdAttachments = [...attachments];
    filterdAttachments.splice(index, 1);
    setAttachments(filterdAttachments);
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

        <label htmlFor="view">Integration Type</label>
        <Select
          value={selectedIntegrationType}
          onValueChange={(value) => {
            setSelectedIntegrationType(value as IntegrationType);
          }}
        >
          <SelectTrigger className="w-full -mt-3">
            <SelectValue placeholder="select integration Type" />
          </SelectTrigger>
          <SelectContent>
            {integrationType?.map((integrationType) => {
              return (
                <>
                  <SelectItem value={integrationType}>
                    {integrationType}
                  </SelectItem>
                </>
              );
            })}
          </SelectContent>
        </Select>
        <div>
          <MultiSelectTextInput
            setData={setToData}
            title="To"
            prevValue={prevData.toData}
            type="email"
          />
          <MultiSelectTextInput
            setData={setCcData}
            title="Cc"
            prevValue={prevData.ccData}
            type="email"
          />
          <MultiSelectTextInput
            setData={setBccData}
            title="Bcc"
            prevValue={prevData.bccData}
            type="email"
          />
        </div>
        <Input
          title="Subject:"
          name="title"
          placeholder="Subject"
          type="text"
          onChange={handleChange}
          value={emailInfo.title[lang]}
        />
        <div className="space-y-1">
          <label htmlFor="text">Body:</label>
          <Textarea
            name="body"
            rows={4}
            id="text"
            className="h-full"
            placeholder="Type your message ...."
            onChange={handleChange}
            value={emailInfo.body[lang]}
          />
        </div>
        <FilesInput
          onChange={handleAttachments}
          ACCEPTED_FILE_TYPES={[
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "image/jpeg",
            "image/png",
            "image/gif",
            "image/bmp",
          ]}
        />

        <div className="w-full ">
          {attachments.map((attachment, index) => {
            return (
              <AttachmentIcon
                key={index}
                attachment={attachment}
                index={index}
                handleDeleteAttachment={handleDeleteAttachment}
              />
            );
          })}
        </div>
      </div>

      <SheetFooter>
        <Button onClick={handleSave} type="submit" loading={isLoading}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(SendEmailSheet);
