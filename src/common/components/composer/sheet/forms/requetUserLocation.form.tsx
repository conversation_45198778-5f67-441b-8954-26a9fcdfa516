import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON>Footer } from "common/ui/sheet";
import generateStorageId from "helpers/generateStorageId";
import { memo, useEffect, useRef, useState } from "react";
import { Node } from "reactflow";
import { IPromptForm } from "types/composer.types";
import useMultiLanguage from "common/hooks/useMultiLanguage";
import SelectEntity from "../selectEntity";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";
import HelpHoverCard from "common/components/cards/helpHoverCard";

const RequetUserLocationSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  setEntities,
  globals
}) => {
  const { value, setValue, result } = useMultiLanguage(
    nodeData?.data?.config[0]?.handlerParams[0] || ""
  );
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [text, setText] = useState("");
  const [conditions, setConditions] = useState([]);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState("");
  const [oldEntites, setOldEntiteis] = useState("");
  const voiceDataRef = useRef({
    voice_path_male: "",
    voice_path_female: "",
  });
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setText(nodeData?.data?.config[0].handlerParams[0]);
      setConditions(nodeData?.data?.config[1]?.handlerParams[1]);
      setWidgetName(nodeData?.data?.config[0]?.name);
      setEntity(nodeData?.data?.config[1]?.handlerParams[0]);
      setOldEntiteis(nodeData?.data?.config[1]?.recieving);
      voiceDataRef.current = nodeData?.data?.config[0]?.handlerParams[2];
    }
  }, [nodeData || oldEntites]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = result;
    nodeDataToEditClone.data.config[0].handlerParams[1] ='location';
    nodeDataToEditClone.data.config[1].handlerParams[1] = conditions.map(
      (a) => a
    );
    nodeDataToEditClone.data.config[1].handlerParams[0] = entity;
    nodeDataToEditClone.data.config[0].name = widgetName;

    nodeDataToEditClone.data.config[0].handlerParams[2] = {voice_path_male:voiceDataRef.current.voice_path_male,voice_path_female:voiceDataRef.current.voice_path_female};

    onSaveNodeData(nodeDataToEditClone);
  };

  const handleUpdateVoice = (data: TVoiceBody) => {
    const updatedVoices = data;
    delete updatedVoices.bot_id;
    delete updatedVoices.user_id;
    voiceDataRef.current = {...voiceDataRef.current,...updatedVoices};
  };

  return (
    <>
      <p className="text-yellow-600">only supported for WhatsApp and web</p>

      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div className="space-y-1">

        <VoiceModalWrapper
       handleSave={handleSave}
       handleUpdateVoice={handleUpdateVoice}
       setShowModal={setShowModal}
       showModal={showModal}
       text={value}
       voice_path_female={nodeData?.data?.config[0]?.handlerParams[2]?.voice_path_female || ''}     
       voice_path_male={nodeData?.data?.config[0]?.handlerParams[2]?.voice_path_male || ''} 
         />

          <label htmlFor="text" className="flex gap-5 items-end">Text
          <small className="text-white/50">
            - Characters Limit: WhatsApp: 4096

          </small>
          <HelpHoverCard>
              <div >
                This Block will return an object that contains:
                <ul className="list-disc list-inside mt-2">
                  <li>latitude</li>
                  <li>longitude</li>
                  <li>url</li>
                  <li>title</li>
                  <li>description</li>
                </ul>
              </div>
            </HelpHoverCard>
          </label>
          <Textarea
            onChange={(event) => setValue(event.target.value)}
            value={value}
            rows={4}
            id="text"
            className="h-full"
          />
          <small className="float-right text-white/50">
            {value?.length || 0} characters
          </small>
        </div>
        <label htmlFor="entity">Entity Storage</label>
        <SelectEntity 
          entities={entities}
          onValueChange={(value) => {
            setEntity(value);
          }}
          entity={entity}
          globals={globals}
        />
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(RequetUserLocationSheet);
