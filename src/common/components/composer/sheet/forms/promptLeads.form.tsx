import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import SelectEntity from "../selectEntity";

const PromptLeadsForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  globals
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState({
    email: "",
    phone: "",
  });
  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setEntity(nodeData.data.config[0].handlerParams[0]);
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].handlerParams[0] = entity;
    nodeToEditClone.data.config[0].name = widgetName;
    onSaveNodeData(nodeToEditClone);
  };
  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div className="space-y-2 pb-5">
          <h1>Choose entities to map them to the fields for the lead</h1>
          <hr className="text-white/25" />
          <div className="space-y-3 ">
            <div className="flex items-center justify-between">
              <span>Email</span>
              <SelectEntity 
              entities={entities}
              onValueChange={(value) => {
                setEntity({ ...entity, email: value });
              }}
              entity={entity.email}
              globals={globals}
              width="w-[180px]"
              />
            </div>
            <div className="flex items-center justify-between">
              <span>Phone</span>
              <SelectEntity
                entities={entities}
                onValueChange={(value) => {
                  setEntity({ ...entity, phone: value });
                }}
                entity={entity.phone}
                globals={globals}
                width="w-[180px]"
                />
            </div>
          </div>
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(PromptLeadsForm);
