import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { SheetFooter } from "common/ui/sheet";
import { Button } from "common/ui/button";
import SelectEntity from "../selectEntity";

const ChartForm: IPromptForm = ({
  nodeData,
  entities,
  onSaveNodeData,
  globals
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [searchStoreEntity, setSearchStoreEntity] = useState("");
  const [yAxsis, setYAxis] = useState("");
  const [xAxis, setxAxis] = useState("");
  const [x_title, setx_title] = useState("");
  const [y_title, sety_title] = useState("");
  const [dataType, setdataType] = useState("");
  const [chartType, setchartType] = useState("");
  const [graph_name, setgraph_name] = useState("");
  const [colorSet, setColorSet] = useState("");
  const [showInputs, setShowInputs] = useState(false); // State to track if chart type is selected

  const chartTypeArray = ['Bar', 'Line', 'Pie'];
  const setColorArr = [
    ['#FFD9C0', '#8CC0DE', '#CCEEBC', '#FAF0D7'],
    ['#461959', '#7A316F', '#CD6688', '#AED8CC'],
    ['#1D5B79', '#468B97', '#EF6262', '#F3AA60']
  ];

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0]?.name);
      setSearchStoreEntity(nodeData.data.config[0]?.handlerParams[0].entity);
      setdataType(nodeData.data.config[0]?.handlerParams[0].mapper.data_type);
      setYAxis(nodeData.data.config[0]?.handlerParams[0].mapper.yAxis);
      setxAxis(nodeData.data.config[0]?.handlerParams[0].mapper.xAxis);
      setx_title(nodeData.data.config[0]?.handlerParams[0].mapper.x_title);
      sety_title(nodeData.data.config[0]?.handlerParams[0].mapper.y_title);
      setchartType(nodeData.data.config[0]?.handlerParams[0].mapper.chart_type);
      setgraph_name(nodeData.data.config[0]?.handlerParams[0].mapper.graph_name);
      setColorSet(nodeData.data.config[0]?.handlerParams[0].mapper.color_set);
      setShowInputs(!!nodeData.data.config[0]?.handlerParams[0].mapper.chart_type); // Set showInputs based on whether chart type is already selected
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };

    nodeDataToEditClone.data.config[0].handlerParams[0].entity = searchStoreEntity;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.data_type = dataType;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.yAxis = yAxsis;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.xAxis = xAxis;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.chart_type = chartType;
    nodeDataToEditClone.data.config[0].name = widgetName;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.y_title = y_title;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.x_title = x_title;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.graph_name = graph_name;
    nodeDataToEditClone.data.config[0].handlerParams[0].mapper.color_set = colorSet;
    onSaveNodeData(nodeDataToEditClone);
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />
        <div className="flex flex-col gap-2">
          <label htmlFor="entity">Entity to Get Data From</label>
          <SelectEntity 
            entity={searchStoreEntity}
            onValueChange={(value) => {
              setSearchStoreEntity(value);
            }}
            entities={entities}
            globals={globals}
          />
          <label htmlFor="entity3">Data Type</label>
              <Select
                onValueChange={(value) => {
                  setdataType(value);
                }}
                value={dataType}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select an entity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='array of object'>array of object</SelectItem>
                  <SelectItem value='object'>object</SelectItem>
                </SelectContent>
              </Select>
          <Input 
                title="Graph Name"
                name="graph_name"
                placeholder="Enter graph name"
                type="text"
                onChange={(event) => {
                  setgraph_name(event.target.value);
                }} 
                value={graph_name}
              />
          <label htmlFor="entity4">Chart Type</label>
          <Select
            onValueChange={(value) => {
              setchartType(value);
              setShowInputs(true); 
            }}
            value={chartType}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Chart Type" />
            </SelectTrigger>
            <SelectContent>
              {chartTypeArray.map((type, i) => {
                return (
                  <SelectItem key={i} value={type}>
                    {type}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          {showInputs && (
            <>
              <Input 
                title={chartType === 'Pie' ? "Label" : "X-axis"}
                name="xAxis"
                placeholder={chartType === 'Pie' ? "Enter label" : "Enter X-axis"}
                type="text"
                onChange={(event) => {
                  setxAxis(event.target.value);
                }}
                value={xAxis}
              />
               <Input 
                    title={chartType === 'Pie' ? "Value" : "Y-axis"}
                    name="yAxis"
                    placeholder={chartType === 'Pie' ? "Enter Value" : "Enter Y-axis"}
                    type="text"
                    onChange={(event) => {
                      setYAxis(event.target.value);
                    }}
                    value={yAxsis}
                  />
              {chartType !== 'Pie' && (
                <>
                  <Input 
                    title="X Title"
                    name="x_title"
                    placeholder="Enter Title for X-axis"
                    type="text"
                    onChange={(event) => {
                      if (chartType !== 'Pie')
                          setx_title(event.target.value);
                    }}
                    value={x_title}
                  />
                    <Input 
                      title="Y Title"
                      name="y_title"
                      placeholder="Enter Title for Y-axis"
                      type="text"
                      onChange={(event) => {
                        if (chartType !== 'Pie')
                          sety_title(event.target.value);
                      }} 
                      value={y_title}
                    />
                </>
              )}
              <label htmlFor="entity4">Select Color Set</label>
              <Select
                onValueChange={(value) => {
                  setColorSet(value);
                }}
                value={colorSet}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Color Set" />
                </SelectTrigger>
                <SelectContent>
                  {setColorArr.map((type, i) => {
                    return (
                      <SelectItem key={i} value={type.toString()}>
                        <div className="relative w-full w-100 flex">
                          {type.map((color, j) => (
                            <div
                              key={j}
                              style={{ backgroundColor: color, color: color }}
                              className={`flex-1 min-w-1/4 min-h-full`}
                            >dffffffffffff</div>
                          ))}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </>
          )}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(ChartForm);

               
