import { uploadBotImages } from "apis/file.api";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { SheetFooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, use, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import { CardEditor } from "views/knowledgeBase/components";
import { transformLangObjToText, transformTextToLanguageObj } from "helpers/multiLang";
import { useLangStore } from "store/language/lang.store";

const SendCarouselForm: IPromptForm = ({ nodeData, onSaveNodeData, entities, globals }) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [cardsConfig, setCardsConfig] = useState(null);
  const [imagesToUplaod, setImagesToUpload] = useState([]);
  const [widgetName, setWidgetName] = useState("");
  const { lang, changeLang } = useLangStore();
  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      const cardConfigClone = JSON.parse(
        JSON.stringify(nodeConfig.handlerParams[0])
      );

      cardConfigClone?.attachments?.forEach((attachment) => {
        attachment.body = attachment?.body.map((body) => {
          if (body?.id === "video") {
            return {
              ...body,
              sources: [
                {
                  mimeType: "video/mp4",
                  url: transformTextToLanguageObj(body.sources[0].url),
                },
              ],
            };
          }
          if (body?.id === "image") {
            return {
              ...body,
              url: transformTextToLanguageObj(body?.url),
            };
          }
          if (body?.id === "heading" || body?.id === "subheading") {
            return {
              ...body,
              text: transformTextToLanguageObj(body?.text),
            };
          }
          if (body?.id === "voiceData") {
            return {
              ...body,
              voiceData: body?.voiceData || {voice_path_female:"",voice_path_male:""},
            };
          }
        });
        attachment.actions = attachment?.actions.map((action) => {
          if (action?.type === "Action.Submit") {
            return {
              ...action,
              title: transformTextToLanguageObj(action?.title),
              data: transformTextToLanguageObj(action?.data),
            };
          }
          if (action?.type === "Action.OpenUrl") {
            return {
              ...action,
              title: transformTextToLanguageObj(action?.title),
              url: transformTextToLanguageObj(action?.url),
            };
          }
        });
      });
      console.log(cardConfigClone);
      Object.keys(cardConfigClone)?.length
        ? setCardsConfig(cardConfigClone)
        : setCardsConfig({});

      // setCardsConfig(nodeConfig.handlerParams[0]);
      setWidgetName(nodeConfig.name);
    }
  }, [nodeData, nodeConfig]);
  useEffect(() => {
    console.log(cardsConfig);
  }, [cardsConfig]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
      ? nodeDataToEditClone.data.config[0]
      : nodeDataToEditClone.data.config;
    console.log(cardsConfig);
    const cardConfigClone = JSON.parse(JSON.stringify(cardsConfig));
     cardConfigClone?.attachments?.forEach((attachment) => {
       attachment.body = attachment?.body.map((body) => {
         if (body?.id === "video") {
           return {
             ...body,
             sources: [
               {
                 mimeType: "video/mp4",
                 url: transformLangObjToText(body.sources[0].url),
               },
             ],
           };
         }
         if (body?.id === "image") {
           return {
             ...body,
             url: transformLangObjToText(body?.url),
           };
         }
         if (body?.id === "heading" || body?.id === "subheading") {
           return {
             ...body,
             text: transformLangObjToText(body?.text),
           };
         }
         if (body?.id === "voiceData") {
           return {
             ...body,
             voiceData: body?.voiceData,
           };
         }
       });
       attachment.actions = attachment?.actions.map((action) => {
         if (action?.type === "Action.Submit") {
           return {
             ...action,
             title: transformLangObjToText(action?.title),
             data: transformLangObjToText(action?.data),
           };
         }
         if (action?.type === "Action.OpenUrl") {
           return {
             ...action,
             title: transformLangObjToText(action?.title),
             url: transformLangObjToText(action?.url),
           };
         }
       });
     });
    nodeDataToEditClone.data.config[0].name = widgetName;
    config.handlerParams[0] = {
      ...cardConfigClone,
    };
    config.handlerParams[1] = lang

    imagesToUplaod.map(async (image) => {
      await uploadBotImages({
        ...image,
        path: image?.path + '&image=true',
      });
    });

    onSaveNodeData(nodeDataToEditClone);
  };
  return (
    <>
      <div className="flex flex-col gap-4 py-7 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div className="max-h-[600px] overflow-y-auto">
          {cardsConfig && (
            <CardEditor
              updateCards={(cards) => {
                setCardsConfig({
                  ...cards,
                });
              }}
              uploadImageHandler={(image) => {
                const allImg = [...imagesToUplaod];
                allImg.push(image);
                setImagesToUpload([...allImg]);
              }}
              isUpdate={Boolean(
                Object.keys(nodeConfig?.handlerParams[0] || {}).length
              )}
              isDialog={true}
              cardBlock={cardsConfig}
              handleSave={handleSave}
              entities={entities}
              globals={globals}
            />
          )}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(SendCarouselForm);
