import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import dynamic from "next/dynamic";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import { Expand, Minimize } from "lucide-react";
import Editor from "@monaco-editor/react";
import CustomEditor from "common/ui/customEditor";

const JsEvalForm: IPromptForm = ({ nodeData, onSaveNodeData, onExpandChange, entities, globals }) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState<string>("");
  const [code, setCode] = useState<string>("");
  const [expandAce, setExpandAce] = useState(false);
  const [headers, setHeaders] = useState([
    {
      key: "Content-Type",
      value: "javascript",
    }
  ]);


  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setCode(nodeData.data.config[0].handlerParams[0]);
    }
  }, [nodeData]);

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    nodeDataToEditClone.data.config[0].handlerParams[0] = code;

    nodeDataToEditClone.data.config[0].name = widgetName;

    onSaveNodeData(nodeDataToEditClone);
  };

  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(expandAce);
    }
  }, [expandAce, onExpandChange]);


  return (
    <>
      {!expandAce ? (
        <div className="flex flex-col gap-4 py-7  min-h-[400px]">
          <Input
            title="Widget Name"
            name="widgetName"
            placeholder="Enter widget name"
            type="text"
            onChange={(event) => {
              setWidgetName(event.target.value);
            }}
            value={widgetName}
          />
          <div className="text-sm flex items-center gap-2">
            Add a code snippet that would modify the data stored in entities
            object
            <HelpHoverCard>
              <div>
                This code should use and modify the{" "}
                <span className="text-amber-500 font-bold">entities</span>{" "}
                object where you can access your saved entities.
                <br />
                you can use the{" "}
                <small className="leading-3 tracking-tighter text-amber-500">
                  Example: <br />
                  <code className="text-xs bg-accent">
                    {`
                        const result = entities.data.find((item) => {item.title === "some title"})
                        entities.result = result
                       
                        `}
                  </code>
                  <br />
                  OR
                  <br />
                  <code className="text-xs bg-accent">
                    {` (async function () {
                          // your GET api call
                          }())`}
                  </code>
                </small>
              </div>
            </HelpHoverCard>
          </div>
          <div className="relative h-80 w-full">
            <Expand
              onClick={() => setExpandAce(!expandAce)}
              className="absolute right-0 top-0 z-[99999] hover:scale-105"
            />
            <CustomEditor
              headers={headers}
              onChangeJS={(value) => setCode(value)}
              stringifyBodyValue={code}
              entities={entities}
              globals={globals}
              isMin={true}
            />
          </div>
        </div>
      ) : (
        <>
          <div className="relative h-[50rem] w-full my-5">
            <Minimize
              onClick={() => setExpandAce(!expandAce)}
              className="absolute right-0 top-0 z-[99999] hover:scale-105"
            />
            <CustomEditor
              headers={headers}
              onChangeJS={(value) => setCode(value)}
              stringifyBodyValue={code}
              entities={entities}
              globals={globals}
            />
          </div>
        </>
      )}

      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(JsEvalForm);
