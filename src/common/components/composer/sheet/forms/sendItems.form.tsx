import { Input } from "common/ui/inputs/input";
import { CustomSearch } from "common/ui/inputs/search";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useState } from "react";
import useCategoryStore from "store/category/category.store";
import useItemStore from "store/item/item.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Checkbox } from "common/ui/inputs/checkbox";
import { SheetFooter } from "common/ui/sheet";
import { Button } from "common/ui/button";
import { Node } from "reactflow";
import useBotStore from "store/bot/bot.store";
import Empty from "common/components/empty";

const SendItemsForm: IPromptForm = ({ nodeData, onSaveNodeData, dialog }) => {
  const items = useItemStore((state) => state.items);
  const categories = useCategoryStore((state) => state.categories);
  const bot_id = useBotStore((state) => state.bot.bot_id);

  const [keySearch, setKeySearch] = useState("");
  const [itemsData, setItemsData] = useState([]);
  const [categoryToFilterBy, setCategoryToFilterBy] = useState("All");
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [itemIds, setItemIds] = useState([]);
  const [widgetName, setWidgetName] = useState("");

  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setItemsData([...items]);
      setItemIds(nodeConfig.handlerParams[0].item_ids);
      setWidgetName(nodeConfig.name);
    }
  }, [nodeData]);

  const onSelectItem = (item_id, isSelected) => {
    var itemIdsToUpd = [...itemIds];
    if (isSelected) {
      itemIdsToUpd.push(item_id);
    } else {
      itemIdsToUpd = itemIdsToUpd.filter((a) => a !== item_id);
    }
    setItemIds(itemIdsToUpd);
  };

  const itemsToView = () => {
    var item_to_view = [...itemsData];
    var results = [];
    item_to_view.map((item) => {
      const description1 = item.item_title || "";
      const description2 = item.item_title
        ? item.item_title.replace(/أ|إ/g, "ا")
        : "";
      const match3 = description1.toLowerCase().search(keySearch.toLowerCase());
      const match4 = description2.toLowerCase().search(keySearch.toLowerCase());
      if (match3 !== -1 || match4 !== -1) results.push(item);
    });

    if (categoryToFilterBy !== "All") {
      results = results.filter((a) => a.category_id === +categoryToFilterBy);
    }
    return [...results];
  };

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
    ? nodeDataToEditClone.data.config[0]
    : nodeDataToEditClone.data.config;
    config.handlerParams[0].item_ids = itemIds.map(
      (a) => a
    );
    config.name = widgetName;
    config.handlerParams[0].bot_id = dialog.bot_id;
    onSaveNodeData(nodeDataToEditClone);
  };

  return items.length > 0 ? (
    <>
      <div className="flex flex-col gap-4 py-7 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />
        <div className="flex gap-5">
          <CustomSearch
            placeholder="Search for items"
            onChange={(value) => setKeySearch(value)}
          />
          <Select
            value={categoryToFilterBy}
            onValueChange={(value) => setCategoryToFilterBy(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select an item" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {categories?.map((category, i) => {
                return (
                  <SelectItem key={i} value={category.category_id.toString()}>
                    {category.category_name}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        <div className="max-h-[400px] overflow-y-auto">
          <div className="flex items-center justify-between p-2 pb-3 bg-secondary sticky top-0">
            <Checkbox
              onChange={(event) => {
                if (!event.target.checked) {
                  setItemIds([]);
                } else {
                  setItemIds([...itemsToView().map((b) => b.item_id)]);
                }
              }}
              checked={itemsToView().length === itemIds.length}
              name={`select-all`}
            />
            <div>Item</div>
          </div>
          {itemsToView()?.map((item, i) => {
            return (
              <div
                key={i}
                className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
              >
                <Checkbox
                  onChange={(event) =>
                    onSelectItem(item.item_id, event.target.checked)
                  }
                  checked={itemIds?.includes(item.item_id)}
                  name={`select-${item.item_id}`}
                  key={item.item_id}
                />
                <div>{item.item_title}</div>
              </div>
            );
          })}
        </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  ) : (
    <div className="flex flex-col items-center justify-center gap-4 py-7 min-h-[400px]">
      <Empty />
      <div className=" font-semibold text-white text-center">
        Please add items to your bot so you can use the widget
      </div>
    </div>
  );
};

export default memo(SendItemsForm);
