import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Switch } from "common/ui/inputs/switch";

import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useState } from "react";
import { Node } from "reactflow";
import { useLangStore } from "store/language/lang.store";
import {
  setValuesBasedOnLanguage,
  transformLangObjToText,
  transformTextToLanguageObj,
} from "helpers/multiLang";

import useBotStore from "store/bot/bot.store";
import { getDepartments } from "apis/ticketing.api";
const TicketingSystemForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { lang } = useLangStore();
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [mapper, setMapper] = useState({
    department: { en: "", ar: ""},
    nameQ: { en: "", ar: "" },
    emailQ: { en: "", ar: "" },
    phoneQ: { en: "", ar: "" },
    IssueTitle: { en: "", ar: "" },
    IssueDescription: { en: "", ar: "" },
    endMessageQ: { en: "", ar: "" },
  });

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setMapper({
        department: transformTextToLanguageObj(nodeData.data.config[0]?.handlerParams[0]),
        nameQ: transformTextToLanguageObj(nodeData.data.config[1].handlerParams[0]),
        emailQ: transformTextToLanguageObj(nodeData.data.config[2]?.handlerParams[0]),
        phoneQ: transformTextToLanguageObj(nodeData.data.config[3]?.handlerParams[0]),
        IssueTitle: transformTextToLanguageObj(nodeData.data.config[4]?.handlerParams[0]),
        IssueDescription: transformTextToLanguageObj(nodeData.data.config[5]?.handlerParams[0]),
        endMessageQ: transformTextToLanguageObj(nodeData.data.config[6]?.handlerParams[0]),
      });
    }
  }, [nodeData]);



  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[0] = 
      transformLangObjToText(mapper.department);
    nodeToEditClone.data.config[1].handlerParams[0] = 
      transformLangObjToText(mapper.nameQ);
    nodeToEditClone.data.config[2].handlerParams[0] = 
      transformLangObjToText(mapper.emailQ);
    nodeToEditClone.data.config[3].handlerParams[0] =
      transformLangObjToText(mapper.phoneQ);
    nodeToEditClone.data.config[4].handlerParams[0] =
      transformLangObjToText(mapper.IssueTitle);
    nodeToEditClone.data.config[5].handlerParams[0] = 
      transformLangObjToText(mapper.IssueDescription);
    nodeToEditClone.data.config[6].handlerParams[0] = 
      transformLangObjToText(mapper.endMessageQ);
    onSaveNodeData(nodeToEditClone);
  };
  const ticketingPlaceHolders = {
    Category_AR: "اختر نوع المشكلة:",
    Category_EN: "Select a category:",
    Name_AR: "أدخل اسمك:",
    Name_EN: "What is your name?",
    Email_AR: "أدخل بريدك الإلكتروني:",
    Email_EN: "What is your email?",
    Phone_AR: "أدخل رقم هاتفك:",
    Phone_EN: "What is your phone number?",
    Title_AR: "ما هي المشكلة التي تواجهها؟",
    Title_EN: "What seems to be the issue?",
    Description_AR: "يرجى توضيح المشكلة بالتفصيل:",
    Description_EN: "Can you provide more details about the problem?",
    RatingMessage_AR: "كيف تقيم هذه المحادثة؟",
    RatingMessage_EN: "How would you rate this conversation?",
    FeedbackMessage_AR: "كيف كانت محادثتك مع موظفنا اليوم؟",
    FeedbackMessage_EN: "How was your chat with our agent today?",
    End_AR: "شكرًا لتواصلك معنا، سيتم الرد عليك قريبًا!",
    End_EN: "Thank you for reaching out!",
  };
  return (
    <>

        <div className="space-y-3 pb-5 mt-5">
          <div className="flex items-center justify-between">
            <Input
              title="Widget Name"
              name="widgetName"
              placeholder="Widget Name"
              type="text"
              onChange={(event) => {
                setWidgetName(event.target.value);
              }}
              value={widgetName}
            />
          </div>
          <hr className="text-white/25 pb-5"/>
          <div className="flex items-center justify-between">
            <Input
              title="Category Question"
              name="categoryQ"
              placeholder={lang === 'en' ? ticketingPlaceHolders.Category_EN : ticketingPlaceHolders.Category_AR }
              type="text"
              onChange={(event) => {
                setMapper({
                  ...mapper,
                  department: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.department,
                  lang
                  ),
                });
              }}
              value={mapper.department[lang]}
            />
          </div>
          <div className="flex items-center justify-between">
            <Switch
              name="nameIncluded"
              label="Include Name Question"
              checked={mapper.nameQ[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  lang === 'en'
                  ?
                   setMapper({ ...mapper, nameQ: { ...mapper.nameQ, en: ticketingPlaceHolders.Name_EN } }) 
                  :
                   setMapper({ ...mapper, nameQ: { ...mapper.nameQ, ar: ticketingPlaceHolders.Name_AR } });
                } else {
                    setMapper({ ...mapper, nameQ: { ...mapper.nameQ, [lang]: "" } });
                  }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
              title=""
              name="nameQ"
              placeholder= {lang === 'en' ? ticketingPlaceHolders.Name_EN : ticketingPlaceHolders.Name_AR }
              type="text"
              onChange={(event) => {
                setMapper({
                  ...mapper,
                  nameQ: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.nameQ,
                  lang
                  ),
                });
              }}
              value={mapper.nameQ[lang]}
            />
          </div>
          <div className="flex items-center justify-between">
            <Switch
              name="emailIncluded"
              label="Include Email Question"
              checked={mapper.emailQ[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  lang === 'en'
                  ?
                   setMapper({ ...mapper, emailQ: { ...mapper.emailQ, en: ticketingPlaceHolders.Email_EN } })
                  :
                   setMapper({ ...mapper, emailQ: { ...mapper.emailQ, ar: ticketingPlaceHolders.Email_AR } });
                } else {
                  setMapper({ ...mapper, emailQ: { ...mapper.emailQ, [lang]: "" } });
                }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
            title=""
            name="emailQ"
            placeholder={lang === 'en' ? ticketingPlaceHolders.Email_EN : ticketingPlaceHolders.Email_AR }
            type="text"
            onChange={(event) => {
              setMapper({
                ...mapper,
                emailQ: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.emailQ,
                  lang
                  ),
                });
              }}
              value={mapper.emailQ[lang]}
              />  
          </div>

          <div className="flex items-center justify-between">
            <Switch
              name="phoneIncluded"
              label="Include Phone Question"
              checked={mapper.phoneQ[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  lang === 'en'
                  ?
                   setMapper({ ...mapper, phoneQ: { ...mapper.phoneQ, en: ticketingPlaceHolders.Phone_EN } })
                  :
                   setMapper({ ...mapper, phoneQ: { ...mapper.phoneQ, ar: ticketingPlaceHolders.Phone_AR } })
                } else {
                  setMapper({ ...mapper, phoneQ: { ...mapper.phoneQ, [lang]: "" } });
                }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
            title=""
            name="phoneQ"
            placeholder={lang === 'en' ? ticketingPlaceHolders.Phone_EN : ticketingPlaceHolders.Phone_AR }
            type="text"
            onChange={(event) => {
              setMapper({
                ...mapper,
                phoneQ: setValuesBasedOnLanguage(
                  event.target.value,
                  mapper.phoneQ,
                  lang
                  ),
                });
              }}
              value={mapper.phoneQ[lang]}
              />  
          </div>
          
          <div className="flex items-center justify-between">
            <Input
              title="Issue Title"
              name="widgetName"
              placeholder={lang === 'en' ? ticketingPlaceHolders.Title_EN : ticketingPlaceHolders.Title_AR }
              type="text"
              onChange={(event) => {
                setMapper({ ...mapper, IssueTitle: setValuesBasedOnLanguage(event.target.value, mapper.IssueTitle, lang) });
              }}
              value={mapper.IssueTitle[lang]}
            />
          </div>
          <div className="flex items-center justify-between">
            <Switch
              name="Issue Description Included"
              label="Include Issue Description  Question"
              checked={mapper.IssueDescription[lang] !== ""}
              onChange={(e) => {
                if (e.target.checked) {
                  lang === 'en'
                  ?
                   setMapper({ ...mapper, IssueDescription: { ...mapper.IssueDescription, en: ticketingPlaceHolders.Description_EN } }) 
                  :
                   setMapper({ ...mapper, IssueDescription: { ...mapper.IssueDescription, ar: ticketingPlaceHolders.Description_AR } });
                } else {
                  setMapper({ ...mapper, IssueDescription: { ...mapper.IssueDescription, [lang]: "" } });
                }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <Input
              title=""
              name="widgetName"
              placeholder={lang === 'en' ? ticketingPlaceHolders.Description_EN : ticketingPlaceHolders.Description_AR }
              type="text"
              value = {mapper.IssueDescription[lang]}
              onChange={(event) => {
                setMapper({ ...mapper, IssueDescription: setValuesBasedOnLanguage(event.target.value, mapper.IssueDescription, lang) });
              }}
            />
          </div>
          
          <hr className="text-white/25 pb-5" />
          <div className="flex items-center justify-between">
            <Input
              title="Ending Message"
              name="endMessageQ"
              placeholder={lang === 'en' ? ticketingPlaceHolders.End_EN : ticketingPlaceHolders.End_AR }
              type="text"
              onChange={(event) => {
                setMapper({
                  ...mapper,
                  endMessageQ: setValuesBasedOnLanguage(
                    event.target.value,
                    mapper.endMessageQ,
                    lang
                  ),
                });
              }}
              value={mapper.endMessageQ[lang] }
            />
          </div>
        </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(TicketingSystemForm);
