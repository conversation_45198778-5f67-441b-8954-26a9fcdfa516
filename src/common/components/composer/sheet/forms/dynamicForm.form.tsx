import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";

import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import { useLangStore } from "store/language/lang.store";
import {
  setValuesBasedOnLanguage,
  transformLangObjToText,
  transformTextToLanguageObj,
} from "helpers/multiLang";

import useBotStore from "store/bot/bot.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Modal } from "common/components/modals";
import SelectEntity from "../selectEntity";
import { Separator } from "@radix-ui/react-select";
import { Switch } from "common/ui/inputs/switch";
import { MinusCircle } from "lucide-react";
import useDynamicFormStore from "store/dynamicForm/dynamicForm.store";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "common/ui/alertDialog";

interface FormField {
  label: string;
  question: string;
  entity: string;
  ask_user: boolean;
}
interface MapperInterface {
  dynamic_form_schema_id: number;
  form: FormField[];
}

const DynamicFormForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  globals,
  entities,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { lang } = useLangStore();
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [mapper, setMapper] = useState<MapperInterface | null>(null);
  const [modalState, setModalState] = useState(false);
  const [dynamicFormModalData, setDynamicFormModalData] = useState({
    title: "",
    schema: [
      {
        label: "",
      },
    ],
  });
  const [tempError, setTempError] = useState(false);

  const { schemas, get_all_dynamic_form_schemas, create_one_dynamic_form,delete_dynamic_form_schemas } =
    useDynamicFormStore();

  useEffect(() => {
    if (bot.bot_id) get_all_dynamic_form_schemas(bot.bot_id);
  }, [bot]);

  // init data to edit
  useEffect(() => {
    if (!mapper) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      setMapper({
        ...nodeData.data.config[0]?.handlerParams[0],
      });
    }
  }, [nodeData]);

  const duplicateLabelError = useMemo(() => {
    const seen = new Set();
    const array = dynamicFormModalData?.schema.map((s) => s?.label);
    for (const item of array) {
      if (seen.has(item)) {
        return item;
      }
      seen.add(item);
    }
    return "";
  }, [dynamicFormModalData]);

  const handleSave = () => {
    const nodeToEditClone = JSON.parse(JSON.stringify(nodeDataToEdit));
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[0] = {
      dynamic_form_schema_id: mapper?.dynamic_form_schema_id,
      form: mapper?.form?.map((t) => ({
        label: t.label,
        question: t.question,
        ask_user: t.ask_user,
        entity: t.entity,
      })),
    };

    onSaveNodeData(nodeToEditClone);
  };

  const updateRow = (index, field, value) => {
    if (!mapper) return;

    const formToUpdate = JSON.parse(
      JSON.stringify(mapper.form)
    ) as MapperInterface["form"];

    if (field === "question") {
      const textObj = transformTextToLanguageObj(
        formToUpdate[index].question,
        false
      );
      textObj[lang] = value;

      formToUpdate[index].question = transformLangObjToText(textObj);
    } else {
      formToUpdate[index][field] = value;
    }
    setMapper({ ...mapper, form: [...formToUpdate] });
  };

  const missingFieldError = useMemo(() => {
    const hasError = mapper?.form?.some((f) => {
      if (f?.ask_user && !f?.question) {
        return true;
      }
      if (!f?.ask_user && !f?.entity) {
        return true; 
      }
      return false;
    });

    setTempError(hasError); 
    return hasError ? "Some fields are missing or invalid." : "";
  }, [mapper]);

  const deleteschema = async () => {
    await delete_dynamic_form_schemas(bot.bot_id, mapper?.dynamic_form_schema_id);
    setMapper(null)
  };

  const Create = async () => {
    await create_one_dynamic_form({
      bot_id: bot.bot_id,
      ...dynamicFormModalData,
    });
    setModalState(false);
    setDynamicFormModalData({
      title: "",
      schema: [
        {
          label: "",
        },
      ],
    });
  };

  const addField = () => {
    setDynamicFormModalData({
      ...dynamicFormModalData,
      schema: [
        ...dynamicFormModalData.schema,
        {
          label: "",
        },
      ],
    });
  };

  const removeField = (index) => {
    if (dynamicFormModalData.schema.length <= 1) return;
    const updatedFields = structuredClone(dynamicFormModalData.schema);
    updatedFields.splice(index, 1);
    setDynamicFormModalData({
      ...dynamicFormModalData,
      schema: updatedFields,
    });
  };

  const handleFieldChange = (index: number, value: string) => {
    const updatedFields = JSON.parse(
      JSON.stringify(dynamicFormModalData.schema)
    );
    updatedFields[index].label = value.trim();
    setDynamicFormModalData({ ...dynamicFormModalData, schema: updatedFields });
  };

  return (
    <>
      <Modal
        title="Create New Form"
        onClose={() => setModalState(false)}
        isOpen={modalState}
        actionLabel={"Create"}
        onSubmit={Create}
        disabled={!dynamicFormModalData.title}
        body={
          <div>
            <Input
              error={!dynamicFormModalData.title ? "Should not be empty" : ""}
              title="Form Title:"
              name="formTitle"
              placeholder="Enter form title"
              type="text"
              onChange={(event) =>
                setDynamicFormModalData({
                  ...dynamicFormModalData,
                  title: event.target.value,
                })
              }
              value={dynamicFormModalData.title}
            />
            <Separator className="my-4 border-t border-gray-300" />

            {dynamicFormModalData.schema.map((field, index) => (
              <div key={index}>
                <div className="flex items-center justify-between">
                  <label>{`Label ${index + 1}`}</label>
                  {(index !== 0 || dynamicFormModalData.schema.length > 1) && (
                    <MinusCircle
                      className="text-red-500"
                      onClick={() => {
                        removeField(index);
                      }}
                    />
                  )}
                </div>
                <Input
                  error={
                    duplicateLabelError &&
                    duplicateLabelError ==
                      dynamicFormModalData.schema[index].label
                      ? "Can not have duplicate labels"
                      : !dynamicFormModalData.schema[index].label
                      ? "Should not be empty"
                      : ""
                  }
                  name={`label-${index}`}
                  placeholder="Enter label"
                  type="text"
                  onChange={(event) =>
                    handleFieldChange(index, event.target.value)
                  }
                  value={field.label.trim()}
                />
                <Separator className="my-4 border-t border-gray-400" />
              </div>
            ))}
          </div>
        }
        secondaryAction={addField}
        secondaryActionLabel={"Add Field"}
      />
      <div className="space-y-3 pb-5 mt-5">
        <div className="flex items-center justify-between">
          <Input
            title="Widget Name"
            name="widgetName"
            placeholder="Widget Name"
            type="text"
            onChange={(event) => setWidgetName(event.target.value)}
            value={widgetName}
          />
        </div>
        <hr className="text-white/25 pb-5" />
        <div>
          <label>Select Form:</label>
          <Select
            disabled={!schemas.length}
            onValueChange={async (value) => {
              const form = schemas.find(
                (s) => s.dynamic_form_schema_id == +value
              );

              const handlerParams = nodeData?.data?.config[0]?.handlerParams[0];
              const isSameForm = handlerParams?.dynamic_form_schema_id === form?.dynamic_form_schema_id;

              if (form && !isSameForm) {
                setMapper({
                  dynamic_form_schema_id: +value,
                  form: form.schema.map((fs) => {
                    return {
                      ask_user: false,
                      question: "",
                      label: fs?.label || "",
                      entity: "",
                    };
                  }),
                });
              }
            }}
            value={String(mapper?.dynamic_form_schema_id)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a form">
                {schemas.find(
                  (s) =>
                    s.dynamic_form_schema_id == mapper?.dynamic_form_schema_id
                )?.title || "Select a form"}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {schemas?.map((f) => (
                <SelectItem
                  key={f.dynamic_form_schema_id}
                  value={String(f.dynamic_form_schema_id)}
                >
                  {f?.title || "No Title"}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Button onClick={() => setModalState(true)}>Create New Form</Button>
        </div>
      </div>

      {mapper?.form?.length ? (
        <div className="mb-2">
          <table className="table-auto border-collapse border border-gray-600 w-full">
            <thead>
              <tr>
                <th className="border border-gray-600 px-4 py-2">Label</th>
                <th className="border border-gray-600 px-4 py-2">Question</th>
                <th className="border border-gray-600 px-4 py-2">Ask User</th>
                <th className="border border-gray-600 px-4 py-2">Entity</th>
              </tr>
            </thead>
            <tbody>
              {mapper.form.map((row, index) => (
                <tr key={row?.label}>
                  <td className="border border-gray-600 px-4 py-2">
                    {row?.label}
                  </td>
                  <td className="border border-gray-600 px-4 py-2">
                    <Input
                      name="question"
                      type="text"
                      disabled={!row.ask_user}
                      value={
                        transformTextToLanguageObj(row?.question, false)[lang]
                      }
                      onChange={(e) => {
                        updateRow(index, "question", e.target.value);
                      }}
                      className="border rounded px-2 py-1 w-full"
                    />
                  </td>
                  <td className="border border-gray-600    ">
                    <div className=" flex justify-center">
                      <Switch
                        name={`ask_user${index}`}
                        checked={row.ask_user}
                        onChange={() => {
                          updateRow(index, "ask_user", !row.ask_user);
                        }}
                      />
                    </div>
                  </td>
                  <td className="border border-gray-600 px-4 py-2">
                    <SelectEntity
                      disable={row.ask_user}
                      entity={row.entity}
                      entities={entities}
                      globals={globals}
                      onValueChange={(value) =>
                        updateRow(index, "entity", value)
                      }
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div></div>
      )}
      <SheetFooter>
             {mapper?.dynamic_form_schema_id &&
         <AlertDialog>
         <AlertDialogTrigger asChild>
           <Button variant="outline">Delete</Button>
         </AlertDialogTrigger>
         <AlertDialogContent>
           <AlertDialogHeader>
             <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
             <AlertDialogDescription>
               This action cannot be undone. This will permanently delete your
               Form.
             </AlertDialogDescription>
           </AlertDialogHeader>
           <AlertDialogFooter>
             <AlertDialogCancel>Cancel</AlertDialogCancel>
             <AlertDialogAction onClick={deleteschema}>Continue</AlertDialogAction>
           </AlertDialogFooter>
         </AlertDialogContent>
       </AlertDialog>
  }
        <Button onClick={handleSave} type="submit" disabled={tempError}>
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(DynamicFormForm);
