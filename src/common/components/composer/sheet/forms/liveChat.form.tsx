import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useMemo, useState } from "react";
import { Node } from "reactflow";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useLiveChatStore from "store/livechat/livechat.store";
import useGenesysStore from "store/genesys/genesys.store";
import { useLangStore } from "store/language/lang.store";
import { transformTextToLanguageObj } from "helpers/multiLang";
import SelectEntity from "../selectEntity";
import { Switch } from "common/ui/inputs/switch";
import HelpHoverCard from "common/components/cards/helpHoverCard";

type IntegrationType = "liveChat integration" | "genesys integration" | "internal live chat integration";

const LiveChatSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  setEntities,
  globals
}) => {
  const { livechat, get_one_livechat } = useLiveChatStore();
  const { genesys, get_one_genesys } = useGenesysStore();
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [suggestions, setSuggestions] = useState([]);
  const [widgetName, setWidgetName] = useState("");
  const [entity, setEntity] = useState({displayName:'',phoneNumber:'',email:''});
  const [sessionData, setSessionData] = useState({endOfHandoverSession:{ar:'',en:''},noAgentAvailable:{ar:'',en:''},onHold:{ar:'',en:''}});
  const [isRatingEnabled, setIsRatingEnabled] = useState(false);
  const [ratingInput, setRatingInput] = useState({ar:'',en:''});
  const [ratingType, setRatingType] = useState<string | null>(null);
  const [includeFeedback, setIncludeFeedback] = useState(false);
  const [feedbackTrigger, setFeedbackTrigger] = useState<number>(0);
  const [feedbackMessage, setFeedbackMessage] = useState({ar:'',en:''});

  const bot = useBotStore((state) => state.bot) as IBot;
  const { lang } = useLangStore();
  const [integrationType, setIntegrationType] = useState<IntegrationType[]>(["liveChat integration" , "genesys integration", "internal live chat integration"]);
  const [selectedIntegrationType, setSelectedIntegrationType] = useState<IntegrationType>(null);

    useEffect(()=>{
      if(bot?.bot_id){
        get_one_genesys(bot?.bot_id)
        get_one_livechat(bot?.bot_id)
      }
    },[bot])

  useEffect(() => {
    const integrations: IntegrationType[] = [];
    if (genesys.genesys_integration_id) {
        integrations.push("genesys integration");
    }
    if (livechat.livechat_integration_id) {
        integrations.push("liveChat integration");
    }
    // FIXME: check for internal live chat integration
    integrations.push("internal live chat integration");
    setIntegrationType(integrations);
  }, [livechat, genesys]);

  useEffect(() => {
    if (nodeData?.data?.config) {
      const prevViewType = nodeData.data.config[0]?.handlerParams[0] || selectedIntegrationType;
      setSelectedIntegrationType(prevViewType)
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0].name);
      // setSuggestions(nodeData.data.config[0].handlerParams[0]);
      setSessionData(nodeData.data.config[0].handlerParams[1])
      setEntity(nodeData.data.config[0].handlerParams[2])
      setIsRatingEnabled(nodeData.data.config[0]?.handlerParams[3]?.ratingEnabled)
      setRatingInput(nodeData.data.config[0]?.handlerParams[3]?.ratingMsg)
      setRatingType(nodeData.data.config[0]?.handlerParams[3]?.ratingType)
      setIncludeFeedback(nodeData.data.config[0]?.handlerParams[3]?.isFeedback)
      setFeedbackTrigger(nodeData.data.config[0]?.handlerParams[3]?.triggerRating)
      setFeedbackMessage(nodeData.data.config[0]?.handlerParams[3]?.feedbackMsg)
    }
  }, [nodeData]);

  const handleRatingInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setRatingInput((prev) => ({
      ...prev,
      [lang]: value,
    }));
  };
  
  const handleFeedbackMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    setFeedbackMessage((prev) => ({
      ...prev,
      [lang]: value,
    }));
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setIsRatingEnabled(isChecked);
    setRatingType("stars");
  
    if (!isChecked) {
      setRatingInput({ar: '', en: ''}); 
      setRatingType("");
      setIncludeFeedback(false);
      setFeedbackTrigger(0);
      setFeedbackMessage({ar: '', en: ''});
    }
  }; 
  
  const handleFeedbackSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIncludeFeedback(e.target.checked);
    if (!e.target.checked) {
      setFeedbackTrigger(0); 
      setFeedbackMessage({ar: '', en: ''});
    } else {
      setFeedbackTrigger(3); 
    }
  };

  const handleSave = () => {
    const nodeToEditClone = { ...nodeDataToEdit };
    nodeToEditClone.data.config[0].handlerParams[0] = selectedIntegrationType;
    nodeToEditClone.data.config[0].name = widgetName;
    nodeToEditClone.data.config[0].handlerParams[1] = sessionData;
    nodeToEditClone.data.config[0].handlerParams[2] = entity;
  
    // Check if handlerParams[3] exists, if not, initialize it as an empty object
    if (!nodeToEditClone.data.config[0].handlerParams[3]) {
      nodeToEditClone.data.config[0].handlerParams[3] = {};
    }
  
    nodeToEditClone.data.config[0].handlerParams[3].ratingEnabled = isRatingEnabled;
    nodeToEditClone.data.config[0].handlerParams[3].ratingMsg = ratingInput;
    nodeToEditClone.data.config[0].handlerParams[3].ratingType = ratingType;
    nodeToEditClone.data.config[0].handlerParams[3].isFeedback = includeFeedback;
    nodeToEditClone.data.config[0].handlerParams[3].triggerRating = feedbackTrigger;
    nodeToEditClone.data.config[0].handlerParams[3].feedbackMsg = feedbackMessage;
  
    // Save the updated node data
    onSaveNodeData(nodeToEditClone);
  };  

  const lcPlaceholders = {
   WelcomeMessage_AR : 'سيتم التواصل معك من قبل الموظف المسؤول الرجاء الانتظار',
   WelcomeMessage_EN : "We are connecting you to an agent, please wait.",
   NoAgent_EN:"No Agent Available",
   NoAgent_AR:"الموظف المسؤول غير متاح",
   EndChat_EN:"Chat has been closed",
   EndChat_AR:"تم اغلاق المحادثة",  
  RatingMessage_EN: "How would you rate this conversation?",
  RatingMessage_AR: "كيف تقيم هذه المحادثة؟",
  FeedbackMessage_EN: "How was your chat with our agent today?",
  FeedbackMessage_AR: "كيف كانت محادثتك مع موظفنا اليوم؟"
  };

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />

<label htmlFor="integration">Integration Type</label>
        <Select
          value={selectedIntegrationType}
          onValueChange={(value) => {
            setSelectedIntegrationType(value as IntegrationType);
          }}
          name="integration"
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="select integration Type" />
          </SelectTrigger>
          <SelectContent>
            {integrationType?.map((integrationType) => {
              return (
                <>
                  <SelectItem value={integrationType}>
                    {integrationType}
                  </SelectItem>
                </>
              );
            })}
          </SelectContent>
        </Select>

        <Input
          title="No Agent Available Message"
          name="noAgentAvailable"
          placeholder= {lang === 'en' ? lcPlaceholders.NoAgent_EN : lcPlaceholders.NoAgent_AR }
          type="text"
          
          onChange={(event) => {
            setSessionData((prev)=>{
               const updated = {...prev}
               updated[event.target.name][lang] = transformTextToLanguageObj(event.target.value)[lang]
              return updated
            })
          }}
          value={sessionData?.noAgentAvailable?.[lang as 'ar' | 'en']}
        />
        
        <Input
          title="Call On Hold Message"
          name="onHold"
          placeholder= {lang === 'en' ? lcPlaceholders.WelcomeMessage_EN:lcPlaceholders.WelcomeMessage_AR }
          type="text"
          onChange={(event) => {
            setSessionData((prev)=>{
               const updated = {...prev}
               updated[event.target.name][lang] = transformTextToLanguageObj(event.target.value)[lang]
              return updated
            })
          }}
          value={sessionData?.onHold?.[lang as 'ar' | 'en']}
        />
        
        <Input
          title="End Of Conversation Message"
          name="endOfHandoverSession"
          placeholder= {lang === 'en' ? lcPlaceholders.EndChat_EN:lcPlaceholders.EndChat_AR}

          type="text"
          onChange={(event) => {
            setSessionData((prev)=>{
              const updated = {...prev}
              updated[event.target.name][lang] = transformTextToLanguageObj(event.target.value)[lang]
             return updated
           })
          }}
          value={sessionData?.endOfHandoverSession?.[lang as 'ar' | 'en']}
        />


        
         <div className="my-2">
        <p>User Details Enities</p>
        <div className="flex items-center justify-center gap-2 w-full">          
        <div className="w-1/3">
        <p >Full Name</p>
        <SelectEntity 
          entities={entities}
          onValueChange={(value) => {
            setEntity((prev)=>{
              return {
                ...prev,
                displayName:value
              }
            });
          }}
          entity={entity?.displayName}
          globals={globals}
          />
          </div>
          <div className="w-1/3">
          <p >Email</p>
        <SelectEntity 
          entities={entities}
          onValueChange={(value) => {
            setEntity((prev)=>{
              return {
                ...prev,
                email:value
              }
            });
          }}
          entity={entity?.email}
          globals={globals}
          />
          </div>
          <div className="w-1/3">
          <p >Phone Number</p>
        <SelectEntity 
          entities={entities}
          onValueChange={(value) => {
            setEntity((prev)=>{
              return {
                ...prev,
                phoneNumber:value
              }
            });
          }}
          entity={entity?.phoneNumber}
          globals={globals}
          />
          </div>
      </div>
        <div>
        {selectedIntegrationType === "internal live chat integration" && (
          <div className="my-4">
            <Switch
              label="Enable End of Session Ratings"
              name='ratingEnabled'
              checked={isRatingEnabled}
              onChange={handleSwitchChange}
            />
          </div>
        )}
        {isRatingEnabled && (
          <>
          <Input
            title="Rating Message"
            name="ratingMsg"
            placeholder= {lang == 'ar' ? lcPlaceholders.RatingMessage_AR : lcPlaceholders.RatingMessage_EN}
            type="text"
            onChange={handleRatingInputChange}
            value={ratingInput?.[lang as 'as' | 'en']}
          />
          <div className="mt-4">
            <p>Rating Type</p>
            <Select
              value={ratingType}
              onValueChange={(value) => setRatingType(value)}
              name="ratingType"
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="select rating type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="stars">Stars</SelectItem>
                <SelectItem value="emojis">Emojis</SelectItem>
                <SelectItem value="text">Text</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4">
            <Switch
              label="Include Additional Feedback"
              checked={includeFeedback}
              onChange={handleFeedbackSwitchChange}
              name="isFeedback"
            />
          </div>
          {includeFeedback && (
            <div className="mt-4">
              <div className="flex items-center gap-2">
                <span>Feedback Trigger Rating</span>
                <HelpHoverCard>
                <p>
                  Select the rating value (1-5) that will trigger the display of the additional feedback text area. 
                  For example, setting this to &apos;3&apos; will prompt users to provide feedback when they rate the conversation 3 stars or lower.
                </p>
                </HelpHoverCard>
              </div>
              <Select
                value={feedbackTrigger.toString()}
                onValueChange={(value) => setFeedbackTrigger(Number(value))}
                name="triggerRating"
              >
                <SelectTrigger className="w-full mt-2">
                  <SelectValue placeholder="Select Rating Trigger" />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                title="Feedback Message"
                name="feedbackMsg"
                placeholder= {lang == 'ar' ? lcPlaceholders.FeedbackMessage_AR : lcPlaceholders.FeedbackMessage_EN}
                type="text"
                onChange={handleFeedbackMessageChange}
                value={feedbackMessage?.[lang as 'as' | 'en']}
              />
            </div>
          )}
        </>
        )}
      </div>
      </div>
      </div>
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(LiveChatSheet);
