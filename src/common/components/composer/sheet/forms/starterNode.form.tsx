import { getTriggers } from "apis/dialog.api";
import { <PERSON><PERSON> } from "common/ui/button";
import { <PERSON><PERSON><PERSON>oot<PERSON> } from "common/ui/sheet";
import { useLoaderContext } from "context/loaderContext";
import { Plus, X } from "lucide-react";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import useTriggerStore from "store/trigger/trigger.store";
import useUserStore from "store/user/user.store";
import { useLangStore } from "store/language/lang.store";
import { isArabic } from "helpers/helper";
import { ITrigger } from "store/trigger/trigger.types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "common/ui/alertDialog";
import HelpHoverCard from "common/components/cards/helpHoverCard";

interface EditableTrigger extends ITrigger {
  saved?: boolean;
  edited?: boolean;
  languageMismatchWarning?: boolean;
}

const StarterNodeForm: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  dialog,
  dialogTriggers,
  setDialogTriggers,
}) => {
  const [triggersToEdit, setTriggersToEdit] = useState<EditableTrigger[]>([]);
  const [trToDelete, setTrToDelete] = useState<ITrigger>(null);
  const user_id = useUserStore((state) => state.user).user_id;
  const {
    update_many_triggers,
    create_many_triggers,
    get_all_triggers,
    delete_one_trigger,
  } = useTriggerStore();
  const triggerContainerRef = useRef<HTMLDivElement>(null); 
  const [loading, setLoading] = useState(false);
  const { loader } = useLoaderContext();

  useEffect(() => {
    if (dialogTriggers)
      setTriggersToEdit(
        filterBasedOnLang(JSON.parse(JSON.stringify(dialogTriggers)))
      );
  }, [dialogTriggers]);

  useEffect(() => {
    get_all_triggers(dialog.bot_id);
  }, []);

  useEffect(() => {
    console.log(triggersToEdit);
    if (triggerContainerRef.current) {
      triggerContainerRef.current.scrollTop = triggerContainerRef.current.scrollHeight;
    }
  }, [triggersToEdit]); 

  const handleSave = async () => {
    if (loading) return;

    setLoading(true);
    loader?.continuousStart();

    const triggersToCreate = [];
    const triggersToUpdate = [];

    triggersToEdit.forEach((tr) => {
      if (!tr.trigger_id) {
        triggersToCreate.push(tr);
      } else if (tr.edited && tr.saved && tr.trigger_id) {
        triggersToUpdate.push(tr);
      }
    });

    if (triggersToUpdate.length) {
      await update_many_triggers(triggersToUpdate);
    }
    if (triggersToCreate.length) {
      await create_many_triggers(triggersToCreate);
    }
    const ndt = await getTriggers(dialog.dialog_id);
    setTriggersToEdit(ndt);
    setDialogTriggers(ndt);
    setTrToDelete(null);
    onSaveNodeData(nodeData);
    setLoading(false);
    loader?.complete();
  };

  const { lang } = useLangStore();

  const filterBasedOnLang = (trgs: ITrigger[]) => {
    const isCurrLangAr = lang === "ar";
    return trgs.filter((tr) => {
      if (isCurrLangAr && isArabic(tr.trigger)) return tr;
      if (!isCurrLangAr && !isArabic(tr.trigger)) return tr;
    });
  };

  return (
    <>
      <div className="space-y-3 mb-5">
        <span>This dialog will be triggered by:</span>
        <br />
        <div className="flex items-center gap-2">
          <span className="text-gray-500"> Only triggers of your specified language will be saved.</span>
          <HelpHoverCard>Check the box next to the input to make it appear on the suggestions buttons when there is a conflict</HelpHoverCard>
        </div>
        <div className="space-y-2">
          <div className={`flex justify-end items-center`}>
            <Button
              onClick={() => {
                const trCopy = [...triggersToEdit];
                trCopy.push({
                  trigger: "",
                  saved: false,
                  edited: true,
                  trigger_name: dialog.dialog_name,
                  trigger_type: "dialog",
                  url: dialog.url,
                  is_main: false,
                  bot_id: dialog.bot_id,
                  lemmatized_trigger: "",
                  dialog_id: dialog.dialog_id,
                } as any);
                setTriggersToEdit(trCopy);
              }}
              className={`rounded-full w-5 h-5 p-1 `}
            >
              <Plus />
            </Button>
          </div>
          <div ref={triggerContainerRef} className="max-h-56 overflow-y-auto p-2 border-2 border-gray-300 border-dashed rounded h-56">
            <div className="space-y-1 py-2">
              {triggersToEdit.map((tr, i) => {
                return (
                  <div key={i} className="flex flex-col gap-3">
                    <div className="flex items-center gap-3">
                      <textarea
                        id="myInput"
                        placeholder="Write a Trigger..."
                        tabIndex={0}
                        dir="auto"
                        value={tr.trigger}
                        className={`rounded bg-transparent border border-white/25 h-full break-words w-full p-3 text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 ${"!border-primary"}`}
                        rows={1}
                        onChange={(e) => {
                          const trCopy = [...triggersToEdit];
                          const newTrigger = e.target.value;
                          trCopy[i].trigger = newTrigger;
                          trCopy[i].lemmatized_trigger = isArabic(newTrigger) ? newTrigger : "";
                          if (trCopy[i].trigger_id) {
                            trCopy[i].edited = true;
                            trCopy[i].saved = true;
                          }

                          const isArabicTrigger = isArabic(newTrigger);
                          const isCurrLangAr = lang === "ar";
                          const isMismatch = (isCurrLangAr && !isArabicTrigger && newTrigger.length > 0) || (!isCurrLangAr && isArabicTrigger && newTrigger.length > 0);
                          trCopy[i].languageMismatchWarning = isMismatch;

                          setTriggersToEdit(trCopy);
                        }}
                      ></textarea>
                      <input
                        title="checkbox"
                        className="w-4 h-4 rounded-full text-primary bg-gray-100 border-gray-300 focus:ring-purple-500 dark:focus:ring-purple-500 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        checked={tr.is_main || false}
                        type="checkbox"
                        onChange={(e) => {
                          const trCopy = JSON.parse(JSON.stringify(triggersToEdit));
                          trCopy.forEach((tr) => {
                            tr.edited = true;
                            if (tr.trigger_id) {
                              tr.saved = true;
                            }
                            tr.is_main = false;
                          });

                          trCopy[i].is_main = e.target.checked;
                          if (trCopy[i].trigger_id) {
                            trCopy[i].edited = true;
                            trCopy[i].saved = true;
                          }

                          setTriggersToEdit(trCopy);
                        }}
                      />
                      {trToDelete && tr.trigger === trToDelete.trigger ? (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <X
                              size={15}
                              className=" bg-red-500 rounded-full cursor-pointer"
                            />
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Are you sure you want to delete this?
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={async () => {
                                  await delete_one_trigger({
                                    trigger_id: trToDelete.trigger_id,
                                    user_id,
                                  });
                                  const ndt = await getTriggers(dialog.dialog_id);
                                  setTriggersToEdit(ndt);
                                  setDialogTriggers(ndt);
                                  setTrToDelete(null);
                                }}
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      ) : (
                        <X
                          onClick={() => {
                            if (tr.trigger_id) {
                              setTrToDelete(tr);
                            } else {
                              setTriggersToEdit(
                                triggersToEdit.filter((_, index) => index !== i)
                              );
                            }
                          }}
                          size={15}
                          className=" bg-red-500 rounded-full cursor-pointer"
                        />
                      )}
                    </div>
                    {tr.languageMismatchWarning && (
                      isArabic(tr.trigger) ? (
                        <div className="p-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 text-sm">
                          <div className="font-bold">Warning: Language Mismatch!</div>
                          <div>If you proceed to save this trigger its content will be saved with Arabic triggers.</div>
                        </div>
                      ) : (
                        <div className="p-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 text-sm">
                          <div className="font-bold">Warning: Language Mismatch!</div>
                          <div>If you proceed to save this trigger its content will be saved with English triggers.</div>
                        </div>
                      )
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      <SheetFooter>
        <Button
          disabled={loading}
          onClick={handleSave}
          type="submit"
          loading={loading}
        >
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(StarterNodeForm);
