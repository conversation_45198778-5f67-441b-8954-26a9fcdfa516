import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { <PERSON><PERSON><PERSON>ooter } from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { Node } from "reactflow";
import useMultiLanguage from "common/hooks/useMultiLanguage";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";


const PromptTextSheet: IPromptForm = ({
  nodeData,
  onSaveNodeData,
  entities,
  globals
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [text, setText] = useState("");
  const [widgetName, setWidgetName] = useState("");
  const voiceDataRef = useRef({
    voice_path_male: "",
    voice_path_female: "",
  });
  const { result, value, setValue } = useMultiLanguage(text);
  const [showModal, setShowModal] = useState(false);

  const nodeConfig = useMemo(() => {
    return nodeData?.data?.config[0]
      ? nodeData?.data?.config[0]
      : nodeData?.data?.config;
  }, [nodeData]);

  useEffect(() => {

    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setText(nodeConfig.handlerParams[0]);
      setWidgetName(nodeConfig.name);
      voiceDataRef.current = nodeConfig.handlerParams[1];

    }
  }, [nodeData]);


  const updateTextToSend = (value) => {
    setText(value);
  };

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };
    const config = nodeDataToEditClone.data.config[0]
      ? nodeDataToEditClone.data.config[0]
      : nodeDataToEditClone.data.config;
    config.handlerParams[0] = result;
    config.handlerParams[1] = {voice_path_male:voiceDataRef.current?.voice_path_male || '',voice_path_female:voiceDataRef.current?.voice_path_female || ''};
    nodeDataToEditClone.data.config[0].handlerParams[1] = {voice_path_male:voiceDataRef.current?.voice_path_male || '',voice_path_female:voiceDataRef.current?.voice_path_female || ''};
    config.name = widgetName;
    onSaveNodeData({
      ...nodeDataToEditClone,
    });
  };



const handleUpdateVoice = (data: TVoiceBody) => {
  const updatedVoices = data;
  delete updatedVoices.bot_id;
  delete updatedVoices.user_id;
  voiceDataRef.current = {...voiceDataRef.current,...updatedVoices};
};

  return (
    <>
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <VoiceModalWrapper
       handleSave={handleSave}
       handleUpdateVoice={handleUpdateVoice}
       setShowModal={setShowModal}
       showModal={showModal}
       text={value}
       voice_path_female={nodeConfig.handlerParams[1]?.voice_path_female || ''}     
       voice_path_male={nodeConfig.handlerParams[1]?.voice_path_male || ''} 
         />
        
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <div className="space-y-1">
          <label htmlFor="text" className="flex gap-5 items-end">Text
          <small className="text-white/50">
            - Characters Limit: WhatsApp: 4096, Facebook: 2000

          </small>
          </label>
          <Textarea
            placeholder="Write your text to send to the user, Use <br /> to make a new line"
            onChange={(event) => {
              setValue(event.target.value);
            }}
            className="h-64 px-3 py-2 "
            value={value}
            entities={entities}
            globals={globals}
          />
          <small className="float-right text-white/50">
            {value?.length || 0} characters
          </small>
        </div>
      </div>
      <br />
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(PromptTextSheet);
