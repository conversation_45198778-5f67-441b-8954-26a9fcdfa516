import { Input } from "common/ui/inputs/input";
import { IPromptForm } from "types/composer.types";
import { FC, memo, useEffect, useState } from "react";
import { Expand, Minimize } from "lucide-react";
import { Node } from "reactflow";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { <PERSON>et<PERSON>ooter } from "common/ui/sheet";
import { Button } from "common/ui/button";
import { Slider } from "common/ui/slider";
import { Checkbox } from "common/ui/inputs/checkbox";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";
import HelpHoverCard from "common/components/cards/helpHoverCard";
import SelectEntity from "../selectEntity";
import Editor from "@monaco-editor/react";

const StoreLookupForm: IPromptForm = ({
  nodeData,
  entities,
  onSaveNodeData,
  globals,
  onExpandChange
}) => {
  const [nodeDataToEdit, setNodeDataToEdit] = useState<Node | null>(null);
  const [widgetName, setWidgetName] = useState("");
  const [sourceType, setsourceType] = useState<"user_question" | "entity">(
    "entity"
  );
  const [searchStoreEntity, setSearchStoreEntity] = useState("");
  const [searchIndex, setsearchIndex] = useState("");
  const [searchEntity, setsearchEntity] = useState("");
  const [result, setResult] = useState("");
  const [searchType, setSearchType] = useState("exact");
  const [threshold, setThreshold] = useState(0.7);
  const [preprocessText, setPreprocessText] = useState(false);
  const [code, setCode] = useState<string>("");
  const [expandAce, setExpandAce] = useState(false);
  const searchTypes = ["fuzzy", "exact", "include", "similarity", "custom"];

  useEffect(() => {
    if (nodeData?.data?.config) {
      setNodeDataToEdit(nodeData);
      setWidgetName(nodeData.data.config[0]?.name);
      setsourceType(
        nodeData.data.config[0]?.handlerParams[0].sourceType || "entity"
      );
      setSearchStoreEntity(
        nodeData.data.config[0]?.handlerParams[0].searchStoreEntity
      );
      setsearchEntity(nodeData.data.config[0]?.handlerParams[0].searchEntity);
      setsearchIndex(nodeData.data.config[0]?.handlerParams[0].searchIndex);
      setResult(nodeData.data.config[0]?.handlerParams[0].entity);
      setSearchType(nodeData.data.config[0]?.handlerParams[0].searchType);
      setThreshold(nodeData.data.config[0]?.handlerParams[0].threshold);
      setPreprocessText(
        nodeData.data.config[0]?.handlerParams[0].preprocessText
      );
      setCode(nodeData.data.config[0]?.handlerParams[0].code);
    }
  }, [nodeData]);

  const handleSourceTypeChange = (value) => {
    if (value === "user_question") {
      setsearchEntity("");
    }
    setsourceType(value);
  };

  const handleSearchTypeChange = (value) => {
    if (value !== "custom") {
      setCode("");
    } else {
      setsearchIndex("");
    }
    setSearchType(value);
  };

  const handleSave = () => {
    const nodeDataToEditClone = { ...nodeDataToEdit };

    nodeDataToEditClone.data.config[0].handlerParams[0].searchStoreEntity =
      searchStoreEntity;
    nodeDataToEditClone.data.config[0].handlerParams[0].searchEntity =
      searchEntity;
    nodeDataToEditClone.data.config[0].handlerParams[0].searchIndex =
      searchIndex;
    nodeDataToEditClone.data.config[0].handlerParams[0].entity = result;
    nodeDataToEditClone.data.config[0].handlerParams[0].searchType = searchType;
    nodeDataToEditClone.data.config[0].handlerParams[0].threshold = threshold;
    nodeDataToEditClone.data.config[0].handlerParams[0].preprocessText =
      preprocessText;
    nodeDataToEditClone.data.config[0].name = widgetName;
    nodeDataToEditClone.data.config[0].handlerParams[0].sourceType = sourceType;
    nodeDataToEditClone.data.config[0].handlerParams[0].code = code;

    onSaveNodeData(nodeDataToEditClone);
  };

  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(expandAce);
    }
  }, [expandAce, onExpandChange]);

  return (
    <>
    {!expandAce ? (
      <div className="flex flex-col gap-4 py-10 min-h-[400px]">
        <Input
          title="Widget Name"
          name="widgetName"
          placeholder="Enter widget name"
          type="text"
          onChange={(event) => {
            setWidgetName(event.target.value);
          }}
          value={widgetName}
        />
        <hr className="text-white/25" />
        <div className="flex flex-col gap-2">
          <div className="flex w-full gap-2">
            <div className="grow flex flex-col gap-1">
              <label className="flex items-center gap-2">
                Source Type
                <HelpHoverCard>
                  <div>
                    Choose the source of the data you want to search for.
                    <br />
                    <small className="leading-3 tracking-tighter text-amber-500">
                      This would be either the user&apos;s question (that
                      triggered the dialog) or an entity that you have stored in
                      another block.
                    </small>
                  </div>
                </HelpHoverCard>
              </label>
              <Select
                onValueChange={(value) => {
                  handleSourceTypeChange(value);
                }}
                value={sourceType}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select an entity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user_question">User Question</SelectItem>
                  <SelectItem value="entity">Entity</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {sourceType === "entity" && (
              <div className="grow flex flex-col gap-1">
                <label className="flex items-center gap-2">
                  User Entity
                  <HelpHoverCard>
                    <div>
                      Choose the entity that has the information you want to
                      search for.
                    </div>
                  </HelpHoverCard>
                </label>
                <SelectEntity
                  entity={searchEntity}
                  onValueChange={(value) => {
                    setsearchEntity(value);
                  }}
                  entities={entities}
                  globals={globals}
                />
              </div>
            )}
          </div>
          <label className="flex gap-2 items-center">
            Entity to get data from
            <HelpHoverCard>
              <div>
                Choose the entity that has the data you want to search in.
                <br />
                <small className="leading-3 tracking-tighter text-amber-500">
                  This would be data that you have stored in the Create Store or
                  in JSON API block.
                </small>
              </div>
            </HelpHoverCard>
          </label>
          <SelectEntity
            entity={searchStoreEntity}
            onValueChange={(value) => {
              setSearchStoreEntity(value);
            }}
            entities={entities}
            globals={globals}
          />
          {searchType !== "custom" && (
            <div className="flex gap-2 items-center">
              <Input
                title={
                  <label className="flex gap-2 items-center">
                    Key to search by
                    <HelpHoverCard>
                      <div>
                        Type the object key in the data that you want the search
                        to be done on.
                        <br />
                        <small className="leading-3 tracking-tighter text-amber-500">
                          Example: title or tasks.title
                        </small>
                      </div>
                    </HelpHoverCard>
                  </label>
                }
                name="index"
                placeholder="Enter key"
                type="text"
                onChange={(event) => {
                  setsearchIndex(event.target.value);
                }}
                value={searchIndex}
              />
            </div>
          )}
          <label className="flex items-center gap-2">
            Result Entity
            <HelpHoverCard>
              <div>
                Choose the entity that will store the result of the search.
              </div>
            </HelpHoverCard>
          </label>
          <SelectEntity
            entity={result}
            onValueChange={(value) => {
              setResult(value);
            }}
            entities={entities}
            globals={globals}
          />
          <hr className="text-white/25" />
          <div className="flex gap-5 items-center">
            <label className="whitespace-nowrap " htmlFor="entity4">
              Search Type
            </label>
            <Select
              onValueChange={(value) => {
                handleSearchTypeChange(value);
              }}
              value={searchType}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a search type" />
              </SelectTrigger>
              <SelectContent>
                {searchTypes.map((type, i) => {
                  return (
                    <SelectItem className="capitalize" key={i} value={type}>
                      {type}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          {searchType === "similarity" && (
            <div className="flex flex-col gap-2">
              <label htmlFor="sim" className="flex justify-between">
                Similarity Threshold
                <span>{threshold}</span>
              </label>
              <Slider
                defaultValue={[threshold * 100]}
                max={100}
                min={10}
                step={10}
                onValueChange={(value) => {
                  setThreshold(value[0] / 100);
                }}
              />
            </div>
          )}
          {searchType === "custom" && (
            <>
              <div className="text-sm flex items-center gap-2">
                Write your own search function.
                <HelpHoverCard>
                  <div>
                    This function should use and modify the{" "}
                    <span className="text-amber-500 font-bold">entities</span>{" "}
                    object where you can access your saved entities.
                    <br />
                    you can use the{" "}
                    <span className="text-amber-500 font-bold">
                      incomingText
                    </span>{" "}
                    variable to get the user&apos;s input if you choose the user
                    question source type.
                    <br />
                    <small className="leading-3 tracking-tighter text-amber-500">
                      Example: <br />
                      <code className="text-xs bg-accent">
                        {`
                            const result = entities.data.find((item) => {item.title === incomingText})
                            entities.result = result
                            `}
                      </code>
                    </small>
                  </div>
                </HelpHoverCard>
              </div>

              <div className="relative h-[30rem] w-full">
                <Expand
                onClick={() => setExpandAce(!expandAce)}
                className="absolute right-0 top-0 z-[99999] hover:scale-105"
                />
                <Editor
                  defaultLanguage="javascript"
                  defaultValue=""
                  theme="vs-dark"
                  onChange={(value) => setCode(value)}
                  value={code}
                />
              </div>
            </>
          )}
          {searchType !== "custom" && (
            <>
              <hr className="text-white/25" />
              <HoverCard>
                <HoverCardTrigger asChild>
                  <div>
                    <Checkbox
                      name="preprocessText"
                      checked={preprocessText}
                      onChange={(event) => {
                        setPreprocessText(event.target.checked);
                      }}
                      label="Preprocess Text"
                    />
                  </div>
                </HoverCardTrigger>
                <HoverCardContent className="w-80 bg-black border-2 border-gray-300 shadow-xl">
                  <div>
                    This will preprocess the arabic text before searching.
                    Getting the words into roots.
                  </div>
                </HoverCardContent>
              </HoverCard>
            </>
          )}
        </div>
      </div>
    ) : (
      <>
        <div className="relative h-[50rem] w-full my-5">
            <Minimize
              onClick={() => setExpandAce(!expandAce)}
              className="absolute right-0 top-0 z-[99999] hover:scale-105"
            />

            <div className="relative h-[50rem] w-full">
              <Editor
                defaultLanguage="javascript"
                defaultValue=""
                theme="vs-dark"
                onChange={(value) => setCode(value)}
                value={code}
              />
            </div>
          </div>
      </>
    )}
      <SheetFooter>
        <Button onClick={handleSave} type="submit">
          Save changes
        </Button>
      </SheetFooter>
    </>
  );
};

export default memo(StoreLookupForm);
