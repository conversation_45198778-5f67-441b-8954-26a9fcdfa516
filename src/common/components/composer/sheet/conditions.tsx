import {
  Select,
  SelectContent,
  <PERSON>I<PERSON>,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { PlusCircle, Trash2 } from "lucide-react";
import React, { FC, memo } from "react";
import validators from "data/composer/validators";
import { Input } from "common/ui/inputs/input";

interface ConditionsProps {
  conditions: any[];
  addCondition: () => void;
  setConditions: (conditions: any[]) => void;
}

const Conditions: FC<ConditionsProps> = ({
  conditions,
  addCondition,
  setConditions,
}) => {
  const handleDelete = (connector) => {
    var conditionsClone = [...conditions];
    conditionsClone = conditionsClone.filter(
      (c) => c.connector_id !== connector.connector_id
    );
    setConditions(conditionsClone);
  };

  const handleSelect = (value, connector) => {
    const conditionsClone = [...conditions];
    const index = conditionsClone.findIndex(
      (c) => c.connector_id === connector.connector_id
    );
    conditionsClone[index] = {
      ...validators.find((c) => c.criteria === value),
      connector_id: conditionsClone[index].connector_id,
      redirect: conditionsClone[index].redirect,
    };

    setConditions(conditionsClone);
  };

  const handleValueChange = (event, connector) => {
    const conditionsClone = [...conditions];
    const index = conditionsClone.findIndex(
      (c) => c.connector_id === connector.connector_id
    );
    conditionsClone[index] = {
      ...conditionsClone[index],
      value: event.target.value,
    };
    setConditions(conditionsClone);
  };

  return (
    <>
      <div className="flex justify-between items-center pt-3">
        Add Condition
        <PlusCircle
          className="cursor-pointer hover:text-primary"
          onClick={addCondition}
        />
      </div>
      <hr className="text-white/25" />
      <div className="flex flex-col gap-1 max-h-[200px] overflow-y-auto">
        {conditions &&
          conditions
            ?.filter((a) => a.criteria !== "default")
            .map((connector, i) => (
              <div className="space-y-0 scale-90" key={i}>
                <div className="p-2 flex justify-between items-center bg-secondary rounded">
                  <Select
                    value={connector.criteria}
                    onValueChange={(value) => handleSelect(value, connector)}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select a condition" />
                    </SelectTrigger>
                    <SelectContent>
                      {validators?.map((validator, i) => {
                        return (
                          <SelectItem key={i} value={validator.criteria}>
                            {validator.title}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <Trash2
                    onClick={() => handleDelete(connector)}
                    size={17}
                    className="mr-2 text-red-500 hover:text-red-800 cursor-pointer"
                  />
                </div>
                <div className="bg-secondary/50 p-3 pt-2 text-center">
                  {connector.value !== undefined ? (
                    <Input
                      name="value"
                      value={connector.value}
                      placeholder="value"
                      onChange={(event) => handleValueChange(event, connector)}
                    />
                  ) : (
                    <span className="capitalize">{connector.title} added</span>
                  )}
                </div>
              </div>
            ))}
      </div>
    </>
  );
};

export default memo(Conditions);
