import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface SelectEntityProps {
  entities: Record<string, string>;
  entity: string;
  globals: Record<string, string>;
  onValueChange: (value: string) => void;
  width?: string;
  disable?: boolean;
}

const SelectEntity: FC<SelectEntityProps> = ({
  entities,
  onValueChange,
  entity,
  globals,
  width,
  disable,
}) => {
  return (
    <Select value={entity} onValueChange={onValueChange} disabled={disable}>
      <SelectTrigger className={`${width ? width : "w-full"}`}>
        <SelectValue placeholder="Select an entity" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Entities</SelectLabel>
          {Object.keys(entities).length ? (
            Object.keys(entities).map((entity, i) => {
              return (
                <SelectItem key={i} value={entity}>
                  {entity}
                </SelectItem>
              );
            })
          ) : (
            <small className="pl-2">
              There are no entities yet. Please create one first.
            </small>
          )}
        </SelectGroup>
        <SelectGroup>
          <SelectLabel>Globals</SelectLabel>
          {Object.keys(globals).length ? (
            Object.keys(globals).map((global, i) => {
              return (
                <SelectItem key={i} value={`globals.${global}`}>
                  {global}
                </SelectItem>
              );
            })
          ) : (
            <small className="pl-2">
              There are no entities yet. Please create one first.
            </small>
          )}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default memo(SelectEntity);
