import { FC, memo, useCallback, useEffect, useMemo, useState } from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";
import { IPromptForm } from "types/composer.types";
import { Node } from "reactflow";
import { IDialog } from "store/dialog/dialog.types";
import { ITrigger } from "store/trigger/trigger.types";
import BlockFactory from "lib/Factory/BlockFactory";

interface indexProps {
  selectedNode: Node | null;
  setSelectedNode: (node: Node | null) => void;
  onSaveNodeData: (node: Node) => void;
  entities: Record<string, string>;
  setEntities: (entities: Record<string, string>) => void;
  nodes: Node[];
  dialog: IDialog;
  dialogTriggers: ITrigger[];
  setDialogTriggers: (triggers: ITrigger[]) => void;
  globals: Record<string, string>;
}

const Index: FC<indexProps> = ({
  selectedNode,
  setSelectedNode,
  onSaveNodeData,
  entities,
  setEntities,
  nodes,
  dialog,
  dialogTriggers,
  setDialogTriggers,
  globals
}) => {
  const [expand, setExpand] = useState(false);
  const open = useMemo(() => Boolean(selectedNode), [selectedNode]);

  const PromptForm: IPromptForm = useMemo(
    () =>
      BlockFactory.createBlock(
        selectedNode.data.block.block_type
      ).getFrom(),
    [selectedNode]
  );

  const closeSheet = useCallback(
    () => setSelectedNode(null),
    [setSelectedNode]
  );

  const handleExpandChange = useCallback((newExpandState: boolean) => {
    setExpand(newExpandState);
  }, []);

  return (
    <Sheet  open={open} onOpenChange={closeSheet}>
      <SheetContent
        size={
          (
            selectedNode.data.label === "JS Code" 
            || selectedNode.data.label === "Dynamic Table" 
            || selectedNode.data.label === "Create Store"
            || selectedNode.data.label === "Store Lookup"
            || selectedNode.data.label === "JSON API"
          )
          && expand
          ? "xl"
          : "sm"
        }
        className={
          (
            selectedNode.data.label === "JS Code" 
            || selectedNode.data.label === "Dynamic Table" 
            || selectedNode.data.label === "Create Store"
            || selectedNode.data.label === "Store Lookup"
            || selectedNode.data.label === "JSON API"
          )
          && expand
          ? "text-white overflow-auto"
          : "text-white overflow-auto w-[40rem]"
        }
      >
        <SheetHeader>
          <SheetTitle className="flex gap-2 items-center">
            <selectedNode.data.block.icon
              color={selectedNode.data.block.color}
            />
            {selectedNode.data.block.block_type.replaceAll("_", " ")}
          </SheetTitle>
          <SheetDescription>
            {selectedNode.data.block.description}
          </SheetDescription>
        </SheetHeader>
        <PromptForm
          nodeData={selectedNode}
          onSaveNodeData={onSaveNodeData}
          dialog={dialog}
          setEntities={setEntities}
          entities={entities}
          nodes={nodes}
          dialogTriggers={dialogTriggers}
          setDialogTriggers={setDialogTriggers}
          globals={globals}
          onExpandChange={
            selectedNode.data.label === "JS Code" 
            || selectedNode.data.label === "Dynamic Table" 
            || selectedNode.data.label === "Create Store" 
            || selectedNode.data.label === "Store Lookup" 
            || selectedNode.data.label === "JSON API"
            ? handleExpandChange : undefined}
        />
      </SheetContent>
    </Sheet>
  );
};

export default memo(Index);