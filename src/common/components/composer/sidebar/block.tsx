import { use<PERSON><PERSON>back, FC } from "react";
import { IBlock } from "types/composer.types";
import { InfoIcon } from "lucide-react";
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from "common/ui/hoverCard";

interface Props {
    block: IBlock
}

const Block: FC<Props> = ({
    block
}) => {

    const onDragStart = useCallback((event) => {
        event.dataTransfer.setData("application/reactflow", block.block_type);
        event.dataTransfer.effectAllowed = "move";
    }, [block]);

    return (
        <div
            onDragStart={onDragStart}
            className="w-full grid grid-rows-1 gap-1 bg-themeSecondary rounded-md p-2 py-3 text-white shadow-xl border-2 border-l-8 border-black cursor-grab"
            style={{ borderLeftColor: block.color }}
            draggable
        >
            <div className="flex justify-between">
                <div className="flex items-center">
                    <block.icon
                        color={block.color}
                    />
                    <span className="ml-2 capitalize">{block.block_type.replaceAll("_"," ")}</span>
                </div>
                <HoverCard>
                    <HoverCardTrigger asChild>
                        <InfoIcon
                            color={block.color}
                            className="cursor-pointer"
                        />
                    </HoverCardTrigger>
                    <HoverCardContent align="start"  className="w-80 bg-black border-2 border-gray-800 shadow-xl">
                        <div className="flex justify-between space-x-8">
                        
                            <div className="space-y-1">
                                <h4 className="text-md  mb-4 flex felx-inline items-center">
                                    <span className="p-2 rounded-2xl bg-gray-800 ">
                                    <block.icon
                                    color={block.color}
                                />
                                    </span>
                                
                                  <span className="ml-2 font-bold"> {block.block_type}</span> 
                                </h4>
                                <p className="text-sm">
                                    {block.description}
                                </p>
                             
                            </div>
                        </div>
                    </HoverCardContent>
                </HoverCard>
            </div>
        </div>


    );
};

export default Block;