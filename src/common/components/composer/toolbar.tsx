import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  <PERSON><PERSON>r,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarShortcut,
  MenubarSub,
  MenubarSubContent,
  MenubarSubTrigger,
  MenubarTrigger,
} from "common/ui/menubar";
import { Button } from "common/ui/button";
import { FileWarningIcon, RocketIcon, SettingsIcon, Trash2, X } from "lucide-react";
import useDialogStore from "store/dialog/dialog.store";
import { createOne, updateOne } from "apis/dialog.api";
import generateStorageId from "helpers/generateStorageId";
import { uploadFile } from "apis/file.api";
import useBotStore from "store/bot/bot.store";
import { IDialog, IDialogExpiryConfig } from "store/dialog/dialog.types";
import views from "views";
import useConfirmModal from "common/hooks/useConfirmModal";
import { ZoomInOut } from "reactflow";

import { useLangStore } from "store/language/lang.store";
import { FullModal } from "../modals";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Textarea } from "common/ui/inputs/textarea";
import useMultiLanguage from "common/hooks/useMultiLanguage";
import { changeIsLive, changeIsStaging, deleteDVC } from "apis/dialogversioncontrol.api";
import toast from "react-hot-toast";
import { ITrigger } from "store/trigger/trigger.types";

interface Props {
  onUpdate: () => void;
  onPublish: () => void;
  onDuplicate: () => void;
  onPublishLoadings: boolean;
  onPublishChanges: number;
  onUpdateChanges: number;
  dialog: IDialog;
  setDialog: (dialog: IDialog) => void;
  zoomIn: ZoomInOut;
  zoomOut: ZoomInOut;
  zoomToContent: () => void;
  resetZoom: () => void;
  isExpiry: boolean;
  setIsExpiry: (value: boolean) => void;
  dialogSettings: IDialogExpiryConfig;
  setDialogSettings: (value: IDialogExpiryConfig) => void;
  setOnPublishChanges: (value: number) => void;
  setOnUpdateChanges: (value: number) => void;
  setPreviewVersions: (value: boolean) => void;
  previewVersions: boolean;
  DVCToShow: IDialog;
  setDVCToShow: (dialog: IDialog) => void;
  live: boolean;
  setLive: (value: boolean) =>void;
  staging: boolean;
  setStaging: (value: boolean) =>void;
  refresh: boolean;
  setRefresh: (value: boolean) =>void;
  dialogTriggers: ITrigger[];
  onDuplicateDialog: boolean
  setOnDuplicateDialog: (value: boolean) =>void;
  dialogToduplicateName: string;
  setDialogToduplicateName: (value: string) =>void;
}

const ToolBar: React.FC<Props> = ({
  onUpdate,
  onPublish,
  onPublishLoadings,
  onUpdateChanges,
  onPublishChanges,
  onDuplicate,
  dialog,
  setDialog,
  zoomIn,
  zoomOut,
  zoomToContent,
  resetZoom,
  isExpiry,
  setIsExpiry,
  dialogSettings,
  setDialogSettings,
  setOnPublishChanges,
  setOnUpdateChanges,
  previewVersions,
  setPreviewVersions,
  DVCToShow,
  setDVCToShow,
  live,
  setLive,
  staging,
  setStaging,
  refresh,
  setRefresh,
  dialogTriggers,
  onDuplicateDialog,
  setOnDuplicateDialog,
  dialogToduplicateName,
  setDialogToduplicateName
  
}) => {
  const { deleteOne } = useDialogStore();
  const bot = useBotStore((state) => state.bot);
  const confirmModal = useConfirmModal();
  const { lang, changeLang } = useLangStore();
  
  const handleNew = () => {
    const path = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;

    uploadFile({
      path,
      file_body: JSON.stringify({
        entities: {},
        steps: [],
        edges: [],
      }),
    }).then(() => {
      createOne({
        dialog_name: "Blank Dialog",
        url: path,
        bot_id: bot.bot_id,
      }).then((data) => {
        if (data && !data.message) {
          window.open(`/composer/${data.dialog_id}`, "_blank");
        }
      });
    });
  };

  const handleDelete = async () => {
    await deleteOne(dialog.dialog_id);
    setTimeout(() => {
      window.location.href = `/?view=${views.botViews.KNOWLEDGE_BASE_VIEW}`;
    }, 1000);
  };

  const onDelete = () => {
    confirmModal.onOpen();
    confirmModal.setOnConfirm(async () => await handleDelete());
  };

  //change which dvc is viewing -Editable- (DVC.isStging = true) 
  const changeIsStagingDialog = useCallback(async(DVCToShow)=>{
    await changeIsStaging({dialog_version_control_id: DVCToShow.dialog_version_control_id, bot_id: dialog.bot_id, user_id: bot.user_id} ,DVCToShow);
    setPreviewVersions(!previewVersions)
    window.location.reload();
  }, [])

  //change whis dvc is published (DVC.is_live = true)
  const changeIsLiveDialog = useCallback(async(DVCToShow )=>{
    await changeIsLive({dialog_version_control_id: DVCToShow.dialog_version_control_id, bot_id: dialog.bot_id, user_id: bot.user_id}, DVCToShow);
  },[])

  //delete dvc (but can not delete the dvc.is_staging === true || dvc.is_live === true)
  const deleteDVCDialog = useCallback(async(DVCToShow)=>{
    console.log("DVCToShow DeleteDVC", DVCToShow)
    await deleteDVC(DVCToShow.dialog_version_control_id);
    setRefresh(!refresh)
  },[])

  

  const handleShortcuts = useCallback(
    (event) => {
      event.preventDefault();
      const isMacOS = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "b") {
        handleNew();
      }

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "s") {
        event.preventDefault();
        onUpdate();
      }

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "d") {
        event.preventDefault();
        setOnDuplicateDialog(true);
        console.log("msh sha3'al")
      }

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "=") {
        zoomIn();
      }

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "-") {
        zoomOut();
      }

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "0") {
        resetZoom();
      }

      if ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "9") {
        zoomToContent();
      }
    },
    [
      handleNew,
      onPublish,
      onDuplicate,
      zoomIn,
      zoomOut,
      resetZoom,
      zoomToContent,
    ]
  );

  const ignore = (event) => {
    const isMacOS = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);

    if (
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "b") ||
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "s") ||
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "d") ||
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "=") ||
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "-") ||
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "0") ||
      ((event.ctrlKey || (isMacOS && event.metaKey)) && event.key === "9")
    )
      event.preventDefault();
  };

  useEffect(() => {
    !previewVersions ? 
    (window.addEventListener("keyup", handleShortcuts),
    window.addEventListener("keydown", ignore)) :
    console.log("");
    return () => {
      window.removeEventListener("keyup", handleShortcuts);
      window.removeEventListener("keydown", ignore);
    };

  }, [handleShortcuts]);


  //NOTE - id convention ----> composer_nav_<menu_index>_<item_index>_<subitem_index>
  const navs = [
    {
      menu_id: "composer_nav_0",
      menu_trigger: "File",
      menu_content: {
        items: [
          {
            item_id: "composer_item_0_0",
            item_label: "New",
            item_shortcut: "⌘B",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: handleNew,
          },
          {
            item_id: "composer_item_0_1",
            item_label: "Save",
            item_shortcut: "⌘S",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: onUpdate, 
          },
          {
            item_id: "composer_item_0_2",
            item_label: "Duplicate",
            item_shortcut: "⌘D",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: ()=>{setOnDuplicateDialog(true)},
          },
          {
            item_id: "composer_item_0_3",
            item_label: "Move to Trash",
            item_shortcut: null,
            item_seperator: true,
            item_sub_menu: null,
            onclickHandler: onDelete,
          },
        ],
      },
    },
    {
      menu_id: "composer_nav_1",
      menu_trigger: "View",
      menu_content: {
        items: [
          {
            item_id: "composer_item_1_0",
            item_label: "Zoom In",
            item_shortcut: "⌘+",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: () => zoomIn(),
          },
          {
            item_id: "composer_item_1_1",
            item_label: "Zoom Out",
            item_shortcut: "⌘-",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: () => zoomOut(),
          },
          {
            item_id: "composer_item_1_2",
            item_label: "Zoom to Content",
            item_shortcut: "⌘9",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: () => zoomToContent(),
          },
          {
            item_id: "composer_item_1_3",
            item_label: "Reset Zoom",
            item_shortcut: "⌘0",
            item_seperator: false,
            item_sub_menu: null,
            onclickHandler: () => resetZoom(),
          },
        ],
      },
    },
  ];
  // FIXME - Duplicate Dialog Modal not working
  const [showModal, setShowModal] = useState(false);
  const [showPublish, setShowPublish] = useState(false);
  
  return (
    <>
      <DialogSettingsModal
        dialogSettings={dialogSettings}
        setDialogSettings={setDialogSettings}
        setShowModal={setShowModal}
        showModal={showModal}
        isExpiry={isExpiry}
        setIsExpiry={setIsExpiry}
        setOnPublishChanges={setOnPublishChanges}
        setOnUpdateChanges={setOnUpdateChanges}
      />

      {/* when Clicking the publish btn it will show a dialog that will make sure that u want to publish the dialog */}
      <PublishDialog
        setShowPublish={setShowPublish}
        showPublish={showPublish}
        onPublish={onPublish}
      />

      <DuplicateDialog
        onDuplicateDialog={onDuplicateDialog}
        setOnDuplicateDialog={setOnDuplicateDialog}
        onDuplicate={onDuplicate}
        dialogToduplicateName={dialogToduplicateName}
        setDialogToduplicateName={setDialogToduplicateName}
      />

      <header className="header w-full bg-themeSecondary max-h-[50px] h-[50px]  px-6 shadow-2xl z-1 text-white shadow-b-2xl border-b-2 border-gray-800">
        {/* {showModal && (
        <CreateDialog
          showModal={showModal}
          setShowModal={setShowModal}
          dialogToUpdate={dialogToUpdate}
          dialogToDuplicate={dialogToDuplicate}
          setDialogToUpdate={setDialogToUpdate}
          setDialogToDuplicate={setDialogToDuplicate}
        />
      )} */}

        <div className="grid grid-cols-12 h-full w-full  ">
          {
            previewVersions ? 
              <div className="col-span-8 flex items-center"></div>
            :
              <div className="col-span-8 flex items-center">
                <Menubar className="p-0 text-md">
                   {navs.map((nav) => (
                    <MenubarMenu key={nav.menu_id}>
                      <MenubarTrigger className="p-0 pr-2 flex justify-start hover:cursor-pointer">
                        {nav.menu_trigger}
                      </MenubarTrigger>
                      <MenubarContent className="bg-themeSecondary shadow-2xl text-white border-0 ">
                        {nav.menu_content.items.map((item) => (
                          <React.Fragment key={item.item_id}>
                            {item.item_sub_menu ? (
                              <MenubarSub>
                                <MenubarSubTrigger>
                                  {item.item_label}
                                </MenubarSubTrigger>
                                <MenubarSubContent className="bg-themeSecondary shadow-2xl text-white border-0">
                                  {item.item_sub_menu.items.map((subItem) => (
                                    <React.Fragment key={subItem.item_id}>
                                      <MenubarItem onClick={subItem.onclickHandler}>
                                        {subItem.item_label}
                                      </MenubarItem>
                                      {subItem.item_shortcut ? (
                                        <MenubarShortcut>
                                          {subItem.item_shortcut}
                                        </MenubarShortcut>
                                      ) : null}
                                      {subItem.item_seperator ? (
                                        <MenubarSeparator />
                                      ) : null}
                                    </React.Fragment>
                                  ))}
                                </MenubarSubContent>
                              </MenubarSub>
                            ) : (
                              <MenubarItem onClick={item.onclickHandler}>
                                {item.item_label}
                                {item.item_shortcut ? (
                                  <MenubarShortcut>
                                    {item.item_shortcut}
                                  </MenubarShortcut>
                                ) : null}
                                {item.item_seperator ? (
                                  <MenubarSeparator className="bg-white" />
                                ) : null}
                              </MenubarItem>
                            )}
                          </React.Fragment>
                        ))}
                      </MenubarContent>
                    </MenubarMenu>
                  ))} 
                  <Button
                    onClick={changeLang}
                    className="capitalize "
                    size="sm"
                    variant="ghost"
                  >
                    {lang}
                  </Button>
                  <Button
                    onClick={() => setShowModal(true)}
                    variant="ghost"
                    size="sm"
                  >
                    <SettingsIcon className="w-5" />
                  </Button>
                </Menubar>
              </div>
          }
          
          <div className="col-span-4 flex justify-end gap-5 items-center"> 
          {/* if there is any changes this div will show them */}
            {onUpdateChanges !== 0 && (
              <p>
                {onUpdateChanges}{" "}
                {onUpdateChanges === 1 ? "change" : "changes"}
              </p>
            )}
            
            {/* if previewVersion is true the btn are in editting mode other wise they are in there normal state */}
            <Button
              disabled={dialogTriggers?.length === 0}
              onClick={()=> {
                setPreviewVersions(!previewVersions); 
                console.log(previewVersions, "previewVersions");
              }}
              className="hover:text-white px-4 w-32 bg-black text-primary border-2   border-primary font-bold"
            >
              {
              previewVersions ?<X/> : "Versions"
              }
            </Button>
            {
              previewVersions &&
              (<Button
                disabled={live || staging}
                onClick={()=>deleteDVCDialog(DVCToShow)}
                className="hover:text-white px-4 w-32 bg-black text-primary border-2   border-primary font-bold"
              >
                <Trash2/>
              </Button>)
            }
            {previewVersions ?  
              <Button
                disabled={staging}
                onClick={()=>{changeIsStagingDialog(DVCToShow);}}
                className="hover:text-white px-4 w-32 bg-black text-primary border-2   border-primary font-bold"
              >
                Edit
              </Button> 
            :
              <Button
                disabled={onUpdateChanges === 0 || dialogTriggers?.length === 0}
                onClick={onUpdate}
                className="hover:text-white px-4 w-32 bg-black text-primary border-2   border-primary font-bold"
              >
                Update
              </Button>
            }
            {previewVersions ?  
              <Button
                disabled={live}
                onClick={()=>{changeIsLiveDialog(DVCToShow); setPreviewVersions(!previewVersions)}}
                className="hover:text-white px-4 w-32 bg-black text-primary border-2   border-primary font-bold flex items-center justify-center"
              >
                Publish
              </Button>
            :
              <Button
                disabled={Boolean(onUpdateChanges) || dialogTriggers?.length === 0}
                onClick={()=>{setShowPublish(true)}}
                loading={onPublishLoadings}
                className="hover:text-white px-4 w-32 bg-black text-primary border-2   border-primary font-bold flex items-center justify-center"
              >
                <RocketIcon />
                &nbsp; Publish
              </Button>
            }
          </div>
        </div>
      </header>
    </>
  );
};

const DialogSettingsModal = ({
  dialogSettings,
  setDialogSettings,
  setShowModal,
  showModal,
  isExpiry,
  setIsExpiry,
  setOnPublishChanges,
  setOnUpdateChanges
}) => {
  const dialogs = useDialogStore((state) => state.dialogs);
  const [isExpiryActive, setIsExpiryActive] = useState(isExpiry || false);
  const [dialogSettingsTemp, setDialogSettingsTemp] = useState<IDialogExpiryConfig>({
    expiry_min: 30,
    check_at: "each_message",
    action: {
      type: "send_message",
      config: {
        message: "__ar__:#### __en__:####",
        dialog: {
          path: "",
          lang: "__ar__:##ar## __en__:##en##",
        },
      },
    },
  });

  const { result, value, setValue } = useMultiLanguage(dialogSettingsTemp.action?.config?.message || "");
  const { lang, changeLang } = useLangStore();

  useEffect(() => {
    if (dialogSettings !== dialogSettingsTemp) {
      setDialogSettingsTemp(dialogSettings);
    }
    if (isExpiry !== isExpiryActive) {
      setIsExpiryActive(isExpiry);
    }
  }, [dialogSettings, isExpiry]);

  const handleSave = () => {
    setOnPublishChanges((change) => change + 1);

    setDialogSettings({
      ...dialogSettingsTemp,
      action: {
        ...dialogSettingsTemp.action,
        config: {
          ...dialogSettingsTemp.action.config,
          message: result,
          dialog: {
            ...dialogSettingsTemp.action.config.dialog,
            lang: `__ar__:##ar## __en__:##en##`,
          },
        },
      },
    });

    setIsExpiry(isExpiryActive);
    setShowModal(false);
    setOnUpdateChanges((change) => change + 1);
  };

  const handleExpiryToggle = (e) => {
    const checked = e.target.checked;
    setIsExpiryActive(checked);
  };

  return (
    <FullModal
      className="text-white"
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      title="Dialog Settings"
      onSave={handleSave}
      footer
    >
      <div className="flex items-center gap-5">
        <label htmlFor="expiry" className="text-lg">Expiry</label>
        <Checkbox
          onChange={handleExpiryToggle}
          checked={isExpiryActive}
          name="expiry"
        />
      </div>

      {isExpiryActive && (
        <div className="space-y-5">
          <hr className="my-5" />

          {/* Expiry Time Input */}
          <div className="flex items-center gap-5">
            <label htmlFor="expiry_min" className="text-md whitespace-nowrap">Expiry Time (in minutes)</label>
            <Input
              type="number"
              name="expiry_min"
              value={dialogSettingsTemp.expiry_min}
              onChange={(e) => setDialogSettingsTemp({
                ...dialogSettingsTemp,
                expiry_min: e.target.value,
              })}
            />
          </div>

          {/* Check At Selector */}
          <div className="flex items-center gap-5">
            <label htmlFor="check_at" className="text-md whitespace-nowrap">Check expiry at</label>
            <Select
              name="check_at"
              key={dialogSettingsTemp.check_at}
              value={dialogSettingsTemp.check_at}
              onValueChange={(value: 'each_message' | 'start_of_dialog') => setDialogSettingsTemp({
                ...dialogSettingsTemp,
                check_at: value,
              })}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="each_message">Each User Interaction</SelectItem>
                <SelectItem value="start_of_dialog">Start of Dialog</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Action Selector */}
          <div className="flex items-center gap-5">
            <label htmlFor="action" className="text-md whitespace-nowrap">Action</label>
            <Select
              name="action"
              key={dialogSettingsTemp.action.type}
              value={dialogSettingsTemp.action.type}
              onValueChange={(value: "send_message" | "trigger_dialog") => setDialogSettingsTemp({
                ...dialogSettingsTemp,
                action: {
                  ...dialogSettingsTemp.action,
                  type: value,
                },
              })}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="send_message">Send Message</SelectItem>
                <SelectItem value="trigger_dialog">Trigger Dialog</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Message or Dialog Input */}
          {dialogSettingsTemp.action.type === "send_message" ? (
            <div className="flex gap-5">
              <Button onClick={changeLang} className="capitalize" size="default" variant="outline">{lang}</Button>
              <div className="flex gap-5 self-stretch w-full">
                <label htmlFor="config" className="text-md whitespace-nowrap">Message</label>
                <Textarea
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-5">
              <label htmlFor="config" className="text-md whitespace-nowrap">Dialog</label>
                <Select
                  name="config"
                  value={dialogSettingsTemp.action?.config?.dialog?.path}
                  onValueChange={(value) => {
                    const dialog = dialogs?.find((dialog) => dialog.url === value)
                    setDialogSettingsTemp({
                      ...dialogSettingsTemp,
                      action: {
                        ...dialogSettingsTemp.action,
                        config: {
                          ...dialogSettingsTemp.action.config,
                          dialog: {
                            ...dialogSettingsTemp.action.config.dialog,
                            path: value,
                            dialog_id: dialog?.dialog_id,
                            dialog_name: dialog?.dialog_name,
                          },
                        },
                      },
                    })
                  }}
                >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select a dialog" />
                </SelectTrigger>
                <SelectContent>
                  {dialogs.map((dialog, i) => (
                    <SelectItem key={i} value={dialog.url}>
                      {dialog.dialog_name || "Untitled"}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )
        }
        </div>
      )}
    </FullModal>
  );
};

//LINK - publish dialog
const PublishDialog = ({
  setShowPublish,
  showPublish,
  onPublish,
}) => {
  const publish = async () => {
    if (onPublish) {
      setShowPublish(false);
      const result = await onPublish();
      if (JSON.stringify(result)?.includes('already published')) {
        toast('This version is already published.', {
          icon: '⚠️'
        });
      } else {
        toast.success('Publishing...');
      }
    }
  };
  return (
    <FullModal
    className="text-white"
    isOpen={showPublish}
    onClose={() => setShowPublish(false)}
    title="Dialog Settings"
    onSave={publish} //change this to publish not save
    footer
    >
      <div>Are you sure you want to make this the live version?</div>
    </FullModal>
  );
};

const DuplicateDialog = ({
  onDuplicateDialog,
  setOnDuplicateDialog,
  onDuplicate,
  dialogToduplicateName,
  setDialogToduplicateName
}) => {
  const handleSave = (dialogName) => {
    if(!setDialogToduplicateName) return
    setDialogToduplicateName(dialogName)
  };
  return (
    <FullModal
    className="text-white"
    isOpen={onDuplicateDialog}
    onClose={() => setOnDuplicateDialog(false)}
    title="Duplicate Dialog"
    onSave={()=>{onDuplicate();}}
    footer
    >
    <div>
      <p className="mb-4">Are you sure you want to duplicate the live version of this dialog?</p>
      <input 
        type="text" 
        placeholder="Enter the dialog name (optional)" 
        className="w-full p-2 bg-neutral-800/90 border border-gray-600 rounded text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2"
        id="dialog_name"
        value={dialogToduplicateName}
        onChange={(e) => {handleSave(e.target.value);}}
        />
    </div>

    </FullModal>
  );
};

export default ToolBar;
