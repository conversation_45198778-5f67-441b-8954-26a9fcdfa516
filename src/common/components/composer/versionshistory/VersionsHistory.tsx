import { useCallback, useEffect, useState } from "react";
import { ITrigger } from "store/trigger/trigger.types";
import { IDialog } from "store/dialog/dialog.types";
import { getAll, getDVC } from "apis/dialogversioncontrol.api";
import { Edit, History, Radio } from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
interface Props {
  dialogTriggers: ITrigger[];
  dialog: IDialog;
}

const VersionsHistory = ({ dialog, setDialog, setDVCToShow, setLoading, setLive, setStaging, refresh}) => {
    const [ver, setVer] = useState([]);
    const [selectedItem, setSelectedItem] = useState(null);
    const [hoveredIcon, setHoveredIcon] = useState("");
    const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });



  useEffect(() => {
    getAll(dialog.dialog_id).then((data) => {
      const sortedVer = [...data].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
      setVer(sortedVer)
    });
  }, [refresh]);

  //change the dialog so it will show the picked one 
  const getDialog = useCallback((item)=>{
    getDVC(item.dialog_version_control_id).then((res)=>{
      setDialog(res);
    })
  }, []);

  const handleClick = (item) => {
    setLoading(true);
    setSelectedItem(item.dialog_version_control_id);
    getDialog(item);
    setDVCToShow(item);
    setLoading(false);
    //added so the btn in the toolbar can be disabled
    setLive(item.is_live);
    setStaging(item.is_staging)
  };
  
  const handleMouseOver = (iconType, event) => {
    setHoveredIcon(iconType);
    setHoverPosition({ x: event.clientX, y: event.clientY });
  };

  const handleMouseOut = () => {
    setHoveredIcon("");
  };

  //sort the versions depending on createdAt   
  const initialize = async () => {
    if (ver.length > 0) {
      const sortedVer = ver.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      const item = sortedVer.find(item => item.is_staging === true);
      setLive(sortedVer.find(item => item.is_live === true));
      if (item) {
        handleClick(item);
      }
    }
  };

  useEffect(() => {
    initialize();
  }, [ver]);

  return (
    <div className="flex flex-col justify-between items-center w-full h-full  text-gray-400 border-r-2 border-gray-800 bg-black shadow-xl ">

          <div className="flex items-center justify-center bg-primary px-5 py-2 mt-2 text-black rounded-xl font-bold text-base">
              Versions
            </div>
          <div className="flex flex-col gap-2 px-2 h-[calc(100vh-110px)] w-full overflow-y-auto">

            {
              ver.map((item, i)=>{
                const date = new Date(item.updatedAt);
                const isSelected = selectedItem === item.dialog_version_control_id;
                return (
                  <div 
                    key={i} 
                    className={`h-16 p-1 border text-sm flex items-center justify-center rounded-xl cursor-pointer 
                        ${isSelected ? 'bg-primary text-black' : 'text-white  border-primary hover:text-primary'}
                        ${item.is_staging ? 'bg-yellow-500 bg-opacity-80 border-yellow-500' : ''}
                        ${item.is_live ? 'bg-green-500 bg-opacity-80 border-green-500' : ''}
                      `}
                    onClick={()=>handleClick(item)}
                    onMouseOut={handleMouseOut}
                  >
                    {
                      item.is_staging ? 
                        <Edit className="h-20 p-1" onMouseOver={(e) => handleMouseOver("The Editable Version", e)} />: 
                        (item.is_live ? <Radio className="h-20 p-1" onMouseOver={(e) => handleMouseOver("The Live Version", e)} /> : 
                          <History className="h-4"/>)
                    }
                    <div className="py-2 flex-col gap-6">
                      <br />
                      <span className="p-3">{format(date.getTime(), 'dd/MM/yyyy HH:mm:ss')}</span>
                      <span className="p-3 flex justify-end text-xs text-gray-600">{formatDistanceToNow(date, { addSuffix: true})}</span>
                    </div>
                  </div>
                )
              })
            } 
          </div>
          {hoveredIcon && (
        <div
          className="absolute p-4 bg-black text-white rounded shadow-lg"
          style={{ top: hoverPosition.y, left: hoverPosition.x }}
        >
          {hoveredIcon}
        </div>
      )}
    </div>
  );
};


export default VersionsHistory;
