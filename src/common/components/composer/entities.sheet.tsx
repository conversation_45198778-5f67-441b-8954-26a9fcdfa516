import { FC, memo, useEffect, useMemo, useState } from "react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "common/ui/sheet";
import { Brain, BrainCircuit, Globe, Info, Plus, Trash2, X } from "lucide-react";
import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import EntitiesTypes from "data/composer/entitiesTypes";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { updateConfig } from "apis/config.api";
import { IDialog } from "store/dialog/dialog.types";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@radix-ui/react-hover-card";


interface EntitiesSheetProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  entities: Record<string, string>;
  setEntities: (entities: Record<string, string>) => void;
  globals: Record<string, string>;
  dialog: IDialog;
  setGlobals: (globals: Record<string, string>) => void;
  setOnUpdateChanges: (value: number) => void;
  onUpdateChanges: number;
}

const EntitiesSheet: FC<EntitiesSheetProps> = ({
  open,
  setOpen,
  entities,
  setEntities,
  globals,
  dialog,
  setGlobals,
  setOnUpdateChanges,
  onUpdateChanges,
}) => {
  const [entitiesToAdd, setEntitiesToAdd] = useState([]);
  const [globalsToAdd, setGlobalsToAdd] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const asArray = Object.entries(entities);

    setEntitiesToAdd(asArray);
  }, [entities]);

  useEffect(() => {
    const asArray = Object.entries(globals);

    setGlobalsToAdd(asArray);
  }, [globals]);

  const AddEntity = () => {
    setEntitiesToAdd((prevEntitiesToAdd) => {
      return [...prevEntitiesToAdd, ["", ""]];
    });
  };

  const AddGlobal = () => {
    setGlobalsToAdd((prevGlobalsToAdd) => {
      return [...prevGlobalsToAdd, ["", ""]];
    });
  };

  const DeleteEntity = (index: number) => {
    const newEntitiesToAdd = [...entitiesToAdd];
    newEntitiesToAdd.splice(index, 1);
    setEntitiesToAdd(newEntitiesToAdd);
    const updatedEntities = Object.fromEntries(newEntitiesToAdd);
    setEntities(updatedEntities);
    setOnUpdateChanges(onUpdateChanges + 1);
  };

  const DeleteGlobal = (index: number) => {
    setGlobalsToAdd((prevGlobalsToAdd) => {
      const newGlobalsToAdd = [...prevGlobalsToAdd];
      newGlobalsToAdd.splice(index, 1);
      return newGlobalsToAdd;
    });
  };

  const handleInputChange = (key: string, index: number) => {
    setEntitiesToAdd((prevEntitiesToAdd) => {
      const newEntitiesToAdd = [...prevEntitiesToAdd];
      newEntitiesToAdd[index][0] = key;

      return newEntitiesToAdd;
    });
  };

  const handleGlobalInputChange = (key: string, index: number) => {
    setGlobalsToAdd((prevGlobalsToAdd) => {
      const newGlobalsToAdd = [...prevGlobalsToAdd];
      newGlobalsToAdd[index][0] = key;

      return newGlobalsToAdd;
    });
  };

  const handleSelectChange = (value: string, index: number) => {
    setEntitiesToAdd((prevEntitiesToAdd) => {
      const newEntitiesToAdd = [...prevEntitiesToAdd];
      newEntitiesToAdd[index][1] = value;
      return newEntitiesToAdd;
    });
  };

  const handleGlobalSelectChange = (value: string, index: number) => {
    setGlobalsToAdd((prevGlobalsToAdd) => {
      const newGlobalsToAdd = [...prevGlobalsToAdd];
      newGlobalsToAdd[index][1] = value;
      return newGlobalsToAdd;
    });
  };

  const isDisabled = useMemo(() => {
    let isDisabled = false;

    const updatedEntities = Object.fromEntries(entitiesToAdd);

    if (JSON.stringify(updatedEntities) === JSON.stringify(entities)) {
      isDisabled = true;
    } else {
      isDisabled = false;
    }
    // check if any of the entities is empty
    for (const [key, value] of Object.entries(updatedEntities)) {
      if (key === "" || value === "") {
        isDisabled = true;
        break;
      }
    }

    return isDisabled;
  }, [entities, entitiesToAdd]);

  const isGlobalDisabled = useMemo(() => {
    let isDisabled = false;

    const updatedGlobals = Object.fromEntries(globalsToAdd);

    if (JSON.stringify(updatedGlobals) === JSON.stringify(globals)) {
      isDisabled = true;
    } else {
      isDisabled = false;
    }
    // check if any of the entities is empty
    for (const [key, value] of Object.entries(updatedGlobals)) {
      if (key === "" || value === "") {
        isDisabled = true;
        break;
      }
    }

    return isDisabled;
  }, [globals, globalsToAdd]);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent size="sm" className="text-white overflow-auto">
        <SheetHeader>
          <SheetTitle className="flex gap-2 items-center">
            <BrainCircuit />
            Dialog Entities
          </SheetTitle>
          <SheetDescription>
            Entities are only accessed by the dialog they are defined in.
            <HoverCard>
              <HoverCardTrigger>
                <Info size={20} className="cursor-pointer" />
              </HoverCardTrigger>
              <HoverCardContent className="w-60 bg-black ring-2 ring-gray-500/50 text-sm">
                <div className="p-3">
                  Update the Dialog to save the Entities
                </div>
              </HoverCardContent>
            </HoverCard>
          </SheetDescription>
        </SheetHeader>
        <div className="flex flex-col gap-5 mt-5 mb-10 max-h-96 overflow-y-auto">
          <Button
            onClick={AddEntity}
            className={`rounded-full w-5 h-5 p-1 self-end`}
          >
            <Plus />
          </Button>
          <div className="max-h-[500px] overflow-y-auto">
            {entitiesToAdd.map((entity, i) => {
              return (
                <div className="flex items-center gap-3" key={i}>
                  <Input
                    type="text"
                    value={entity[0]}
                    onChange={(e) => {
                      handleInputChange(e.target.value, i);
                      const updatedEntities = [...entitiesToAdd];
                      updatedEntities[i][0] = e.target.value;
                      setEntities(Object.fromEntries(updatedEntities));
                      setOnUpdateChanges(onUpdateChanges + 1);
                    }}
                    name="entity"
                  />
                  <Select
                    value={entity[1]}
                    onValueChange={(value) => {
                      handleSelectChange(value, i);
                      const updatedEntities = [...entitiesToAdd];
                      updatedEntities[i][1] = value;
                      setEntities(Object.fromEntries(updatedEntities));
                      setOnUpdateChanges(onUpdateChanges + 1);
                    }}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select a type" />
                    </SelectTrigger>
                    <SelectContent>
                      {EntitiesTypes.map((type, i) => {
                        return (
                          <SelectItem key={i} value={type}>
                            {type}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <Trash2
                    className="w-5 h-5 cursor-pointer hover:text-red-500"
                    onClick={() => DeleteEntity(i)}
                  />
                </div>
              );
            })}
          </div>
          {/* <Button 
            onClick={() => {
              const asObject = Object.fromEntries(entitiesToAdd);
              setEntities(asObject);
              setOpen(false);
            }}
            disabled={isDisabled}
          >
            Save
          </Button> */}
        </div>
        <hr />
        <h1 className="font-semibold text-lg pt-3 pb-2">
          <Globe className="inline-block mr-2 w-5" />
          Global Entities
        </h1>
        <SheetDescription>{`Add global entities that can be used in any dialog. You can use them by typing \${globals.}`}</SheetDescription>
        <div className="flex flex-col gap-5 mt-5 max-h-96 overflow-y-auto">
          <Button
            onClick={AddGlobal}
            className={`rounded-full w-5 h-5 p-1 self-end`}
          >
            <Plus />
          </Button>
          <div className="max-h-[500px] overflow-y-auto">
            {globalsToAdd.map((global, i) => {
              return (
                <div className="flex items-center gap-3" key={i}>
                  <Input
                    type="text"
                    value={global[0]}
                    onChange={(e) => {
                      handleGlobalInputChange(e.target.value, i);
                    }}
                    name="global"
                  />
                  <Select
                    value={global[1]}
                    onValueChange={(value) => {
                      handleGlobalSelectChange(value, i);
                    }}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select a type" />
                    </SelectTrigger>
                    <SelectContent>
                      {EntitiesTypes.map((type, i) => {
                        return (
                          <SelectItem key={i} value={type}>
                            {type}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <Trash2
                    className="w-5 h-5 cursor-pointer hover:text-red-500"
                    onClick={() => DeleteGlobal(i)}
                  />
                </div>
              );
            })}
          </div>
          <Button
            onClick={() => {
              setLoading(true);
              const asObject = Object.fromEntries(globalsToAdd);
              updateConfig({ bot_id: dialog.bot_id, globals: asObject }).then(
                () => {
                  setGlobals(asObject);
                  setLoading(false);
                }
              );
            }}
            disabled={isGlobalDisabled}
            loading={loading}
          >
            Save
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default memo(EntitiesSheet);
