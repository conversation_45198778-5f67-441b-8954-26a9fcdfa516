import { <PERSON><PERSON> } from "common/ui/button";
import { FlaskConical, X } from "lucide-react";
import { FC, memo, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import useDialogStore from "store/dialog/dialog.store";
import { IDialog } from "store/dialog/dialog.types";
import { ITrigger } from "store/trigger/trigger.types";

interface TestSidebarProps {
  setShowTest: (showTest: boolean) => void;
  showTest: boolean;
  bot_id: string;
  dialogTriggers: ITrigger[];
  setDialogTriggers: (value: ITrigger[]) => void;
  DVCToShow: IDialog;
}

const TestSidebar: FC<TestSidebarProps> = ({ setShowTest, showTest , bot_id, dialogTriggers, setDialogTriggers, DVCToShow}) => {

  // const dialogTriggers = useDialogStore(state => state.dialogTriggers);

  return (
    <div className="h-full">
    <div className="sticky text-white flex flex-col gap-2 border-b border-dark h-[50px] ">
        <div className="p-2 flex justify-between items-center">
          <h1 className="flex gap-2">
            <FlaskConical className="text-primary" />
            Test Your Dialog
          </h1>

          <Button variant="ghost" className="p-0">
            <X onClick={() => setShowTest(false)} />
          </Button>
        </div>

        
      </div>
      {/* added dvc_url to the like so it will test the chosen dvc */}
      <div className="h-[calc(100%-50px)]">
        <iframe
            className="h-full w-full"
            src={`https://chatbot-incubator-refactor-g668y.ondigitalocean.app/?bot_id=${bot_id}&theme=dark&hide_header=true&trigger=${dialogTriggers[0].trigger}&dvc_url=${DVCToShow.url}&channelID=composer`}
            //  src={`http://localhost:3001/?bot_id=${bot_id}&theme=dark&hide_header=true&trigger=${dialogTriggers[0].trigger}&dvc_url=${DVCToShow.url}&channelID=composer`}
            allow="clipboard-read; clipboard-write;"
            id="searchat-chatbot-iframe-test"
        ></iframe>
      </div>
    </div>
  
  );
};

export default memo(TestSidebar);
