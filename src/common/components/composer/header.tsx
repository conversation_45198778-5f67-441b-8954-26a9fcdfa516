import React, { use<PERSON><PERSON>back, useMemo } from "react";
import { Avatar } from "common/ui/avatar";
import useDialogStore from "store/dialog/dialog.store";
import useUserStore from "store/user/user.store";
import { IDialog } from "store/dialog/dialog.types";
import { updateOne } from "apis/dialog.api";
import debounce from "helpers/debouncer";
import { Button } from "common/ui/button";
import { FlaskConical, Info } from "lucide-react";
import { toast } from "react-hot-toast";
import { ITrigger } from "store/trigger/trigger.types";
import { Alert } from "common/ui/alert";
import { UserMenu } from "common/components/sidebars/components";

interface Props {
  dialog: IDialog;
  setDialog: (dialog: IDialog) => void;
  showTest: boolean;
  setShowTest: (value: boolean) => void;
  dialogTriggers: ITrigger[]
  setDialogTriggers: (value: ITrigger[]) => void;
}

const Header: React.FC<Props> = ({
  dialog,
  setDialog,
  showTest,
  setShowTest,
  dialogTriggers,
  setDialogTriggers
}) => {
  const dialogName = useMemo(() => {
    if (dialog) {
      return dialog.dialog_name;
    }
  }, [dialog]);

  // const dialogTriggers = useDialogStore((state) => state.dialogTriggers);

  const onEditValue = (value: string) => {
    updateOne({
      ...dialog,
      dialog_id: dialog.dialog_id,
      dialog_name: value,
    }).then((res) => {
      if (res && !res.message) {
        setDialog(res);
      }
    });
  };

  const handleOnChange = useCallback(
    debounce((e) => onEditValue(e.currentTarget.textContent)),
    []
  );

  return (
    <header className="header  bg-black max-h-[50px] h-[50px] px-6 shadow-2xl z-1 text-white border-b-2 border-gray-800">
      <div className="grid grid-cols-12 h-full w-full pr-1   items-center">
        <div className="col-span-2 flex justify-start  items-center">
          <img className="h-8" src="../assets/backgrounds/logo.png"></img>{" "}
          &nbsp;{" "}
          <p
            contentEditable
            onBlur={(e) => {
              onEditValue(e.currentTarget.textContent);
            }}
            onChange={handleOnChange}
            className="whitespace-nowrap"
          >
            {dialogName}
          </p>
        </div>
        <div className="col-span-2"></div>
        <div className="col-span-3">
          {dialogTriggers?.length === 0 && (
          <div className="bg-amber-300 text-black p-1 rounded text-center flex gap-2">
            <Info />
            Please add a trigger to activate your dialog
          </div>
          )}
        </div>
        <div className="col-span-5 flex gap-5 items-center justify-end">
          <Button
          disabled={dialogTriggers?.length === 0}
            className="p-0 px-3 scale-75 "
            onClick={() => {
              if (!dialogTriggers) {
                toast.error("Please add a trigger to test your dialog");
              } else {
                setShowTest(!showTest);
              }
            }}
          >
            <FlaskConical size={19} className="mr-2" />
            Test Dialog
          </Button>
          <UserMenu inDialog />
        </div>
      </div>
    </header>
  );
};

export default Header;
