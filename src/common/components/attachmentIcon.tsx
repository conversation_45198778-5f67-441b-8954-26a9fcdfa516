import React, { useEffect } from "react";
import { LucideIcon } from "lucide-react";
import { FileText } from "lucide-react";
import { File } from "lucide-react";
import { Image } from "lucide-react";
import { Button } from "common/ui/button";
import PdfSVG from "common/icons/PdfSVG";

interface IAttachmentIcon extends React.HTMLAttributes<HTMLDivElement> {
  Icon?: LucideIcon;
  svg?: React.FC;
  attachment?: any;
  handleDeleteAttachment: any;
  index: number;
}
const AttachmentIcon: React.FC<IAttachmentIcon> = ({
  attachment,
  handleDeleteAttachment,
  index,
}) => {
  return (
    <div className="relative w-full flex my-3 rounded-md  items-center h-12 border-2 border-dotted border-[#58585c]   ">

      <div className="w-1/12 ml-1">
        {attachment.type?.includes("pdf") ? (
          <PdfSVG/>
        ) : attachment.type?.includes("image") ? (
          <>
            <Image size={20} color="blue" />
          </>
        ) : attachment.type?.includes("text") ? (
          <FileText size={20} />
        ) : (
          <>
            <File size={20} color="yellow" />
          </>
        )}
      </div>
      <div className="w-64 overflow-hidden">
        <p
          className="text-sm"
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {attachment.filename}
        </p>
      </div>
      <Button  className="bg-red-500 hover:bg-red-700 w-14 h-7 mx-2" onClick={() => handleDeleteAttachment(index)}>Delete</Button>
    </div>
  );
};

export default AttachmentIcon;
