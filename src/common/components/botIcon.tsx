import { Plus } from "lucide-react";
import React from "react";
import { cn } from "lib/utils";

interface BotIconProps {
  icon: string;
  className?: string;
  onClick?: () => void;
  isUpload?: boolean;
  uploadImg?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onIconClick?: () => void;
  name?: string;
}

export const BotIcon: React.FC<BotIconProps> = ({
  icon,
  onClick,
  className = "w-10 h-10",
  isUpload,
  uploadImg,
  name,
  onIconClick,
}) => {
  return (
    <label htmlFor={name} className={`${isUpload ? "cursor-pointer" : ""}`}>
      {isUpload && (
        <input
          id={name}
          type="file"
          className="hidden"
          name={name}
          onChange={(e) => {
            uploadImg(e);
          }}
          accept="image/*"
        />
      )}
      {icon ? (
        <div
          className={cn(
            `flex justify-center items-center  rounded ${
              isUpload ? "border" : ""
            } border-white/25`,
            className
          )}
          onClick={onIconClick}
        >
          <img
            id={`img-${name}`}
            src={icon}
            className={cn(" object-cover object-center rounded", className)}
            alt=""
          />
        </div>
      ) : (
        <div
          className={cn(
            "cursor-pointer border-2 border-white/50 rounded flex justify-center items-center",
            className
          )}
          onClick={onClick}
        >
          <Plus />
        </div>
      )}
    </label>
  );
};
