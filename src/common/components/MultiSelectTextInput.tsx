import React, { Keyboard<PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useMemo } from "react";

import CreatableSelect from "react-select/creatable";

const components = {
  DropdownIndicator: null,
};

export interface Option {
  readonly label: string;
  readonly value: string;
}

const createOption = (label: string) => ({
  label,
  value: label,
});
type MultiSelectTextInput = {
  title: string;
  type: "email" | "text" | "password";
  setData: (Data: readonly Option[]) => void;
  prevValue: string[];
  placeholder?: string;
};

export const customStyles = {
  control: (provided, state) => ({
    ...provided,
    backgroundColor: "transparent",
    outline: state.isFocused ? "1.8px solid white" : "#b7b7b8",
    borderColor: "#b7b7b8",
    marginTop: "5px",
    marginBottom: "5px",
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused ? "transparent" : "transparent",
  }),
  singleValue: (provided) => ({
    ...provided,
  }),
  input: (provided, state) => ({
    ...provided,
    color: "white",
    borderColor: "white",
  }),

  indicatorSeparator: (provided) => ({
    ...provided,
    color: "red",
  }),

  clearIndicator: (provided) => ({
    ...provided,
    color: "red",
  }),

  valueContainer: (provided) => ({
    ...provided,
    color: "red",
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: "black",
  }),
  multiValue: (provided) => ({
    ...provided,
    borderColor: "white",
  }),
};
const MultiSelectTextInput: React.FC<MultiSelectTextInput> = ({
  setData,
  title,
  prevValue,
  type,
  placeholder,
}) => {
  const [inputValue, setInputValue] = React.useState("");
  const [value, setValue] = React.useState<readonly Option[]>([]);
  const [error, setError] = React.useState("");

  useEffect(() => {
    setData(value);
  }, [value]);
  useMemo(() => {
    const prevData = prevValue.map((value) => {
      return {
        label: value,
        value: value,
      };
    });
    setValue(prevData);
  }, [prevValue]);

  const handleText = (text: string) => {
    if (type === "email" && text.trim()[0] !== "$") {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      const isValid = emailRegex.test(text);
      if (isValid) {
        setValue((prev) => [...prev, createOption(text)]);
        setInputValue("");
        setError("");
      } else {
        setError("Invalid email");
      }
    } else {
      setValue((prev) => [...prev, createOption(text)]);
      setInputValue("");
    }
  };

  const handleKeyDown: KeyboardEventHandler = (event) => {
    if (!inputValue) return;
    switch (event.key) {
      case "Enter":
      case "Tab":
        handleText(inputValue);
        event.preventDefault();
    }
  };

  return (
    <div className="my-2">
      <label htmlFor={title}>{title}:</label>
      <CreatableSelect
        id={title}
        components={components}
        inputValue={inputValue}
        isClearable
        isMulti
        menuIsOpen={false}
        onChange={(newValue) => setValue(newValue)}
        onInputChange={(newValue) => setInputValue(newValue)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder || "<EMAIL>"}
        value={value}
        styles={customStyles}
      />
      <p className="text-red-500 text-sm">{error}</p>
    </div>
  );
};

export default MultiSelectTextInput;
