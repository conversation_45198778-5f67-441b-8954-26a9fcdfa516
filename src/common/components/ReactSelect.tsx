import React from "react";
import Select from "react-select";
import CreatableSelect from "react-select/creatable";

export const ReactSelect = ({ value, options, onChange, isMulti = false }) => {
  return (
    <CreatableSelect
      value={
        options.find((option) => option.value === value) || {
          value: value,
          label: value,
        }
      }
      isMulti={isMulti}
      options={options}
      onChange={(e) => {
       if(e) onChange(e.value);
      }}
      menuPlacement="bottom"
      menuShouldBlockScroll={false}
      menuShouldScrollIntoView
      menuPosition="absolute"
    
    //   isClearable
      styles={{
        control: (provided, state) => ({
          ...provided,
          backgroundColor: "#1F1F1F",
          border: "none",
          borderRadius: "0.5rem",
          boxShadow: "none",
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          minHeight: "1rem",
          padding: "0.3rem 1rem",
          "&:hover": {
            borderColor: "#fff",
          },
          "&:placeholder": {
            color: "#fff",
          },
          position: 'relative'
        }),
        menu: (provided, state) => ({
          ...provided,
          backgroundColor: "#1F1F1F",
          border: "none",
          borderRadius: "0.5rem",
          boxShadow: "none",
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          padding: "0.5rem 1rem",
          zIndex: 99999999999999,
          "&:hover": {
            borderColor: "#fff",
          },
        }),
        option: (provided, state) => ({
          ...provided,
          backgroundColor: state.isSelected ? "#1F1F1F" : "transparent",
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          padding: "0.5rem 1rem",
          "&:hover": {
            backgroundColor: "#1F1F1F",
            color: "#fff",
          },
        }),
        singleValue: (provided, state) => ({
          ...provided,
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
        }),
        valueContainer: (provided, state) => ({
          ...provided,
          padding: "0.3rem 1rem",
        }),
        input: (provided, state) => ({
          ...provided,
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          border: "none",
          boxShadow: "none !important",
          "&:focus": {
            borderColor: "#fff",
            boxShadow: "none !important",
          },
        }),
      }}
    />
  );
};
