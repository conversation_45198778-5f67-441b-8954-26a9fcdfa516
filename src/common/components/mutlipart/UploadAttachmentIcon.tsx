import { Upload } from "lucide-react";
import React from "react";
type UploadAttachmentIconProps = {
  onChange: (files: File[] | FileList,index:number) => Promise<boolean>;
  index:number;
};
const UploadAttachmentIcon:React.FC<UploadAttachmentIconProps> = ({ onChange,index }) => {
  return (
    <div className=" hover:scale-105 ">
      <label className="cursor-pointer">
        <Upload />
        <input
          accept="application/pdf,
application/msword,
application/vnd.openxmlformats-officedocument.wordprocessingml.document,
application/vnd.ms-powerpoint,
application/vnd.openxmlformats-officedocument.presentationml.presentation,
text/plain,
image/jpeg,
image/png,
image/gif,
image/bmp"
          className="hidden"
          title="upload"
          name="upload"
          type="file"
          onChange={(e) => onChange(e.target.files,index)}
        />
      </label>
    </div>
  );
};

export default UploadAttachmentIcon;
