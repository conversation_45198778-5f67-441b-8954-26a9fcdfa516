import React, { SetStateAction, useEffect } from "react";
import { LucideIcon, MoreHorizontal } from "lucide-react";
import { FileText } from "lucide-react";
import { File } from "lucide-react";
import { Image } from "lucide-react";
import { <PERSON><PERSON> } from "../../../common/ui/button";
import PdfSVG from "../../../common/icons/PdfSVG";
import { Input } from "../../../common/ui/inputs/input";
import { TField } from "../composer/sheet/forms/jsonAPI.form";
import UploadAttachmentIcon from "./UploadAttachmentIcon";
import { CardMenu } from "./CardMenu";

interface IAttachmentIcon extends React.HTMLAttributes<HTMLDivElement> {
  Icon?: LucideIcon;
  svg?: React.FC;
  onChangeField: (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => void;
  field: TField;
  handleAttachments: (
    files: File[] | FileList,
    index: number
  ) => Promise<boolean>;
}
const BinaryAttachment: React.FC<IAttachmentIcon> = ({
  onChangeField,
  field,
  handleAttachments,
}) => {
  return (
    <div className="relative w-full flex my-3 p-0.5 px-5 rounded-md  items-center justify-around h-16 border-2 gap-2 border-dotted border-[#58585c]   ">
      <div className="w-1/2  flex items-center justify-between p-3 my-1 gap-5 h-11 border-2 border-[#58585c]  rounded-md ">
        <div className=" flex ">
          {field.attachment.type?.includes("pdf") ? (
            <PdfSVG />
          ) : field.attachment.type?.includes("image") ? (
            <>
              <Image size={28} color="blue" />
            </>
          ) : field.attachment.type?.includes("text") ? (
            <FileText size={28} />
          ) : (
            <>
              <File size={28} color="yellow" />
            </>
          )}
        </div>
        <div className=" overflow-hidden ">
          <p
            className="text-lg"
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {field.attachment.filename}
          </p>
        </div>
      </div>

      <UploadAttachmentIcon onChange={handleAttachments} index={0} />
    </div>
  );
};

export default BinaryAttachment;
