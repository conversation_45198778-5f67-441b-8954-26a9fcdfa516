import React, { SetStateAction, useEffect } from "react";
import { LucideIcon, MoreHorizontal } from "lucide-react";
import { FileText } from "lucide-react";
import { File } from "lucide-react";
import { Image } from "lucide-react";
import { <PERSON><PERSON> } from "../../../common/ui/button";
import PdfSVG from "../../../common/icons/PdfSVG";
import { Input } from "../../../common/ui/inputs/input";
import { TField } from "../composer/sheet/forms/jsonAPI.form";
import UploadAttachmentIcon from "./UploadAttachmentIcon";
import { CardMenu } from "./CardMenu";

interface IAttachmentIcon extends React.HTMLAttributes<HTMLDivElement> {
  Icon?: LucideIcon;
  svg?: React.FC;
  index: number;
  onChangeField: (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => void;
  field: TField;
  handleDeleteField: any;
  handleAttachments: (
    files: File[] | FileList,
    index: number
  ) => Promise<boolean>;

  handleFieldTypeChange: (index: number, type: string) => void;
}
const MultiPartAttachment: React.FC<IAttachmentIcon> = ({
  index,
  onChangeField,
  field,
  handleDeleteField,
  handleAttachments,
  handleFieldTypeChange,
}) => {
  return (
    <div className="relative w-full flex my-3 p-0.5 rounded-md  items-center justify-between h-16 border-2 gap-2 border-dotted border-[#58585c]   ">
      <div className="w-1/3 flex items-center m-0.5 p-0.5 h-full justify-center gap-3">
        <Input
          key={"fieldName" + index}
          name={`fieldName`}
          onChange={(e) => {
            onChangeField(e, index);
          }}
          placeholder={`key ${index}`}
          value={field.fieldName}
        />
      </div>

      {field.type === "file" ? (
        <div className="w-1/3  flex items-center justify-between p-3 my-1 gap-5 h-11 border-2 border-[#58585c]  rounded-md ">
          <div className=" flex ">
            {field.attachment.type?.includes("pdf") ? (
              <PdfSVG />
            ) : field.attachment.type?.includes("image") ? (
              <>
                <Image size={28} color="blue" />
              </>
            ) : field.attachment.type?.includes("text") ? (
              <FileText size={28} />
            ) : (
              <>
                <File size={28} color="yellow" />
              </>
            )}
          </div>
          <div className=" overflow-hidden ">
            <p
              className="text-lg"
              style={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {field.attachment.filename}
            </p>
          </div>
        </div>
      ) : (
        <div className="w-1/3 flex h-full items-center justify-center">
          <Input
            
            key={"value" + index}
            name={`value`}
            onChange={(e) => {
              onChangeField(e, index);
            }}
            placeholder={`value ${index} (Media URL)`}
            value={field.value}
            
          />
        </div>
      )}
      {field.type === "file" ? (
        <UploadAttachmentIcon onChange={handleAttachments} index={index} />
      ) : null}

      <CardMenu handleFieldTypeChange={handleFieldTypeChange} index={index}>
        <div className="hover:backdrop-brightness-200 h-5 flex items-center self-center justify-center rounded-lg px-1 py-2">
          <MoreHorizontal className="cursor-pointer" size={30} />
        </div>
      </CardMenu>

      <div className="w-1/6 flex items-center justify-end">
        <Button
          className="bg-red-500 hover:bg-red-700 w-14 h-7 mx-2"
          onClick={() => handleDeleteField(index)}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};

export default MultiPartAttachment;
