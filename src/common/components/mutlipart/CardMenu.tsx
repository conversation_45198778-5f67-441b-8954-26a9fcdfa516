"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "common/ui/dropdownMenu";
import toast from "react-hot-toast";
import {
  Link,
  Text,
} from "lucide-react";
import { File } from "lucide-react";

interface CardMenuProps {
  children: React.ReactNode;
  handleFieldTypeChange: (index: number, type: string) => void;
  index:number
}

export const CardMenu: React.FC<CardMenuProps> = ({ children,handleFieldTypeChange,index }) => {


  
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
        <DropdownMenuContent className="ml-8 -mb-5 w-10 bg-secondary border-white/25 text-white">
          <DropdownMenuSeparator className=" bg-white/25" />
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                handleFieldTypeChange(index,"text")
              }}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Text className="mr-2 h-4 w-4" />
              <span>Text</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                handleFieldTypeChange(index,"file")
              }}
              className="cursor-pointer hover:!bg-white/25"
            >
              <File className="mr-2 h-4 w-4" />
              <span>File</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                handleFieldTypeChange(index,"url_media")
              }}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Link className="mr-2 h-4 w-4" />
              <span>url media</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
