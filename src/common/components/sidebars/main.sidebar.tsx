import { memo } from "react";
import { Home, LayoutGrid, Book, BookIcon, MessageSquare } from "lucide-react";

import constant from "constant";
import { MainSidebarButton, UserMenu } from "./components";
import views from "views";

interface MainSideBarProps {
  view: string;
  setView: (view: string) => void;
}

const MainSideBar: React.FC<MainSideBarProps> = ({ view, setView }) => {
  return (
    <div className="fixed top-0 z-40 flex flex-col justify-between items-center w-16 h-full overflow-hidden text-gray-400 bg-[#202024]  border-r-2 border-gray-800 shadow-xl ">
      <div className="flex flex-col items-center w-full mt-2">
        <MainSidebarButton
          id={views.mainViews.HOME_VIEW}
          view={view}
          setView={setView}
          title="Home"
          icon={Home}
        />
        <MainSidebarButton
          id={views.mainViews.BOTS_VIEW}
          view={view}
          setView={setView}
          title="Bots"
          icon={LayoutGrid}
        />

        <MainSidebarButton
          view={view}
          setView={setView}
          title="Docs"
          icon={BookIcon}
          link={constant.DOC_URL}
        />
      </div>

      <UserMenu setView={setView} />
    </div>
  );
};

export default memo(MainSideBar);
