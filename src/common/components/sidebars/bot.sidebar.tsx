import {
  Activity,
  FileText,
  FlaskConical,
  Newspaper,
  Plus,
  Settings,
  Settings2,
  StoreIcon,
  TrendingUp,
  UserCog,
  RadioTowerIcon,
  Table2,
  Headset,
  Tags,
  ExternalLinkIcon,
} from "lucide-react";
import React, { useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { NavItem } from "./components";
import useGPTStore from "store/gpt/gpt.store";
import useSearchSettingsStore from "store/search/search.store";
import { BotIcon } from "../botIcon";
import views from "views";
import useUserStore from "store/user/user.store";
import { IEditorPrivilges } from "store/priviliges/UserPrivilege.types";
import useUserPrivilegeStore from "store/priviliges/UserPrivilege.store";
import planFunctionChecker from "helpers/planFunctionChecker";
import GPTSVG from "common/icons/GPTSVG";
import { getOne as getBotWhatsapp } from "apis/whatsapp.api";
import { getAgent } from "apis/internalLiveChat.api";
import useInternalLiveChatStore from "store/internalLivechat/internalLivechat.store";
import useTicketingStore from "store/ticketing/ticketing.store";
import useLLMStore from "store/llmIntegration/llm.store";

interface BotSideBarProps {
  view: string;
  setView: (view: string) => void;
}

export const BotSidebar: React.FC<BotSideBarProps> = ({ view, setView }) => {
  const user = useUserStore((state) => state.user);
  const { bot, planfunction } = useBotStore();
  const llm = useLLMStore((state) => state.llm);
  const searchSettings = useSearchSettingsStore(
    (state) => state.searchSettings
  );
  const { priviliges, isAdmin } = useUserPrivilegeStore();

  const {agent} = useInternalLiveChatStore();

  const {support_agent} = useTicketingStore();

  const hasEditorAccess = (privilege: keyof IEditorPrivilges | undefined) => {
    return Boolean(priviliges[privilege] || user.user_id === 17 || isAdmin);
  };

  const hasAgentAccess = () => {
    const isAgentPrivilege =  Boolean(priviliges["agent_privilege"]);
    // console.log(priviliges, agent)
    return isAgentPrivilege && agent?.agent_id;
  };

  
  const hasTicketingAccess = () => {
    const isAgentPrivilege =  Boolean(priviliges["ticketing_privilege"]);
    
    return isAgentPrivilege && support_agent?.support_agent_id;
  }


  const [botWhatsapp, setBotWhatsapp] = React.useState<any>(null);

  const getBotWhatsappData = async () => {
    const botWhatsappData = await getBotWhatsapp(bot.bot_id);
    console.log(botWhatsappData);
    setBotWhatsapp(botWhatsappData);
  };

  React.useEffect(() => {
    getBotWhatsappData();
  }, [bot.bot_id]);

  return (
    <div className="fixed top-0 z-40 overflow-y-auto w-60 h-full bg-[#29292F] p-6 space-y-16">
      <div className="flex items-center gap-3 sticky -top-5 py-2 bg-accent">
        <BotIcon
          icon={bot.icon}
          onClick={() => setView(views.botViews.CONFIGURE_VIEW)}
        />
        <p className=" text-xl text-center">{bot.bot_name}</p>
      </div>
      <div className="space-y-8">
        {/* <div className="space-y-3"> */}
        <NavItem
          title="Overview"
          icon={Newspaper}
          id={views.botViews.OVERVIEW_VIEW}
          view={view}
          setView={setView}
        />
        {(hasEditorAccess("sales_dashboard_privilege") ||
          hasEditorAccess("transaction_dashboard_privilege")) && (
          <NavItem
            title="Dashboard"
            icon={TrendingUp}
            id={views.botViews.DASHBOARD_VIEW}
            view={view}
            setView={setView}
            available={planFunctionChecker(
              planfunction,
              "transaction_dashboard"
            )}
          />
        )}
        {/* </div> */}

        {(hasEditorAccess("builder_privilege") ||
          hasEditorAccess("dialog_privilege")) && (
          // <div className="space-y-2">
          <NavItem
            title="Knowledge Base"
            icon={FileText}
            id={views.botViews.KNOWLEDGE_BASE_VIEW}
            view={view}
            setView={setView}
            svg={
              llm && llm?.status_active && GPTSVG
              //  searchSettings?.engine === "gpt" &&
            }
          />

          // </div>
        )}
        {hasEditorAccess("addon_privilege") && (
          <NavItem
            title="Integrations"
            icon={Settings2}
            id={views.botViews.INTEGRATIONS_VIEW}
            view={view}
            setView={setView}
          />
        )}
        {/* <div className="space-y-3"> */}
        {hasEditorAccess("editor_privilege") && (
          <NavItem
            title="Editors"
            icon={UserCog}
            id={views.botViews.EDITORS_VIEW}
            view={view}
            setView={setView}
          />
        )}
        <NavItem
          title="Bot Logs"
          icon={Table2}
          id={views.botViews.LOG_VIEW}
          view={view}
          setView={setView}
        />

        {hasEditorAccess("store_privilege") &&
          user.user_id !== 269 &&
          bot.bot_id !== 666 && (
            <NavItem
              title="Store"
              icon={StoreIcon}
              id={views.botViews.STORE_VIEW}
              view={view}
              setView={setView}
            />
          )}
        {/* </div> */}

        {(hasEditorAccess("appearance_privilege") ||
          hasEditorAccess("deployment_privilege")) && (
          <div className="space-y-2">
            <NavItem
              title="Configure"
              icon={Settings}
              id={views.botViews.CONFIGURE_VIEW}
              view={view}
              setView={setView}
            />
            {/* <div className="flex flex-col pl-2 pt-2 text-sm">
              <NavItem
                title="Plans"
                sub
                id={views.botViews.UPGRADE_VIEW}
                view={view}
                setView={setView}
              />
              {hasEditorAccess(undefined) && (
                <NavItem
                  title="Bot Info"
                  sub
                  id={views.botViews.BOT_INFO_VIEW}
                  view={view}
                  setView={setView}
                />
              )}
              {hasEditorAccess("appearance_privilege") && (
                <NavItem
                  title="Appearance"
                  sub
                  id={views.botViews.APPEARANCE_VIEW}
                  view={view}
                  setView={setView}
                  available={planFunctionChecker(planfunction, "web_designer")}
                />
              )}
              {hasEditorAccess("deployment_privilege") && (
                <NavItem
                  title="Publish"
                  sub
                  id={views.botViews.PUBLISH_VIEW}
                  view={view}
                  setView={setView}
                />
              )}
            </div> */}
          </div>
        )}

        {hasAgentAccess() ? (
          <NavItem
            title="Agent Page"
            icon={Headset}
            id={views.botViews.AGENT_VIEW}
            view={view}
            setView={setView}
          />
        ) : null}
        {/* <NavItem
          title="Test Bot"
          icon={FlaskConical}
          id={views.botViews.TEST_BOT_VIEW}
          view={view}
          setView={setView}
        /> */}

        {
          hasTicketingAccess() ? (
            <NavItem
              title="Ticketing "
              icon={Tags}
              url={`/customer-service/${bot.bot_id}/${user.user_id}`}
              view={view}
              setView={setView}
            />
          ) : null
        }

        {botWhatsapp && botWhatsapp?.waba && (
          <NavItem
            title="BROADCAST"
            icon={RadioTowerIcon}
            id={views.botViews.BROADCAST_VIEW}
            view={view}
            setView={setView}
          />
        )}


      </div>
    </div>
  );
};
