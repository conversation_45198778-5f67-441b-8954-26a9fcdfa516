import { ExternalLink, LucideIcon } from "lucide-react";
import React from "react";
import { toast } from "react-hot-toast";

interface NavItemProps {
  title: string;
  icon?: LucideIcon;
  setView: (view: string) => void;
  id?: string;
  view: string;
  sub?: boolean;
  svg?: React.FC<{ width: string; height: string }> | LucideIcon;
  available?: boolean;
  url?: string;
}

export const NavItem: React.FC<NavItemProps> = ({
  title,
  icon: Icon,
  setView,
  id,
  view,
  sub,
  svg: Svg,
  available = true,
  url
}) => {
  return (
    <div
      className={`flex items-center gap-2 text-sm font-bold  ${
        view === id ? "text-primary font-bold" : ""
      } ${
        sub ? "border-l border-white  pl-7 h-8 flex items-center " : "uppercase"
      } ${
        available
          ? "cursor-pointer hover:text-primary"
          : "text-white/25 cursor-not-allowed"
      } `}
      onClick={() => {
        if(url) {
          window.open(url, "_blank");
          return;
        }
        if (available) {
          setView(id);
        } else {
          // change view to upgrade plan
          toast("You need to upgrade your plan to access this feature", {
            position: "bottom-center",
            icon: "🔒",
          });
        }
      }}
    >
      {!sub && <Icon size={15} />} {title}{" "}
      {Svg && <Svg width="20px" height="20px" />}
      {url && <ExternalLink size={15} className="text-white/50"/>}
    </div>
  );
};
