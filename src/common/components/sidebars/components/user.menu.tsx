"use client";
import { LogOut, User } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "common/ui/dropdownMenu";
import useUserStore from "store/user/user.store";
import { useRouter } from "next/router";

interface Props {
  setView?: (view: string) => void;
  inDialog?: boolean;
}


export function UserMenu({ setView = () => window.open("/?view=profile-view", "_blank"), inDialog }: Props) {
  const router = useRouter();
  const { logout, user } = useUserStore();
  const handleLogout = () => {
    logout();
    router.push("/");
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        
        <div className={` ${inDialog ? "" : "p-3"}  flex justify-center items-center cursor-pointer`}>
          {user.photo ? (
            <img
              src={user.photo}
              alt="profile"
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <User className="mr-2 h-6 w-6" />
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="ml-8 -mb-5 w-56 bg-secondary border-white/25 text-white">
        <DropdownMenuLabel>
          <div className="text-white">My Account</div>
          <DropdownMenuSeparator className=" bg-white/25" />
          <div className="capitalize text-gray-400">{user?.user_name}</div>
          <div className=" text-gray-400">{user?.email}</div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className=" bg-white/25" />

          <DropdownMenuItem
            className="cursor-pointer hover:!bg-white/25"
            onClick={() => setView("profile-view")}
          >
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>


        <DropdownMenuItem
          className="cursor-pointer hover:!bg-white/25"
          onClick={handleLogout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
