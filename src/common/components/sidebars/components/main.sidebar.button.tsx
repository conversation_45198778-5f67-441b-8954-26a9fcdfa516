import { LucideIcon } from "lucide-react";
import React from "react";

interface MainSidebarButtonProps {
  title: string;
  icon: LucideIcon;
  setView: (view: string) => void;
  id?: string;
  view: string;
  link?: string;
}

export const MainSidebarButton: React.FC<MainSidebarButtonProps> = ({
  title,
  icon: Icon,
  setView,
  id,
  view,
  link,
}) => {
  return (
    <button
      className={`flex items-center justify-center w-full  ${
        view.includes(id) ? "border-l-[3px] border-primary text-[#DBDBDC] " : ""
      }   hover:bg-gray-700/40 `}
      onClick={() => {
        if (Boolean(id)) {
          window.scrollTo(0, 0);
          setView(id);
        } else {
          window.open(link, "_blank");
        }
      }}
    >
      <div className="grid grid-rows-2 grid-flow-col max-h-full min-h-full py-2">
        <section className="row-span-1 h-full flex items-center justify-center">
          <Icon size={22} />
        </section>
        <section className="row-span-1 h-full flex items-center justify-center text">
          <p className="text-[11px]  font-bold">{title}</p>
        </section>
      </div>
    </button>
  );
};
