import { Button } from "common/ui/button";
import { LucideIcon } from "lucide-react";
import React from "react";

interface MainPageHeaderProps {
  title: string;
  description: string;
  btn?: boolean;
  btnText?: string;
  btnIcon?: LucideIcon;
  btnOnClick?: () => void;
  children?: React.ReactNode;
}

export const MainPageHeader: React.FC<MainPageHeaderProps> = ({
  title,
  description,
  btn = false,
  btnText = "Settings",
  btnIcon: BtnIcon,
  btnOnClick,
  children,
}) => {
  return (
    <>
      <div className="flex flex-wrap justify-between items-center">
        <div className="flex flex-col gap-3">
          <p className="capitalize text-3xl font-bold">{title}</p>
          <span className="text-white/50 font-bold">{description}</span>
        </div>
        {btn && btnOnClick && (
          <Button onClick={btnOnClick}>
            {BtnIcon && <BtnIcon className="mr-2 h-4 w-4" />}
            {btnText}
          </Button>
        )}
        {children}
      </div>
      <hr className="text-white/25 mt-3" />
    </>
  );
};
