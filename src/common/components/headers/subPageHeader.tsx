import { But<PERSON> } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import { LucideIcon } from "lucide-react";
import React from "react";
import { IChildren } from "types/common.types";
import TriggersSheet from "views/knowledgeBase/subviews/dialogs/components/triggers/TriggersSheet";

interface SubPageHeaderProps {
  setKeySearch?: (value: string) => void;
  title: string;
  description: string;
  search?: boolean;
  searchPlaceholder?: string;
  btn?: boolean;
  btnText?: string;
  btnIcon?: LucideIcon;
  btnOnClick?: () => void;
  btnSheet?: React.FC<IChildren>;
  disabled?: boolean;
  children?: React.ReactNode;
}

export const SubPageHeader: React.FC<SubPageHeaderProps> = ({
  setKeySearch,
  title,
  description,
  search = false,
  searchPlaceholder = "Search...",
  btn = false,
  btnText = "Create New",
  btnIcon: BtnIcon,
  btnOnClick,
  btnSheet: BtnSheet,
  disabled,
  children,
}) => {
  return (
    <>
      <div className="flex flex-wrap justify-between items-center">
        <div className=" flex flex-col gap-3">
        <p className="capitalize text-3xl font-bold">{title}</p>
          <span className="text-white/50 font-bold">{description}</span>
        </div>
        {search && (
          <div className="w-5/12">
            <CustomSearch
              placeholder={searchPlaceholder}
              onChange={(value) => setKeySearch(value)}
            />
          </div>
        )}
        <div className="flex flex-col gap-2">
        {btn && BtnSheet && (
          <BtnSheet>
            <Button disabled={disabled}>
              {BtnIcon && <BtnIcon className="mr-2 h-4 w-4" />}
              {btnText}
            </Button>
          </BtnSheet>
        )}
        {btn && !BtnSheet && (
          <Button onClick={btnOnClick} disabled={disabled}>
            {BtnIcon && <BtnIcon className="mr-2 h-4 w-4" />}
            {btnText}
          </Button>
        )}
        {btn && !BtnSheet && (
          <TriggersSheet/>
        )}
        </div>
        {children}
      </div>
      <hr className="text-white/50" />
    </>
  );
};
