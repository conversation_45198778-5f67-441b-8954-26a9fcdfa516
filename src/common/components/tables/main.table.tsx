import { Checkbox } from "common/ui/inputs/checkbox";
import Pagination from "common/ui/pagination";
import { cn } from "lib/utils";
import { Loader2, LucideIcon } from "lucide-react";
import React, { useState, useMemo, useEffect } from "react";

type ColumnType = {
  name: string;
  key: string;
};

type ActionType = {
  label: string;
  icon?: LucideIcon;
  onClick?: (item: any) => void;
  component?: (props: any) => React.ReactNode;
};

interface MainTableProps {
  data: any[];
  columns: Array<ColumnType>;
  actions?: Array<ActionType>;
  itemsPerPage?: number;
  loading?: boolean;
  className?: string;
  checkBoxes?: boolean;
  onSelect?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isSelectAll?: boolean;
  isSelectOne?: (id: string) => boolean;
  idKey?: string;
}

export const MainTable: React.FC<MainTableProps> = ({
  data,
  columns,
  actions,
  itemsPerPage,
  loading,
  className,
  checkBoxes,
  onSelect,
  isSelectAll,
  isSelectOne,
  idKey,
}) => {
  const isCheckBoxes = Boolean(checkBoxes && onSelect && isSelectOne && idKey)
  const [currentPage, setCurrentPage] = useState(1);
  const [currentTableData, setCurrentTableData] = useState(data);

  useEffect(() => {
    if (itemsPerPage) {
      const firstPageIndex = (currentPage - 1) * itemsPerPage;
      const lastPageIndex = firstPageIndex + itemsPerPage;
      setCurrentTableData(data?.slice(firstPageIndex, lastPageIndex));
    } else {
      setCurrentTableData(data);
    }
    if (currentPage > 1 && data?.length <= (currentPage - 1) * itemsPerPage) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage, data, itemsPerPage]);

  return (
    <>
      <table className={cn("w-full", className)}>
        <thead className="w-full sticky top-0 z-30 bg-[#1A1A1D] ">
          <tr>
            {columns?.map((column, i) => {
              return (
                <th
                  key={i}
                  className="px-4 py-5 font-medium text-left text-white"
                >
                  <div className="flex items-center gap-2 ">
                  {
                    isCheckBoxes && i === 0 && (
                      <Checkbox
                    name="select-all"
                    checked={isSelectAll}
                    onChange={onSelect}
                    />
                    )
                  }
                  {column.name}

                  </div>
                </th>
              );
            })}
            {actions && <th className="px-4 py-5"></th>}
          </tr>
        </thead>

        <tbody className="divide-y divide-white/25 py-2  bg-accent">
          {loading?  <tr className="text-center bg-white/25">
              <td
                className="p-5 animate-pulse capitalize"
                colSpan={actions ? columns.length + 1 : columns.length}
              >
                <Loader2 className="animate-spin text-white mx-auto" size={25} />
              </td>
            </tr> :
          
          currentTableData?.length ? (
            currentTableData?.map((item, idx) => {
              return (
                <tr key={idx} className=" hover:backdrop-brightness-125">
                  {columns?.map((column, i) => {
                    return (
                      <td
                        key={i}
                        className="max-w-[300px] truncate px-4 py-2 whitespace-break-spaces break-words text-gray-200"
                      >
                        <div className="flex items-center gap-2 ">
                          {isCheckBoxes && i === 0 && (
                            <Checkbox
                              checked={isSelectOne(item?.[idKey]?.toString())}
                              onChange={onSelect}
                              name={`select-${item?.[idKey]}`}
                              key={idx}
                            />
                          )}
                          {column.key.includes("price") && item["currency"] ? (
                            item?.[column.key] + " " + item["currency"]
                          ) : column.key.includes("status") ? (
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                item[column.key] === "active" || item[column.key] === "APPROVED" || item[column.key] === "Active"
                                  ? "bg-green-100 text-green-800"
                                  : item[column.key] === "received"
                                  ? "bg-green-100 text-green-800"
                                  : item[column.key] === "not received" ||item[column.key] === "NOTAPPROVED" ||  item[column.key] === "REJECTED"
                                  ? "bg-red-100 text-red-800" 
                                  : ""
                                  
                              }`}
                            >
                              {item[column.key].toString()}
                            </span>
                          ) : (
                           <p className="break-words max-w-[18rem]">{item[column.key] || "-"}</p>  
                          )}
                        </div>
                      </td>
                    );
                  })}
                  {actions && (
                    <td className=" px-4 py-2   relative">
                      <div className="flex gap-2 justify-end">
                        {actions?.map(
                          ({ label, icon: Icon, onClick, component }, i) => (
                            <React.Fragment key={label}>
                              {component ? (
                                component({ item })
                              ) : (
                                <Icon
                                  key={i}
                                  onClick={() => onClick(item)}
                                  className={`text-white ${
                                    label === "Delete"
                                      ? "hover:text-red-500"
                                      : "hover:text-primary"
                                  } cursor-pointer`}
                                  size={15}
                                />
                              )}
                            </React.Fragment>
                          )
                        )}
                      </div>
                    </td>
                  )}
                </tr>
              );
            })
          ) : (
            <tr className="text-center bg-white/25">
              <td
                className="p-5 capitalize"
                colSpan={actions ? columns.length + 1 : columns.length}
              >
                No data was found
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {itemsPerPage && (
        <Pagination
          currentPage={currentPage}
          totalCount={data?.length}
          pageSize={itemsPerPage}
          onPageChange={(page) => setCurrentPage(page)}
        />
      )}
    </>
  );
};
