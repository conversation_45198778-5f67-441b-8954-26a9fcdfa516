import useConfirmModal from "common/hooks/useConfirmModal";
import EmptySVG from "common/icons/EmptySVG";
import { Avatar, AvatarFallback, AvatarImage } from "common/ui/avatar";
import { Button } from "common/ui/button";
import { File, Headset, Mail, MessageCircle, Paperclip, Phone, User, User2, X } from "lucide-react";
import React, { useMemo, useState } from "react";
import toast from "react-hot-toast";
import showdown from "showdown";
import { Ticket, TicketCommentAttachment } from "store/ticketing/ticketing.types";
import CustomRichTextEditor from "./customRichEditor";
import { createComment, updateTicket } from "apis/ticketing.api";
import constant from "constant";
import { uploadBotImages } from "apis/file.api";
import generateStorageId from "helpers/generateStorageId";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

type Props = {
  currentTicket: Ticket;
  setCurrentTicket: (ticket: Ticket) => void;
  support_agent_id?: number;
  theme?: string;
  removeNotification?: () => void;
};

export default function TicketCard({
  currentTicket,
  setCurrentTicket,
  support_agent_id,
  theme = "light",
  removeNotification
}: Props) {
  const confirmModal = useConfirmModal();
  const [addCommentLoading, setAddCommentLoading] = useState<boolean>(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [comment, setComment] = useState<string>("");
  const [imageToPreview, setImageToPreview] = useState<TicketCommentAttachment | null>(null);

  const isOwner = useMemo(() => {
    return (
      support_agent_id && currentTicket.support_agent_id === support_agent_id
    );
  }, [support_agent_id, currentTicket]);

  const handlePreviewImage = (attachment:TicketCommentAttachment) => {
    setImageToPreview(attachment);
  };

  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileChange = (e) => {
    const files: File[] = Array.from(e.target.files);

    setAttachments([...attachments, ...files]);
  };

  const removeAttachment = (index) => {
    const newAttachments = attachments.filter((_, i) => i !== index);
    setAttachments(newAttachments);
  };

  const markdownToHtmlConverter = (value) => {
    var converter = new showdown.Converter();

    const html = converter.makeHtml(value);
    return html;
  };

  const handleAddComment = async () => {
    // validate text and attachments
    if (!comment) {
      toast.error("Comment cannot be empty");
      return;
    }

    if (attachments?.length > 5) {
      toast.error("You can only upload upto 5 attachments");
      return;
    }

    setAddCommentLoading(true);

    let attachmentsToSend: {
      url: string;
      name: string;
      type: string;
    }[] = [];

    const uploadPromises = attachments.map((attachment) => {
      const formData = new FormData();
      formData.append("file", attachment);

      const randomText = generateStorageId(5);

      const path = `Bots/${
        currentTicket.bot.file_name
      }/Ticketing/Attachments/${randomText}-${attachment.name}`;

      return uploadBotImages({
        path: path,
        formData: formData,
      })
        .then((res) => {
          const imageURL = constant.MEDIA_STORAGE_URL + path;
          attachmentsToSend.push({ url: imageURL, name: attachment.name, type: attachment.type});
        })
        .catch((err) => console.log(err));
    });

    Promise.all(uploadPromises)
      .then(async () => {
        const updatedComments = await createComment({
          ticket_id: currentTicket.ticket_id,
          comment,
          attachments: attachmentsToSend?.length ? attachmentsToSend : null,
          support_agent_id: support_agent_id ? support_agent_id : null,
        });

        if(support_agent_id && removeNotification) {
          removeNotification();
        }

        if (updatedComments.length) {
          toast.success("Comment added successfully");
          setComment("");
          setAttachments([]);
          setCurrentTicket({
            ...currentTicket,
            comments: [...updatedComments],
          });
        }
      })
      .finally(() => {
        setAddCommentLoading(false);
      });
  };

  if(!currentTicket?.ticket_id) return

  return (
    <div
      className={`p-5 md:px-10 border ${
        theme === "dark" ? "border-white/25" : "border-gray-900/30"
      } rounded-lg`}
    >
    
    
    
    <div className="mb-5   rounded-lg">

    <div className="flex justify-between items-center border-b pb-4 mb-4">
    <h1 className="text-3xl font-semibold ">
      {currentTicket.title}
    </h1>
    <p className="text-sm">
      ID: <span className="font-medium">{currentTicket.ticket_uuid}</span>
    </p>
    </div>
  

   <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">



     <fieldset className="border rounded-md   p-3 w-fit">
    <legend className="text-base text-wrap leading-6 font-bold"> Customer Details</legend>
    <section className="  flex items-center justify-center gap-3">
      {currentTicket?.customer_name && 
         <div className="flex  items-center justify-start gap-2 text-center w-full md:w-auto mb-4 md:mb-0">
         <User2 className="w-5 h-5" />
         <p className=" font-medium text-sm">
           {currentTicket?.customer_name}
         </p>
       </div>
      }
 
 {currentTicket?.customer_email && 
     <div className="flex items-center justify-start gap-2 text-center w-full md:w-auto mb-4 md:mb-0">
     <Mail className="w-5 h-5" />
     <a     
     href={`mailto:${currentTicket?.customer_email}`} 
      className=" font-medium text-sm">
       {currentTicket?.customer_email}
     </a>
   </div>
 }


{currentTicket?.customer_phone && 
    <div className="flex  items-center justify-start gap-2 text-center w-full md:w-auto">
    <Phone className="w-5 h-5" />
    <a
     href={`tel:${currentTicket?.customer_phone}`} 
     className="font-medium text-sm">
      {currentTicket?.customer_phone}
    </a>
   </div>
}

     </section>

    </fieldset>

    {isOwner ? (
      <div className="flex items-center">
        <Select
          value={currentTicket.status}
          onValueChange={async (value) => {
            const updatedTicket = await updateTicket(
              currentTicket.ticket_uuid,
              {
                status: value,
                support_agent_id: support_agent_id,
              }
            );
            if (updatedTicket.ticket_id) {
              setCurrentTicket(updatedTicket);
              toast.success("Status updated successfully");
            } else {
              toast.error("Failed to update status");
            }
          }}
        >
          <SelectTrigger className="w-[180px]  border rounded-lg ">
            <SelectValue placeholder="Select a status" />
          </SelectTrigger>
          <SelectContent>
            {["open", "waiting for customer response", "closed", "reopened"].map(
              (status, i) => {
                return (
                  <SelectItem  key={i} value={status}>
                    <span className="capitalize">{status}</span>
                  </SelectItem>
                );
              }
            )}
          </SelectContent>
        </Select>
      </div>
    ) : (
      <div className="text-sm  border rounded-full px-4 py-2 capitalize">
        {currentTicket.status}
      </div>
    )}
  </div>




    <fieldset className="border rounded-md   p-3 w-fit">
    <legend className="text-base text-wrap leading-6 font-bold"> Issue Details</legend>
    <p className="">{currentTicket.description}</p>

    </fieldset>

</div>


 
      {currentTicket.comments?.length > 0 ? (
        <div>
          <h1 className="font-bold text-xl my-2">
            Comments ({currentTicket.comments.length})
          </h1>
          <div
            className={`p-5 border ${
              theme === "dark" ? "border-white/25" : "border-gray-900/30"
            } rounded-lg h-fit max-h-[500px] overflow-y-auto`}
          >
            {currentTicket.comments.map((comment, index) => (
              <div
                key={index}
                className={`mb-4 border ${
                  theme === "dark" ? "border-white/25" : "border-gray-900/30"
                } p-2 rounded-lg`}
              >
                <div className="py-2 flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src="/placeholder-avatar.jpg" />
                      <AvatarFallback>
                        {comment.sender === "customer" ? (
                          <User className="w-4 h-4" />
                        ) : (
                          <Headset className="w-4 h-4" />
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <span className={"font-semibold"}>
                      {comment.sender === "customer"
                        ? currentTicket.customer_name
                        : comment.support_agent.editor.email}
                    </span>
                  </div>
                  <span
                    className={`text-sm ${
                      theme === "dark" ? "text-gray-400" : "text-gray-500"
                    } `}
                  >
                    {new Date(comment.createdAt).toLocaleString()}
                  </span>
                </div>
                <div className="py-2">
                  <div
                    className={` ${
                      theme === "dark" ? "prose prose-white" : "prose"
                    }`}
                    dangerouslySetInnerHTML={{
                      __html: markdownToHtmlConverter(comment.comment),
                    }}
                  />
                  {comment.attachments?.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-semibold">Attachments:</p>
                      <div className="flex flex-wrap gap-3 items-center mt-1">
                        {comment.attachments.map((attachment, index) => (
                          
                            attachment.type?.includes("image") ? (
                          
                          <div
                            key={index}
                            className="flex items-center rounded-md cursor-pointer"
                            onClick={() => handlePreviewImage(attachment)}
                          >
                            {/* NOTE: only images are handled now */}
                            <img
                              src={attachment.url}
                              alt={attachment.name}
                              className="w-10 h-10 object-cover"
                            />
                          </div>
                        ) : (
                          <a
                            key={index}
                            className="flex items-center bg-gray-300/25 p-2 rounded-md cursor-pointer"
                            href={attachment.url}
                            download
                          >
                            <File className="w-4 h-4 mr-2" />
                            <span className="text-sm">{attachment.name}</span>
                          </a>
                        )

                        ))}
                      
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="mt-8 p-5 border rounded-xl flex flex-col gap-3 font-bold items-center">
          There are no comments on this ticket yet.
          <div className="w-20 h-20">
            <EmptySVG />
          </div>
        </div>
      )}

      {currentTicket.status !== "closed" && (!support_agent_id || isOwner) ? (
        <div className="mt-2 space-y-2">
          <CustomRichTextEditor
            onChangeRichText={(value) => {
              setComment(value);
            }}
            value={comment}
          />
          <div className="flex flex-wrap gap-2 mt-2">
            {attachments.map((attachment, index) => (
              <div
                key={index}
                className="flex items-center bg-gray-300/25 rounded-md "
              >
                <File className="w-4 h-4 mr-2" />
                <span className="text-sm">{attachment.name}</span>
                <span
                  onClick={() => removeAttachment(index)}
                  className="ml-2 h-8 w-8 flex justify-center items-center cursor-pointer"
                >
                  <X className=" h-4 w-4" />
                </span>
              </div>
            ))}
          </div>

          <div className="flex justify-between w-full">
            <Button
              variant="outline"
              onClick={() => fileInputRef.current.click()}
            >
              <Paperclip className="w-4 h-4 mr-2" />
              Attach Files
            </Button>
            <input
              aria-label="file-input"
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
              ref={fileInputRef}
              accept="image/*, application/pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt, .csv"
            />
            <Button
              disabled={!comment}
              onClick={() => {
                if (!comment || !comment.trim()?.replace(/\s/gm, "")) {
                  toast.error("Comment cannot be empty");
                  return;
                }

                confirmModal.onOpen();
                confirmModal.setType("add");
                confirmModal.setText(
                  "Are you sure you want to add this comment?"
                );
                confirmModal.setSubtext("You can't undo this action.");
                confirmModal.setBtntext("Add Comment");
                confirmModal.setOnConfirm(handleAddComment);
              }}
              loading={addCommentLoading}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Add Comment
            </Button>
          </div>
        </div>
      ): null}
      {imageToPreview && (
        <div className={`fixed inset-0 w-full h-full p-5 flex items-center justify-center bg-black/80  `}>
          <section className={`w-1/2 h-1/2 ${theme ==='dark' ? "bg-black/90 border-gray-700" : "bg-neutral-100/90"}  border rounded-lg relative flex flex-col items-center justify-center`}>
          <p>{imageToPreview.name}</p>
          <img
            src={imageToPreview.url}
            alt="preview"
            className="w-[70%] h-[90%] object-contain"
          />
          <div className={`absolute top-2 right-2 cursor-pointer hover:scale-105  w-8 h-8 ${theme ==='dark' ? "bg-slate-800" : "bg-gray-400"}   shadow-md flex items-center justify-center rounded-full`}>
          <X
            className="text-white"
            size={25}
            onClick={() => setImageToPreview(null)}
            />
          </div>
            </section>
        </div>
      )}
    </div>
  );
}
