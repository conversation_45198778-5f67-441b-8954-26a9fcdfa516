"use client";

import useItemStore from "store/item/item.store";
import { IItem } from "store/item/item.types";
import { z } from "zod";
// const items = useItemStore((state) => state.items) as IItem[];

// const checkItemNameDuplicate = value => {
//     const duplicate = items.find(a => a.item_title === value);

//     if (duplicate) {
//       setDuplicateError(true);
//     } else {
//       setDuplicateError(false);
//     }
//   };

// check with zod if item name is duplicate

// const CreateItemSchema = z.object({
//   item_name: z
//     .string()
//     .min(1, { message: "Item name is required" })
//     .refine((value) => !items.find((a) => a.item_title === value), {
//       message: "Item name is reserved, Try another one",
//     }),
//   item_description: z
//     .string()
//     .min(1, { message: "Item description is required" }),
//   item_price: z.string().nonempty("Item price is required"),
//   item_category: z.string().nonempty("Item category is required"),
//   item_icons: z.array(z.string()),
//   item_qty: z.number().nonnegative("Item quantity is required"),
// });

// type CreateItemType = z.infer<typeof CreateItemSchema>;

const CreateCategorySchema = z.object({
  category_name: z
    .string()
    .min(1, "Category name is required")
    .min(3, "Category name must be at least 3 characters")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Category name is required"),
  category_description: z.string(),
  category_icon: z.string().optional().nullable(),
});

type CreateCategoryType = z.infer<typeof CreateCategorySchema>;

export type { CreateCategoryType };
export { CreateCategorySchema };
