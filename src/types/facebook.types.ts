type InstagramBusinessPage = {
    id: string;
    name: string;
    username: string;
    access_token: string;
    connected?: boolean;
    facebook_channel_id?: number; 
}

type TUserFacebookPage = {
    facebook_channel_id?: number; 
    access_token: string;
    category: string;
    category_list: [
      {
        id: string;
        name: string;
      }
    ];
    name: string;
    id: string;
    tasks: string[];
    connected?: boolean;
    instagram_business_account?: InstagramBusinessPage;
  };
  
  type TConnectedPage = {
    facebook_channel_id?: number;
    pageId: string;
    page_token: string;
    channel: "facebook" | "instagram";
    name: string;
    createdAt?: string;
    updatedAt?: string;
    bot_id: number;
    connected?: boolean;
    instagram_business_account?: InstagramBusinessPage;
  };
  

  type TPagesData = {
    page: TUserFacebookPage;
    data:
      | [
          {
            category: string;
            link: string;
            name: string;
            id: string;
            subscribed_fields: string[];
          } | null
        ]
      | [];
  };
  export type {
    TUserFacebookPage,
    TConnectedPage,
    TPagesData,
  };