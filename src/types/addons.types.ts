import * as z from "zod";

type VoiceType = {
  bot_id: number;
  bot_voice_id: number;
  createdAt?: string;
  show_stt_text: boolean;
  stt_active: boolean;
  tts_active: boolean;
  tts_gender: "male" | "female";
  updatedAt?: string;
  voice_active: boolean;
  custom_voice_active: boolean;
  female_voice:string;
  male_voice:string;
};

type WeatherType = {
  bot_id: number;
  weather_active: boolean;
  weather_id: number;
  trigger_name: string;
  createdAt: string;
  updatedAt: string;
};

type ReportAddonType = {
  ask_complain: boolean;
  ask_email: boolean;
  ask_phone: boolean;
  ask_username: boolean;
  bot_id: number;
  createdAt: string;
  report_plugin_id: number;
  status_active: boolean;
  updatedAt: string;
};

type LeadAddonType = {
  bot_id: number;
  check_email: boolean;
  check_phone: boolean;
  createdAt: string;
  is_optional: boolean;
  lead_plugin_id: number;
  updatedAt: string;
  active: boolean;
};

const CartSettingsSchema = z.object({
  cart_currency: z.string().nonempty(),
  vat_ammount: z.number().min(0, { message: "VAT amount must be positive" }),
  cart_delivery: z.number().nonnegative(),
  vat_reg_num: z.string().optional(),
  decimal: z.number().nonnegative(),
  email: z.string().email().min(1, { message: "Email is required" }),
  phone_number: z
    .string()
    .min(1, { message: "Phone number is required" })
    .min(10),
  phone_verified: z.boolean().refine((val) => val === true, {
    message: "Phone number must be verified",
  }),
  email_verified: z.boolean().refine((val) => val === true, {
    message: "Email must be verified",
  }),
});

type CartSettings = z.infer<typeof RefinedCartSettingsSchema>;

const RefinedCartSettingsSchema = CartSettingsSchema.refine(
  (val) => {
    if (val.vat_ammount && val.vat_ammount > 0) {
      return val.vat_reg_num !== "";
    }
    return true;
  },
  {
    path: ["vat_reg_num"],
    message: "VAT registration number is required",
  }
);

export type {
  VoiceType,
  WeatherType,
  ReportAddonType,
  CartSettings,
  LeadAddonType,
};
export { RefinedCartSettingsSchema, CartSettingsSchema };
