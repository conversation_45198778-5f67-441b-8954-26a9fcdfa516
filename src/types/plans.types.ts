interface IPlan {
  cart: boolean;
  createdAt: string;
  currency: string;
  facebook_channel: boolean;
  fallback_popup: boolean;
  googlesheet_item_integration: boolean;
  googlesheet_qna_integration: boolean;
  instegram_channel: boolean;
  marketing_popup: boolean;
  max_apis: number;
  max_cards: number;
  max_categories: number;
  max_dialogs: number;
  max_editors: number;
  max_faqs: number;
  max_feature_items: number;
  max_features: number;
  max_item_options: number;
  max_items: number;
  max_offer_items: number;
  max_offers: number;
  max_orders: number;
  max_popups: number;
  max_reservations: number;
  max_rooms: number;
  max_suggestions: number;
  max_tables: number;
  max_users: number;
  monthly_transaction: number;
  monthly_users: number;
  offer: boolean;
  plan_description: string;
  plan_function_id: number;
  plan_id: number;
  plan_name: string;
  plan_order: number;
  plan_price: number;
  reservation: boolean;
  sales_dashboard: boolean;
  shopify_item_integration: boolean;
  shopify_web_plugin: boolean;
  test_chat: boolean;
  transaction_dashboard: boolean;
  updatedAt: string;
  voice: boolean;
  voice_transactions: number;
  weather: boolean;
  web_channel: boolean;
  web_designer: boolean;
  whatsapp_channel: boolean;
  zendesk_livechat_integration: boolean;
  zendesk_ticketing_integration: boolean;
}

export type { IPlan };
