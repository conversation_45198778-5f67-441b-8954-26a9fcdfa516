import { IBadWordConfig } from "store/badword/badword.types";

type botConfigType = {
  addToCartMessage: string;
  afterSendingVerificationCodeMessage: string;
  askAboutItemSpecsMessage: string;
  askAboutReservationDateMessage: string;
  bot_category: null;
  bot_config_id: number;
  bot_icon: string;
  bot_id: number;
  bot_name: string;
  bot_type: string;
  busniessClosedMessage: string;
  cartAlreadyCheckedOutMessage: string;
  checkoutCartMessage: string;
  checkoutCartSuggestionMessage: string;
  checkoutMessage: string;
  confirmSendingVerificationCodeMessage: string;
  conflictMessage: string;
  continueMessage: string;
  createdAt: string;
  deleteCartMessage: string;
  deleteMessage: string;
  deleted: boolean;
  description: string;
  distanceMessage: string;
  domain: string | null;
  dropCartSuggestionMessage: string;
  fallback_dialog: string;
  fallback_message: string;
  file_name: string;
  is_template: boolean;
  itemCardTourAskAboutSpecsMessage: string;
  itemCardTourDescriptionMessage: string;
  itemCardTourItemPriceMessage: string;
  itemCardTourShareMessage: string;
  itemCardTourTtsMessage: string;
  itemsfoundMessage: string;
  language: string;
  mainTourCloseChatbotWindowMessage: string;
  mainTourMainMenuMessage: string;
  mainTourMicMessage: string;
  mainTourResizeChatbotWindowMessage: string;
  mainTourTextInputMessage: string;
  mainTourToggleMicLanguageMessage: string;
  mock_endpoint: string | null;
  outOfContextMessage: string;
  plan_id: number;
  processAlreadyProceedMessage: string;
  processCanceledMessage: string;
  quantityMessage: string;
  rejectSendingVerificationCodeButtonMessage: string;
  requestAddressMessage: string;
  requestItemNoteMessage: string;
  requestNameMessage: string;
  requestNumberOfGuestsMessage: string;
  reservedSuccessfullyMessage: string;
  service_secret: string;
  stand_by_messages: string;
  status: string;
  suggested_actions: Array<any>;
  tableNotAvailableMessage: string;
  updatedAt: string;
  user_id: number;
  verifyPhoneMessage: string;
  verifySuccessfulMessage: string;
  welcome_dialog: string;
  welcome_message_set: Array<any>;
  bad_word_config: IBadWordConfig | null;
  wrongVerificationCodeButtonMessage: string;
};

export type { botConfigType };
