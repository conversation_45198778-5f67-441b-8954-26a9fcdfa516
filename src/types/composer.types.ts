import {LucideIcon} from "lucide-react";
import { FC } from "react";
import { Node } from "reactflow"
import { IDialog } from "store/dialog/dialog.types";
import { ITrigger } from "store/trigger/trigger.types";

export interface IBlock {
    block_type: string
    icon: LucideIcon,
    color: string,
    label: string,
    isSimple?: boolean,
    description?: string,
    hasConnectors?: boolean,
}


export interface IBlockConfig {
    block_type: string,
    name: string,
    handler: string,
    handlerParams: any[]
}

export interface IPromptFormProps {
    nodeData:  Node;
    onSaveNodeData: (node: Node) => void;
    entities: Record<string,string>;
    setEntities: (entities: Record<string,string>) => void;
    nodes: Node[];
    dialog?: IDialog
    dialogTriggers: ITrigger[]
    setDialogTriggers: (triggers: ITrigger[]) => void;
    globals?: Record<string,string>; 
    onExpandChange?: (expand: boolean) => void;
}
  
export type IPromptForm =  FC<IPromptFormProps>;


export type INodeContent = FC<Node>;
