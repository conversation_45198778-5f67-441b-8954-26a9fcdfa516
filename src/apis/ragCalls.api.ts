import constant from "constant";
import { createPostJSON } from "helpers/custom";

const testLLM = async (data: {
    llm_config:{
        llm_key: string,
        llm_type: string
  }}) => {
    return fetch(
      constant.i2i_RAG_SERVER.concat('test/llm'),
      createPostJSON({data})
    )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
export { testLLM, }