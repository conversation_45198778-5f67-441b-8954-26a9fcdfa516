import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/dialog";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const getOneByUrl = (url: string) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/url", "?url=", url),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTriggers = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/triggers", "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTriggerbydialoId = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/gettriggers", "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getIsLiveURL = async (dialog_id) =>{
  return fetch(
    constant.SERVER_URL.concat(dest, "/isLive", "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const setFallback = async(dialog_id) =>{
  console.log("working", dialog_id);
  return fetch(
    constant.SERVER_URL.concat(dest, "/fallback", "?dialog_id=", dialog_id),
    createPutJSON({dialog_id})
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}

const setWelcome = async(dialog_id) =>{
  return fetch(
    constant.SERVER_URL.concat(dest, "/welcome", "?dialog_id=", dialog_id),
    createPutJSON({dialog_id})
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}


const getFallback = async(bot_id) =>{
  return fetch(
    constant.SERVER_URL.concat(dest, "/fallback", "?bot_id=", bot_id),
    createGetJSON()
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}

const getWelcome = async(bot_id) =>{
  return fetch(
    constant.SERVER_URL.concat(dest, "/welcome", "?bot_id=", bot_id),
    createGetJSON()
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}

const removeFallback = async(bot_id) =>{
  return fetch(
    constant.SERVER_URL.concat(dest, "/fallbackremove", "?bot_id=", bot_id),
    createPutJSON({bot_id})
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}

const removeWelcome = async(bot_id) =>{
  return fetch(
    constant.SERVER_URL.concat(dest, "/welcomeremove", "?bot_id=", bot_id),
    createPutJSON({bot_id})
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}

const duplicate = async(dialog, dialogName)=>{
  console.log(dialogName);
  return fetch(
    constant.SERVER_URL.concat(dest, `/duplicate?dialog_id=${dialog.dialog_id}&bot_id=${dialog.bot_id}&dialogName=${dialogName}`),
    createPostJSON(dialog)
  )
  .then((response) =>{
    return response.json();
  })
  .catch((err) => console.log((err)));
}


export {
  getAll,
  getOne,
  createOne,
  updateOne,
  deleteOne,
  getTriggers,
  getOneByUrl,
  getIsLiveURL,
  setFallback,
  setWelcome,
  getFallback,
  getWelcome,
  removeFallback,
  removeWelcome,
  getTriggerbydialoId,
  duplicate,
};
