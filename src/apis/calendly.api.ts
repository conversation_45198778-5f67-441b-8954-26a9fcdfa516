import constant from "constant";
import { createGetJSO<PERSON>, create<PERSON>ostJSO<PERSON> } from "helpers/custom";
const client_id = constant.CALENDLY_CLIENT_ID;
const client_secret = constant.CALENDLY_CLIENT_SECRET;
const combinedCredentials = `${client_id}:${client_secret}`;
const encodedCredentials = btoa(combinedCredentials);

const connectCalendly = async (code) => {
  const options = {
    method: "POST",
    headers: {
      //   "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Basic ${encodedCredentials}`,
    },
    body: new URLSearchParams({
      grant_type: "authorization_code",
      code: code,
      redirect_uri: constant.CALENDLY_REDIRECT_URI,
    }),
  };
  return fetch(constant.CALENDLY_AUTH_URL.concat("/token"), options)
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

/**
 * @param {string} access_token
 * @returns {Promise} Promise object represents the user data
 * @description Get user data and scheduling url from Calendly API
 */
const getUserData = async (access_token) => {
  const options = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${access_token}`,
    },
  };

  return fetch("https://api.calendly.com/users/me", options)
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

/**
 * refresh token expires once used
 */

const getAccessTokenFromRefreshToken = (refresh_token) => {
  const options = {
    method: "POST",
    headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Basic ${encodedCredentials}`,
    },
    body: new URLSearchParams({
      grant_type: "refresh_token",
      refresh_token: refresh_token,
    }),
  };
  return fetch(constant.CALENDLY_AUTH_URL.concat("/token"), options)
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

export { connectCalendly, getAccessTokenFromRefreshToken, getUserData };
