import constant from "constant";
import { createPostJSON } from "helpers/custom";

export const getTrStats = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_stats`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrHeatMap = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_heatmap`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrMessagesUsersDate = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_messages_users_date`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrAnswered = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_answered_date`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrMessagesUsersChannels = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_messages_users_channel`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrFunctionsRequests = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_functions_requests_count`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrUsersPerYear = async (bot_id: number) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_users_year`),
      createPostJSON({ bot_id })
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrLastMessages = async (bot_id: number) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_last_messages`),
      createPostJSON({ bot_id })
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrTopNotAnswered = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_top_not_answered`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrMessagesUsersCountry = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_messages_users_country`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrNotAnsweredInfo = async (data: {
  bot_id: number;
  message: string;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_not_answered_info`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrConversations = async (
  data: {
    bot_id: number;
  },
  page: number = 1
) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_conversations?page=${page}`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getTrConversationHistory = async (
  data: {
    bot_id: number;
    conversation_id: string;
  },
  page: number = 1
) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_chat_history?page=${page}`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const findTRConversation = async (data: {
  bot_id: number;
  searchQuery: string;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_conversations_find`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};

export const getCheckpointStats = async (data: {
  bot_id: number;
  start_day: number;
  start_month: number;
  start_year: number;
  end_day: number | null;
  end_month: number | null;
  end_year: number | null;
}) => {
  try {
    const response = await fetch(
      constant.SERVER_URL.concat(`/tr_checkpoints`),
      createPostJSON(data)
    );
    return await response.json();
  } catch (error) {
    return null;
  }
};
