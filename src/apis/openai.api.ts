import constant from "constant";

const transcribe = async (key, file) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("model", "whisper-1");

  return fetch(constant.OPENAI_WHISPER_URL, {
    method: "POST",
    headers: {
      // "Content-Type": "multipart/form-data",
      Authorization: `Bearer ${key}`,
    },
    body: formData,
  }).then((res) => {
    return res.json();
  });
};

export { transcribe };
