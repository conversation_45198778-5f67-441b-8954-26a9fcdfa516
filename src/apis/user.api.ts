import constant from "constant";
import { createPostJSO<PERSON>, createPutJSON } from "helpers/custom";

const login = (data) => {
  return fetch(constant.SERVER_URL.concat("/login"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const generateToken = (data) => {
  return fetch(
    constant.SERVER_URL.concat("/auth/generate/token"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const register = (data) => {
  return fetch(constant.SERVER_URL.concat("/register"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const logout = (data) => {
  return fetch(constant.SERVER_URL.concat("/logout"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const forgot = (data) => {
  return fetch(
    constant.SERVER_URL.concat("/forgot-password"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const verifyToken = (data) => {
  return fetch(
    constant.SERVER_URL.concat("/verify-token"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const setPassword = (data) => {
  return fetch(
    constant.SERVER_URL.concat("/new-password"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateProfile = (data) => {
  return fetch(constant.SERVER_URL.concat("/user"), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  login,
  register,
  logout,
  forgot,
  setPassword,
  updateProfile,
  generateToken,
  verifyToken,
};
