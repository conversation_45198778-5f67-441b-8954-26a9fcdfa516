import constant from "constant";
import { createGetJSON } from "helpers/custom";

const dest = "/custom-dashboard";

const getCustomDashboard = async (bot_id: number) => {
    try {
      const response = await fetch(
        constant.SERVER_URL.concat(dest, "?bot_id=", `${bot_id}`),
        createGetJSON()
      );
      return response.json();
    } catch (err) {
      console.error("Error fetching custom dashboard:", err);
      return [];
    }
  };


export { getCustomDashboard };
