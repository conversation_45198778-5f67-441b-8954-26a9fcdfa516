import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
} from "helpers/custom";

const dest = "/dynamic/form/schema";

const getOne = (bot_id, dynamic_form_schema_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "?bot_id=",
      bot_id,
      "&dynamic_form_schema_id=",
      dynamic_form_schema_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createSchema = (payload: {
  bot_id: number;
  schema: Record<"label", string>[];
  title: string;
}) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON({ ...payload }))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteSchema = (bot_id, dynamic_form_schema_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest),
    createDELETEJSON({ bot_id, dynamic_form_schema_id })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSchemaResponses = (dynamic_form_schema_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      "/dynamic/form/response?dynamic_form_schema_id=",
      dynamic_form_schema_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getOne, getAll, createSchema, deleteSchema, getSchemaResponses };
