import axios from "axios";
import constant from "constant";
import { createGet<PERSON><PERSON><PERSON>, createPost<PERSON>SO<PERSON> } from "helpers/custom";

const uploadSendMessageAttachments = async (attachment) => {
  const config = {
    headers: {
      "content-type": "multipart/form-data",
    },
  };
  const attachmentURL = await axios.post(
    constant.STORAGE_SERVER_URL.concat(
      `uploadImages?path=${
        attachment.type.includes("image")
          ? attachment.path + "&image=true"
          : attachment.path
      }`
    ),
    attachment.formData,
    config
  );
  return { ...attachment, URL: attachmentURL.config.url };
};
const uploadBotImages = (image) => {
  const config = {
    headers: {
      "content-type": "multipart/form-data",
    },
  };
  return axios.post(
    constant.STORAGE_SERVER_URL.concat(`uploadImages?path=${image.path}`),
    image.formData,
    config
  );
};

const uploadFormData = (file) => {
  const config = {
    headers: {
      "content-type": "multipart/form-data",
    },
  };
  return axios.post(
    constant.STORAGE_SERVER_URL.concat(`uploadImages?path=${file.path}`),
    file.formData,
    config
  );
};

const uploadFile = (data) => {
  return fetch(
    constant.STORAGE_SERVER_URL.concat("uploadFile"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getFile = (path) => {
  return fetch(
    constant.STORAGE_SERVER_URL.concat(`getFile?path=${path}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteFile = (path) => {
  return fetch(
    constant.STORAGE_SERVER_URL.concat(`deleteFile?path=${path}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const publishFile = (body) => {
  return fetch(
    constant.STORAGE_SERVER_URL.concat(`bot/publish/test`),
    createPostJSON(body)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const generateWebchat = (body) => {
  return fetch(
    constant.STORAGE_SERVER_URL.concat(`bot/generate/webchat`),
    createPostJSON(body)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const uploadBufferImage = (body) => {
  return fetch(
    constant.STORAGE_SERVER_URL.concat(`uploadBufferImage`),
    createPostJSON(body)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  uploadSendMessageAttachments,
  uploadBotImages,
  uploadFile,
  getFile,
  deleteFile,
  publishFile,
  generateWebchat,
  uploadBufferImage,
  uploadFormData,
};
