import constant from "constant";
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createGetJSON,
  createDELETEJSON,
} from "helpers/custom";

const dest = "/plans";

export interface SlackIntegrationObj {
  slack_integration_id: number;
  bot_id: number;
  channel_id: string;
  channel: string;
  api_token: string;
  createdAt: string;
  updatedAt: string;
}
const getSlackBotToken = async (bot_id: number): Promise<string> => {
  const res = await fetch(
    constant.MOCK_URL.concat("/api/slack/jwt"),
    createPostJSON({ bot_id })
  );
  const data = (await res.json()) as { token: string };
  return data.token;
};

const getAllConnections = async (
  bot_id: number
): Promise<SlackIntegrationObj[]> => {
  const res = await fetch(
    constant.SERVER_URL.concat("/slack-integration/bot/", bot_id.toString()),
    createGetJSON()
  );
  const data = (await res.json()) as any[];
  return data;
};

const deleteOneConnection = async (
  bot_id: number,
  channel: string
): Promise<boolean> => {
  const res = await fetch(
    constant.SERVER_URL.concat("/slack-integration"),
    createDELETEJSON({ bot_id, channel })
  );
  const data = (await res.json()) as any[];

  if (Object.keys(data)?.includes("message")) return false;
  return true;
};

export { getSlackBotToken, getAllConnections, deleteOneConnection };
