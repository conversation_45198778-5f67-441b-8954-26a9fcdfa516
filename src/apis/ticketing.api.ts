import constant from "constant";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON>et<PERSON><PERSON><PERSON>, create<PERSON>ostJSO<PERSON>, createPutJSON } from "helpers/custom";
import { Ticket } from "store/ticketing/ticketing.types";

const getTicket = async (ticketUUID: string) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/ticket/${ticketUUID}`,
    createGetJSON()
  );

  return res.json();
};

type TicketKeys = "customer_email" | "status" | "support_agent_id";

type SingleTicketKey = {
  [K in TicketKeys]: Pick<Ticket, K>;
}[TicketKeys];

const updateTicket = async (
  ticketUUID: string,
  data: SingleTicketKey & { support_agent_id?: number | null }
) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/ticket/${ticketUUID}`,
    createPutJSON(data)
  );

  return res.json();
};

const createComment = async (data: {
  ticket_id: number;
  comment: string;
  support_agent_id?: number;
  attachments:
    | {
        url: string;
        name: string;
      }[]
    | null;
}) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/comment`,
    createPostJSON(data)
  );

  return res.json();
};

const getSupportAgent = async (data: { user_id: number; bot_id: number }) => {
  try {
    const res = await fetch(
      `${constant.SERVER_URL}/ticketing/support-agent?user_id=${data.user_id}&bot_id=${data.bot_id}`,
      createGetJSON()
    );

    return res.json();
  } catch (error) {
    return { error: true };
  }
};

const getTicketsPerAgentDepartments = async (support_agent_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/tickets/agent/${support_agent_id}/departments`,
    createGetJSON()
  );

  return res.json();
};

const assignTicketToAgent = async (data: {
  ticket_uuid: string;
  ticket_id: number;
  support_agent_id: number;
  assigned_by: number;
}) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/ticket/assign`,
    createPostJSON(data)
  );

  return res.json();
};

const getTicketingIntegration = async (bot_id: number) => {
  return fetch(
    `${constant.SERVER_URL}/ticketing/integration?bot_id=${bot_id}`,
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getDepartments = async (bot_id) =>{
  return fetch(
    `${constant.SERVER_URL}/ticketing/departments?bot_id=${bot_id}`,
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
}

const createIntegration = async (bot_id, time_zone) =>{
  return fetch(
    `${constant.SERVER_URL}/ticketing/integration`,
    createPostJSON({bot_id, time_zone})
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
}

const updateIntegration = async (data) =>{
  return fetch(
    `${constant.SERVER_URL}/ticketing/integration`,
    createPutJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
}

const createDepartmentWithCategories = async (data) =>{
  return fetch(
    `${constant.SERVER_URL}/ticketing/categories`,
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
}

const getEditorsWithAgentDetails = async (bot_id) => {
  return fetch(
    `${constant.SERVER_URL}/ticketing/all-editors-agents?bot_id=${bot_id}`,
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const createAgent = async (data) => {
  return fetch(
    `${constant.SERVER_URL}/ticketing/agent`,
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};
const createRating = async (data: {
  ticket_id: number;
  rating: number;
  rating_msg: string;
}) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/rating`,
    createPostJSON(data)
  );

  return res.json();
};

const getTicketingOverview = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `/ticketing/ticketing-overview?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };

const getTicketRating = async (ticket_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/rating?ticket_id=${ticket_id}`,
    createGetJSON()
  );

  return res.json();
};

const getDepartmentCategories = async (department_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/category?department_id=${department_id}`,
    createGetJSON()
  );

  return res.json();
};

const getAgentDepartment = async (editor_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/agent/department?editor_id=${editor_id}`,
    createGetJSON()
  );

  return res.json();
};

const updateAgent = async (data:{
  editor_id: number,
  departments: number[],
  time_zone: string,
}) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/updateAgent`,
    createPutJSON(data)
  );
  return res.json();
};

const deleteAgent = async (editor_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/deleteAgent`,
    createDELETEJSON({editor_id: editor_id})
  );

  return res.json();
};

const deleteDepartment = async (department_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/departments`,
    createDELETEJSON({department_id: department_id})
  );

  return res.json();
};

const updateCategories = async (data: {
  department_id: number,
  editCategories: string[],
  addCategories: string[]
}) => {
  const res = await fetch(
    `${constant.SERVER_URL}/ticketing/categories`,
    createPutJSON(data)
  );

  return res.json();
};



export {
  getTicket,
  updateTicket,
  createComment,
  getSupportAgent,
  getTicketsPerAgentDepartments,
  assignTicketToAgent,
  getTicketingIntegration,
  getDepartments,
  createIntegration,
  updateIntegration,
  createDepartmentWithCategories,
  getEditorsWithAgentDetails,
  createAgent,
  createRating,
  getTicketingOverview,
  getTicketRating,
  getAgentDepartment,
  updateAgent,
  deleteAgent,
  getDepartmentCategories,
  deleteDepartment,
  updateCategories,
};
