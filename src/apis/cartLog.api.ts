import constant from "constant";
import { createGetJSON, createPutJSON } from "helpers/custom";

const dest = "/cart/log";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const updateItem = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest),
    createPutJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getAll, updateItem };
