const getAllBorad = async (token, key) => {
  const res = await fetch(
    `https://api.trello.com/1/members/me/boards?token=${token}&key=${key}`
  ).then((res) => res.json());
  return res;
};
const checkExist = async (token, key) => {
  const res = await fetch(
    `https://api.trello.com/1/members/me/boards?token=${token}&key=${key}`
  );
  return res;
};
const getList = async (id, token, key) => {
  const res = await fetch(
    `https://api.trello.com/1/boards/${id}/lists?token=${token}&key=${key}`
  ).then((res) => res.json());
  return res;
};
const getAllMembers = async (id, token, key) => {
  const res = await fetch(
    `https://api.trello.com/1/boards/${id}/members?token=${token}&key=${key}`
  ).then((res) => res.json());
  return res;
};
const getAllMembersInBorad = async (id, token, key) => {
  const res = await fetch(
    `https://api.trello.com/1/boards/${id}/members?token=${token}&key=${key}`
  ).then((res) => res.json());
  return res;
};
const getAllCardsToMembers = async (idMembers, token, key) => {
  const res = await fetch(
    `https://api.trello.com/1/members/${idMembers}/cards?token=${token}&key=${key}`
  ).then((res) => res.json());
  return res;
};
const getCardsinList = async (token, key, idList) => {
  const res = await fetch(
    `https://api.trello.com/1/lists/${idList}/cards?key=${key}&token=${token}`
  ).then((res) => res.json());
  return res;
};
export {
  getAllBorad,
  getList,
  getAllMembers,
  getAllMembersInBorad,
  getAllCardsToMembers,
  checkExist,
  getCardsinList,
};
