import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";
import { TConnectedPage, TPagesData, TUserFacebookPage } from "types/facebook.types";


const dest = "/facebook";
const page = "/facebook/page";
const pages = "/facebook/pages";

const getOne = (pageId) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?pageId=", pageId),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const getAll = (bot_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(
      pages,
      "?bot_id=" + bot_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
//page_token, pageId, bot_id
const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getPagesFromToken = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "/oauth"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getPageByBotId = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "/page"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const disconnectPage = async (data: {
  facebook_channel_id: number;
  bot_id: number;
}) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};


export {
  getOne,
  createOne,
  updateOne,
  getPagesFromToken,
  getPageByBotId,
  disconnectPage,
  getAll,
};
