import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const getSessions = async (agent_id: number, bot_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(
      `/internal-livechat/sessions?agent_id=${agent_id}&bot_id=${bot_id}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const getAgent = async (bot_id, user_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      "/agent",
      "?bot_id=",
      bot_id,
      "&user_id=",
      user_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const getTransactions = async (session_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      "/internal-livechat/transactions",
      "?session_id=",
      session_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const closeChat = async (data: { session_id: number; agent_id: number; bot_id: number; }) => {
  return fetch(
    constant.SERVER_URL.concat("/internal-livechat/close-chat"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const rejectChat = async (data: { session_id: number; agent_id: number; bot_id: number; }) => {
  return fetch(
    constant.SERVER_URL.concat("/internal-livechat/reject-chat"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateAgentStatus = async (agent_id, bot_id) => {
  return fetch(
    constant.SERVER_URL.concat("/update-agent-status", "?agent_id=", agent_id, "&bot_id=", bot_id),
    createPostJSON({})
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const updateSessionStatus = async (data: {
  session_id: number;
  status: string;
  bot_id: number;
}) => {
  return fetch(
    constant.SERVER_URL.concat("/update-session-status"),
    createPostJSON(data)
  )
  .then((response)=>{
    return response.json();
  })
  .catch((err) => console.log(err));
}

const getLiveCalculated = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `/internal-livechat/live-Chat-calc?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const getInternalLivechatIntegration = async (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      "/internal-livechat/integration",
      "?bot_id=",
      bot_id),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };

  const getAgentsDataCount =async (bot_id) =>{
    return fetch(
      constant.SERVER_URL.concat(
        `/internal-livechat/get-agents-Data?bot_id=${bot_id}`
      ),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };
  
  const getSessionDataCount =async (bot_id) =>{
    return fetch(
      constant.SERVER_URL.concat(
        `/internal-livechat/get-Sessions-Data?bot_id=${bot_id}`
      ),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };
  
  const getQueueDataCount =async (bot_id) =>{
    return fetch(
      constant.SERVER_URL.concat(
        `/internal-livechat/get-Queues-data?bot_id=${bot_id}`
      ),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };

const createInternalLivechatIntegration = async (data: {
  bot_id: number;
  max_sessions_per_agent: number;
}) => {
  return fetch(
    constant.SERVER_URL.concat("/internal-livechat/integration"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const updateInternalLivechatIntegration = async (data: {
  bot_id: number;
  integration_id: number;
  max_sessions_per_agent: number;
}) => {
  return fetch(
    constant.SERVER_URL.concat("/internal-livechat/integration"),
    createPutJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const getEditorsWithAgentDetails = async (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat("/all-editors-agents", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const bulkCreateAgents = async (data) => {
  return fetch(
    constant.SERVER_URL.concat("/internal-livechat/create-agents"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.error(err));
};

const updateAgent = async (data) =>{
  return fetch(
    constant.SERVER_URL.concat("/internal-livechat/update-agent"),
    createPutJSON({agent_id: data.agent_id, ...data})
  )
  .then((response) => {
    return response.json();
  })
  .catch((err) => console.error(err));
}

export {
  getSessions,
  getAgent,
  getTransactions,
  rejectChat,
  updateAgentStatus,
  updateSessionStatus,
  getInternalLivechatIntegration,
  createInternalLivechatIntegration,
  updateInternalLivechatIntegration,
  getEditorsWithAgentDetails,
  bulkCreateAgents,
  getLiveCalculated,
  closeChat,
  getAgentsDataCount, 
  getQueueDataCount, 
  getSessionDataCount,
  updateAgent
};
