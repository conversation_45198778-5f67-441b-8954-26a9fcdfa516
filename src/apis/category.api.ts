import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/category";
const all = "/categories";
const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?bot_id=", bot_id, "&limit=1000"),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

/**
   * NOTE EXAMPLE Data
  {
      "bot_id": 1,
      "category_name":"",
      "category_description":""
  }
   */
const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createMany = (data) => {
  return fetch(constant.SERVER_URL.concat("/categories"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
/**
   * NOTE EXAMPLE Data
  {
      "category_id": 1
  }
   */
const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const deleteAllCategory = (bot_id) => {
  return fetch(constant.SERVER_URL.concat(all), createDELETEJSON(bot_id))
    .then((response) => {
      return response.json();
    })
    .catch((err) => {
      console.log(err);
    });
};
/** 
   * NOTE EXAMPLE Data
  {
      "category_id": 5,
      "bot_id": 1,
      "category_description": "ATLAS",
      "category_name": "",
      "updatedAt": "2021-06-14T03:34:23.365Z",
      "createdAt": "2021-06-14T03:34:23.365Z"
  }
   */
const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getAll,
  createOne,
  deleteAllCategory,
  updateOne,
  deleteOne,
  createMany,
};
