import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/trigger";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (trigger_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?trigger_id=", trigger_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getFirst = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s/first?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getByName = (trigger_name) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s/name?trigger_name=", trigger_name),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAlternative = (bot_id, trigger, url) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "s/alternative?bot_id=",
      bot_id,
      "&trigger=",
      trigger,
      "&url=",
      url
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteMany = (triggers) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s"),
    createDELETEJSON(triggers)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s"), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getBotTriggers = (data) =>{
  return fetch(
    constant.SERVER_URL.concat(`${dest}bot?bot_id=${data.bot_id}`),
    createGetJSON()
  )
  .then((response) => {
    return response.json();
  })
  .catch((err) => console.log(err));
}

export {
  getAll,
  getOne,
  createOne,
  updateOne,
  deleteOne,
  getAlternative,
  deleteMany,
  getFirst,
  getByName,
  updateMany,
  createMany,
  getBotTriggers,
};
