import constant from "../constant";
import { createGetJSON, createPutJSON } from "../helpers/custom";

const dest = "/dashboard";
const health = "/dashboard/health/current";
const getDashboard = (bot_id) => {
  return fetch(constant.SERVER_URL.concat(dest, "?bot_id=", bot_id), createGetJSON())
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateDashboard = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getCurrentHealth = async (bot_id) => {
  const res = await fetch(
    constant.SERVER_URL.concat(health, "?bot_id=", bot_id),
    createGetJSON()
  ).then((res) => {
    return res.json();
  });
  return res;
};

export { getDashboard, updateDashboard, getCurrentHealth };
