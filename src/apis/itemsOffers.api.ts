import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";
const dest = "/offer/items";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (offer_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?offer_id=", offer_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createMany = (offer_id, bot_id, data) => {
  return fetch(
    constant.SERVER_URL.concat(dest),
    createPostJSON({ offer_id, bot_id, offer_items: data })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getAll, getOne, createMany, deleteOne };
