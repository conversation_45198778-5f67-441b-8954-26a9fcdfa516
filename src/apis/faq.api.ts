import axios from "axios";
import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/faq";
const destVoice = "/faqV";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (faq_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?faq_id=", faq_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteMany = (data) => {
  return fetch(constant.SERVER_URL.concat("/v2", dest, "s"), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteAll = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/all"),
    createDELETEJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getFirst = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s/first", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAlters = (bot_id, faq_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "s/alternative",
      "?bot_id=",
      bot_id,
      "&",
      "faq_id=",
      faq_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s"), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createTips = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/tips"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const scrapeFaqs = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "/scraping"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const importFaqsPdf = (formData, bot_id) => {
  const config = {
    headers: {
      "content-type": "multipart/form-data",
    },
  };
  return axios
    .post(
      constant.SERVER_URL.concat(dest, "s", "/pdf", "?bot_id=", bot_id),
      formData,
      config
    )
    .then((response) => {
      return response.data;
    })
    .catch((err) => console.log(err));
};

const updateOneVoice = (data) => {
  return fetch(constant.SERVER_URL.concat(destVoice), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getAll,
  getOne,
  createOne,
  updateOne,
  deleteOne,
  deleteMany,
  deleteAll,
  createMany,
  getFirst,
  getAlters,
  updateMany,
  createTips,
  scrapeFaqs,
  importFaqsPdf,
  updateOneVoice,
};
