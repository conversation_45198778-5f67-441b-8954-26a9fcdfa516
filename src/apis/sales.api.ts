import constant from "constant";
import { createGetJSON } from "helpers/custom";

const dest = "/cart/logs/";

const getTopSold = (bot_id, n, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `${dest}sold/?bot_id=${bot_id}&N=${n}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByCategory = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `${dest}category/?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByOffer = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `${dest}offer/?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByContinent = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `${dest}continent/?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByCountry = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `${dest}country/?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByCity = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      `${dest}city/?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByDay = (bot_id, year) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "day", "?bot_id=", bot_id, "&year=", year),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getSalesByMonth = (bot_id, year) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "month",
      "?bot_id=",
      bot_id,
      "&year=",
      year
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getTopSold,
  getSalesByCategory,
  getSalesByOffer,
  getSalesByContinent,
  getSalesByCountry,
  getSalesByCity,
  getSalesByMonth,
  getSalesByDay,
};
