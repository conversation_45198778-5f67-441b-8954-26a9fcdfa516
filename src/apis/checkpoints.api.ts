import axios from "axios";
import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/checkpoint";

const getAll = async (
  bot_id: number,
  page?: number,
  pageSize?: number,
  start_day?: number,
  start_month?: number,
  start_year?: number,
  end_day?: number,
  end_month?: number,
  end_year?: number
) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s"),
    createPostJSON({
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
      page,
      pageSize,
    })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = async (data: {
  tag: string;
  conversation_id: string;
  bot_id: number;
  channel: string;
  dialog_id: number;
}) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateCheckpoint = async (checkpoint: any) => {
  return fetch(
    constant.SERVER_URL.concat(dest),
    createPutJSON({ ...checkpoint })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = async (dialog_checkpoint_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(dest),
    createDELETEJSON(dialog_checkpoint_id)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = async (dialog_checkpoint_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "/?dialog_checkpoint_id=",
      dialog_checkpoint_id.toString()
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getDialogsWithCheckpoints = async (
  bot_id: number,
  start_day?: number,
  start_month?: number,
  start_year?: number,
  end_day?: number,
  end_month?: number,
  end_year?: number
) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s_dialogs"),
    createPostJSON({
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getDialogCheckpoints = async (
  dialog_id: number,
  start_day?: number,
  start_month?: number,
  start_year?: number,
  end_day?: number,
  end_month?: number,
  end_year?: number
) => {
  return fetch(
    constant.SERVER_URL.concat("/dialog_checkpoints"),
    createPostJSON({
      dialog_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const search = async (data: any, page?: number, pageSize?: number) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s/search"),
    createPostJSON({ ...data, page: page, pageSize: pageSize })
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getAll,
  createOne,
  updateCheckpoint,
  deleteOne,
  getOne,
  getDialogsWithCheckpoints,
  getDialogCheckpoints,
  search,
};
