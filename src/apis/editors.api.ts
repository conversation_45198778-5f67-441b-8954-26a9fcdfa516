import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/editor";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s"), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getEditorPrivilages = (bot_id, user_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "/privilage",
      "?bot_id=",
      bot_id,
      "&user_id=",
      user_id
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getAll, createOne, updateOne, deleteOne, getEditorPrivilages, deleteMany };
