import constant from "constant";
import { createGetJSON, createPutJSON } from "helpers/custom";

const dest = "/theme";
const all = "/themes";

const getOnetheme = async (theme_id) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}?theme_id=${theme_id}`,
    createGetJSON()
  ).then((res) => res);

  return res;
};

const updateOneTheme = async (data) => {
  data.theme_id = 4;
  return fetch(`${constant.SERVER_URL}${dest}`, createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
export { getOnetheme, updateOneTheme };
