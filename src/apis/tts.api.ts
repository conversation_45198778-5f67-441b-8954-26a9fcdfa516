import { createPostJSO<PERSON> } from "helpers/custom";
import constant from "constant";

export const getSpeechText = async (data: {
  text: string;
  voice: string;
  object_name: string;
}): Promise<{ remote_path: string }> => {
  return fetch(constant.TTS_URL, createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export const tashkeelCall = async (data: {
  text: string;
}): Promise<{ result: string }> => {
  try {
    const response = await fetch(
      "https://bot-server-2.azurewebsites.net/api/tashkeel",
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );
    const res_data = await response.json();
    return res_data;
  } catch (error) {
    return {result:data?.text}
  }
};
function generateUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
function removeBetweenSymbols(inputString: string) {
  if(typeof inputString !== "string" || inputString?.length === 0) return inputString
  const doubleHyphensRegex = /--.*?--/g;
  const regex2 = /\([^)]+\)/g;
  const emptyCurlyBracesRegex = /\{\{\}\}/g;  // Remove {{}}
  return inputString.replace(doubleHyphensRegex, "").replace(regex2, "").replace(emptyCurlyBracesRegex, "");
}

export const getAudio = async (voice: string, text: string) => {
  try {
    const content = removeBetweenSymbols(text);
    const randomUUID = generateUUID();
    const data = await getSpeechText({
      text: content,
      voice: voice,
      object_name: "tts/" + randomUUID + ".mp3",
    });
    return data
  } catch {
    console.log("error get voice");
  } finally {
  }
};
