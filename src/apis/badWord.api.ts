import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/badword";
const many = "/badwords";
const all = "/badword/all";

const createMany = (data) => {
  return fetch(constant.SERVER_URL.concat(many), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest,), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteAll = (data) => {
  return fetch(constant.SERVER_URL.concat(all), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteMany = (data) => {
  return fetch(constant.SERVER_URL.concat(many), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (bad_word_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `?bad_word_id=${bad_word_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAll = (bot_id:number) => {
  return fetch(
    constant.SERVER_URL.concat(many, `?bot_id=${bot_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  createMany,
  createOne,
  deleteOne,
  deleteAll,
  getOne,
  getAll,
  updateOne,
  deleteMany
};
