import constant from "constant";
import { createGetJSO<PERSON>, create<PERSON>ostJSO<PERSON>, createPutJSON } from "helpers/custom";

const dest = "/checkpoint/plugin";

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `?bot_id=${bot_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { updateOne, createOne, getOne };