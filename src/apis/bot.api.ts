import constant from "constant";
import {
  createPostJSON,
  createGetJSON,
  createDELETEJSON,
  createPutJSON,
} from "helpers/custom";
import { IBot } from "store/bot/bot.types";

const dest = "/bot";

const getAll = (user_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "?user_id=", user_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = async (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      console.log(response, "responseresponseresponseresponse");
      return response.json();
    })
    .catch((err) => console.log(err, "errerrerrerrerrerr"));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const duplicateOne = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s", "/transfer"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const restartOne = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/restart"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const duplicateBot = async (data: IBot) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "/duplicate?bot_id=",
      data.bot_id.toString()
    ),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getAll,
  getOne,
  createOne,
  updateOne,
  deleteOne,
  duplicateOne,
  restartOne,
  duplicateBot,
};
