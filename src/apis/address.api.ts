import constant from "constant";
import { createGetJSON, createPostJSON, createPutJSON } from "helpers/custom";

const dest = "/bot/info";
const geolocationURL = "https://api.ipstack.com/";

const getOne = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getUserLocation = async () => {
  // const { ip } = await fetch("https://api.ipify.org/?format=json").then((res) =>
  //   res.json()
  // );
  let ip = "";
  try {
    ip = await fetch("https://api.ipify.org/?format=json").then((res) =>
      res.json()
    );
  } catch (err) {
    console.log(err);
  }
  return fetch(
    geolocationURL.concat(ip, "?access_key=35b4a47097d19b00559c135bd8423124"),
    {
      method: "GET",
      headers: {
        Accept: "application/json",
      },
      mode: "cors",
    }
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getUserLocationImage = (bodyToSend) => {
  return fetch(
    constant.SERVER_URL.concat("/bot/user/location"),
    createPostJSON(bodyToSend)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const geocodingLocation = (address) => {
  return fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${constant.GOOGLE_MAP_KEY}`,
    {
      method: "GET",
      headers: {
        Accept: "application/json",
      },
      // credentials: "include",
      mode: "cors",
      //   timeout: 5000,
    }
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getOne,
  updateOne,
  getUserLocation,
  getUserLocationImage,
  geocodingLocation,
};
