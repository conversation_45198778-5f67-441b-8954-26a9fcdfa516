import constant from "constant";
import { createGetJSON } from "helpers/custom";

const dest = "/dashboard";

const getConversationIds = async (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/conversationIds", "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => response.json())
    .catch((err) => console.log(err));
};

const getConversationDetails = async (conversation_id, bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/conversationDetails", "?conversation_id=", conversation_id, "&bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => response.json())
    .catch((err) => console.log(err));
};

export { getConversationDetails, getConversationIds };
