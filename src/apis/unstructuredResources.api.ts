import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/unstructured/resource";
const all = "/unstructured/resources";

const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(all, "?bot_id=", bot_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteMany = (triggers) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s"),
    createDELETEJSON(triggers)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getAll, createOne, createMany, deleteOne, deleteMany };
