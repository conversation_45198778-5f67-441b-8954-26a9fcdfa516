import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/item";
const all = "/items";
const getAll = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      "s",
      "?bot_id=",
      bot_id,
      "&limit=10000",
      "&include_item_hide=true&price_to=10000000000000"
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?item_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const createMany = (data) => {
  //FIXME
  return fetch(constant.SERVER_URL.concat(dest, "s"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createPutJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteMany = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "s/many"), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getAll, getOne, createOne, updateOne, deleteOne, createMany, deleteMany };
