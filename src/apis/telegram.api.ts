import constant from "constant";
import {
  create<PERSON>ost<PERSON><PERSON><PERSON>,
  createGetJSON,
  createDELETEJSON,
} from "helpers/custom";

const dest = "/plans"; 

export interface TelegramIntegrationObj {
  telegram_integration_id: number;
  bot_id: number;
  token: string;
  createdAt?: string;
  updatedAt?: string;
}
const getTelegramIntegration = async (bot_id: number): Promise<TelegramIntegrationObj> => {
  const res = await fetch(
    constant.SERVER_URL.concat(`/telegram-integration/${bot_id}`),
    createGetJSON()
  );
  const data = (await res.json()) as TelegramIntegrationObj;
  return data;
};

const createTelegramIntegration = async (token: string ,bot_id:number): Promise<TelegramIntegrationObj> => {
    const res = await fetch(
        constant.SERVER_URL.concat("/telegram-integration"),
        createPostJSON({ token,bot_id })
      );
      const data = (await res.json()) as TelegramIntegrationObj;
      return data;

}

const deleteTelegramIntegration = async (
    bot_id: number,
  ): Promise<boolean> => {
    const res = await fetch(
      constant.SERVER_URL.concat("/telegram-integration"),
      createDELETEJSON({ bot_id })
    );
    const data = (await res.json()) as TelegramIntegrationObj;
  
    if (Object.keys(data)?.includes("message")) return false;
    return true;
  };

export { getTelegramIntegration,createTelegramIntegration,deleteTelegramIntegration  };
