import constant from "constant";
import { createGetJSON, createPostJSON } from "helpers/custom";

const dest = "/jett/invoices/paid";

const getPaidJettTransactions = () => {
  return fetch(constant.SERVER_URL.concat(dest), createGetJSON())
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getReleasedJettTransactions = () => {
  return fetch(constant.SERVER_URL.concat('/jett/invoices/deleted'), createGetJSON())
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getPaidJettTransactions , getReleasedJettTransactions};
