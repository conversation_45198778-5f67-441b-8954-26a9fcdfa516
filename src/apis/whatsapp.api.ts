import constant from "constant";
import { createGetJSO<PERSON>, create<PERSON>ostJSO<PERSON>, createPutJSON } from "helpers/custom";

const dest = "/whatsapp";
const destVonage = "/whatsapp-vonage";
const getOne = async (bot_id) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}?bot_id=${bot_id}`,
    createGetJSON()
  ).then((res) => res.json());
  return res;
};

const createOne = async (data) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}`,
    createPostJSON({ ...data })
  ).then((res) => res.json());
  return res;
};
const createOneVonage = async (data) => {
  const res = await fetch(
    `${constant.SERVER_URL}${destVonage}`,
    createPostJSON({ ...data })
  ).then((res) => res.json());
  return res;
};
const updateOne = async (data, whatsapp_id: number, bot_id: number) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}`,
    createPutJSON({ ...data, bot_id: bot_id, whatsapp_id: whatsapp_id })
  ).then((res) => res.json());
  return res;
};

const createWaTemplate = async (data, bot_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(`/whatsapp/templates?bot_id=${bot_id}`),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const getWaTemplates = async (bot_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(`/whatsapp/templates?bot_id=${bot_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const waMedia = async (image: any, bot_id: number) => {
  return fetch(
    constant.SERVER_URL.concat(`/whatsapp/mediaVonage?bot_id=${bot_id}`),
    createPostJSON(image)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
export {
  createOne,
  getOne,
  getWaTemplates,
  createWaTemplate,
  waMedia,
  updateOne,
  createOneVonage,
};
