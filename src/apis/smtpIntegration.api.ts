import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

const dest = "/smtp";

const createOne = async (data) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}`,
    createPostJSON({ ...data })
  ).then((res) => res.json());
  return res;
};

const sendHtml = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "/html"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getOne = async (bot_id) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}?bot_id=${bot_id}`,
    createGetJSON()
  ).then((res) => res.json());
  return res;
};

const updateOne = async (data) => {
  const res = await fetch(
    `${constant.SERVER_URL}${dest}`,
    createPutJSON(data)
  ).then((res) => res.json());
  return res;
};

const deleteOne = (data) => {
  return fetch(constant.SERVER_URL.concat(dest), createDELETEJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { getOne, updateOne, sendHtml, createOne, deleteOne };
