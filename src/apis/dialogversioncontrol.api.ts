import constant from "constant";
import {
  createDELETEJSON,
  createGetJSON,
  createPostJSON,
  createPutJSON,
} from "helpers/custom";

//url
const dest = "/dialogversioncontrol";

//used in previous versions table 
const getAll = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "s" , "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getIsStagingTrue = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/stagingtrue", "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};


const getStagingFalse = (dialog_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/stagingFalse", "?dialog_id=", dialog_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getDVC = (dialog_version_control_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/dvc", "?dialog_version_control_id=", dialog_version_control_id),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    }).catch((err) => console.log("err"));
};

const update = async (data, dialog) => {
  return await fetch(
    constant.SERVER_URL.concat(dest, `/update?dialog_id=${data.dialog_id}&bot_id=${data.bot_id}&user_id=${data.user_id}`),
    createPutJSON(dialog))
    .then((response) => {
        return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteDVC = (dialog_version_control_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "?dialog_version_control_id=", dialog_version_control_id),
    createDELETEJSON({
      dialog_version_control_id: dialog_version_control_id
    }))
    .then((response) => {
        return response.json();
    })
    .catch((err) => console.log(err));
};

const publish = (data, dialog) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `/publish?dialog_id=${data.dialog_id}&bot_id=${data.bot_id}&user_id=${data.user_id}`),
    createPutJSON(dialog))
    .then((response) => {
        return response.json();
    })
    .catch((err) => console.log(err));
};

const changeIsLive = (data, dialog) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `/changeIsLive?dialog_version_control_id=${data.dialog_version_control_id}&bot_id=${data.bot_id}&user_id=${data.user_id}`),
    createPutJSON(dialog))
    .then((response) => {
        return response.json();
    })
    .catch((err) => console.log(err));
};


const changeIsStaging = (data, dialog) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `/changeIsStaging?dialog_version_control_id=${data.dialog_version_control_id}&bot_id=${data.bot_id}&user_id=${data.user_id}`),
    createPutJSON(dialog))
    .then((response) => {
        return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getAll,
  getStagingFalse,
  getIsStagingTrue,
  getDVC,
  update,
  deleteDVC,
  publish,
  changeIsLive,
  changeIsStaging,
};
