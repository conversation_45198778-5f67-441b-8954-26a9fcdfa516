import constant from "constant";
import {
  createGetJSON,
  createPostJSON,
} from "helpers/custom";
import { Tbroadcast } from "views/broadcast/boradcasts";

const dest = "/whatsapp/broadcast";
const all= "/whatsapp/broadcasts"
const logsByBroadCast= "/whatsapp/broadcast/logs"
const allNumbers= "/whatsapp/numbers"
import * as signalR from '@microsoft/signalr';

const createOne = (data:Tbroadcast) => {
    return fetch(constant.SERVER_URL.concat(dest), createPostJSON(data))
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.log(err));
  };
  const getbroadcasts = async (bot_id:number) => {
    return fetch(
      constant.SERVER_URL.concat(all, "?bot_id=", bot_id.toString()),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };

  const getbroadcastLogs = async (broadcast_id:number) => {
    return fetch(
      constant.SERVER_URL.concat(logsByBroadCast, "?broadcast_id=", broadcast_id.toString()),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };
  const getWaPhoneNumbers = async (bot_id:number) => {

    return fetch(
      constant.SERVER_URL.concat(allNumbers, "?bot_id=", bot_id.toString()),
      createGetJSON()
    )
      .then((response) => {
        return response.json();
      })
      .catch((err) => console.error(err));
  };

  const signalRNegotiate = (bot_id:number, broadcast_id:number, setIncomingActivity) => {
    const socketServer = "https://i2i-messaging.azurewebsites.net";
  
    const userId = `whatsapp_broadcasting_bot_${bot_id}_${broadcast_id}`;
    const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot_id}&type=server`;
    console.log("negotiationURL", negotiationURL);
    console.log("user_id", userId);
  
    const connectionOptions = {
      transportType: signalR.HttpTransportType.WebSockets, // Adjust the transport type as needed
      logger: signalR.LogLevel.Information, // Adjust the log level as needed
    };
  
    const _connection = new signalR.HubConnectionBuilder()
      .withUrl(negotiationURL)
      .build();

    _connection
      .start()
      .then(() => {
        console.log("SHOULD BE WORKING");
      })
      .catch((err) => {
        console.error(err);
        setTimeout(() => signalRNegotiate(bot_id, broadcast_id, setIncomingActivity), 5000);
      });
  
    _connection.on("chatMessage", (message) => {
      console.log("chatMessage message", message);
      // modify this
      setIncomingActivity({
        status: JSON.parse(message).status,
        phoneNumber: JSON.parse(message).phoneNumber,
      });
    });
  

  
    _connection.onclose(() => {
      console.log("Connection closed, attempting to reconnect...");
      setTimeout(() => signalRNegotiate(bot_id, broadcast_id, setIncomingActivity), 5000);
    });
  };

  const sendBroadcast = (data) => {
    return fetch(constant.SIGNALR_BROKER_URL.concat("whatsappbroadcasting"), createPostJSON(data))
      .then((response) => {
        return response.json();
      })
      .then((res) => {
        console.log("res", res);
      })
      .catch((err) => console.log(err));
  };

export { createOne,getbroadcasts,getbroadcastLogs,getWaPhoneNumbers,signalRNegotiate, sendBroadcast};
