import constant from "constant";
import { createDELETEJSO<PERSON>, createGetJSON, createPostJSON, createPutJSON } from "helpers/custom";
const dest = "/llm/integration";

const getLLMIntegration = async (bot_id: number) => {
    return fetch(
      constant.SERVER_URL.concat(dest, "?bot_id=",bot_id.toString()),
      createGetJSON()
    )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const setLLMIntegration = async (data:{
    bot_id: number,
    llm_type: string,
    llm_model: string,
    llm_key: string,
    llm_temperature?: number,
    chunk_size?: number,
    chunk_overlap?: number,
    chat_history?: boolean,
    store_url?: string, 
    status_active?: boolean, 
    store_type?: string,
    collection_name?: string,
    personal_vector_db?: boolean,
    top_k?: number,
    chunk_methodology?: string,
    persona?: string
    llm_embedding_model?: string,
    fetch_k?: number,
    lambda_mult?: number
}) => {
    return fetch(
      constant.SERVER_URL.concat(dest),
      createPostJSON(data)
    )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const updateLLMIntegration = async (data: {
    LLM_integration_id:number,
    bot_id: number,
    llm_type?: string,
    llm_model?: string,
    llm_key?: string,
    llm_temperature?: number,
    chunk_size?: number,
    chunk_overlap?: number,
    chat_history?: boolean,
    store_url?: string, 
    status_active?: boolean, 
    store_type?: string,
    collection_name?: string,
    personal_vector_db?: boolean,
    top_k?: number,
    chunk_methodology?: string,
    connection_string?:string,
    persona?: string,
    llm_embedding_model?: string,
    fetch_k?: number,
    lambda_mult?: number
 }) => {
    return fetch(
      constant.SERVER_URL.concat(dest),
      createPutJSON(data)
    )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const deleteLLMIntegration = async (data: {
    LLM_integration_id:number,
    bot_id: number,
 }) => {
    return fetch(
      constant.SERVER_URL.concat(dest),
      createDELETEJSON(data)
    )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const testLLM = async (data: {
  llm_config:{
      llm_key: string,
      llm_type: string
}}) => {
  return fetch(
    constant.SERVER_URL.concat("/llm/checkLLM"),
    createPostJSON(data)
  )
  .then((response) => {
    return response.json();
  })
  .catch((err) => console.log(err));
};

const testStore = async (data: {
  store_config:{
    store_url: string,
    store_type: string
  }}) => {
  return fetch(
    constant.SERVER_URL.concat("/llm/store"),
    createPostJSON(data)
  )
  .then((response) => {
    return response.json();
  })
  .catch((err) => console.log(err));
};

export { 
    getLLMIntegration,
    setLLMIntegration,
    updateLLMIntegration,
    deleteLLMIntegration,
    testLLM,
    testStore,
};
