import constant from "constant";
import { createPostJSO<PERSON> } from "helpers/custom";

const dest = "/email";

const verifyEmailCode = (data) => {
  return fetch(constant.SERVER_URL.concat(dest, "/send"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const generateEmailVerificationCode = (data) => {
  return fetch(
    constant.SERVER_URL.concat(dest, "/generate"),
    createPostJSON(data)
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { verifyEmailCode, generateEmailVerificationCode };
