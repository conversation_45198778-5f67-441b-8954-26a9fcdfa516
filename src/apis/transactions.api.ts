import constant from "constant";
import { createGetJSON } from "helpers/custom";

const dest = "/dashboard/";
const getTotalTransactions = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `count?bot_id=${bot_id}&period=${period}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTotalVoiceTransactions = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `voice?bot_id=${bot_id}&period=${period}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAvgTransactions = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `avgday?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getDaysSinceLanunch = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `launch?bot_id=${bot_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionAnswered = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `/answer?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionByCategory = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `category?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionPerDay = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `days?bot_id=${bot_id}&period=${period}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTrendOfAnswered = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `trend?bot_id=${bot_id}&period=${period}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAvgPerWeek = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `avgweekday?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getAvgPerHour = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `avghour?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionsCategoryTable = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `category?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getLastTransactionsTable = (bot_id, NMessages = 15) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `lastn?bot_id=${bot_id}&No=${NMessages || 15}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionNotAnswered = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `notanswered?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionByFunctionRequested = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `function?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getConversations = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `conversation?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getWaConversations = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `wa_conversation?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionsPerChannel = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `channel?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getWhatsappPhoneNumbers = (bot_id, period) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `whatsapp/numbers?bot_id=${bot_id}&period=${period}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionPerPeriod = (bot_id, period, phone) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `trasactions/period?bot_id=${bot_id}&period=${period}&phone=${phone}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getTransactionsGrouped = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `transactions/grouped?bot_id=${bot_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getMostAskedQuestionsPerCountry = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(dest, `mostAsked/country?bot_id=${bot_id}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

//ask duaa if i should get it directly from the sp 
const getTransactionCalculated = (bot_id, day, month, year, endDay, endMonth, endYear) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `transaction/calculation?bot_id=${bot_id}&day=${day}&month=${month}&year=${year}&endDay=${endDay}&endMonth=${endMonth}&endYear=${endYear}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const getConversationsCalculated = (bot_id) => {
  return fetch(
    constant.SERVER_URL.concat(
      dest,
      `conversations/calculation?bot_id=${bot_id}`
    ),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export {
  getTotalTransactions,
  getTotalVoiceTransactions,
  getAvgTransactions,
  getDaysSinceLanunch,
  getTransactionAnswered,
  getTransactionByCategory,
  getTransactionPerDay,
  getTrendOfAnswered,
  getAvgPerWeek,
  getAvgPerHour,
  getTransactionsCategoryTable,
  getLastTransactionsTable,
  getTransactionNotAnswered,
  getTransactionByFunctionRequested,
  getConversations,
  getTransactionsPerChannel,
  getWaConversations,
  getWhatsappPhoneNumbers,
  getTransactionPerPeriod,
  getTransactionsGrouped,
  getMostAskedQuestionsPerCountry,
  getTransactionCalculated,
  getConversationsCalculated,
};
