[{"name": "Afghanistan", "code": "AF", "alpha-3": "AFG", "country-code": "004", "iso_3166-2": "ISO 3166-2:AF", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Åland Islands", "code": "AX", "alpha-3": "ALA", "country-code": "248", "iso_3166-2": "ISO 3166-2:AX", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Albania", "code": "AL", "alpha-3": "ALB", "country-code": "008", "iso_3166-2": "ISO 3166-2:AL", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Algeria", "code": "DZ", "alpha-3": "DZA", "country-code": "012", "iso_3166-2": "ISO 3166-2:DZ", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "American Samoa", "code": "AS", "alpha-3": "ASM", "country-code": "016", "iso_3166-2": "ISO 3166-2:AS", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Andorra", "code": "AD", "alpha-3": "AND", "country-code": "020", "iso_3166-2": "ISO 3166-2:AD", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Angola", "code": "AO", "alpha-3": "AGO", "country-code": "024", "iso_3166-2": "ISO 3166-2:AO", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AI", "alpha-3": "AIA", "country-code": "660", "iso_3166-2": "ISO 3166-2:AI", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Antarctica", "code": "AQ", "alpha-3": "ATA", "country-code": "010", "iso_3166-2": "ISO 3166-2:AQ", "region": "", "sub-region": "", "intermediate-region": "", "region-code": "", "sub-region-code": "", "intermediate-region-code": ""}, {"name": "Antigua and Barbuda", "code": "AG", "alpha-3": "ATG", "country-code": "028", "iso_3166-2": "ISO 3166-2:AG", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Argentina", "code": "AR", "alpha-3": "ARG", "country-code": "032", "iso_3166-2": "ISO 3166-2:AR", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Armenia", "code": "AM", "alpha-3": "ARM", "country-code": "051", "iso_3166-2": "ISO 3166-2:AM", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Aruba", "code": "AW", "alpha-3": "ABW", "country-code": "533", "iso_3166-2": "ISO 3166-2:AW", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Australia", "code": "AU", "alpha-3": "AUS", "country-code": "036", "iso_3166-2": "ISO 3166-2:AU", "region": "Oceania", "sub-region": "Australia and New Zealand", "intermediate-region": "", "region-code": "009", "sub-region-code": "053", "intermediate-region-code": ""}, {"name": "Austria", "code": "AT", "alpha-3": "AUT", "country-code": "040", "iso_3166-2": "ISO 3166-2:AT", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Azerbaijan", "code": "AZ", "alpha-3": "AZE", "country-code": "031", "iso_3166-2": "ISO 3166-2:AZ", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Bahamas", "code": "BS", "alpha-3": "BHS", "country-code": "044", "iso_3166-2": "ISO 3166-2:BS", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Bahrain", "code": "BH", "alpha-3": "BHR", "country-code": "048", "iso_3166-2": "ISO 3166-2:BH", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Bangladesh", "code": "BD", "alpha-3": "BGD", "country-code": "050", "iso_3166-2": "ISO 3166-2:BD", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Barbados", "code": "BB", "alpha-3": "BRB", "country-code": "052", "iso_3166-2": "ISO 3166-2:BB", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Belarus", "code": "BY", "alpha-3": "BLR", "country-code": "112", "iso_3166-2": "ISO 3166-2:BY", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Belgium", "code": "BE", "alpha-3": "BEL", "country-code": "056", "iso_3166-2": "ISO 3166-2:BE", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Belize", "code": "BZ", "alpha-3": "BLZ", "country-code": "084", "iso_3166-2": "ISO 3166-2:BZ", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Benin", "code": "BJ", "alpha-3": "BEN", "country-code": "204", "iso_3166-2": "ISO 3166-2:BJ", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Bermuda", "code": "BM", "alpha-3": "BMU", "country-code": "060", "iso_3166-2": "ISO 3166-2:BM", "region": "Americas", "sub-region": "Northern America", "intermediate-region": "", "region-code": "019", "sub-region-code": "021", "intermediate-region-code": ""}, {"name": "Bhutan", "code": "BT", "alpha-3": "BTN", "country-code": "064", "iso_3166-2": "ISO 3166-2:BT", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Bolivia (Plurinational State of)", "code": "BO", "alpha-3": "BOL", "country-code": "068", "iso_3166-2": "ISO 3166-2:BO", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Bonaire, Sint Eustatius and Saba", "code": "BQ", "alpha-3": "BES", "country-code": "535", "iso_3166-2": "ISO 3166-2:BQ", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Bosnia and Herzegovina", "code": "BA", "alpha-3": "BIH", "country-code": "070", "iso_3166-2": "ISO 3166-2:BA", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Botswana", "code": "BW", "alpha-3": "BWA", "country-code": "072", "iso_3166-2": "ISO 3166-2:BW", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Southern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "018"}, {"name": "Bouvet Island", "code": "BV", "alpha-3": "BVT", "country-code": "074", "iso_3166-2": "ISO 3166-2:BV", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Brazil", "code": "BR", "alpha-3": "BRA", "country-code": "076", "iso_3166-2": "ISO 3166-2:BR", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "British Indian Ocean Territory", "code": "IO", "alpha-3": "IOT", "country-code": "086", "iso_3166-2": "ISO 3166-2:IO", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Brunei Darussalam", "code": "BN", "alpha-3": "BRN", "country-code": "096", "iso_3166-2": "ISO 3166-2:BN", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Bulgaria", "code": "BG", "alpha-3": "BGR", "country-code": "100", "iso_3166-2": "ISO 3166-2:BG", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Burkina Faso", "code": "BF", "alpha-3": "BFA", "country-code": "854", "iso_3166-2": "ISO 3166-2:BF", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Burundi", "code": "BI", "alpha-3": "BDI", "country-code": "108", "iso_3166-2": "ISO 3166-2:BI", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Cabo Verde", "code": "CV", "alpha-3": "CPV", "country-code": "132", "iso_3166-2": "ISO 3166-2:CV", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Cambodia", "code": "KH", "alpha-3": "KHM", "country-code": "116", "iso_3166-2": "ISO 3166-2:KH", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Cameroon", "code": "CM", "alpha-3": "CMR", "country-code": "120", "iso_3166-2": "ISO 3166-2:CM", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Canada", "code": "CA", "alpha-3": "CAN", "country-code": "124", "iso_3166-2": "ISO 3166-2:CA", "region": "Americas", "sub-region": "Northern America", "intermediate-region": "", "region-code": "019", "sub-region-code": "021", "intermediate-region-code": ""}, {"name": "Cayman Islands", "code": "KY", "alpha-3": "CYM", "country-code": "136", "iso_3166-2": "ISO 3166-2:KY", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Central African Republic", "code": "CF", "alpha-3": "CAF", "country-code": "140", "iso_3166-2": "ISO 3166-2:CF", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Chad", "code": "TD", "alpha-3": "TCD", "country-code": "148", "iso_3166-2": "ISO 3166-2:TD", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Chile", "code": "CL", "alpha-3": "CHL", "country-code": "152", "iso_3166-2": "ISO 3166-2:CL", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "China", "code": "CN", "alpha-3": "CHN", "country-code": "156", "iso_3166-2": "ISO 3166-2:CN", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Christmas Island", "code": "CX", "alpha-3": "CXR", "country-code": "162", "iso_3166-2": "ISO 3166-2:CX", "region": "Oceania", "sub-region": "Australia and New Zealand", "intermediate-region": "", "region-code": "009", "sub-region-code": "053", "intermediate-region-code": ""}, {"name": "Cocos (Keeling) Islands", "code": "CC", "alpha-3": "CCK", "country-code": "166", "iso_3166-2": "ISO 3166-2:CC", "region": "Oceania", "sub-region": "Australia and New Zealand", "intermediate-region": "", "region-code": "009", "sub-region-code": "053", "intermediate-region-code": ""}, {"name": "Colombia", "code": "CO", "alpha-3": "COL", "country-code": "170", "iso_3166-2": "ISO 3166-2:CO", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Comoros", "code": "KM", "alpha-3": "COM", "country-code": "174", "iso_3166-2": "ISO 3166-2:KM", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Congo", "code": "CG", "alpha-3": "COG", "country-code": "178", "iso_3166-2": "ISO 3166-2:CG", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Congo, Democratic Republic of the", "code": "CD", "alpha-3": "COD", "country-code": "180", "iso_3166-2": "ISO 3166-2:CD", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Cook Islands", "code": "CK", "alpha-3": "COK", "country-code": "184", "iso_3166-2": "ISO 3166-2:CK", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Costa Rica", "code": "CR", "alpha-3": "CRI", "country-code": "188", "iso_3166-2": "ISO 3166-2:CR", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Côte d'Ivoire", "code": "CI", "alpha-3": "CIV", "country-code": "384", "iso_3166-2": "ISO 3166-2:CI", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Croatia", "code": "HR", "alpha-3": "HRV", "country-code": "191", "iso_3166-2": "ISO 3166-2:HR", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Cuba", "code": "CU", "alpha-3": "CUB", "country-code": "192", "iso_3166-2": "ISO 3166-2:CU", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Curaçao", "code": "CW", "alpha-3": "CUW", "country-code": "531", "iso_3166-2": "ISO 3166-2:CW", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Cyprus", "code": "CY", "alpha-3": "CYP", "country-code": "196", "iso_3166-2": "ISO 3166-2:CY", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Czechia", "code": "CZ", "alpha-3": "CZE", "country-code": "203", "iso_3166-2": "ISO 3166-2:CZ", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Denmark", "code": "DK", "alpha-3": "DNK", "country-code": "208", "iso_3166-2": "ISO 3166-2:DK", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Djibouti", "code": "DJ", "alpha-3": "DJI", "country-code": "262", "iso_3166-2": "ISO 3166-2:DJ", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Dominica", "code": "DM", "alpha-3": "DMA", "country-code": "212", "iso_3166-2": "ISO 3166-2:DM", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Dominican Republic", "code": "DO", "alpha-3": "DOM", "country-code": "214", "iso_3166-2": "ISO 3166-2:DO", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Ecuador", "code": "EC", "alpha-3": "ECU", "country-code": "218", "iso_3166-2": "ISO 3166-2:EC", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Egypt", "code": "EG", "alpha-3": "EGY", "country-code": "818", "iso_3166-2": "ISO 3166-2:EG", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "El Salvador", "code": "SV", "alpha-3": "SLV", "country-code": "222", "iso_3166-2": "ISO 3166-2:SV", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Equatorial Guinea", "code": "GQ", "alpha-3": "GNQ", "country-code": "226", "iso_3166-2": "ISO 3166-2:GQ", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Eritrea", "code": "ER", "alpha-3": "ERI", "country-code": "232", "iso_3166-2": "ISO 3166-2:ER", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Estonia", "code": "EE", "alpha-3": "EST", "country-code": "233", "iso_3166-2": "ISO 3166-2:EE", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": "SZ", "alpha-3": "SWZ", "country-code": "748", "iso_3166-2": "ISO 3166-2:SZ", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Southern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "018"}, {"name": "Ethiopia", "code": "ET", "alpha-3": "ETH", "country-code": "231", "iso_3166-2": "ISO 3166-2:ET", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Falkland Islands (Malvinas)", "code": "FK", "alpha-3": "FLK", "country-code": "238", "iso_3166-2": "ISO 3166-2:FK", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Faroe Islands", "code": "FO", "alpha-3": "FRO", "country-code": "234", "iso_3166-2": "ISO 3166-2:FO", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Fiji", "code": "FJ", "alpha-3": "FJI", "country-code": "242", "iso_3166-2": "ISO 3166-2:FJ", "region": "Oceania", "sub-region": "Melanesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "054", "intermediate-region-code": ""}, {"name": "Finland", "code": "FI", "alpha-3": "FIN", "country-code": "246", "iso_3166-2": "ISO 3166-2:FI", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "France", "code": "FR", "alpha-3": "FRA", "country-code": "250", "iso_3166-2": "ISO 3166-2:FR", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "French Guiana", "code": "GF", "alpha-3": "GUF", "country-code": "254", "iso_3166-2": "ISO 3166-2:GF", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "French Polynesia", "code": "PF", "alpha-3": "PYF", "country-code": "258", "iso_3166-2": "ISO 3166-2:PF", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "French Southern Territories", "code": "TF", "alpha-3": "ATF", "country-code": "260", "iso_3166-2": "ISO 3166-2:TF", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Gabon", "code": "GA", "alpha-3": "GAB", "country-code": "266", "iso_3166-2": "ISO 3166-2:GA", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Gambia", "code": "GM", "alpha-3": "GMB", "country-code": "270", "iso_3166-2": "ISO 3166-2:GM", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Georgia", "code": "GE", "alpha-3": "GEO", "country-code": "268", "iso_3166-2": "ISO 3166-2:GE", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Germany", "code": "DE", "alpha-3": "DEU", "country-code": "276", "iso_3166-2": "ISO 3166-2:DE", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Ghana", "code": "GH", "alpha-3": "GHA", "country-code": "288", "iso_3166-2": "ISO 3166-2:GH", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Gibraltar", "code": "GI", "alpha-3": "GIB", "country-code": "292", "iso_3166-2": "ISO 3166-2:GI", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Greece", "code": "GR", "alpha-3": "GRC", "country-code": "300", "iso_3166-2": "ISO 3166-2:GR", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Greenland", "code": "GL", "alpha-3": "GRL", "country-code": "304", "iso_3166-2": "ISO 3166-2:GL", "region": "Americas", "sub-region": "Northern America", "intermediate-region": "", "region-code": "019", "sub-region-code": "021", "intermediate-region-code": ""}, {"name": "Grenada", "code": "GD", "alpha-3": "GRD", "country-code": "308", "iso_3166-2": "ISO 3166-2:GD", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Guadeloupe", "code": "GP", "alpha-3": "GLP", "country-code": "312", "iso_3166-2": "ISO 3166-2:GP", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Guam", "code": "GU", "alpha-3": "GUM", "country-code": "316", "iso_3166-2": "ISO 3166-2:GU", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Guatemala", "code": "GT", "alpha-3": "GTM", "country-code": "320", "iso_3166-2": "ISO 3166-2:GT", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Guernsey", "code": "GG", "alpha-3": "GGY", "country-code": "831", "iso_3166-2": "ISO 3166-2:GG", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "Channel Islands", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": "830"}, {"name": "Guinea", "code": "GN", "alpha-3": "GIN", "country-code": "324", "iso_3166-2": "ISO 3166-2:GN", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Guinea-Bissau", "code": "GW", "alpha-3": "GNB", "country-code": "624", "iso_3166-2": "ISO 3166-2:GW", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Guyana", "code": "GY", "alpha-3": "GUY", "country-code": "328", "iso_3166-2": "ISO 3166-2:GY", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Haiti", "code": "HT", "alpha-3": "HTI", "country-code": "332", "iso_3166-2": "ISO 3166-2:HT", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Heard Island and McDonald Islands", "code": "HM", "alpha-3": "HMD", "country-code": "334", "iso_3166-2": "ISO 3166-2:HM", "region": "Oceania", "sub-region": "Australia and New Zealand", "intermediate-region": "", "region-code": "009", "sub-region-code": "053", "intermediate-region-code": ""}, {"name": "Holy See", "code": "VA", "alpha-3": "VAT", "country-code": "336", "iso_3166-2": "ISO 3166-2:VA", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Honduras", "code": "HN", "alpha-3": "HND", "country-code": "340", "iso_3166-2": "ISO 3166-2:HN", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Hong Kong", "code": "HK", "alpha-3": "HKG", "country-code": "344", "iso_3166-2": "ISO 3166-2:HK", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Hungary", "code": "HU", "alpha-3": "HUN", "country-code": "348", "iso_3166-2": "ISO 3166-2:HU", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Iceland", "code": "IS", "alpha-3": "ISL", "country-code": "352", "iso_3166-2": "ISO 3166-2:IS", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "India", "code": "IN", "alpha-3": "IND", "country-code": "356", "iso_3166-2": "ISO 3166-2:IN", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Indonesia", "code": "ID", "alpha-3": "IDN", "country-code": "360", "iso_3166-2": "ISO 3166-2:ID", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Iran (Islamic Republic of)", "code": "IR", "alpha-3": "IRN", "country-code": "364", "iso_3166-2": "ISO 3166-2:IR", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Iraq", "code": "IQ", "alpha-3": "IRQ", "country-code": "368", "iso_3166-2": "ISO 3166-2:IQ", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Ireland", "code": "IE", "alpha-3": "IRL", "country-code": "372", "iso_3166-2": "ISO 3166-2:IE", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Isle of Man", "code": "IM", "alpha-3": "IMN", "country-code": "833", "iso_3166-2": "ISO 3166-2:IM", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Palestinian Territories", "code": "IL", "alpha-3": "ISR", "country-code": "376", "iso_3166-2": "ISO 3166-2:IL", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Italy", "code": "IT", "alpha-3": "ITA", "country-code": "380", "iso_3166-2": "ISO 3166-2:IT", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Jamaica", "code": "JM", "alpha-3": "JAM", "country-code": "388", "iso_3166-2": "ISO 3166-2:JM", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Japan", "code": "JP", "alpha-3": "JPN", "country-code": "392", "iso_3166-2": "ISO 3166-2:JP", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Jersey", "code": "JE", "alpha-3": "JEY", "country-code": "832", "iso_3166-2": "ISO 3166-2:JE", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "Channel Islands", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": "830"}, {"name": "Jordan", "code": "JO", "alpha-3": "JOR", "country-code": "400", "iso_3166-2": "ISO 3166-2:JO", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Kazakhstan", "code": "KZ", "alpha-3": "KAZ", "country-code": "398", "iso_3166-2": "ISO 3166-2:KZ", "region": "Asia", "sub-region": "Central Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "143", "intermediate-region-code": ""}, {"name": "Kenya", "code": "KE", "alpha-3": "KEN", "country-code": "404", "iso_3166-2": "ISO 3166-2:KE", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Kiribati", "code": "KI", "alpha-3": "KIR", "country-code": "296", "iso_3166-2": "ISO 3166-2:KI", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Korea (Democratic People's Republic of)", "code": "KP", "alpha-3": "PRK", "country-code": "408", "iso_3166-2": "ISO 3166-2:KP", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Korea, Republic of", "code": "KR", "alpha-3": "KOR", "country-code": "410", "iso_3166-2": "ISO 3166-2:KR", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Kuwait", "code": "KW", "alpha-3": "KWT", "country-code": "414", "iso_3166-2": "ISO 3166-2:KW", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Kyrgyzstan", "code": "KG", "alpha-3": "KGZ", "country-code": "417", "iso_3166-2": "ISO 3166-2:KG", "region": "Asia", "sub-region": "Central Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "143", "intermediate-region-code": ""}, {"name": "Lao People's Democratic Republic", "code": "LA", "alpha-3": "LAO", "country-code": "418", "iso_3166-2": "ISO 3166-2:LA", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Latvia", "code": "LV", "alpha-3": "LVA", "country-code": "428", "iso_3166-2": "ISO 3166-2:LV", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Lebanon", "code": "LB", "alpha-3": "LBN", "country-code": "422", "iso_3166-2": "ISO 3166-2:LB", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Lesotho", "code": "LS", "alpha-3": "LSO", "country-code": "426", "iso_3166-2": "ISO 3166-2:LS", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Southern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "018"}, {"name": "Liberia", "code": "LR", "alpha-3": "LBR", "country-code": "430", "iso_3166-2": "ISO 3166-2:LR", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Libya", "code": "LY", "alpha-3": "LBY", "country-code": "434", "iso_3166-2": "ISO 3166-2:LY", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "Liechtenstein", "code": "LI", "alpha-3": "LIE", "country-code": "438", "iso_3166-2": "ISO 3166-2:LI", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Lithuania", "code": "LT", "alpha-3": "LTU", "country-code": "440", "iso_3166-2": "ISO 3166-2:LT", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Luxembourg", "code": "LU", "alpha-3": "LUX", "country-code": "442", "iso_3166-2": "ISO 3166-2:LU", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Macao", "code": "MO", "alpha-3": "MAC", "country-code": "446", "iso_3166-2": "ISO 3166-2:MO", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Madagascar", "code": "MG", "alpha-3": "MDG", "country-code": "450", "iso_3166-2": "ISO 3166-2:MG", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Malawi", "code": "MW", "alpha-3": "MWI", "country-code": "454", "iso_3166-2": "ISO 3166-2:MW", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Malaysia", "code": "MY", "alpha-3": "MYS", "country-code": "458", "iso_3166-2": "ISO 3166-2:MY", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Maldives", "code": "MV", "alpha-3": "MDV", "country-code": "462", "iso_3166-2": "ISO 3166-2:MV", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Mali", "code": "ML", "alpha-3": "MLI", "country-code": "466", "iso_3166-2": "ISO 3166-2:ML", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Malta", "code": "MT", "alpha-3": "MLT", "country-code": "470", "iso_3166-2": "ISO 3166-2:MT", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Marshall Islands", "code": "MH", "alpha-3": "MHL", "country-code": "584", "iso_3166-2": "ISO 3166-2:MH", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Martinique", "code": "MQ", "alpha-3": "MTQ", "country-code": "474", "iso_3166-2": "ISO 3166-2:MQ", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Mauritania", "code": "MR", "alpha-3": "MRT", "country-code": "478", "iso_3166-2": "ISO 3166-2:MR", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Mauritius", "code": "MU", "alpha-3": "MUS", "country-code": "480", "iso_3166-2": "ISO 3166-2:MU", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Mayotte", "code": "YT", "alpha-3": "MYT", "country-code": "175", "iso_3166-2": "ISO 3166-2:YT", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Mexico", "code": "MX", "alpha-3": "MEX", "country-code": "484", "iso_3166-2": "ISO 3166-2:MX", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Micronesia (Federated States of)", "code": "FM", "alpha-3": "FSM", "country-code": "583", "iso_3166-2": "ISO 3166-2:FM", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Moldova, Republic of", "code": "MD", "alpha-3": "MDA", "country-code": "498", "iso_3166-2": "ISO 3166-2:MD", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Monaco", "code": "MC", "alpha-3": "MCO", "country-code": "492", "iso_3166-2": "ISO 3166-2:MC", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Mongolia", "code": "MN", "alpha-3": "MNG", "country-code": "496", "iso_3166-2": "ISO 3166-2:MN", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Montenegro", "code": "ME", "alpha-3": "MNE", "country-code": "499", "iso_3166-2": "ISO 3166-2:ME", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Montserrat", "code": "MS", "alpha-3": "MSR", "country-code": "500", "iso_3166-2": "ISO 3166-2:MS", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Morocco", "code": "MA", "alpha-3": "MAR", "country-code": "504", "iso_3166-2": "ISO 3166-2:MA", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "Mozambique", "code": "MZ", "alpha-3": "MOZ", "country-code": "508", "iso_3166-2": "ISO 3166-2:MZ", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Myanmar", "code": "MM", "alpha-3": "MMR", "country-code": "104", "iso_3166-2": "ISO 3166-2:MM", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Namibia", "code": "NA", "alpha-3": "NAM", "country-code": "516", "iso_3166-2": "ISO 3166-2:NA", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Southern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "018"}, {"name": "Nauru", "code": "NR", "alpha-3": "NRU", "country-code": "520", "iso_3166-2": "ISO 3166-2:NR", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Nepal", "code": "NP", "alpha-3": "NPL", "country-code": "524", "iso_3166-2": "ISO 3166-2:NP", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Netherlands", "code": "NL", "alpha-3": "NLD", "country-code": "528", "iso_3166-2": "ISO 3166-2:NL", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "New Caledonia", "code": "NC", "alpha-3": "NCL", "country-code": "540", "iso_3166-2": "ISO 3166-2:NC", "region": "Oceania", "sub-region": "Melanesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "054", "intermediate-region-code": ""}, {"name": "New Zealand", "code": "NZ", "alpha-3": "NZL", "country-code": "554", "iso_3166-2": "ISO 3166-2:NZ", "region": "Oceania", "sub-region": "Australia and New Zealand", "intermediate-region": "", "region-code": "009", "sub-region-code": "053", "intermediate-region-code": ""}, {"name": "Nicaragua", "code": "NI", "alpha-3": "NIC", "country-code": "558", "iso_3166-2": "ISO 3166-2:NI", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Niger", "code": "NE", "alpha-3": "NER", "country-code": "562", "iso_3166-2": "ISO 3166-2:NE", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Nigeria", "code": "NG", "alpha-3": "NGA", "country-code": "566", "iso_3166-2": "ISO 3166-2:NG", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Niue", "code": "NU", "alpha-3": "NIU", "country-code": "570", "iso_3166-2": "ISO 3166-2:NU", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Norfolk Island", "code": "NF", "alpha-3": "NFK", "country-code": "574", "iso_3166-2": "ISO 3166-2:NF", "region": "Oceania", "sub-region": "Australia and New Zealand", "intermediate-region": "", "region-code": "009", "sub-region-code": "053", "intermediate-region-code": ""}, {"name": "North Macedonia", "code": "MK", "alpha-3": "MKD", "country-code": "807", "iso_3166-2": "ISO 3166-2:MK", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Northern Mariana Islands", "code": "MP", "alpha-3": "MNP", "country-code": "580", "iso_3166-2": "ISO 3166-2:MP", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Norway", "code": "NO", "alpha-3": "NOR", "country-code": "578", "iso_3166-2": "ISO 3166-2:NO", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Oman", "code": "OM", "alpha-3": "OMN", "country-code": "512", "iso_3166-2": "ISO 3166-2:OM", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Pakistan", "code": "PK", "alpha-3": "PAK", "country-code": "586", "iso_3166-2": "ISO 3166-2:PK", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "<PERSON><PERSON>", "code": "PW", "alpha-3": "PLW", "country-code": "585", "iso_3166-2": "ISO 3166-2:PW", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Palestine, State of", "code": "PS", "alpha-3": "PSE", "country-code": "275", "iso_3166-2": "ISO 3166-2:PS", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Panama", "code": "PA", "alpha-3": "PAN", "country-code": "591", "iso_3166-2": "ISO 3166-2:PA", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Central America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "013"}, {"name": "Papua New Guinea", "code": "PG", "alpha-3": "PNG", "country-code": "598", "iso_3166-2": "ISO 3166-2:PG", "region": "Oceania", "sub-region": "Melanesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "054", "intermediate-region-code": ""}, {"name": "Paraguay", "code": "PY", "alpha-3": "PRY", "country-code": "600", "iso_3166-2": "ISO 3166-2:PY", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Peru", "code": "PE", "alpha-3": "PER", "country-code": "604", "iso_3166-2": "ISO 3166-2:PE", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Philippines", "code": "PH", "alpha-3": "PHL", "country-code": "608", "iso_3166-2": "ISO 3166-2:PH", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Pitcairn", "code": "PN", "alpha-3": "PCN", "country-code": "612", "iso_3166-2": "ISO 3166-2:PN", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Poland", "code": "PL", "alpha-3": "POL", "country-code": "616", "iso_3166-2": "ISO 3166-2:PL", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Portugal", "code": "PT", "alpha-3": "PRT", "country-code": "620", "iso_3166-2": "ISO 3166-2:PT", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Puerto Rico", "code": "PR", "alpha-3": "PRI", "country-code": "630", "iso_3166-2": "ISO 3166-2:PR", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Qatar", "code": "QA", "alpha-3": "QAT", "country-code": "634", "iso_3166-2": "ISO 3166-2:QA", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Réunion", "code": "RE", "alpha-3": "REU", "country-code": "638", "iso_3166-2": "ISO 3166-2:RE", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Romania", "code": "RO", "alpha-3": "ROU", "country-code": "642", "iso_3166-2": "ISO 3166-2:RO", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Russian Federation", "code": "RU", "alpha-3": "RUS", "country-code": "643", "iso_3166-2": "ISO 3166-2:RU", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Rwanda", "code": "RW", "alpha-3": "RWA", "country-code": "646", "iso_3166-2": "ISO 3166-2:RW", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "<PERSON>", "code": "BL", "alpha-3": "BLM", "country-code": "652", "iso_3166-2": "ISO 3166-2:BL", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Saint Helena, Ascension and Tristan <PERSON>ha", "code": "SH", "alpha-3": "SHN", "country-code": "654", "iso_3166-2": "ISO 3166-2:SH", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Saint Kitts and Nevis", "code": "KN", "alpha-3": "KNA", "country-code": "659", "iso_3166-2": "ISO 3166-2:KN", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Saint Lucia", "code": "LC", "alpha-3": "LCA", "country-code": "662", "iso_3166-2": "ISO 3166-2:LC", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "<PERSON> (French part)", "code": "MF", "alpha-3": "MAF", "country-code": "663", "iso_3166-2": "ISO 3166-2:MF", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Saint Pierre and Miquelon", "code": "PM", "alpha-3": "SPM", "country-code": "666", "iso_3166-2": "ISO 3166-2:PM", "region": "Americas", "sub-region": "Northern America", "intermediate-region": "", "region-code": "019", "sub-region-code": "021", "intermediate-region-code": ""}, {"name": "Saint Vincent and the Grenadines", "code": "VC", "alpha-3": "VCT", "country-code": "670", "iso_3166-2": "ISO 3166-2:VC", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Samoa", "code": "WS", "alpha-3": "WSM", "country-code": "882", "iso_3166-2": "ISO 3166-2:WS", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "San Marino", "code": "SM", "alpha-3": "SMR", "country-code": "674", "iso_3166-2": "ISO 3166-2:SM", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Sao Tome and Principe", "code": "ST", "alpha-3": "STP", "country-code": "678", "iso_3166-2": "ISO 3166-2:ST", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Middle Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "017"}, {"name": "Saudi Arabia", "code": "SA", "alpha-3": "SAU", "country-code": "682", "iso_3166-2": "ISO 3166-2:SA", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Senegal", "code": "SN", "alpha-3": "SEN", "country-code": "686", "iso_3166-2": "ISO 3166-2:SN", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Serbia", "code": "RS", "alpha-3": "SRB", "country-code": "688", "iso_3166-2": "ISO 3166-2:RS", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Seychelles", "code": "SC", "alpha-3": "SYC", "country-code": "690", "iso_3166-2": "ISO 3166-2:SC", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Sierra Leone", "code": "SL", "alpha-3": "SLE", "country-code": "694", "iso_3166-2": "ISO 3166-2:SL", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Singapore", "code": "SG", "alpha-3": "SGP", "country-code": "702", "iso_3166-2": "ISO 3166-2:SG", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "code": "SX", "alpha-3": "SXM", "country-code": "534", "iso_3166-2": "ISO 3166-2:SX", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Slovakia", "code": "SK", "alpha-3": "SVK", "country-code": "703", "iso_3166-2": "ISO 3166-2:SK", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "Slovenia", "code": "SI", "alpha-3": "SVN", "country-code": "705", "iso_3166-2": "ISO 3166-2:SI", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Solomon Islands", "code": "SB", "alpha-3": "SLB", "country-code": "090", "iso_3166-2": "ISO 3166-2:SB", "region": "Oceania", "sub-region": "Melanesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "054", "intermediate-region-code": ""}, {"name": "Somalia", "code": "SO", "alpha-3": "SOM", "country-code": "706", "iso_3166-2": "ISO 3166-2:SO", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "South Africa", "code": "ZA", "alpha-3": "ZAF", "country-code": "710", "iso_3166-2": "ISO 3166-2:ZA", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Southern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "018"}, {"name": "South Georgia and the South Sandwich Islands", "code": "GS", "alpha-3": "SGS", "country-code": "239", "iso_3166-2": "ISO 3166-2:GS", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "South Sudan", "code": "SS", "alpha-3": "SSD", "country-code": "728", "iso_3166-2": "ISO 3166-2:SS", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Spain", "code": "ES", "alpha-3": "ESP", "country-code": "724", "iso_3166-2": "ISO 3166-2:ES", "region": "Europe", "sub-region": "Southern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "039", "intermediate-region-code": ""}, {"name": "Sri Lanka", "code": "LK", "alpha-3": "LKA", "country-code": "144", "iso_3166-2": "ISO 3166-2:LK", "region": "Asia", "sub-region": "Southern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "034", "intermediate-region-code": ""}, {"name": "Sudan", "code": "SD", "alpha-3": "SDN", "country-code": "729", "iso_3166-2": "ISO 3166-2:SD", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "Suriname", "code": "SR", "alpha-3": "SUR", "country-code": "740", "iso_3166-2": "ISO 3166-2:SR", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Svalbard and <PERSON>", "code": "SJ", "alpha-3": "SJM", "country-code": "744", "iso_3166-2": "ISO 3166-2:SJ", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Sweden", "code": "SE", "alpha-3": "SWE", "country-code": "752", "iso_3166-2": "ISO 3166-2:SE", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "Switzerland", "code": "CH", "alpha-3": "CHE", "country-code": "756", "iso_3166-2": "ISO 3166-2:CH", "region": "Europe", "sub-region": "Western Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "155", "intermediate-region-code": ""}, {"name": "Syrian Arab Republic", "code": "SY", "alpha-3": "SYR", "country-code": "760", "iso_3166-2": "ISO 3166-2:SY", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Taiwan, Province of China", "code": "TW", "alpha-3": "TWN", "country-code": "158", "iso_3166-2": "ISO 3166-2:TW", "region": "Asia", "sub-region": "Eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "030", "intermediate-region-code": ""}, {"name": "Tajikistan", "code": "TJ", "alpha-3": "TJK", "country-code": "762", "iso_3166-2": "ISO 3166-2:TJ", "region": "Asia", "sub-region": "Central Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "143", "intermediate-region-code": ""}, {"name": "Tanzania, United Republic of", "code": "TZ", "alpha-3": "TZA", "country-code": "834", "iso_3166-2": "ISO 3166-2:TZ", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Thailand", "code": "TH", "alpha-3": "THA", "country-code": "764", "iso_3166-2": "ISO 3166-2:TH", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Timor-Leste", "code": "TL", "alpha-3": "TLS", "country-code": "626", "iso_3166-2": "ISO 3166-2:TL", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Togo", "code": "TG", "alpha-3": "TGO", "country-code": "768", "iso_3166-2": "ISO 3166-2:TG", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Western Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "011"}, {"name": "Tokelau", "code": "TK", "alpha-3": "TKL", "country-code": "772", "iso_3166-2": "ISO 3166-2:TK", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Tonga", "code": "TO", "alpha-3": "TON", "country-code": "776", "iso_3166-2": "ISO 3166-2:TO", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Trinidad and Tobago", "code": "TT", "alpha-3": "TTO", "country-code": "780", "iso_3166-2": "ISO 3166-2:TT", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Tunisia", "code": "TN", "alpha-3": "TUN", "country-code": "788", "iso_3166-2": "ISO 3166-2:TN", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "Turkey", "code": "TR", "alpha-3": "TUR", "country-code": "792", "iso_3166-2": "ISO 3166-2:TR", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Turkmenistan", "code": "TM", "alpha-3": "TKM", "country-code": "795", "iso_3166-2": "ISO 3166-2:TM", "region": "Asia", "sub-region": "Central Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "143", "intermediate-region-code": ""}, {"name": "Turks and Caicos Islands", "code": "TC", "alpha-3": "TCA", "country-code": "796", "iso_3166-2": "ISO 3166-2:TC", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Tuvalu", "code": "TV", "alpha-3": "TUV", "country-code": "798", "iso_3166-2": "ISO 3166-2:TV", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Uganda", "code": "UG", "alpha-3": "UGA", "country-code": "800", "iso_3166-2": "ISO 3166-2:UG", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Ukraine", "code": "UA", "alpha-3": "UKR", "country-code": "804", "iso_3166-2": "ISO 3166-2:UA", "region": "Europe", "sub-region": "Eastern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "151", "intermediate-region-code": ""}, {"name": "United Arab Emirates", "code": "AE", "alpha-3": "ARE", "country-code": "784", "iso_3166-2": "ISO 3166-2:AE", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "United Kingdom of Great Britain and Northern Ireland", "code": "GB", "alpha-3": "GBR", "country-code": "826", "iso_3166-2": "ISO 3166-2:GB", "region": "Europe", "sub-region": "Northern Europe", "intermediate-region": "", "region-code": "150", "sub-region-code": "154", "intermediate-region-code": ""}, {"name": "United States of America", "code": "US", "alpha-3": "USA", "country-code": "840", "iso_3166-2": "ISO 3166-2:US", "region": "Americas", "sub-region": "Northern America", "intermediate-region": "", "region-code": "019", "sub-region-code": "021", "intermediate-region-code": ""}, {"name": "United States Minor Outlying Islands", "code": "UM", "alpha-3": "UMI", "country-code": "581", "iso_3166-2": "ISO 3166-2:UM", "region": "Oceania", "sub-region": "Micronesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "057", "intermediate-region-code": ""}, {"name": "Uruguay", "code": "UY", "alpha-3": "URY", "country-code": "858", "iso_3166-2": "ISO 3166-2:UY", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Uzbekistan", "code": "UZ", "alpha-3": "UZB", "country-code": "860", "iso_3166-2": "ISO 3166-2:UZ", "region": "Asia", "sub-region": "Central Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "143", "intermediate-region-code": ""}, {"name": "Vanuatu", "code": "VU", "alpha-3": "VUT", "country-code": "548", "iso_3166-2": "ISO 3166-2:VU", "region": "Oceania", "sub-region": "Melanesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "054", "intermediate-region-code": ""}, {"name": "Venezuela (Bolivarian Republic of)", "code": "VE", "alpha-3": "VEN", "country-code": "862", "iso_3166-2": "ISO 3166-2:VE", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "South America", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "005"}, {"name": "Viet Nam", "code": "VN", "alpha-3": "VNM", "country-code": "704", "iso_3166-2": "ISO 3166-2:VN", "region": "Asia", "sub-region": "South-eastern Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "035", "intermediate-region-code": ""}, {"name": "Virgin Islands (British)", "code": "VG", "alpha-3": "VGB", "country-code": "092", "iso_3166-2": "ISO 3166-2:VG", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Virgin Islands (U.S.)", "code": "VI", "alpha-3": "VIR", "country-code": "850", "iso_3166-2": "ISO 3166-2:VI", "region": "Americas", "sub-region": "Latin America and the Caribbean", "intermediate-region": "Caribbean", "region-code": "019", "sub-region-code": "419", "intermediate-region-code": "029"}, {"name": "Wallis and Futuna", "code": "WF", "alpha-3": "WLF", "country-code": "876", "iso_3166-2": "ISO 3166-2:WF", "region": "Oceania", "sub-region": "Polynesia", "intermediate-region": "", "region-code": "009", "sub-region-code": "061", "intermediate-region-code": ""}, {"name": "Western Sahara", "code": "EH", "alpha-3": "ESH", "country-code": "732", "iso_3166-2": "ISO 3166-2:EH", "region": "Africa", "sub-region": "Northern Africa", "intermediate-region": "", "region-code": "002", "sub-region-code": "015", "intermediate-region-code": ""}, {"name": "Yemen", "code": "YE", "alpha-3": "YEM", "country-code": "887", "iso_3166-2": "ISO 3166-2:YE", "region": "Asia", "sub-region": "Western Asia", "intermediate-region": "", "region-code": "142", "sub-region-code": "145", "intermediate-region-code": ""}, {"name": "Zambia", "code": "ZM", "alpha-3": "ZMB", "country-code": "894", "iso_3166-2": "ISO 3166-2:ZM", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}, {"name": "Zimbabwe", "code": "ZW", "alpha-3": "ZWE", "country-code": "716", "iso_3166-2": "ISO 3166-2:ZW", "region": "Africa", "sub-region": "Sub-Saharan Africa", "intermediate-region": "Eastern Africa", "region-code": "002", "sub-region-code": "202", "intermediate-region-code": "014"}]