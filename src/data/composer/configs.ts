export default function getBlocks() {
  return {
    Decision: [
      {
        block_type: "Decision",
        name: "",
        handler: "decidePathBasedOnEntity",
        handlerParams: [
          [
            {
              criteria: "default",
              redirect: "",
              title: "not matched",
              connector_id: "0",
            },
          ],
          "",
        ],
      },
    ],
    "Request User Data": [
      {
        block_type: "Request User Data",
        node_id: "",
        name: "",
        handler: "promptText",
        handlerParams: ["", "", { voice_path_male: "", voice_path_female: "" }],
      },
      {
        block_type: "Request User Data",
        node_id: "",
        recieving: "",
        name: "",
        handler: "validateIncomingText",
        handlerParams: [
          [
            {
              criteria: "default",
              redirect: "",
              title: "not matched",
              connector_id: "0",
            },
          ],
        ],
      },
    ],
    "Send Message": [
      {
        block_type: "Send Message",
        node_id: "",
        name: "",
        handler: "promptTextAndContinue",
        handlerParams: ["", { voice_path_male: "", voice_path_female: "" }],
      },
    ],
    "Send Carousel": [
      {
        block_type: "Send Carousel",
        node_id: "",
        name: "",
        handler: "promptCardAndContinue",
        handlerParams: [{}, ""],
      },
    ],
    "Send Suggestions": [
      {
        block_type: "Send Suggestions",
        node_id: "",
        name: "",
        handler: "promptSuggestionsAndContinue",
        handlerParams: [
          [],
          {},
          "",
          { voice_path_male: "", voice_path_female: "" },
        ],
      },
      {
        block_type: "Send Suggestions",
        node_id: "",
        name: "",
        handler: "checkInPool",
        handlerParams: [
          [
            {
              title: "not matched",
              redirect: "",
              connector_id: "0",
            },
          ],
        ],
      },
    ],
    "Send Items": [
      {
        block_type: "Send Items",
        name: "",
        handler: "generateItemCriteria",
        handlerParams: [
          {
            entity: "__url",
            bot_id: "",
            item_title: null,
            item_ids: [],
            category_name: null,
            feature_name: null,
            feature_value: null,
          },
        ],
      },
      {
        block_type: "Send Items",
        name: "",
        handler: "callAPI",
        handlerParams: [
          {
            method: "GET",
            url: "${__url}",
            headers: {
              "Content-Type": "application/json",
            },
            entity: "response",
          },
        ],
      },
      {
        name: "",
        handler: "promptItemCardsAndContinue",
        handlerParams: ["response"],
      },
    ],
    "Send Categories": [
      {
        block_type: "Send Categories",
        name: "",
        handler: "generateCategoryCriteria",
        handlerParams: [
          {
            entity: "__url",
            bot_id: "",
            category_ids: [],
            no_load_more: true,
          },
        ],
      },
      {
        block_type: "Send Categories",
        name: "",
        handler: "callAPI",
        handlerParams: [
          {
            method: "GET",
            url: "${__url}",
            headers: {
              "Content-Type": "application/json",
            },
            entity: "response",
          },
        ],
      },
      {
        block_type: "Send Categories",
        name: "",
        handler: "promptCategoryCards",
        handlerParams: ["response"],
      },
    ],
    "Send Promotions": [
      {
        block_type: "Send Promotions",
        name: "",
        handler: "generateOfferCriteria",
        handlerParams: [
          {
            entity: "__url",
            bot_id: "",
            offer_ids: [],
            no_load_more: true,
          },
        ],
      },
      {
        block_type: "Send Promotions",
        name: "",
        handler: "callAPI",
        handlerParams: [
          {
            method: "GET",
            url: "${__url}",
            headers: {
              "Content-Type": "application/json",
            },
            entity: "response",
          },
        ],
      },
      {
        block_type: "Send Promotions",
        name: "",
        handler: "promptOfferCards",
        handlerParams: ["response"],
      },
    ],
    "Jump To Widget": [
      {
        block_type: "Jump To Widget",
        name: "",
        handler: "redirectToStep",
        handlerParams: [
          {
            path: "",
            step: {},
          },
        ],
      },
    ],
    "Trigger Dialog": [
      {
        block_type: "Trigger Dialog",
        name: "",
        handler: "triggerDialog",
        handlerParams: [
          {
            trigger_id: "",
            dialog_name: "",
            path: "",
          },
          "__ar__:##ar## __en__:##en##",
        ],
      },
    ],
    "Set Entity": [
      {
        block_type: "Set Entity",
        name: "",
        handler: "setEntityValue",
        handlerParams: [{}],
      },
    ],
    "Knowledge Base Lookup": [
      {
        block_type: "Knowledge Base Lookup",
        name: "",
        handler: "kbLookup",
        handlerParams: [
          [
            {
              connector_id: "0",
              title: "No result",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "matched",
              redirect: "",
            },
          ],
        ],
      },
    ],
    end_dialog: [
      {
        block_type: "end_dialog",
        name: "",
        handler: "endDialog",
        handlerParams: [],
      },
    ],
    "JSON API": [
      {
        block_type: "JSON API",
        name: "",
        handler: "callAPIJSON",
        handlerParams: [
          {
            method: "GET",
            url: "",
            headers: {},
            body: {},
            isCustomResponse: false,
            entity: "",
          },
          [
            {
              connector_id: "0",
              title: "Success",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "Failed",
              redirect: "",
            },
          ],
        ],
      },
    ],
    "Dynamic Card": [
      {
        block_type: "Dynamic Card",
        name: "",
        handler: "promptDynamicCard",
        handlerParams: [
          {
            entity: "",
            mapper: {
              image_url: "",
              video_url: "",
              heading: "",
              subheading: "",
            },
          },
        ],
      },
    ],
    "Send Email": [
      {
        block_type: "Send Email",
        name: "",
        handler: "promptSendGridStep",
        handlerParams: [
          {
            mapper: {
              cc: [],
              bcc: [],
              email: [],
              title: "",
              body: "",
              integration: "",
            },
            attachments: [],
          },
          "",
        ],
      },
    ],
    trello: [
      {
        block_type: "trello",
        name: "",
        handler: "promptTrelloStep",
        handlerParams: ["", {}, {}],
      },
    ],
    google: [
      {
        block_type: "google",
        name: "",
        handler: "promptGoogleStep",
        handlerParams: ["", {}, {}],
      },
    ],
    delay_block: [
      {
        block_type: "delay_block",
        name: "",
        handler: "promptDelayBlockStep",
        handlerParams: [""],
      },
    ],
    "Dynamic Table": [
      {
        block_type: "Dynamic Table",
        name: "",
        handler: "promptDynamicTable",
        handlerParams: [
          "",
          {},
          {
            img: "",
            pdfTitle: "",
          },
          {
            saveToEntity: false,
            selectedEntity: "",
          },
        ],
      },
    ],
    "Prompt Calender": [
      {
        block_type: "Prompt Calender",
        name: "",
        handler: "promptCalender",
        handlerParams: [""],
      },
      {
        block_type: "Prompt Calender",
        name: "",
        handler: "continueStepDialog",
        handlerParams: [],
        recieving: "",
      },
    ],
    "Prompt Dynamic Suggestions": [
      {
        block_type: "Prompt Dynamic Suggestions",
        name: "",
        handler: "promptDynamicSuggestions",
        handlerParams: [{}, ""],
      },
      {
        block_type: "Prompt Dynamic Suggestions",
        name: "",
        handler: "dynamicHandler",
        handlerParams: [
          [
            {
              title: "matched",
              redirect: "",
              connector_id: "1",
            },
            {
              title: "not matched",
              redirect: "",
              connector_id: "0",
            },
          ],
        ],
        recieving: "",
      },
    ],
    Lead_Generation: [
      {
        block_type: "Lead_Generation",
        name: "",
        handler: "promptLeadGeneration",
        handlerParams: [{}],
      },
    ],
    "Prompt Reports": [
      {
        block_type: "Prompt Reports",
        name: "",
        handler: "reportNameHandler",
        handlerParams: [""],
      },
      {
        block_type: "Prompt Reports",
        name: "",
        handler: "reportEmailHandler",
        handlerParams: [""],
      },
      {
        block_type: "Prompt Reports",
        name: "",
        handler: "reportPhoneHandler",
        handlerParams: [""],
      },
      {
        block_type: "Prompt Reports",
        name: "",
        handler: "reportFeedbackHandler",
        handlerParams: [
          {
            type: "text", // || rate
            style: "stars", // || emoji
            question: "",
          },
        ],
      },
      {
        block_type: "Prompt Reports",
        name: "",
        handler: "reportSaveHandler",
        handlerParams: [""],
      },
    ],
    "Create Store": [
      {
        block_type: "Create Store",
        name: "",
        handler: "createEntityStore",
        handlerParams: [
          {
            entity: "",
            data: [],
          },
        ],
      },
    ],
    "Store Lookup": [
      {
        block_type: "Store Lookup",
        name: "",
        handler: "entityStoreLookup",
        handlerParams: [
          {
            sourceType: "",
            searchStoreEntity: "",
            searchIndex: "",
            searchEntity: "",
            entity: "",
            searchType: "",
            threshold: "0.7",
            code: "",
            preprocessText: false,
          },
          [
            {
              connector_id: "0",
              title: "No result",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "matched",
              redirect: "",
            },
          ],
        ],
      },
    ],
    Chart: [
      {
        block_type: "Chart",
        name: "",
        handler: "promptChart",
        handlerParams: [
          {
            entity: "",
            mapper: {
              chart_type: "",
              data_type: "",
              xAxis: "",
              yAxis: "",
              x_title: "",
              y_title: "",
              graph_name: "",
              color_set: "",
            },
          },
        ],
      },
    ],
    Textraction: [
      {
        block_type: "Textraction",
        name: "",
        handler: "textraction",
        handlerParams: [
          {
            sourceType: "user_question", // || "entity",
            sourceEntity: "",
            entity: "",
            type: "",
            typeOption: "",
          },
          [
            {
              connector_id: "0",
              title: "No result",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "matched",
              redirect: "",
            },
          ],
        ],
      },
    ],
    "JS Code": [
      {
        block_type: "JS Code",
        name: "",
        handler: "JSEval",
        handlerParams: [""],
      },
    ],
    "Live Chat": [
      {
        block_type: "Live Chat",
        name: "",
        handler: "lcStart",
        handlerParams: [
          "",
          {
            endOfHandoverSession: { ar: "", en: "" },
            noAgentAvailable: { ar: "", en: "" },
            onHold: { ar: "", en: "" },
          },
          { displayName: "", phoneNumber: "", email: "" },
          {
            ratingEnabled: false,
            ratingMsg: { ar: "", en: "" },
            ratingType: "",
            isFeedback: false,
            triggerRating: 0,
            feedbackMsg: { ar: "", en: "" },
          },
        ],
      },
      {
        block_type: "Live Chat",
        node_id: "",
        name: "",
        recieving: "",
        handler: "lcRate",
        handlerParams: [],
      },
      {
        block_type: "Live Chat",
        node_id: "",
        name: "",
        recieving: "",
        handler: "lcFeedback",
        handlerParams: [],
      },
      {
        block_type: "Live Chat",
        node_id: "",
        recieving: "",
        name: "",
        handler: "lcEnd",
        handlerParams: [
          [
            {
              connector_id: "0",
              title: "Chat Ended",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "No Agent Available",
              redirect: "",
            },
          ],
        ],
      },
    ],
    Repeat: [
      {
        block_type: "Repeat",
        name: "",
        handler: "loopCounter",
        handlerParams: [
          {
            max: 1,
          },
          [
            {
              connector_id: "0",
              title: "Repeat",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "Failed",
              redirect: "",
            },
          ],
        ],
      },
    ],
    Conditions: [
      {
        block_type: "Conditions",
        name: "",
        handler: "conditions",
        handlerParams: [
          [["", ""]],
          [
            {
              connector_id: "0",
              title: "True",
              redirect: "",
            },
            {
              connector_id: "1",
              title: "False",
              redirect: "",
            },
          ],
        ],
      },
    ],
    "Whatsapp Number": [
      {
        block_type: "Whatsapp Number",
        name: "",
        handler: "getWaNumber",
        handlerParams: [""],
      },
    ],
    "Html Block": [
      {
        block_type: "Html Block",
        name: "",
        handler: "promptHtml",
        handlerParams: [
          {
            html_code: "",
          },
          {
            isPdf: false,
            pdfTitle: {},
          },
          {
            saveToEntity: false,
            selectedEntity: "",
          },
        ],
      },
    ],
    "Starter Node": [],

    "Get Channel Id": [
      {
        block_type: "Get Channel Id",
        name: "",
        handler: "getChannelId",
        handlerParams: [""],
      },
    ],
    "Get Unique Conversation Id": [
      {
        block_type: "Get Unique Conversation Id",
        name: "",
        handler: "getConversationId",
        handlerParams: [""],
      },
    ],
    "Send Map Card": [
      {
        block_type: "Send Map Card",
        name: "",
        handler: "promptMapCardAndContinue",
        handlerParams: [
          {
            lat: 0,
            long: 0,
            title: "",
            description: "",
          },
        ],
      },
    ],
    Checkpoint: [
      {
        block_type: "Checkpoint",
        name: "",
        handler: "promptCheckpointAndContinue",
        handlerParams: ["", 0],
      },
    ],
    "Send Attachment": [
      {
        block_type: "Send Attachment",
        name: "",
        handler: "promptAttachment",
        handlerParams: [
          {
            mapper: {},
            attachments: [],
          },
          "",
        ],
      },
    ],

    "Request Attachment": [
      {
        block_type: "Request Attachment",
        node_id: "",
        name: "",
        handler: "promptText",
        handlerParams: [
          "",
          "attachment",
          { voice_path_male: "", voice_path_female: "" },
        ],
      },
      {
        block_type: "Request Attachment",
        node_id: "",
        recieving: "",
        name: "",
        handler: "validateIncomingAttachment",
        handlerParams: [
          {
            size: 1,
            attachment_type: "file",
            entity: "",
            saveAs: "url",
          },
          [
            {
              criteria: "attachment",
              redirect: "",
              title: "matched",
              connector_id: "0",
            },
            {
              criteria: "default",
              redirect: "",
              title: "not matched",
              connector_id: "1",
            },
          ],
        ],
      },
    ],
    "Request Location": [
      {
        block_type: "Request Location",
        node_id: "",
        name: "",
        handler: "promptText",
        handlerParams: ["", [], { voice_path_male: "", voice_path_female: "" }],
      },
      {
        block_type: "Request Location",
        node_id: "",
        // recieving: "",
        name: "",
        handler: "requestLocation",
        handlerParams: [
          "",
          [
            {
              criteria: "location",
              redirect: "",
              title: "matched",
              connector_id: "0",
            },
            {
              criteria: "default",
              redirect: "",
              title: "not matched",
              connector_id: "1",
            },
          ],
        ],
      },
    ],

    "Ticketing system": [
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingCategoryHandler",
        handlerParams: [""],
      },
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingNameHandler",
        handlerParams: [""],
      },
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingEmailHandler",
        handlerParams: [""],
      },
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingPhoneHandler",
        handlerParams: [""],
      },
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingTitleHandler",
        handlerParams: [""],
      },
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingDescriptionHandler",
        handlerParams: [""],
      },
      {
        block_type: "Ticketing system",
        name: "",
        handler: "TicketingEndHandler",
        handlerParams: [""],
      },
    ],

    "Dynamic Report": [
      {
        block_type: "Dynamic Report",
        name: "",
        handler: "dynamicFormHandler",
        handlerParams: [
          {
            dynamic_form_schema_id: "",
            form: [],
          },
        ],
      },
    ],
    "LLM Agent": [
      {
        block_type: "LLM Agent",
        node_id: "",
        name: "",
        handler: "agentCallHandler",
        handlerParams: [{
          agent_type: "textraction",
          input: {
            type: 'entity',
            source_entity: ""
          },
          custom_prompt: '',
          output_structure: [],
          output_entity: '',
          output_language: '__ar__:##arabic## __en__:##english##'
        }],
      },
    ],
  };
}
