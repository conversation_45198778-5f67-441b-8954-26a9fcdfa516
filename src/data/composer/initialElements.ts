import { ListStartIcon } from "lucide-react";
import {
  MarkerType,
  Edge
} from "reactflow";

export const nodes = [
  {
    id: 'node_trigger',
    type: 'starter',
    data: {
        label: 'Starter Node',
        handleOnClickBlock: (node) => {},
        block: {
            block_type: 'Starter Node',
            icon: ListStartIcon,
            color: '#9F6FFC',
        },
    },
    position: { x: 350, y: 250 },
  }
]


export const edge: Edge = {
  id: null,
  source: null,
  target: null,
  sourceHandle: null,
  targetHandle: null,
  label: "Next",
  type: "custom",
  labelStyle: { fill: "#000000", fontWeight: 500 },
  labelBgPadding: [10, 4],
  labelBgBorderRadius: 8,
  labelBgStyle: { fill: "#70EAFF", color: "#000000" },
  markerEnd: { type: MarkerType.ArrowClosed },
  style: { strokeWidth: "3px" },
  animated: true,
  deletable: true
};