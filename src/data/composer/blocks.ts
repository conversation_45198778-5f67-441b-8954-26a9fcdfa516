import {
  SheetIcon,
  MessageSquare,
  FormInputIcon,
  CreditCard,
  Mouse,
  TableIcon,
  LightbulbIcon,
  FlagIcon,
  Contact,
  ShoppingBag,
  BoxesIcon,
  BadgePercentIcon,
  CloudCog,
  BracesIcon,
  GitFork,
  IterationCcw,
  FolderSearch,
  PhoneOff,
  Store,
  PackageSearch,
  BarChart,
  Type,
  Code,
  MessagesSquare,
  Repeat1,
  Shuffle,
  Mail,
  Smartphone,
  Terminal,
  Tv,
  MessageSquareDashed,
  Map,  
  FlagTriangleLeft,
  Hash,
  File,
  MapPin,
  Tag,
  Clipboard,
  Bot,
} from "lucide-react";

export default [
  {
    block_type: "Send Message",
    icon: MessageSquare,
    description:
      'The "Send Message" block enables your chatbot to communicate text messages to users. Customize and position this block in the dialogue flow to create responsive, engaging interactions.',
    color: "#8ecae6",
    label: "Prompt Text",
    isSimple: true,
    hasConnectors: false,
    group: "Basic Blocks",
  },
  {
    block_type: "Request User Data",
    icon: FormInputIcon,
    description:
      "This block prompts the user for specific details, validates their input, ensures consent, and securely stores this information for later use in the chatbot's conversational flow.",
    color: "#219ebc",
    label: "Request User Data",
    isSimple: true,
    hasConnectors: true,
    group: "Basic Blocks",
  },
  {
    block_type: "Request Attachment",
    icon: File,
    description:
      "This block prompts the user for specific details, uploading the attachment",
    color: "#F4BE4F",
    label: "Request Attachment",
    isSimple: false,
    hasConnectors: true,
    group: "Basic Blocks",
  },
  {
    block_type: "Send Attachment",
    icon: File,
    description: "This block enables your chatbot to send an Attachment.",
    color: "#ffb703",
    label: "Send Attachment",
    hasConnectors: false,
    isSimple: false,
    group: "Basic Blocks",
    // avaliabitlyDependency: "sg_dep",
  },
  {
    block_type: "Send Carousel",
    icon: CreditCard,
    description:
      "This block enables the chatbot to display a series of interactive cards (carousel) to the user, providing visually rich content and multiple options to choose from for an engaging user experience.",
    color: "#fb8500",
    label: "Prompt Card",
    isSimple: true,
    hasConnectors: false,
    group: "Basic Blocks",
  },
  {
    block_type: "Send Suggestions",
    icon: Mouse,
    description:
      "This block enables the chatbot to display a series of interactive suggestions to the user, providing multiple options to choose from for an engaging user experience.",
    color: "#FF3C38",
    label: "Prompt Suggestions",
    isSimple: true,
    hasConnectors: true,
    group: "Basic Blocks",
  },
  {
    block_type: "Send Items",
    icon: ShoppingBag,
    description:
      "This block enables the chatbot to display a series of items to the user, where they can view item details and interact with it.",
    color: "#7ca982",
    label: "Send Items",
    isSimple: false,
    hasConnectors: false,
    group: "E-commerce Blocks",
  },
  {
    block_type: "Send Categories",
    icon: BoxesIcon,
    description:
      "This block enables the chatbot to display store categories to the user, where they can select a category to view the items within that category.",
    color: "#ffe1a8",
    label: "Send Categories",
    isSimple: false,
    hasConnectors: false,
    group: "E-commerce Blocks",
  },
  {
    block_type: "Send Promotions",
    icon: BadgePercentIcon,
    description:
      "This block enables the chatbot to display promotions to the user, where they can view the items within that promotion.",
    color: "#e26d5c",
    label: "Send Promotions",
    isSimple: false,
    hasConnectors: false,
    group: "E-commerce Blocks",
  },
  {
    block_type: "JSON API",
    icon: BracesIcon,
    description:
      "This block enables the chatbot to make a call to a JSON API, and use the response to continue the conversation.",
    color: "#ef476f",
    label: "API Integration",
    isSimple: false,
    hasConnectors: true,
    group: "Integration Blocks",
  },
  {
    block_type: "Trigger Dialog",
    icon: GitFork,
    description:
      "This block enables the chatbot to trigger another dialog, and continue the conversation from there.",
    color: "#7DBBC3",
    label: "Trigger A Dialog",
    isSimple: false,
    hasConnectors: false,
    group: "Flow Blocks",
    lastNode: true,
  },
  {
    block_type: "Jump To Widget",
    icon: IterationCcw,
    description:
      "This block enables the chatbot to jump to a widget, and continue the conversation from there.",
    color: "#A23E48",
    label: "Jump To Widget",
    isSimple: false,
    hasConnectors: false,
    lastNode: true,
    group: "Flow Blocks",
  },
  {
    block_type: "end_dialog",
    icon: PhoneOff,
    background: "#dcedf4",
    color: "#FF3C38",
    label: "End Dialog ",
    group: "Flow Blocks",
    lastNode: true,
    noForm: true,
  },
  {
    block_type: "Knowledge Base Lookup",
    icon: FolderSearch,
    description:
      "This block enables the chatbot to search the knowledge base for an answer and send it to the user.",
    color: "#FF8C42",
    label: "Knowledge Base Lookup",
    isSimple: false,
    hasConnectors: true,
    noForm: true,
    group: "Flow Blocks",
  },
  {
    block_type: "Dynamic Card",
    icon: CreditCard,
    description:
      "This block enables the chatbot to send data from an api in the form of visually appealing cards.",
    color: "#227C9D",
    label: "Prompt Dynamic Card",
    isSimple: false,
    hasConnectors: false,
    group: "Dynamic Blocks",
  },
  {
    block_type: "Dynamic Table",
    icon: TableIcon,
    description:
      "This block enables the chatbot to send data from an api in the form of a table.",
    color: "#EAAC8B",
    label: "Prompt Dynamic Table",
    isSimple: false,
    hasConnectors: false,
    group: "Dynamic Blocks",
  },
  {
    block_type: "Dynamic Report",
    icon: Clipboard,
    description:
      "build a custom form and prompt user to fill it or use the entities",
    color: "#E56B6F",
    label: "Dynamic Report",
    isSimple: false,
    hasConnectors: false,
    group: "Dynamic Blocks",
  },
  {
    block_type: "Prompt Dynamic Suggestions",
    icon: LightbulbIcon,
    description:
      "This block enables the chatbot to send data from an api in the form of interactive suggestions.",
    background: "#ffd166",
    color: "#dd4045",
    label: "Prompt Dynamic Suggestions",
    isSimple: false,
    hasConnectors: true,
    group: "Dynamic Blocks",
  },
  {
    block_type: "Prompt Reports",
    icon: FlagIcon,
    description:
      "This block enables the collection of users feedbacks and reports.",
    background: "#e7e3f7",
    color: "#ffd166",
    label: "Prompt Reports",
    avaliabitlyDependency: "report_dep",
    group: "Integration Blocks",
    hasConnectors: true,
  },
  {
    block_type: "Lead_Generation",
    icon: Contact,
    description:
      "This block enables the collection of users contact information.",
    background: "#def7f4",
    color: "#06d6a0",
    label: "Lead Generation",
    avaliabitlyDependency: "lead_dep",
    group: "Integration Blocks",
  },
  {
    block_type: "Send Email",
    icon: Mail,
    description: "This block enables your chatbot to send an Email.",
    color: "#118ab2",
    label: "Send Email",
    hasConnectors: false,
    group: "Integration Blocks",
    avaliabitlyDependency: "sg_dep",
  },
  {
    block_type: "Create Store",
    icon: Store,
    description:
      "Create store to save raw data that you can use to look up information from.",
    background: "#def7f4",
    color: "#ffbdb5",
    label: "Create Store",
    group: "Data Blocks",
  },
  {
    block_type: "Store Lookup",
    icon: PackageSearch,
    description:
      "This block enables the chatbot to search the data store for the user input.",
    background: "#def7f4",
    color: "#f00ff0",
    label: "Store Lookup",
    hasConnectors: true,
    group: "Data Blocks",
  },
  {
    block_type: "Textraction",
    icon: Type,
    description:
      "This block enables the chatbot to extract entities from a user input.",
    color: "#2ed187",
    label: "Textraction",
    hasConnectors: true,
    group: "Data Blocks",
  },
  {
    block_type: "Chart",
    icon: BarChart,
    description:
      "The 'Chart' block takes a given dataset as input and produces a corresponding chart to visually represent the data for users.",
    background: "#def7f4",
    color: "#3d93c2",
    label: "Chart",
    group: "Data Blocks",
  },
  {
    block_type: "JS Code",
    icon: Code,
    description: "Write your own code to update entities.",
    background: "#def7f4",
    color: "#246EB9",
    label: "JS Code",
    group: "Dynamic Blocks",
  },
  {
    block_type: "Live Chat",
    icon: MessagesSquare,
    description: "Connect your chatbot to a live chat agent.",
    background: "#84A9C0",
    color: "#84A9C0",
    label: "Live Chat",
    isSimple: false,
    group: "Integration Blocks",
    hasConnectors: true,
    noForm: false,
    // lastNode: true,
    avaliabitlyDependency: "lc_dep",
  },
  {
    block_type: "Repeat",
    icon: Repeat1,
    description:
      "This block will run the block connected to the repeat if still in range",
    background: "#def7f4",
    color: "#c08497",
    label: "Repeat",
    isSimple: false,
    group: "Flow Blocks",
    hasConnectors: true,
  },
  {
    block_type: "Conditions",
    icon: Shuffle,
    description: "Use this block to add conditional logic to your flow.",
    background: "#def7f4",
    color: "#481fef",
    label: "Conditions",
    isSimple: false,
    group: "Flow Blocks",
    hasConnectors: true,
  },
  {
    block_type: "Whatsapp Number",
    icon: Smartphone,
    description:
      "This block will automatically assign user's number to an entity of your choice. Use it after checking if the user is from whatsapp with the conditions block.",
    background: "#6CFA7A",
    color: "#0BC144",
    label: "Whatsapp Number",
    isSimple: false,
    group: "Dynamic Blocks",
    hasConnectors: false,
  },
  {
    block_type: "Html Block",
    icon: Terminal,
    description: "This block of code is used write html code ",
    background: "#def7f4",
    color: "#3d93c2",
    label: "Html Block",
    group: "Data Blocks",
  },

  {
    block_type: "Get Channel Id",
    icon: Tv,
    description: "Use this block to get your channel ID. ",
    background: "#2ed187",
    color: "#FFD166",
    label: "Get Channel Id",
    isSimple: false,
    group: "Dynamic Blocks",
    hasConnectors: false,
  },

  {
    block_type: "Get Unique Conversation Id",
    icon: MessageSquareDashed,
    description: "Use this block to get your Unique Conversation ID.",
    background: "#2ed187",
    color: "#A690A4",
    label: "Get Unique Conversation Id",
    isSimple: false,
    group: "Dynamic Blocks",
    hasConnectors: false,
  },
  {
    block_type: "Send Map Card",
    icon: Map,
    description: "Use this block to send a map.",
    background: "#2ed187",
    color: "#118AB2",
    label: "Send Map Card",
    isSimple: false,
    group: "Dynamic Blocks",
    hasConnectors: false,
  },
  {
    block_type: "Checkpoint",
    icon: FlagTriangleLeft,
    description:
      "Create a check to see if the user has reached a certain point in the conversation.",
    background: "#2ed187",
    color: "#2dda78",
    label: "Checkpoint",
    isSimple: false,
    group: "Flow Blocks",
    hasConnectors: false,
  },
  {
    block_type: "Request Location",
    icon: MapPin,
    description: "this block asks the user to send their location",
    color: "#8B0000",
    label: "Request Location",
    hasConnectors: true,
    isSimple: false,
    group: "Basic Blocks",
  },
  {
    block_type: "Ticketing system",
    icon: Tag,
    description: "Efficient Issue Tracking for Seamless Customer Support ",
    color: "#274c77",
    label: "Ticketing system",
    hasConnectors: true,
    isSimple: false,
    group: "Integration Blocks",
    avaliabitlyDependency: "ticketing_dep",
  },
  {
    block_type: "LLM Agent",
    icon: Bot,
    description:
      'Achieve your tasks with the power of large language models.',
    color: "#ffffff",
    label: "LLM Agent",
    isSimple: false,
    hasConnectors: false,
    group: "Integration Blocks",
    avaliabitlyDependency: "llm_dep",
  },
];
