"use client";
import { useState, useEffect, memo, lazy } from "react";
import { MainSidebar, BotSidebar } from "common/components/sidebars";
import { HomeView } from "views/home";
import { BotsView } from "views/bots";
import { ProfileView } from "views/profile";
import useAuthentication from "common/hooks/useAuthentication";
import useUserStore from "store/user/user.store";
import { generateToken } from "apis/user.api";
import { CreateBotModal } from "views/bots/components";
import DocHead from "common/components/docHead";
import { ConfirmModal } from "common/components/modals/confirm.modal";
import { Cairo, Source_Sans_Pro } from "next/font/google";
import { Toaster } from "react-hot-toast";

import { OneBotView } from "views/oneBot";
import { ContactModal } from "views/contact";
import { useLoaderContext } from "context/loaderContext";
import ClientOnly from "common/components/ClientOnly";
const source_sans = Source_Sans_Pro({
  weight: ["200", "300", "400", "600", "700", "900"],
  subsets: ["latin"],
});

const cairo = Cairo({
  weight: ["200", "300", "400", "600"],
  subsets: ["arabic"],
});

const HomePage = () => {
  const { user } = useUserStore();
  const [view, setView] = useState("home-view");
  const [token, setToken] = useState("");
  const [tokenLoaded, setTokenLoaded] = useState(false);
  const { loader } = useLoaderContext();

  useEffect(() => {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    let views = urlParams.get("view");
    const url = new URL(window.location.href);

    if (Boolean(url.hash) && url.hash.split("=")[0] === "#access_token") {
      views = "bots-view-7";
    }
    if (!views || views === "") {
      const defaultValue = "home-view";
      changeView(defaultValue);
    } else {
      setView(views);
    }
  }, []);

  useEffect(() => {
    if (user?.user_id) {
      generateToken({
        user_id: user.user_id,
      }).then((data) => {
        if (data?.token) {
          setToken(data?.token);
        }
        setTokenLoaded(true);
      });
    } else {
      setTokenLoaded(true);
    }
  }, []);
  useAuthentication(token, tokenLoaded);

  const changeView = (value) => {
    loader?.continuousStart();
    var url = new URL(window.location.href);

    var search_params = url.searchParams;

    search_params.delete("view_id");
    search_params.set("view", value);

    url.search = search_params.toString();

    var new_url = url.toString();
    if (window.history.pushState) {
      window.history.pushState({ path: new_url }, "", new_url);
    }
    setView(value);
    loader?.complete();
  };
  return (
    <>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      <ClientOnly>
        <div>
          <Toaster
            toastOptions={{
              className: "",
              style: {
                borderRadius: "10px",
                background: "#333",
                color: "#fff",
              },
              position: "bottom-center",
            }}
          />
        </div>

        <div
          className={`h-screen overflow-y-auto text-white/80 font-general bg-secondary  ${source_sans.className} `}
        >
          <MainSidebar view={view} setView={changeView} />
          <div className="pl-16">
            {view === "bots-view" ? (
              <BotsView setView={changeView} />
            ) : view === "profile-view" ? (
              <ProfileView />
            ) : view.includes("bots-view-") ? (
              <OneBotView view={view} setView={changeView} />
            ) : (
              <HomeView setView={changeView} />
            )}
          </div>
          <ConfirmModal />
          <CreateBotModal setView={changeView} />
          <ContactModal />
        </div>
      </ClientOnly>
    </>
  );
};

export default memo(HomePage);
