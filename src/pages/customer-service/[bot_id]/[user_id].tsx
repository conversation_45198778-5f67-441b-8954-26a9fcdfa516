import { uploadBotImages } from "apis/file.api";
import {
  assignTicketToAgent,
  createComment,
  getSupportAgent,
  getTicket,
  getTicketsPerAgentDepartments,
} from "apis/ticketing.api";
import ClientOnly from "common/components/ClientOnly";
import DocHead from "common/components/docHead";
import { ConfirmModal } from "common/components/modals/confirm.modal";
import TicketCard from "common/components/tickets/ticket";
import { Avatar, AvatarFallback, AvatarImage } from "common/ui/avatar";
import { Button } from "common/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "common/ui/dropdownMenu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import constant from "constant";
import generateStorageId from "helpers/generateStorageId";
import {
  <PERSON><PERSON>,
  <PERSON>,
  EllipsisVerticalIcon,
  User,
} from "lucide-react";
import NotAuthorized from "pages/401";
import React, { useCallback, useEffect } from "react";
import toast, { Toaster } from "react-hot-toast";
import { Ticket } from "store/ticketing/ticketing.types";
import useUserStore from "store/user/user.store";
import * as signalR from "@microsoft/signalr";
import { useRouter } from "next/router";
import { formatDistanceToNow } from 'date-fns';

const AgentPage = ({ bot_id, user_id, supportAgent }) => {
  const router = useRouter();
  const user = useUserStore((state) => state.user);

  const [currentTicket, setCurrentTicket] = React.useState<Ticket | null>(null);
  const [assignedTickets, setAssignedTickets] = React.useState<Ticket[]>([]);
  const [unassignedTickets, setUnassignedTickets] = React.useState<Ticket[]>(
    []
  );
  const [agentCategories, setAgentCategories] = React.useState([]);

  const [incomingActivity, setIncomingActivity] = React.useState(null);

  const signalRNegotiate = async (bot_id, agent_id) => {
    const socketServer = "https://i2i-messaging.azurewebsites.net";
    const userId = `ticketing__${bot_id}`;
    const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot_id}&type=server`;
    const _connection = new signalR.HubConnectionBuilder()
      .withUrl(negotiationURL)
      .build();
    _connection
      .start()
      .then(() => {
        console.log("SHOULD BE WORKING", userId);
      })
      .catch((err) => {
        console.error("SignalR connection error:", err);
      });

    _connection.on("chatMessage", (message) => {

      if (message.type === "new_ticket") {
        setIncomingActivity(message);
      } else if (message.type === "ticket_assigned") {
        if (message.ticket.support_agent_id !== agent_id) {
          setIncomingActivity(message);
        }
      } else if (message.type === "new_comment_unassigned") {
        setIncomingActivity(message);
      }
    });
  };

  const signalRNegotiatePerAgent = async (bot_id, agent_id) => {
    const socketServer = "https://i2i-messaging.azurewebsites.net";
    const userId = `ticketing__${bot_id}_${agent_id}`;
    const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot_id}&type=server`;
    const _connection = new signalR.HubConnectionBuilder()
      .withUrl(negotiationURL)
      .build();
    _connection
      .start()
      .then(() => {
        console.log("SHOULD BE WORKING", userId);
      })
      .catch((err) => {
        console.error("SignalR connection error:", err);
      });

    _connection.on("chatMessage", (message) => {

      if (message.type === "new_comment") {
        setIncomingActivity(message);
      }
    });
  };

  const handleNewCommentUnassigned = (incomingActivity) => {
    if (
      currentTicket &&
      currentTicket.ticket_id === incomingActivity.comment.ticket_id
    ) {
      setCurrentTicket({
        ...currentTicket,
        comments: [...currentTicket.comments, incomingActivity.comment],
      });
    }
  };

  const handleTicketAssigned = (incomingActivity) => {
    toast("Ticket was assigned");
    const ticketsCopy = [...unassignedTickets];

    const filtered = ticketsCopy.filter(
      (t) => t.ticket_id !== incomingActivity.ticket.ticket_id
    );

    setUnassignedTickets([...filtered]);
    if (
      currentTicket &&
      +currentTicket.ticket_id === +incomingActivity.ticket.ticket_id
    ) {
      setCurrentTicket(null);
      const { pathname, query } = router;
      const updatedQuery = { ...query };
      delete updatedQuery.ticket;
      router.replace(
        {
          pathname,
          query: updatedQuery,
        },
        undefined,
        { shallow: true }
      );
    }
  };

  const handleNewTicket = (incomingActivity) => {
    if (agentCategories.includes(incomingActivity.ticket.category_id)) {
      toast("New ticket was created");
      const ticketsCopy = [...unassignedTickets];
      ticketsCopy.push(incomingActivity.ticket);
      setUnassignedTickets(ticketsCopy);
    }
  };

  const handleNewComment = (incomingActivity) => {
    toast("New comment was added to a ticket");
    if (
      currentTicket &&
      currentTicket.ticket_id === incomingActivity.comment.ticket_id
    ) {
      setCurrentTicket({
        ...currentTicket,
        comments: [...currentTicket.comments, incomingActivity.comment],
      });
    } else {
      setAssignedTickets(
        assignedTickets.map((t) => {
          if (t.ticket_id === incomingActivity.ticket_id) {
            return {
              ...t,
              isLastCommentByCustomer: true,
            };
          }
          return t;
        })
      );
    }
  };

  useEffect(() => {
    if (!incomingActivity) return;

    switch (incomingActivity.type) {
      case "ticket_assigned":
        handleTicketAssigned(incomingActivity);
        break;
      case "new_ticket":
        handleNewTicket(incomingActivity);
        break;
      case "new_comment":
        handleNewComment(incomingActivity);
        break;
      case "new_comment_unassigned":
        handleNewCommentUnassigned(incomingActivity);
        break;
    }

    setIncomingActivity(null);
  }, [incomingActivity]);

  useEffect(() => {
    const populatedTickets = () => {
      getTicketsPerAgentDepartments(supportAgent.support_agent_id).then(
        (res) => {
          if (res) {
            setAssignedTickets(
              res.tickets.filter(
                (ticket) =>
                  ticket.support_agent_id === supportAgent.support_agent_id
              ).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            );
            setUnassignedTickets(
              res.tickets.filter((ticket) => !ticket.support_agent_id).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            );
            setAgentCategories(res.categoryIds);
          }
        }
      );
    };
    if (supportAgent && supportAgent.support_agent_id) {
      populatedTickets();
    }
  }, [supportAgent]);

  useEffect(() => {
    if (!supportAgent || !supportAgent.support_agent_id) return;

    signalRNegotiatePerAgent(bot_id, supportAgent.support_agent_id);
    signalRNegotiate(bot_id, supportAgent.support_agent_id);
  }, [supportAgent]);


  const statusClassName = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-500";
      case "closed":
        return "bg-red-500";
      default:
        return "bg-white !text-black";
    }
  };

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const ticket_uuid = urlParams.get("ticket");

    if (ticket_uuid) {
      getTicket(ticket_uuid).then((res) => {
        setCurrentTicket(res);
      });
    }
  }, []);

  const handleOpenTicket = (ticket) => {
    window.history.pushState(
      {},
      "",
      `/customer-service/${bot_id}/${user_id}?ticket=${ticket.ticket_uuid}`
    );
    getTicket(ticket.ticket_uuid).then((res) => {
      setCurrentTicket(res);
    });
  };

  return (
    <ClientOnly>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      {!supportAgent ||
      !user ||
      !user?.user_id ||
      +user?.user_id !== +user_id ? (
        <NotAuthorized />
      ) : (
        <div className="bg-accent  w-screen overflow-hidden grid grid-cols-12 text-white h-screen">
          <aside className="col-span-3 bg-slate-950 rounded-tr-3xl rounded-br-3xl flex flex-col relative">
            <div className="m-2 px-5 py-2 flex items-center gap-5 border-b border-white/25 h-[8%]">
              <Avatar>
                <AvatarImage src={supportAgent.user?.photo} />
                <AvatarFallback>
                  <User className="w-5 h-5" />
                </AvatarFallback>
              </Avatar>
              <h2 className="text-white text-lg flex flex-col">
                <span className="capitalize">
                  {supportAgent.user?.user_name}
                </span>
                <span className="text-xs">{supportAgent.user?.email}</span>
              </h2>
            </div>
            <div className="">
              <Tabs defaultValue="assigned">
                <TabsList className={`grid w-full grid-cols-2 px-5`}>
                  <TabsTrigger value="assigned">
                    Your Tickets ({assignedTickets.length})
                  </TabsTrigger>

                  <TabsTrigger value="unassigned">
                    Unassigned Tickets ({unassignedTickets.length})
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="assigned" className="border-0  max-h-[calc(100vh-200px)]  overflow-y-auto">
                  {assignedTickets.length > 0 && (
                    <div className="space-y-1  pb-10">
                      {assignedTickets.map((ticket) => (
                        <div
                          key={ticket.ticket_id}
                          className={`border ${currentTicket?.ticket_id === ticket.ticket_id ? 'border-primary' : 'border-white/25'}  p-2 rounded-md cursor-pointer hover:bg-white/10 active:bg-white/20 active:shadow-lg relative`}
                          onClick={() => {
                            handleOpenTicket(ticket);
                          }}
                        >
                          {ticket.isLastCommentByCustomer && (
                            <Dot
                              className="absolute top-1 right-2  text-red-300"
                              size={30}
                            />
                          )}
                          <div className="flex flex-col px-1">
                          <div className="flex items-center justify-between">
                              <span className="">{ticket.title}</span>
                              <span className="text-xs ">{formatDistanceToNow(new Date(ticket.createdAt))} ago</span> 
                            </div>
                            {/* <span className="text-sm text-white/50">
                              {ticket.ticket_uuid}
                            </span> */}
                            <div className="flex justify-between mb-1 mt-5">
                              <span className="text-[10px] text-white/50 border border-white/25 p-1 rounded-2xl">
                                {ticket.category?.category}
                              </span>
                              <span
                                className={`text-white ${statusClassName(
                                  currentTicket?.ticket_id === ticket.ticket_id ?  currentTicket.status : ticket.status
                                )} text-xs px-2 py-1 rounded-xl capitalize`}
                              >
                                {currentTicket?.ticket_id === ticket.ticket_id ?  currentTicket.status : ticket.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </TabsContent>
                <TabsContent value="unassigned" className="border-0 max-h-[calc(100vh-200px)]  overflow-y-auto">
                  {unassignedTickets.length > 0 && (
                    <div className="space-y-1 pb-10 overflow-y-auto">
                      {unassignedTickets.map((ticket) => (
                        <div
                          key={ticket.ticket_id}
                          className={`border ${currentTicket?.ticket_id === ticket.ticket_id ? 'border-primary' : 'border-white/25'} p-2 rounded-md cursor-pointer hover:bg-white/10 active:bg-white/20 active:shadow-lg relative`}
                          onClick={() => handleOpenTicket(ticket)}
                        >
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <EllipsisVerticalIcon
                                className="w-5 h-5 absolute top-3 right-1 hover:bg-gray-600/25 rounded-full"
                                onClick={() => {}}
                              />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="ml-8 -mb-5 w-10 bg-secondary border-white/25 text-white">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  assignTicketToAgent({
                                    ticket_id: ticket.ticket_id,
                                    support_agent_id:
                                      supportAgent.support_agent_id,
                                    assigned_by: supportAgent.support_agent_id,
                                    ticket_uuid: ticket.ticket_uuid,
                                  }).then((res) => {
                                    if (res.ticket_id) {
                                      toast.success("Ticket assigned to you");
                                      setAssignedTickets([
                                        ...assignedTickets,
                                        ticket,
                                      ]);
                                      setUnassignedTickets(
                                        unassignedTickets.filter(
                                          (t) =>
                                            t.ticket_id !== ticket.ticket_id
                                        )
                                      );

                                      setCurrentTicket(res);
                                    } else {
                                      toast.error("Failed to assign ticket");
                                    }
                                  });
                                }}
                                className="cursor-pointer hover:!bg-white/25"
                              >
                                Assign to me
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>

                          <div className="flex flex-col px-1">
                            <div className="flex items-center justify-between">
                              <span className="w-2/3">{ticket.title}</span>
                              <span className="text-xs ">{formatDistanceToNow(new Date(ticket.createdAt))} ago</span> 
                              <span className="text-xs"></span> 
                            </div>
                            {/* <span className="text-sm text-white/50">
                              {ticket.ticket_uuid}
                            </span> */}
                            <div className="flex justify-between mb-1 mt-5">
                              <span className="text-[10px] text-white/50 border border-white/25 p-1 rounded-2xl">
                                {ticket.category?.category}
                              </span>
                              <span
                                className={`${statusClassName(
                                  ticket.status
                                )} text-white text-xs px-2 py-1 rounded-xl capitalize`}
                              >
                                {ticket.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
            <div className="px-5 pt-5 flex items-center gap-1 h-[8%] absolute bottom-3 right-1">
              <div className="text-xs text-[10px] text-white/30 ">
                Customer service for bot:
              </div>
              <Avatar>
                <AvatarImage
                  className="scale-75"
                  src={supportAgent.bot?.icon}
                />
                <AvatarFallback>
                  <Bot className="w-5 h-5" />
                </AvatarFallback>
              </Avatar>
              <h2 className="text-white text-sm capitalize">
                {supportAgent.bot?.bot_name}
              </h2>
            </div>
          </aside>
          <div className="p-5 pb-10 col-span-9 overflow-y-auto">
            {currentTicket ? (
              <TicketCard
                currentTicket={currentTicket}
                setCurrentTicket={setCurrentTicket}
                support_agent_id={supportAgent.support_agent_id}
                theme="dark"
                removeNotification={
                  () => {
                    setAssignedTickets(
                      assignedTickets.map((t) => {
                        if (t.ticket_id === currentTicket.ticket_id) {
                          return {
                            ...t,
                            isLastCommentByCustomer: false,
                          };
                        }
                        return t;
                      })
                    );
                  }
                }
              />
            ) : null}
          </div>
        </div>
      )}
      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      <ConfirmModal />
    </ClientOnly>
  );
};

export const getStaticPaths = async () => {
  return {
    paths: [],
    fallback: "blocking",
  };
};

export const getStaticProps = async (context) => {
  const { bot_id, user_id } = context.params;

  let supportAgent = null;

  try {
    if (!isNaN(+user_id) && !isNaN(+bot_id))
      supportAgent = await getSupportAgent({ user_id, bot_id });
    else supportAgent = null;
    if (!supportAgent || !supportAgent.support_agent_id) {
      return {
        props: {
          bot_id,
          user_id,
          supportAgent: null,
        },
      };
    }
  } catch (error) {
    console.log(error);
  }

  return {
    props: { bot_id, user_id, supportAgent },
  };
};

export default AgentPage;
