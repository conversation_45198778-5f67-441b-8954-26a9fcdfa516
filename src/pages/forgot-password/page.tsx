import Link from "next/link";
import React, { memo, useState } from "react";
import { ShieldClose } from "lucide-react";
import useUserStore from "store/user/user.store";
import {
  ForgotPasswordInfo,
  ForgotPasswordSchema,
} from "store/user/user.types";
import { Toaster } from "react-hot-toast";
import checkForErrors from "helpers/forms";
import { Button } from "common/ui/button";

const ForgotPasswordPage = () => {
  const { forgot_password, message, loading } = useUserStore();

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    email: "",
  });

  const handleSubmit = async (data, event) => {
    event.preventDefault();
    const isErrors = checkForErrors(
      {
        zodSchema: ForgotPasswordSchema,
        data,
      },
      setErrors
    );

    if (isErrors) return;

    await forgot_password(data);
  };

  const validateField =
    (field: keyof ForgotPasswordInfo) =>
    (value: unknown): string => {
      const parsedResult = ForgotPasswordSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  return (
    <>
      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      <div
        className="min-h-screen max-h-screen  text-white flex justify-center p-30"
        style={{
          background: "url(./assets/backgrounds/bg-main-dark-1440w.jpg)",
          backdropFilter: "blur(8px)",
        }}
      >
        <div className="max-w-screen-sm  m-0 sm:m-10 shadow-2xl sm:rounded-xl flex justify-center flex-1 p-4 z-100 glass">
          <div className=" flex  justify-center items-center">
            <div className="p-16">
              <div className="width-full flex items-center justify-center mb-8">
                <img
                  className="h-20 floating"
                  src="./assets/backgrounds/logo.png"
                ></img>
              </div>
              <h1 className="text-4xl font-bold leading-tight tracking-tight text-white md:text-4xl text-center mb-4">
                Forgot your password?
              </h1>
              <p className="text-center text-gray-200 mb-4">
                Don&apos;t worry! Please enter the email address that you
                registered with in Searchat!
              </p>
              {message && (
                <div className="p-3 rounded-lg border border-green-500 text-green-500 capitalize">
                  &#x2713; {message}
                </div>
              )}
              <form
                className="space-y-4 md:space-y-6 mt-20"
                onSubmit={(e) => handleSubmit(formData, e)}
              >
                <div className="relative">
                  <label htmlFor="email" className="sr-only ">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    className="bg-gray-50 border text-black border-gray-300 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-100  dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="Email"
                    onChange={(event) =>
                      onChangeHandler(event.target.name, event.target.value)
                    }
                  />
                  {errors.email && (
                    <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                      <ShieldClose size={12} />
                      {errors.email}
                    </span>
                  )}
                </div>

                <Button
                  loading={loading}
                  type="submit"
                  className="w-full text-white "
                >
                  Request reset link
                </Button>
                <p className="text-sm text-center font-light text-gray-200 dark:text-gray-200">
                  <Link
                    href="/login"
                    className="font-medium text-primary hover:underline dark:text-primary"
                  >
                    Back to Login!
                  </Link>
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default memo(ForgotPasswordPage);
