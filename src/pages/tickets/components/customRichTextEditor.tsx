"use client";
import React, { useEffect, useRef, useState } from "react";
import "react-quill/dist/quill.snow.css";
import "react-quill/dist/quill.bubble.css";
import "react-quill/dist/quill.core.css";
import TurndownService from "turndown";
import showdown from "showdown";
import dynamic from "next/dynamic";

const QuillNoSSRWrapper = dynamic(import("react-quill"), {
  ssr: false,
  loading: () => <p>Loading ...</p>,
});

const modules = {
  toolbar: [
    [{ header: "1" }, { header: "2" }],
    [{ size: [] }],
    ["bold", "italic", "blockquote"],
    [
      { list: "ordered" },
      { list: "bullet" },
      { indent: "-1" },
      { indent: "+1" },
    ],
    [
      { align: "" },
      { align: "center" },
      { align: "right" },
      { align: "justify" },
    ],
    ["clean"],
  ],
  clipboard: {
    // toggle to add extra line breaks when pasting HTML:
    matchVisual: false,
  },
};

const formats = [
  "header",
  "size",
  "bold",
  "italic",

  "blockquote",
  "list",
  "bullet",
  "indent",
  "align",
];

type Props = {
  onChangeRichText: (value: string) => void;
  value: string;
};

const CustomRichTextEditor = ({
  onChangeRichText,
  value,
}: Props) => {
  const [richText, setRichText] = useState("");

  useEffect(() => {
    if(value === "") setRichText(value);
  }, [value]);


  const onChange = (value) => {
    setRichText(value);
    console.log(value);
    var turndownService = new TurndownService();
 
    var markdown = turndownService.turndown(value);
    
    console.log(markdown);
    onChangeRichText(markdown);
  };

  const markdownToHtmlConverter = (value) => {
    var converter = new showdown.Converter();
  
    return converter.makeHtml(value);
  };

  return (
    <QuillNoSSRWrapper
      value={richText || markdownToHtmlConverter(value) || ""}
      onChange={onChange}
      modules={modules}
      formats={formats}
      placeholder="Write something..."
      className="h-[70%]"
    />
  );
};

export default CustomRichTextEditor;
