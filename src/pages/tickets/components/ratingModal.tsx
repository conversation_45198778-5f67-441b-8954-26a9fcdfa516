import { createRating } from "apis/ticketing.api";
import { cn } from "lib/utils";
import { X } from "lucide-react";
import React, { useState } from "react";

export default function RatingModal({
  setRatingModalOpen,
  ticket_id,
  existingFeedback,
  setExistingFeedback,
}) {
  const [feedback, setFeedback] = React.useState({
    rating: 3,
    rating_msg: "",
  });

  const [loading, setLoading] = useState(false);

  const handleRating = () => {
    setLoading(true);
    createRating({
      rating: feedback.rating,
      rating_msg: feedback.rating_msg.slice(0, 255),
      ticket_id: ticket_id,
    }).then((res) => {
      setLoading(false);
      if (res.rating_id) {
        setExistingFeedback({
          rating: feedback.rating,
          rating_msg: feedback.rating_msg,
        });
      }
    });
  };

  return (
    <div className="fixed inset-0 z-50 min-h-screen bg-gray-900/40 py-6 flex flex-col justify-center sm:py-12">
      <div className="py-3 sm:max-w-xl sm:mx-auto">
        <div className="bg-white min-w-1xl flex flex-col rounded-xl shadow-lg">
          {existingFeedback ? (
            <div className="px-12 py-5 relative">
              <X 
                className="absolute top-4 right-4 cursor-pointer"
                onClick={() => setRatingModalOpen(false)}
              />
              <div className="p-5">
                <h2 className="text-gray-800 text-3xl font-semibold">
                  Thank you for your feedback!
                </h2>

                <div className="flex items-center justify-center space-x-2 mt-4">
                  <StarRating
                    disabled
                    currentRating={existingFeedback.rating}
                  />
                </div>

                <div className="text-gray-800 mt-4 text-center border border-gray-400 rounded-md">
                  {existingFeedback.rating_msg}
                </div>
              </div>
            </div>
          ) : (
            <>
              <div className="px-12 py-5">
                <h2 className="text-gray-800 text-3xl font-semibold">
                  Your opinion matters to us!
                </h2>
              </div>
              <div className="bg-gray-200 w-full flex flex-col items-center">
                <div className="flex flex-col items-center py-6 space-y-3">
                  <span className="text-lg text-gray-800">
                    How was quality of the support?
                  </span>
                  <StarRating
                    currentRating={feedback.rating}
                    setCurrentRating={(rating) =>
                      setFeedback({ ...feedback, rating })
                    }
                  />
                </div>
                <div className="w-3/4 flex flex-col">
                  <textarea
                    rows={3}
                    placeholder="Tell us more about your experience"
                    className="p-4 text-gray-500 rounded-xl resize-none"
                    value={feedback.rating_msg}
                    onChange={(e) =>
                      setFeedback({ ...feedback, rating_msg: e.target.value })
                    }
                    contextMenu="paste"
                    maxLength={255}
                  />
                  <button
                    disabled={loading}
                    onClick={handleRating}
                    className="py-3 my-8 text-lg bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl text-white"
                  >
                    Rate now
                  </button>
                </div>
              </div>
              <div className="h-20 flex items-center justify-center">
                <button
                  onClick={() => {
                    setRatingModalOpen(false);
                  }}
                  className="text-gray-600"
                >
                  Maybe later
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

const StarRating = ({
  currentRating,
  disabled = false,
  setCurrentRating = null,
}) => {
  const handleChange = (event) => {
    setCurrentRating(Number(event.target.value));
  };

  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, index) => {
        const starValue = index + 1;
        return (
          <label
            key={starValue}
            htmlFor={`star${starValue}`}
            className={`${
              disabled ? "" : "cursor-pointer transition hover:scale-125"
            }`}
          >
            <span className="sr-only">
              {starValue} star{starValue > 1 ? "s" : ""}
            </span>
            <input
              disabled={disabled}
              id={`star${starValue}`}
              type="radio"
              className="sr-only"
              name="rating"
              value={starValue}
              checked={currentRating === starValue}
              onChange={handleChange}
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
              viewBox="0 0 24 24"
              fill="currentColor"
              className={cn(
                "w-6 h-6",
                currentRating >= starValue
                  ? "text-amber-500"
                  : "text-neutral-600 dark:text-neutral-300"
              )}
            >
              <path
                fillRule="evenodd"
                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z"
                clipRule="evenodd"
              />
            </svg>
          </label>
        );
      })}
    </div>
  );
};
