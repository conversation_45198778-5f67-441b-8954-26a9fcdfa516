import ClientOnly from "common/components/ClientOnly";
import { NextPage } from "next";
import React, { useEffect, useState } from "react";
import { getTicket, getTicketRating, updateTicket } from "apis/ticketing.api";
import { Ticket } from "store/ticketing/ticketing.types";
import { Avatar, AvatarFallback, AvatarImage } from "common/ui/avatar";
import { Button } from "common/ui/button";
import { Bot, Edit, X } from "lucide-react";
import { Input } from "common/ui/inputs/input";
import toast, { Toaster } from "react-hot-toast";
import { ConfirmModal } from "common/components/modals/confirm.modal";
import useConfirmModal from "common/hooks/useConfirmModal";
import DocHead from "common/components/docHead";
import TicketCard from "common/components/tickets/ticket";
import * as signalR from "@microsoft/signalr";
import RatingModal from "./components/ratingModal";

type Props = {
  uuid: string;
  ticket: Ticket | null;
};

const Page: NextPage<Props> = ({ uuid, ticket }) => {
  const [currentTicket, setCurrentTicket] = useState<Ticket | null>(ticket);
  const [changeEmail, setChangeEmail] = useState<boolean>(false);
  const [currentEmail, setCurrentEmail] = useState<string | null>(
    ticket.customer_email
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [unsubscribeLoading, setUnsubscribeLoading] = useState<boolean>(false);
  const [ratingModalOpen, setRatingModalOpen] = useState<boolean>(false);
  const [existingFeedback, setExistingFeedback] = useState(null);

  const confirmModal = useConfirmModal();

  useEffect(() => {
    // set ratingModalOPen true if ticket is closed and no feedback is given
    if (currentTicket?.status === "closed" && !existingFeedback) {
      setRatingModalOpen(true);
    }
  },[currentTicket, existingFeedback])

  useEffect(() => {
    const signalRNegotiate = async (ticket_uuid: string, bot_id: number) => {
      const socketServer = "https://i2i-messaging.azurewebsites.net";
      const userId = `ticketing__${ticket_uuid}`;
      const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot_id}&type=server`;
      const _connection = new signalR.HubConnectionBuilder()
        .withUrl(negotiationURL)
        .build();
      _connection
        .start()
        .then(() => {
          console.log("SHOULD BE WORKING", userId);
        })
        .catch((err) => {
          console.error("SignalR connection error:", err);
        });

      _connection.on("chatMessage", async (message) => {
        console.log("chatMessage message", message);
        const updatedTicket = await getTicket(ticket_uuid);
        if (updatedTicket) {
          setCurrentTicket(updatedTicket);
        }
      });
    };
    if (ticket?.ticket_uuid && ticket?.bot?.bot_id) {
      signalRNegotiate(ticket.ticket_uuid, ticket.bot.bot_id);
    }
  }, [ticket.ticket_uuid, ticket.bot.bot_id]);

  useEffect(() => {
    setCurrentTicket(ticket);
    getTicketRating(ticket.ticket_id).then((res) => {
      if (res.rating_id) {
        setExistingFeedback(res);
      }
    });
  }, [ticket]);

  const handleUpdateEmail = async () => {
    setLoading(true);

    const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(currentEmail);

    if (!isEmailValid) {
      toast.error("Invalid email address");
      setLoading(false);
      return;
    }

    const newTicket = await updateTicket(uuid, {
      customer_email: currentEmail,
    });
    if (newTicket.ticket_id) {
      setCurrentTicket(newTicket);
      toast.success("Email updated successfully");
    }
    setLoading(false);
    setChangeEmail(false);
  };

  const handleUnsubscribe = async () => {
    setUnsubscribeLoading(true);

    const newTicket = await updateTicket(uuid, {
      customer_email: null,
    });
    if (newTicket.ticket_id) {
      setCurrentTicket(newTicket);
      toast.success("Unsubscribed successfully");
    }
    setUnsubscribeLoading(false);
    setChangeEmail(false);
  };

  return (
    <ClientOnly>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      {ratingModalOpen && currentTicket && (
        <RatingModal
          setRatingModalOpen={setRatingModalOpen}
          ticket_id={currentTicket?.ticket_id}
          existingFeedback={existingFeedback}
          setExistingFeedback={setExistingFeedback}
        />
      )}
      <div className="bg-[#f1f1f1] w-screen h-screen overflow-y-auto flex flex-col p-10">
        <div className="flex gap-2 items-center justify-between text-xl capitalize font-bold w-full">
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarImage src={currentTicket.bot.icon} />
              <AvatarFallback>
                <Bot className="w-4 h-4" />
              </AvatarFallback>
            </Avatar>
            {currentTicket.bot.bot_name} bot Customer Service
          </div>

          {
            currentTicket.status === "closed" && !existingFeedback && (
            <Button onClick={
              () => {
                setRatingModalOpen(true);
              }
            }>Rate Us</Button>
          )}
        </div>
        <hr className="my-5 text-gray-300" />

        <div className="grid grid-cols-12 place-items-center lg:place-items-stretch divide-x divide-gray-300 h-full">
          <div className="col-span-12 lg:col-span-3">
            <div className="lg:sticky lg:top-0">
              <div className="lg:w-11/12 my-2 p-2 border border-gray-900/30 shadow-md rounded-xl">
                Dear {currentTicket?.customer_name || "Valued Customer"},
                <br />
                We&apos;re here to assist you with your issue. You can view the
                latest updates here.
                <br />
                <br />
                {currentTicket?.customer_email ? (
                  <div className="flex flex-col gap-2 items-start">
                    <div>
                      You&apos;ll receive email notifications at{" "}
                      <div className="underline group relative">
                        {currentTicket.customer_email}
                        <span
                          onClick={() => setChangeEmail(true)}
                          className="absolute top-0 hidden group-hover:inline-block cursor-pointer"
                        >
                          <Edit size={16} className="text-accent ml-2" />
                        </span>
                      </div>{" "}
                      whenever there&apos;s an update on your issue.
                    </div>
                  </div>
                ) : (
                  <div>
                    Would you like to receive email updates?{" "}
                    <Button
                      variant="link"
                      className="px-0"
                      onClick={() => {
                        setChangeEmail(true);
                      }}
                    >
                      Click here to subscribe.
                    </Button>
                  </div>
                )}
              </div>
              {changeEmail && (
                <div className="w-11/12 flex flex-col gap-2 mt-5 p-5 border border-gray-900/30 rounded-lg shadow-md relative">
                  <X
                    className="absolute top-1 right-1 cursor-pointer w-5 h-5"
                    onClick={() => setChangeEmail(false)}
                  />
                  <Input
                    name="email"
                    type="email"
                    placeholder="Enter your email"
                    className="border-accent"
                    title={
                      currentTicket.customer_email
                        ? "Change Email"
                        : "Subscribe to Email Updates"
                    }
                    value={currentEmail}
                    onChange={(e) => setCurrentEmail(e.target.value)}
                  />
                  <Button
                    loading={loading}
                    disabled={
                      currentTicket.customer_email === currentEmail ||
                      !currentEmail
                    }
                    onClick={() => {
                      handleUpdateEmail();
                    }}
                  >
                    Save
                  </Button>
                  {currentTicket.customer_email && (
                    <Button
                      variant="destructive"
                      onClick={() => {
                        confirmModal.onOpen();
                        confirmModal.setType("delete");
                        confirmModal.setText(
                          "Are you sure you want to unsubscribe?"
                        );
                        confirmModal.setSubtext(
                          "You will no longer receive email updates."
                        );
                        confirmModal.setBtntext("Unsubscribe");
                        confirmModal.setOnConfirm(handleUnsubscribe);
                      }}
                      loading={unsubscribeLoading}
                    >
                      Unsubscribe
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="col-span-12 lg:col-span-9 lg:p-5">
            <TicketCard
              currentTicket={currentTicket}
              setCurrentTicket={setCurrentTicket}
            />
          </div>
        </div>
      </div>

      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      <ConfirmModal />
    </ClientOnly>
  );
};

export const getStaticPaths = async () => {
  return {
    paths: [],
    fallback: "blocking",
  };
};

export const getStaticProps = async (context) => {
  const ticket_uuid = context.params.uuid;
  let ticket = null;
  try {
    ticket = await getTicket(ticket_uuid);
    console.log(ticket);
    if ((ticket && ticket.error) || !ticket) {
      return {
        notFound: true,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      notFound: true,
    };
  }
  return {
    props: { uuid: context.params.uuid, ticket: ticket || null },
  };
};

export default Page;
