import { useRouter } from "next/router";
import React from "react";

export default function NotFound() {
  const router = useRouter();
  return (
    <main className="h-screen w-full flex flex-col justify-center items-center bg-accent">
      <h1 className="text-9xl font-extrabold text-white tracking-widest">
        404
      </h1>
      <div className="bg-primary px-2 text-sm rounded rotate-12 absolute">
        Page Not Found
      </div>
      <button className="mt-5">
        <a className="relative inline-block text-sm font-medium text-primary group active:text-purple-500 focus:outline-none focus:ring">
          <span className="absolute inset-0 transition-transform translate-x-0.5 translate-y-0.5 bg-primary group-hover:translate-y-0 group-hover:translate-x-0"></span>

          <span
            onClick={() => router.push("/")}
            className="relative block px-8 py-3 bg-accent border border-current"
          >
            Go Home
          </span>
        </a>
      </button>
    </main>
  );
}
