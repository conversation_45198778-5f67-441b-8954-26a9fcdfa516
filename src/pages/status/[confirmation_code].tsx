import React from "react";

import { Toaster, toast } from "react-hot-toast";
import DocHead from "common/components/docHead";

import { Button } from "common/ui/button";
import { getFile } from "apis/file.api";

const FBDeletionPage = ({ data }) => {
  console.log(data);
  return !data ? (
    <div className="min-h-screen max-h-screen bg-accent text-white flex flex-col justify-center items-center p-30">
      The link is invalid or broken!
      <Button
        onClick={() => {
          window.location.href = "/";
        }}
      >
        Go to Home
      </Button>
    </div>
  ) : (
    <>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      <div className="min-h-screen max-h-screen bg-accent text-white flex justify-center p-30">
        <div className="flex flex-col justify-center items-center">
          <p>
            Data for user with id: {data.user_id} has been deleted successfully
          </p>
          <p>Confirmation Code: {data.confirmation_code}</p>
        </div>
      </div>
    </>
  );
};

export const getStaticPaths = async () => {
  return {
    paths: [],
    fallback: "blocking",
  };
};

export const getStaticProps = async (context) => {
  const { confirmation_code } = context.params;

  try {
    if (!confirmation_code) {
      return {
        notFound: true,
      };
    } else {
      const path = `Logs/fbDeletionLogs.json`;
      const res = await getFile(path);
      console.log(res);
      if (res.file_data) {
        const json_data = JSON.parse(res.file_data);

        const obj = json_data?.find(
          (a) => a.confirmation_code === confirmation_code
        );

        console.log(obj);

        if (json_data?.length && obj) {
          console.log("here");
          return {
            props: {
              data: obj,
            },
          };
        }
      } else {
        return {
          props: {
            data: null,
          },
        };
      }
    }
  } catch (error) {
    return {
      props: {
        data: null,
      },
    };
    console.log(error);
  }
};

export default FBDeletionPage;
