import { NextPage } from "next";
import React, { useEffect, useState } from "react";
import useAuthentication from "common/hooks/useAuthentication";
import useUserStore from "store/user/user.store";
import { ReactFlowProvider } from "reactflow";
import LoadingScreen from "common/components/loadingScreen";
import {
  <PERSON><PERSON>,
  SideBar,
  FlowCanvas,
  VersionsHistory,
} from "common/components/composer";
import DocHead from "common/components/docHead";
import "reactflow/dist/style.css";
import { ConfirmModal } from "common/components/modals/confirm.modal";
import useUserPrivilegeStore from "store/priviliges/UserPrivilege.store";
import { getTriggers } from "apis/dialog.api";
import { getIsStagingTrue } from "apis/dialogversioncontrol.api";
import { Toaster } from "react-hot-toast";
import TestSidebar from "common/components/composer/testSidebar";
import ClientOnly from "common/components/ClientOnly";
import Empty from "common/components/empty";
import Link from "next/link";

interface Props {
  dialog_id: number;
}

const DialogComposerPage: NextPage<Props> = ({ dialog_id }) => {
  const [DVCToShow, setDVCToShow] = useState(null);
  const [loading, setLoading] = useState(false);
  const { token } = useUserStore((state) => state.user);
  const [dialog, setDialog] = React.useState(null);
  const [dialogTriggers, setDialogTriggers] = React.useState(null);
  const [showTest, setShowTest] = React.useState(false);
  const [previewVersions, setPreviewVersions] = useState(false);
  const [live, setLive] = useState();
  const [staging, setStaging] = useState();
  const [refresh, setRefresh] = useState(false);
  const user_id = useUserStore((state) => state.user).user_id;
  const { user } = useUserStore();
  const { priviliges, isAdmin } = useUserPrivilegeStore();

  const getStagingDialog = () => {
    setLoading(true);
    if (!user_id) return;
    getIsStagingTrue(dialog_id).then((res) => {
      setLoading(false);
      if (res && !res.message) {
        if (
          res.user_id === user_id ||
          user_id === 17 ||
          (!isAdmin && priviliges.dialog_privilege)
        ) {
          console.log("inside das");

          setDialog(res);
          setDVCToShow(res);
        } else {
          setDialog(null);
        }
      } else {
        setDialog(null);
      }
    });
    getTriggers(dialog_id).then((res) => {
      if (res && !res.message) {
        setDialogTriggers(res);
      } else {
        setDialogTriggers(null);
      }
    });
    return dialog_id;
  };

  useEffect(() => {
    if (!previewVersions) getStagingDialog();
  }, [dialog_id, previewVersions, user_id]);

  useEffect(() => {
    console.log(dialog);
  }, [dialog]);

  useAuthentication(token, true);

  return (
    <React.Fragment>
      <ReactFlowProvider>
        <DocHead
          title="Searchat | Chatbot Builder"
          keywords="Chatbot Builder"
          description="Chatbot Builder"
        />
        {loading ? (
          <LoadingScreen />
        ) : !dialog ? (
          <div className="w-full h-screen bg-black flex flex-col items-center justify-center">
            <div className="text-center">
              <Empty text="404" />
              <div className="text-white mb-3">Dialog Not Found.</div>
              <Link href="/" className="text-primary underline cursor-pointer">
                Go Back to Home
              </Link>
            </div>
          </div>
        ) : (
          <ClientOnly>
            <Toaster
              toastOptions={{
                className: "",
                style: {
                  borderRadius: "10px",
                  background: "#333",
                  color: "#fff",
                },
              }}
            />
            <div className="w-full h-max-screen h-min-screen h-screen relative bg-black ">
              <Header
                showTest={showTest}
                setShowTest={setShowTest}
                dialog={dialog}
                setDialog={setDialog}
                dialogTriggers={dialogTriggers}
                setDialogTriggers={setDialogTriggers}
              />
              <div className="grid grid-cols-12 h-[calc(100%-50px)] max-h-[calc(100%-50px)]">
                <div className="lg:col-span-2 col-span-3">
                  {previewVersions ? (
                    <VersionsHistory
                      dialog={dialog}
                      setDialog={setDialog}
                      setDVCToShow={setDVCToShow}
                      setLoading={setLoading}
                      setLive={setLive}
                      setStaging={setStaging}
                      refresh={refresh}
                    />
                  ) : (
                    <SideBar dialogTriggers={dialogTriggers} dialog={dialog} />
                  )}
                </div>
                {showTest && (
                  <div className="col-span-3">
                    <TestSidebar
                      showTest={showTest}
                      setShowTest={setShowTest}
                      bot_id={dialog.bot_id}
                      dialogTriggers={dialogTriggers}
                      setDialogTriggers={setDialogTriggers}
                      DVCToShow={DVCToShow}
                    />
                  </div>
                )}
                <div
                  className={` ${
                    showTest
                      ? "lg:col-span-7 col-span-6 "
                      : "lg:col-span-10 col-span-9"
                  } `}
                >
                  <FlowCanvas
                    dialog={dialog}
                    setDialog={setDialog}
                    dialogTriggers={dialogTriggers}
                    setDialogTriggers={setDialogTriggers}
                    previewVersions={previewVersions}
                    setPreviewVersions={setPreviewVersions}
                    DVCToShow={DVCToShow}
                    setDVCToShow={setDVCToShow}
                    live={live}
                    setLive={setLive}
                    staging={staging}
                    setStaging={setStaging}
                    refresh={refresh}
                    setRefresh={setRefresh}
                    showTest={showTest}
                  />
                </div>
              </div>
            </div>
          </ClientOnly>
        )}
      </ReactFlowProvider>
      <ConfirmModal />
    </React.Fragment>
  );
};

export const getStaticPaths = async () => {
  return {
    paths: [],
    fallback: "blocking",
  };
};

export const getStaticProps = async (context) => {
  return {
    props: { dialog_id: parseInt(context.params.id) },
  };
};

export default DialogComposerPage;
