import useUserStore from "store/user/user.store";
import { LoginUserSchema, UserLoginInfo } from "store/user/user.types";
import Link from "next/link";
import { useEffect, memo, useState } from "react";
import { ShieldClose } from "lucide-react";
import DocHead from "common/components/docHead";
import Typewriter from "typewriter-effect/dist/core";
import useIsLoggedIn from "../../common/hooks/useIsLogedIn";
import { Toaster } from "react-hot-toast";
import checkForErrors from "helpers/forms";

const LoginPage = () => {
  // useIsLoggedIn();
  const { login, message, loading } = useUserStore();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleSubmit = (data, event) => {
    event.preventDefault();
    const isErrors = checkForErrors(
      {
        zodSchema: LoginUserSchema,
        data,
      },
      setErrors
    );

    if (isErrors) return;

    login(data);
  };

  const validateField =
    (field: keyof UserLoginInfo) =>
    (value: unknown): string => {
      const parsedResult = LoginUserSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  useEffect(() => {
    if (!loading) {
      new Typewriter("#typewriter", {
        strings: ["Engages Customers.", "Increases Your Sales."],
        autoStart: true,
        delay: 70,
        loop: true,
      });
    }
  }, [loading]);

  return (
    <>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      {loading ? (
        <div className="w-screen h-screen bg-secondary flex items-center justify-center">
          <img
            className="h-20 animate-bounce"
            src="./assets/backgrounds/logo.png"
          ></img>
        </div>
      ) : (
        <>
          <div
            className="min-h-screen max-h-screen  text-white flex justify-center p-30"
            style={{
              background: "url(./assets/backgrounds/bg-main-dark-1440w.jpg)",
              backdropFilter: "blur(8px)",
            }}
          >
            <div className="max-w-screen-xl  m-0 sm:m-10 shadow-2xl sm:rounded-xl flex justify-center flex-1 p-4 z-100 glass">
              <div className="lg:w-1/2 xl:w-5/12  flex  justify-center items-center">
                <div className="p-16">
                  <div className="width-full flex items-center justify-center mb-8">
                    <img
                      className="h-20 floating"
                      src="./assets/backgrounds/logo.png"
                    ></img>
                  </div>
                  <h1 className="text-4xl font-bold leading-tight tracking-tight text-white md:text-4xl text-center mb-4">
                    Hi, Welcome Back !
                  </h1>
                  <p className="text-center text-gray-200 mb-4">
                    Improve your customer engagement, reduce costs, and increase
                    efficiency.
                  </p>
                  <form
                    onSubmit={(e) => handleSubmit(formData, e)}
                    className="space-y-4 md:space-y-6"
                  >
                    <div className="relative">
                      <label
                        htmlFor="email"
                        className="block mb-2 text-sm font-medium text-white "
                      >
                        Email
                      </label>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        className="bg-gray-50 border text-black border-gray-300 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-100  dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        placeholder="<EMAIL>"
                        onChange={(event) =>
                          onChangeHandler("email", event.target.value)
                        }
                      />
                      {errors.email && (
                        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                          <ShieldClose size={12} />
                          {errors.email}
                        </span>
                      )}
                    </div>
                    <div className="relative">
                      <label
                        htmlFor="password"
                        className="block mb-2 text-sm font-medium text-white"
                      >
                        Password
                      </label>
                      <input
                        type="password"
                        name="password"
                        id="password"
                        placeholder="••••••••"
                        className="bg-gray-50 border border-gray-300 text-black sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-100  dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        onChange={(event) =>
                          onChangeHandler("password", event.target.value)
                        }
                      />
                      {errors.password && (
                        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                          <ShieldClose size={12} />
                          {errors.password}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-end">
                      <Link
                        href="/forgot-password"
                        className="text-sm font-medium text-primary hover:underline dark:text-primary"
                      >
                        Forgot password?
                      </Link>
                    </div>

                    <button
                      type="submit"
                      className="w-full text-white bg-primary hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary  dark:focus:ring-primary-800"
                    >
                      Sign in
                    </button>
                    <p className="text-sm font-light text-gray-200 dark:text-gray-200">
                      Don’t have an account yet?{" "}
                      <Link
                        href="/signup"
                        className="font-medium text-primary hover:underline dark:text-primary"
                      >
                        Sign up
                      </Link>
                    </p>
                  </form>
                </div>
              </div>
              <div
                className="flex-1  shadow-lg border-1 text-center hidden lg:flex rounded-lg "
                style={{
                  background:
                    "url(./assets/backgrounds/bg-subtle-2-dark-1440w.jpg)",
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "cover",
                }}
              >
                <div className="max-w-full max-h-full bg-contain bg-center bg-no-repeat ">
                  <div className="grid grid-rows-6 grid-flow-col max-h-full min-h-full">
                    <section className="row-span-6 h-full flex items-center justify-center">
                      <div className="py-8 px-4 mx-auto max-w-screen-xl text-center lg:py-16 lg:px-12">
                        <h1 className="mb-4 text-4xl font-extrabold tracking-tight leading-none text-white md:text-4xl lg:text-5xl ">
                          Revolutionize Your Business with AI-Powered Chatbots
                          That &nbsp;
                          <span
                            id="typewriter"
                            className="text-primary "
                          ></span>
                        </h1>
                      </div>
                    </section>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default memo(LoginPage);
