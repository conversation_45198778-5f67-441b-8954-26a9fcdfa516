import useUserStore from "store/user/user.store";
import {
  RefinedRegisterUserSchema,
  RegisterUserSchema,
  UserRegisterInfo,
} from "store/user/user.types";
import Link from "next/link";
import { memo, useEffect, useState } from "react";
import Typewriter from "typewriter-effect/dist/core";
import { ShieldClose } from "lucide-react";
import { Toaster } from "react-hot-toast";
import DocHead from "common/components/docHead";
import checkForErrors from "helpers/forms";

const SignupPage = () => {
  const { signup, message, loading, user } = useUserStore();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    user_name: "",
    company_name: "",
    email: "",
    password: "",
    confirm_password: "",
  });

  const handleSubmit = (data, event) => {
    event.preventDefault();
    const isErrors = checkForErrors({
      zodSchema: RefinedRegisterUserSchema,
       data,
    }, setErrors);

    if (isErrors) return;

    signup(data);
  };

  const validateField =
    (field: keyof UserRegisterInfo) =>
    (value: unknown): string => {
      const parsedResult = RegisterUserSchema.pick({ [field]: true } as any).safeParse(
        {
          [field]: value,
        }
      );
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  useEffect(() => {
    if (!loading) {
      new Typewriter("#typewriter", {
        strings: ["Engages Customers.", "Increases Your Sales."],
        autoStart: true,
        delay: 70,
        loop: true,
      });
    }
  }, []);

  return (
    <>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      {loading ? (
        <div className="w-screen h-screen bg-secondary flex items-center justify-center">
          <img
            className="h-20 animate-bounce"
            src="./assets/backgrounds/logo.png"
          ></img>
        </div>
      ) : (
        <>
          <div
            className="min-h-screen max-h-screen  text-white flex justify-center p-30"
            style={{
              background: "url(./assets/backgrounds/bg-main-dark-1440w.jpg)",
              backdropFilter: "blur(8px)",
            }}
          >
            <div className="max-w-screen-xl  m-0 sm:m-10 shadow-2xl sm:rounded-xl flex justify-center flex-1 p-4 z-100 glass">
              <div className="lg:w-1/2 xl:w-5/12  flex  justify-center items-center">
                <div className="p-16">
                  <div className="width-full flex items-center justify-center mb-8">
                    <img
                      className="h-20 floating"
                      src="./assets/backgrounds/logo.png"
                    ></img>
                  </div>
                  <h1 className="text-4xl font-bold leading-tight tracking-tight text-white md:text-4xl text-center mb-4">
                    Welcome to Searchat !
                  </h1>
                  <p className="text-center text-gray-200 mb-4">
                    Improve your customer engagement, reduce costs, and increase
                    efficiency.
                  </p>
                  <form
                    className="space-y-4 md:space-y-6"
                    onSubmit={(e) => handleSubmit(formData, e)}
                  >
                    <div className="flex justify-between items-center gap-1">
                      <div className="relative">
                        <label htmlFor="user_name" className="sr-only">
                          Name
                        </label>
                        <input
                          type="user_name"
                          name="user_name"
                          id="user_name"
                          className={`w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:outline-none  `}
                          placeholder="Name"
                          onChange={(event) =>
                            onChangeHandler("user_name", event.target.value)
                          }
                        />
                        {errors.user_name && (
                          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                            <ShieldClose size={12} /> {errors.user_name}
                          </span>
                        )}
                      </div>
                      <div className="relative">
                        <label htmlFor="company" className="sr-only">
                          Company
                        </label>
                        <input
                          type="company"
                          name="company_name"
                          id="company"
                          className="w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:outline-none"
                          placeholder="Company Name"
                          onChange={(event) =>
                            onChangeHandler(
                              event.target.name,
                              event.target.value
                            )
                          }
                        />
                        {errors.company_name && (
                          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                            <ShieldClose size={12} /> {errors.company_name}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="relative">
                      <label htmlFor="email" className="sr-only">
                        Email
                      </label>
                      <input
                        type="text"
                        name="email"
                        id="email"
                        className="w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:ring-0 focus:outline-none"
                        placeholder="Email"
                        onChange={(event) =>
                          onChangeHandler(event.target.name, event.target.value)
                        }
                      />
                      {errors.email && (
                        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                          <ShieldClose size={12} />
                          {errors.email}
                        </span>
                      )}
                    </div>
                    <div className="relative">
                      <label htmlFor="password" className="sr-only">
                        Password
                      </label>
                      <input
                        type="password"
                        name="password"
                        id="password"
                        placeholder="Enter Password"
                        className="w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:ring-0 focus:outline-none"
                        onChange={(event) =>
                          onChangeHandler(event.target.name, event.target.value)
                        }
                      />
                      {errors.password && (
                        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                          <ShieldClose size={12} />
                          {errors.password}
                        </span>
                      )}
                    </div>
                    <div className="relative">
                      <label htmlFor="password" className="sr-only">
                        Confirm Password
                      </label>
                      <input
                        type="password"
                        name="confirm_password"
                        id="password_confirmation"
                        placeholder="Confirm Password"
                        className="w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:ring-0 focus:outline-none"
                        onChange={(event) =>
                          onChangeHandler(event.target.name, event.target.value)
                        }
                      />
                      {errors.confirm_password && (
                        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                          <ShieldClose size={12} />
                          {errors.confirm_password}
                        </span>
                      )}
                    </div>
                    <button
                      type="submit"
                      className={`w-full text-white bg-primary hover:bg-primary/70 focus:outline-none focus:ring-1 focus:ring-violet-400 focus:bg-primary/60 font-medium rounded-lg text-sm px-5 py-2.5 text-center  disabled:grayscale disabled:cursor-not-allowed  transition duration-150 ease-in-out `}
                    >
                      Sign up
                    </button>
                    <p className="text-sm font-light text-gray-200 dark:text-gray-200">
                      Already have an account?{" "}
                      <Link
                        href="/login"
                        className="font-medium text-primary hover:underline dark:text-primary"
                      >
                        Login
                      </Link>
                    </p>
                  </form>
                </div>
              </div>
              <div
                className="flex-1  shadow-lg border-1 text-center hidden lg:flex rounded-lg "
                style={{
                  background:
                    "url(./assets/backgrounds/bg-subtle-2-dark-1440w.jpg)",
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "cover",
                }}
              >
                <div className="max-w-full max-h-full bg-contain bg-center bg-no-repeat ">
                  <div className="grid grid-rows-6 grid-flow-col max-h-full min-h-full">
                    <section className="row-span-6 h-full flex items-center justify-center">
                      <div className="py-8 px-4 mx-auto max-w-screen-xl text-center lg:py-16 lg:px-12">
                        <h1 className="mb-4 text-4xl font-extrabold tracking-tight leading-none text-white md:text-4xl lg:text-5xl ">
                          Revolutionize Your Business with AI-Powered Chatbots
                          That &nbsp;
                          <span
                            id="typewriter"
                            className="text-primary "
                          ></span>
                        </h1>
                      </div>
                    </section>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default memo(SignupPage);
