import Link from "next/link";
import React, { memo, useEffect, useState } from "react";
import { ShieldClose } from "lucide-react";
import useUserStore from "store/user/user.store";
import {
  RefinedResetPasswordSchema,
  ResetPasswordInfo,
  ResetPasswordSchema,
} from "store/user/user.types";
import { Toaster, toast } from "react-hot-toast";
import useStoreQuery from "common/hooks/useStoreQuery";
import DocHead from "common/components/docHead";
import useIsLoggedIn from "../../common/hooks/useIsLogedIn";
import { verifyToken } from "apis/user.api";
import { Button } from "common/ui/button";
import checkForErrors from "helpers/forms";

const ResetPasswordPage = () => {
  useIsLoggedIn();
  const { reset_password, loading:ResetLoading } = useUserStore();
  var user_id = useStoreQuery("user_id");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    user_id: user_id,
    password: "",
    confirm_password: "",
  });
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // get token from url params
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");
    const user_id = urlParams.get("user_id");

    if (!token || !user_id) {
      toast.error("Wrong route");
      return;
    }

    verifyToken({ token: token, user_id: user_id }).then((res) => {
      if (res?.access) {
        setLoading(false);
      } else {
        // setLoading(false);
        toast.error("Invalid Token");
      }
    });
  }, []);

  useEffect(() => {
    setFormData({ ...formData, user_id: user_id });
  }, [user_id]);

  const handleSubmit = (data, event) => {
    event.preventDefault();
    const isErrors = checkForErrors({
      zodSchema: RefinedResetPasswordSchema,
       data,
    }, setErrors);

    if (isErrors) return;

    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");
    reset_password({ ...data, token }).then((res) => {
      urlParams.delete("token");
      urlParams.delete("user_id");
    });
  };

  const validateField =
    (field: keyof ResetPasswordInfo) =>
    (value: unknown): string => {
      const parsedResult = ResetPasswordSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  return loading ? (
    <div className="min-h-screen max-h-screen bg-accent text-white flex flex-col justify-center items-center p-30">
      The link is invalid or broken!
      <Button
        onClick={() => {
          window.location.href = "/";
        }}
      >
        Go to Home
      </Button>
    </div>
  ) : (
    <>
      <DocHead
        title="Searchat | Chatbot Builder"
        keywords="Chatbot Builder"
        description="Chatbot Builder"
      />
      <Toaster
        toastOptions={{
          className: "",
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        }}
      />
      <div
        className="min-h-screen max-h-screen  text-white flex justify-center p-30"
        style={{
          background: "url(./assets/backgrounds/bg-main-dark-1440w.jpg)",
          backdropFilter: "blur(8px)",
        }}
      >
        <div className="max-w-screen-sm  m-0 sm:m-10 shadow-2xl sm:rounded-xl flex justify-center flex-1 p-4 z-100 glass">
          <div className=" flex  justify-center items-center">
            <div className="p-16">
              <div className="width-full flex items-center justify-center mb-8">
                <img
                  className="h-20 floating"
                  src="./assets/backgrounds/logo.png"
                ></img>
              </div>
              <h1 className="text-4xl font-bold leading-tight tracking-tight text-white md:text-4xl text-center mb-4">
                Reset password
              </h1>
              <p className="text-center text-gray-200 mb-4 capitalize">
                Please create a new password for your account
              </p>
              <form
                className="space-y-4 md:space-y-6 mt-20"
                onSubmit={(e) => handleSubmit(formData, e)}
              >
                <div className="relative">
                  <label htmlFor="password" className="sr-only">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    id="password"
                    placeholder="New Password"
                    className="w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:ring-0 focus:outline-none"
                    onChange={(event) =>
                      onChangeHandler(event.target.name, event.target.value)
                    }
                  />
                  {errors.password && (
                    <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                      <ShieldClose size={12} />
                      {errors.password}
                    </span>
                  )}
                </div>
                <div className="relative">
                  <label htmlFor="password" className="sr-only">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    name="confirm_password"
                    id="password_confirmation"
                    placeholder="Confirm Password"
                    className="w-full rounded-lg border-gray-200 p-3 text-sm text-black focus:ring-0 focus:outline-none"
                    onChange={(event) =>
                      onChangeHandler(event.target.name, event.target.value)
                    }
                  />
                  {errors.confirm_password && (
                    <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
                      <ShieldClose size={12} />
                      {errors.confirm_password}
                    </span>
                  )}
                </div>

                <Button
                loading={ResetLoading}
                  type="submit"
                  className="w-full text-white bg-primary hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary  dark:focus:ring-primary-800"
                >
                  Reset Password
                </Button>
                <p className="text-sm text-center font-light text-gray-200 dark:text-gray-200">
                  <Link
                    href="/login"
                    className="font-medium text-primary hover:underline dark:text-primary"
                  >
                    Back to Login!
                  </Link>
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default memo(ResetPasswordPage);
