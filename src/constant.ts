
const constant = {
  // SERVER_URL: "https://9wcjd90m-5003.euw.devtunnels.ms/api",
  // SERVER_URL: "http://localhost:5003/api",
  SERVER_URL: "https://bot-designer-server-2.azurewebsites.net/api",
  SIGNALR_BROKER_URL: "https://i2i-test.azurewebsites.net/api/",
  // SIGNALR_BROKER_URL: "http://localhost:7071/api/",
  i2i_RAG_SERVER:
    "http://i2i-rag.dubmafhwd3ekana6.uaenorth.azurecontainer.io:8080/api/v1/",
  ASSETS_URL:
    "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/",
  BOT_DESIGNER_ASSETS_URL:
    "	https://infotointell.fra1.digitaloceanspaces.com/assets/bot-designer/",
  MOCK_URL: "https://mock-engine.azurewebsites.net",
  SOCKET_SERVER: "https://i2i-messaging.azurewebsites.net",
  MEDIA_STORAGE_URL: "https://infotointell.fra1.digitaloceanspaces.com/",
  GOOGLE_MAP_KEY: "AIzaSyBl8jqw_9sE9qQv0SHmfhGHqnTGtaGA_u4",
  STORAGE_SERVER_URL: "https://cdn.searchat.com/",
  DOC_URL:
    "https://www.manula.com/manuals/infotointell/chatbot-builder/1/en/topic/getting-started",
  LOCAL_SERVER_URL: "http://localhost:5003/api",
  RAG_STORE_URL: "http://**************:8000",

  IS_ENG_REGEX: /^[A-Za-z0-9]+$/,
  IS_AR_REGEX: new RegExp(
    "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufc3f]|[\ufe70-\ufefc]"
  ),
  IS_URL_REGEX:
    /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g,

  // use new account with production instead of sandbox
  CALENDLY_CLIENT_ID: "lmt84IifaMXhXgnIN0Uv6jVdaYRpEXcB722h4iGfqTo",
  CALENDLY_CLIENT_SECRET: "2Onhg5ut3-O3d8OeGXoze3Grudjt6Qv70PigAn6TZ_8",
  CALENDLY_REDIRECT_URI: "http://localhost:3000/?view=bots-view-6",
  CALENDLY_AUTH_URL: "https://auth.calendly.com/oauth",

  TTS_URL: "https://bot-server-2.azurewebsites.net/api/i2i/tts",

  voiceData: [
    {
      value: "ar-KW-Saad",
      category: "GCC",
      gender: "Male",
      country: "Kw",
      language: "ARABIC",
    },
    {
      value: "ar-KW-Hind",
      category: "GCC",
      gender: "Female",
      country: "Kw",
      language: "ARABIC",
    },
    {
      value: "en-GB-James",
      category: "GB",
      gender: "Male",
      country: "ENG-1",
      language: "English",
    },
    {
      value: "en-GB-Emma",
      category: "US",
      gender: "Female",
      country: "US-1",
      language: "English",
    },
  ],

  PARTNER_ID_360_DIALOG: "jsQ01lPA",

  UNITS: [
    { name: "Piece", value: "piece" },
    { name: "Kilogram", value: "kg" },
    { name: "Gram", value: "g" },
    { name: "Meter", value: "m" },
    { name: "Pack", value: "pack" },
    { name: "Bunch", value: "bunch" },
    { name: "Bag", value: "bag" },
  ],

  DAYS: [
    { name: "Saturday", value: "saturday" },
    { name: "Sunday", value: "sunday" },
    { name: "Monday", value: "monday" },
    { name: "Tuesday", value: "tuesday" },
    { name: "Wednesday", value: "wednesday" },
    { name: "Thursday", value: "thursday" },
    { name: "Friday", value: "friday" },
  ],

  DATE_FORMATS: [
    "DD/MM/YYYY",
    // "dd/mm/yyyy",
    "MM/DD/YYYY",
    // "mm/dd/yyyy",
    "YYYY/MM/DD",
    // "yyyy/mm/dd",
    "DD-MM-YYYY",
    // "dd-mm-yyyy",
    "MM-DD-YYYY",
    // "mm-dd-yyyy",
    "YYYY-MM-DD",
    // "yyyy-mm-dd",
    "DD.MM.YYYY",
    // "dd.mm.yyyy",
    "MM.DD.YYYY",
    // "mm.dd.yyyy",
    "YYYY.MM.DD",
    // "yyyy.mm.dd",
  ],

  OPENAI_WHISPER_URL: "https://api.openai.com/v1/audio/transcriptions",

  JWT_SECRET: "1E6ZDh6YDjlzuyJdYEBgmck1GoAlfz6o4WGKWajN3U",
};

export default constant;
