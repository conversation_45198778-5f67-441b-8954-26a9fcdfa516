import React, { createContext, useContext, useRef } from "react";
import { LoadingBarRef } from "react-top-loading-bar";
interface LoaderContextProps {
  loader: LoadingBarRef | null;
  loaderRef?: React.MutableRefObject<LoadingBarRef | null>;
}

const loaderContext = createContext<LoaderContextProps | null>(null);

export const useLoaderContext = () =>
  useContext(loaderContext) as LoaderContextProps;
interface LoaderProviderProps {
  children: React.ReactNode;
}
const LoaderProvider = ({ children }: LoaderProviderProps) => {
  const loaderRef = useRef<LoadingBarRef | null>(null);
  const loader = loaderRef.current;

  return (
    <loaderContext.Provider value={{ loader, loaderRef }}>
      {children}
    </loaderContext.Provider>
  );
};

export default LoaderProvider;
