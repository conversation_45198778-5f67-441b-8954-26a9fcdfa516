import { MainTable } from "common/components/tables/main.table";
import React, { useState } from "react";

import { ArrowLeft, Edit, Trash2 } from "lucide-react";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { useLangStore } from "store/language/lang.store";
import { ITopic } from "store/topic/topic.types";
import useTOPICStore from "store/topic/topic.store";
import { Input } from "common/ui/inputs/input";
// import MultiSelectTextInput from "common/components/MultiSelectInput";
import MultiSelectTextInput, {
  customStyles,
} from "common/components/MultiSelectTextInput";
import useBotStore from "store/bot/bot.store";
import useMultiLanguage from "common/hooks/useMultiLanguage";
import { extractLangInOneLine } from "helpers/multiLang";
import CreatableSelect from "react-select/creatable";
import { isArabic } from "helpers/helper";
const CreateTopicModal = ({
  topic,
  setTopicToCreate,
}: {
  topic: ITopic;
  setTopicToCreate: (topic: ITopic) => void;
}) => {
  const { create_one_topic, update_one_topic, loading } = useTOPICStore();
  const { lang, changeLang } = useLangStore();
  const { result, value, setValue } = useMultiLanguage(topic?.display_name);
  const [inputValue, setInputValue] = useState("");
  if (!topic) return null;

  const handleCreateOrUpdate = async () => {
    console.log(result);

    if (topic?.topic_id) {
      // update
      await update_one_topic({ ...topic, display_name: result });
      setTopicToCreate(null);
    } else {
      // create
      await create_one_topic({ ...topic, display_name: result });
      setTopicToCreate(null);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center">
        <div
          onClick={() => {
            setTopicToCreate(null);
          }}
          className="flex items-center gap-1.5 mb-2 cursor-pointer w-fit"
        >
          <ArrowLeft />
          <p className="text-xl">Back</p>
        </div>
        <Button
          onClick={changeLang}
          className="capitalize "
          size="default"
          variant="outline"
        >
          {lang}
        </Button>
      </div>
      {/* <hr /> */}
      <Input
        title="Display Name"
        type="text"
        name=""
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
        }}
      />
      <div className="grid grid-cols-1 my-1">
        <label htmlFor="label">Keywords</label>
        <CreatableSelect
          id={"Keywords"}
          components={{
            DropdownIndicator: null,
          }}
          inputValue={inputValue}
          isClearable
          isMulti
          menuIsOpen={false}
          onChange={(newValue) => {
            const newKeywords = newValue.map((keyword) => keyword.value);
            topic.keywords?.length !== newKeywords?.length &&
              setTopicToCreate({ ...topic, keywords: newKeywords });
          }}
          onInputChange={(newValue) => setInputValue(newValue)}
          onKeyDown={(event) => {
            switch (event.key) {
              case "Enter":
              case "Tab":
                // handleText(inputValue);
                // const opt = { value: inputValue, label: inputValue };
                const newKeywords = [...topic.keywords, inputValue];
                setTopicToCreate({ ...topic, keywords: newKeywords });
                setInputValue("");
                event.preventDefault();
            }
          }}
          placeholder="Enter a keyword then press enter"
          value={topic.keywords
            .map((keyword) => {
              return {
                label: keyword,
                value: keyword,
              };
            })
            .filter((keyword) =>
              lang === "ar" ? isArabic(keyword.label) : !isArabic(keyword.label)
            )}
          styles={customStyles}
        />
      </div>
      <div className="w-full flex justify-end">
        <Button
          disabled={loading}
          loading={loading}
          onClick={handleCreateOrUpdate}
        >
          {topic?.topic_id ? "Update" : "Create"}
        </Button>
      </div>
    </div>
  );
};

const MainTopicsTable = ({
  show,
  setTopicToCreate,
}: {
  show: boolean;
  setTopicToCreate: (topic: ITopic) => void;
}) => {
  const { bot } = useBotStore();
  const { topics, delete_one_topic } = useTOPICStore();
  const confirmModal = useConfirmModal();
  const faqTableActions = [
    {
      label: "Edit",
      component: ({ item }) => (
        <div>
          <Edit
            onClick={() => {
              console.log(item);

              setTopicToCreate(item);
            }}
            size={15}
            className="hover:text-primary cursor-pointer"
          />
        </div>
      ),
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(async () =>
          delete_one_topic({ topic_id: item.topic_id })
        );
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
  ];
  if (!show) return null;

  return (
    <div>
      <div className="flex w-full justify-between">
        <p>Create new topic to group your Dialogs & FAQs</p>
        <Button
          onClick={() => {
            setTopicToCreate({
              bot_id: bot.bot_id,
              display_name: "__ar__:#### __en__:####",
              keywords: [],
            } as ITopic);
          }}
        >
          Create Topics
        </Button>
      </div>
      <MainTable
        loading={false}
        data={topics.map((topic) => {
          return {
            ...topic,
            display_name_text: extractLangInOneLine(topic.display_name),
            keywords_text:
              topic.keywords.length > 5
                ? topic.keywords.slice(0, 5).join(", ") + " ..."
                : topic.keywords.join(", "),
          };
        })}
        columns={[
          {
            name: "Display Name",
            key: "display_name_text",
          },
          {
            name: "Keywords",
            key: "keywords_text",
          },
        ]}
        itemsPerPage={20}
        actions={faqTableActions}
        checkBoxes
        // onSelect={(e) => {
        //   console.log(e);
        // }}
        // isSelectAll={false}
        // isSelectOne={(i) => false}
        idKey="faq_id"
      />
    </div>
  );
};

const TopicsModal = () => {
  const [topicToCreate, setTopicToCreate] = useState<ITopic | null>(null);

  //   const {} = useTOPICStore();

  return (
    <div>
      <MainTopicsTable
        setTopicToCreate={setTopicToCreate}
        show={!Boolean(topicToCreate)}
      />
      <CreateTopicModal
        topic={topicToCreate}
        setTopicToCreate={setTopicToCreate}
      />
    </div>
  );
};

export default TopicsModal;
