import { getAlters } from "apis/faq.api";
import { getAlternative } from "apis/trigger.api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "common/ui/alertDialog";
import { Button } from "common/ui/button";
import toast from "react-hot-toast";
import { Plus, X } from "lucide-react";
import React from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useFAQStore from "store/faq/faq.store";
import { IFAQ } from "store/faq/faq.types";
import useTriggerStore from "store/trigger/trigger.store";
import { ITrigger } from "store/trigger/trigger.types";
import useUserStore from "store/user/user.store";

interface AlternativesInputsProps {
  alternatives: any;
  setAlternatives: any;
  newAlternatives: any;
  setNewAlternatives: any;
  copiedAlternatives: any;
  setCopiedAlternatives: any;
  setEditedAlternatives: any;
  faq?: IFAQ;
  trigger?: ITrigger;
  afterDelete?: () => void;
  title?: boolean;
}

export const AlternativesInputs: React.FC<AlternativesInputsProps> = ({
  newAlternatives,
  setNewAlternatives,
  copiedAlternatives,
  setCopiedAlternatives,
  setEditedAlternatives,
  alternatives,
  setAlternatives,
  faq,
  trigger,
  afterDelete,
  title = true,
}) => {
  const bot = useBotStore((state) => state.bot) as IBot;
  const user_id = useUserStore((state) => state.user).user_id;
  const { delete_one_faq } = useFAQStore();
  const { delete_one_trigger } = useTriggerStore();

  var textarea = document.getElementById("myInput");
  textarea?.addEventListener("input", function () {
    textarea.style.height = "auto";
    var textHeight = textarea.scrollHeight;
    textarea.style.height = textHeight + "px";
  });

  const getAllAlters = () => {
    getAlters(bot.bot_id, faq.faq_id).then((data) => {
      if (data) {
        setAlternatives([...data]);
      }
    });
  };

  const addFields = () => {
    let newfield = { question: "", is_main: false };

    setNewAlternatives([...newAlternatives, newfield]);
  };

  const removeFields = (i) => {
    let data = [...newAlternatives];
    data.splice(i, 1);
    setNewAlternatives(data);
  };

  const updateFields = (i, value, alt?) => {
    if (alt) {
      const copied_alternatives = [
        ...copiedAlternatives.map((a) => {
          if (a.faq_id === alt.faq_id) {
            return { ...a, question: value.replace(/\s+/g, " ") };
          }
          return a;
        }),
      ];
      setCopiedAlternatives(copied_alternatives);
      if (
        copied_alternatives.every(
          (a, index) => a.question === alternatives[index].question
        )
      ) {
        console.log("no changes made");

        setEditedAlternatives(false);
      } else {
        console.log("changes made");

        setEditedAlternatives(true);
      }
    } else {
      let data = [...newAlternatives];
      data[i] = value.trim().replace(/\s+/g, " ");
      setNewAlternatives(data);
    }
  };

  const handleAltDelete = (id, type: "saved" | "not_saved", alt?) => {
    if (type === "saved") {
      delete_one_faq({ user_id, faq_id: id, bot_id: bot.bot_id }).then(() => {
        setTimeout(() => {
          getAllAlters();
        }, 100);
      });
    } else {
      removeFields(id);
    }
  };

  const getAllTriggerAlters = () => {
    getAlternative(bot.bot_id, trigger.trigger_id, trigger.url).then((data) => {
      console.log("im here");

      if (data) {
        console.log(data);

        // const alts = data.filter((a) => a.trigger_id !== trigger.trigger_id);
        setAlternatives([...data]);
      }
    });
  };

  const addTriggerFields = () => {
    let newfield = { trigger: "" };

    setNewAlternatives([...newAlternatives, newfield]);
  };

  const updateTriggerFields = (i, value, trigger?) => {
    if (trigger) {
      const copied_alternatives = [
        ...copiedAlternatives.map((a) => {
          if (a.trigger_id === trigger.trigger_id) {
            return { ...a, trigger: value.trim().replace(/\s+/g, " ") };
          }
          return a;
        }),
      ];
      setCopiedAlternatives(copied_alternatives);
      if (
        copied_alternatives.every(
          (a, index) => a.trigger === alternatives[index].trigger
        )
      ) {
        console.log("no changes made");

        setEditedAlternatives(false);
      } else {
        console.log("changes made");

        setEditedAlternatives(true);
      }
    } else {
      let data = [...newAlternatives];
      data[i] = value.trim().replace(/\s+/g, " ");
      setNewAlternatives(data);
    }
  };

  const handleTriggerAltDelete = (id, type: "saved" | "not_saved", alt?) => {
    if (type === "saved") {
      delete_one_trigger({ trigger_id: id, user_id: user_id }).then(() => {
        setTimeout(() => {
          getAllTriggerAlters();
          if (afterDelete) {
            afterDelete();
          } else {
            getAllTriggerAlters();
          }
          toast.success("Alternative deleted successfully");
        }, 800);
      });
    } else {
      removeFields(id);
    }
  };
  return (
    <div className="space-y-2">
      <div
        className={`flex ${
          title ? "justify-between" : "justify-end"
        }  items-center`}
      >
        {title && (
          <label htmlFor="">
            Alternatives <small className="text-white/50">(optional)</small>{" "}
          </label>
        )}
        <Button
          onClick={() => {
            faq ? addFields() : addTriggerFields();
          }}
          className={`rounded-full w-5 h-5 p-1 `}
        >
          <Plus />
        </Button>
      </div>
      <div className=" max-h-56 overflow-y-auto p-2 border-2 border-gray-300 border-dashed rounded h-56">
        <div className="space-y-1 py-2">
          {[...newAlternatives, ...copiedAlternatives]?.map((alt, i) => {
            return (
              <div className="flex items-center gap-3" key={i}>
                <textarea
                  id="myInput"
                  // disabled={Boolean(alt.faq_id)}
                  placeholder="Write an alternative..."
                  tabIndex={0}
                  dir="auto"
                  value={faq ? alt.question : alt.trigger}
                  className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 ${
                    (faq ? alt.faq_id : alt.trigger_id) && "!border-primary"
                  }`}
                  rows={1}
                  onChange={(e) => {
                    faq
                      ? updateFields(i, e.target.value, alt.faq_id ? alt : null)
                      : updateTriggerFields(
                          i,
                          e.target.value,
                          alt.trigger_id ? alt : null
                        );
                  }}
                ></textarea>
                {!afterDelete || (afterDelete && i > 0) ? (
                  (
                    faq
                      ? Boolean(alt.question !== "")
                      : Boolean(alt.trigger !== "")
                  ) ? (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <X
                          size={15}
                          className=" bg-red-500 rounded-full cursor-pointer"
                        />
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Are you sure you want to delete this?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              faq
                                ? handleAltDelete(
                                    alt.faq_id ? alt.faq_id : i,
                                    alt.faq_id ? "saved" : "not_saved",
                                    alt.faq_id ? null : alt
                                  )
                                : handleTriggerAltDelete(
                                    alt.trigger_id ? alt.trigger_id : i,
                                    alt.trigger_id ? "saved" : "not_saved",
                                    alt.trigger_id ? null : alt
                                  );
                            }}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    <X
                      onClick={() => removeFields(i)}
                      size={15}
                      className=" bg-red-500 rounded-full cursor-pointer"
                    />
                  )
                ) : null}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
