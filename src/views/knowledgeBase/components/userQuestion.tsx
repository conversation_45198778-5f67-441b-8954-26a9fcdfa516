import { createTips } from "apis/faq.api";
import { Textarea } from "common/ui/inputs/textarea";
import { ShieldClose } from "lucide-react";
import { useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { IFAQ } from "store/faq/faq.types";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";
import { ITrigger } from "store/trigger/trigger.types";

interface UserQuestionProps {
  trigger?: ITrigger;
  faq?: IFAQ;
  smallTalk?: ISmallTalk;
  onChange: (e) => void;
  value: string;
  error: string;
}

export const UserQuestion: React.FC<UserQuestionProps> = ({
  trigger,
  faq,
  smallTalk,
  onChange,
  value,
  error,
}) => {
  const [duplicates, setDuplicates] = useState([]);
  const [checkIcon, setCheckIcon] = useState(false);

  const bot = useBotStore((state) => state.bot) as IBot;

  const getTips = () => {
    if (value.trim() !== "") {
      var toExclude = {
        faq_id: faq?.faq_id,
        trigger_id: trigger?.trigger_id,
      };
      createTips({
        bot_id: bot.bot_id,
        question: value,
        ...toExclude,
      }).then((data) => {
        const duplicates = [];
        data?.questionTagNER?.forEach((tag) => {
          if (tag.length > 0) {
            tag.forEach((a) => {
              duplicates.push({
                ...a,
              });
            });
          }
        });
        setDuplicates(duplicates);
        setCheckIcon(!duplicates.length ? true : false);
      });
    }
  };
  return (
    <div className="space-y-1 relative">
      <label htmlFor="question" className="text-right">
        User Question *
        {Boolean(duplicates?.length) ? (
          <small className="text-yellow-700"> &bull; Duplicate Warning </small>
        ) : Boolean(checkIcon) ? (
          <small className="text-green-700"> &#x2713; No Conflicts</small>
        ) : (
          ""
        )}
      </label>
      <Textarea value={value} onChange={onChange} onBlur={getTips}></Textarea>
      {error ? (
        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
          <ShieldClose size={12} />
          {error}
        </span>
      ) : null}
    </div>
  );
};
