import { But<PERSON> } from "common/ui/button";
import { Textarea } from "common/ui/inputs/textarea";
import { isArabic } from "helpers/helper";
import { ShieldClose, Sparkles } from "lucide-react";

interface BotVoiceProps {
  onChange: (e) => void;
  value: string;
  title?: string;
  loading?: boolean;
  handleTashkeel?: () => void;
}

export const BotVoice: React.FC<BotVoiceProps> = ({
  onChange,
  value,
  title,
  loading,
  handleTashkeel,
}) => {
  return (
    <div className="space-y-1 relative w-full">
        <div className="flex items-center justify-between gap-1 w-full">
      <label htmlFor="question" className="text-left w-3/4">
        {title ? (
          <>
            <span>{title}</span>
            <span className="text-sm text-primary text-auto-wrap text-left ">{` (only affects pronunciation, not the bot's answer.) `}</span>
          </>
        ) : (
          "Bot Answer *"
        )}
      </label>

      <div className="w-1/4 flex items-center justify-end">
      {isArabic(value) ? (
        <div className="">
          <Button onClick={handleTashkeel} className="">
            <Sparkles
              color="white"
              className={`hover:scale-105 cursor-pointer  ${
                loading ? "animate-spin" : ""
              }`}
            />
            <p>Tashkeel</p>
          </Button>
        </div>
      ) : null}
      </div>
      </div>
      <Textarea
        className="h-32"
        dir={isArabic(value) ? "rtl" : "ltr"}
        value={value}
        onChange={onChange}
      ></Textarea>
    </div>
  );
};
