import { But<PERSON> } from "common/ui/button";
import { FlaskConical, X } from "lucide-react";
import { FC, memo } from "react";
import useBotStore from "store/bot/bot.store";

interface TestingSheetProps{
  setShowTest: (showTest: boolean) => void;
}

const TestingSheet: FC<TestingSheetProps> = ({ setShowTest }) => {
  const bot_id = useBotStore((state) => state.bot.bot_id);
  return (
    <div className=" h-full w-[350px] border-r-2 border-white/25 -ml-[63px] fixed top-0">
      <div className="text-white flex flex-col gap-2 border-b border-dark h-[50px]">
        <div className="p-2 flex justify-between items-center">
          <h1 className="flex gap-2">
            <FlaskConical className="text-primary" />
            Test Your Bot
          </h1>

          <Button variant="ghost" className="p-0">
            <X onClick={() => setShowTest(false)} />
          </Button>
        </div>
      </div>
      <div className="h-[calc(100%-50px)]">
        <iframe
          className="h-full w-full"
          src={`https://chatbot-incubator-refactor-g668y.ondigitalocean.app/?bot_id=${bot_id}&theme=dark`}
          allow="clipboard-read; clipboard-write;"
          id="searchat-chatbot-iframe-test"
        ></iframe>
      </div>
    </div>
  );
};

export default memo(TestingSheet);
