import { But<PERSON> } from "common/ui/button";
import { Plus, X } from "lucide-react";
import { useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "common/ui/alertDialog";
import { ITrigger } from "store/trigger/trigger.types";
import { getFile } from "apis/file.api";
import useTriggerStore from "store/trigger/trigger.store";
import useFAQStore from "store/faq/faq.store";

interface SuggestionButtonsProps {
  copiedSuggestions: string[];
  setCopiedSuggestions: (suggestions: string[]) => void;
  setSuggestedActions?: (suggestions: string[]) => void;
  trigger?: ITrigger;
  suggestedActions: string[];
  error: string;
  title?: string;
  required?: boolean;
}

export const SuggestionButtons: React.FC<SuggestionButtonsProps> = ({
  copiedSuggestions,
  setCopiedSuggestions,
  setSuggestedActions,
  suggestedActions,
  error,
  title,
  required,
  trigger,
}) => {
  const triggers = useTriggerStore((state) => state.triggers);
  const faqs = useFAQStore((state) => state.FAQs);

  useEffect(() => {
    if (trigger) {
      getFile(trigger.url).then((data) => {
        if (data) {
          const config = { ...JSON.parse(data.file_data) };
          setSuggestedActions([...config.actions]);
        }
      });
    }
  }, [trigger]);

  useEffect(() => {
    setCopiedSuggestions([...suggestedActions]);
  }, [suggestedActions]);

  const addSuggestionFields = () => {
    // check for 3 suggestions only
    setCopiedSuggestions([...copiedSuggestions, ""]);
  };

  const removeSuggestionFields = (i) => {
    let data = [...copiedSuggestions];
    data.splice(i, 1);
    setCopiedSuggestions(data);
  };

  const updateSuggestionFields = (index, value) => {
    const newSuggestedActions = [...copiedSuggestions];
    newSuggestedActions[index] = value;
    setCopiedSuggestions([...newSuggestedActions]);
  };

  // const chekActionsAttachedToSuggesstions = (action) =>
  //   triggers.find((a) => a.trigger === action)
  //     ? triggers.find((a) => a.trigger === action)
  //     : faqs.find((a) => a.question === action)
  //     ? faqs.find((a) => a.question === action)
  //     : {};

  return (
    <div className="space-y-5 h-full">
      <div className="space-y-2 py-2">
        <div className="flex justify-between items-center">
          <label htmlFor="">
            {title} {required ? "*" : ""}
            {error ? (
              <small className="text-red-500">-{error}</small>
            ) : null}{" "}
          </label>
          <Button
            // disabled={addMore}
            onClick={addSuggestionFields}
            className="rounded-full w-5 h-5 p-1"
          >
            <Plus />
          </Button>
        </div>
        <div className="max-h-96 h-96  overflow-y-auto p-2 border-2 border-gray-300 border-dashed rounded">
          <div className="space-y-1 py-2">
            {copiedSuggestions?.map((sugg, i) => {
              return (
                <div className="flex items-center gap-3" key={i}>
                  <textarea
                    // id="myInput"
                    maxLength={25}
                    placeholder="Write a suggestion button..."
                    tabIndex={0}
                    dir="auto"
                    value={sugg}
                    className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 `}
                    rows={1}
                    onChange={(e) => {
                      updateSuggestionFields(i, e.target.value);
                    }}
                  ></textarea>
                  {Boolean(sugg !== "") ? (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <X
                          size={15}
                          className=" bg-red-500 rounded-full cursor-pointer"
                        />
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Are you sure you want to delete this?
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => {
                              removeSuggestionFields(i);
                            }}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    <X
                      onClick={() => removeSuggestionFields(i)}
                      size={15}
                      className=" bg-red-500 rounded-full cursor-pointer"
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
