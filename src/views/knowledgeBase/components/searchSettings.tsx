import { FullModal } from "common/components/modals";
import GPTSVG from "common/icons/GPTSVG";
import { Button } from "common/ui/button";
import { Card } from "common/ui/card";
import { Radio } from "common/ui/inputs/radio";
import { useLoaderContext } from "context/loaderContext";
import { Loader2, RefreshCwIcon } from "lucide-react";

import React, { useMemo, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useSearchSettingsStore from "store/search/search.store";

export const SearchSettings = ({
  settings,
  setSettings,
  showModal,
  setShowModal,
}) => {
  const {loader} = useLoaderContext();
  const bot = useBotStore((state) => state.bot);
  const {
    get_one_searchSettings,
    searchSettings,
    update_one_searchSettings,
    create_one_searchSettings,
    loading
  } = useSearchSettingsStore();

  const handleSubmit = () => {
    loader?.continuousStart();
    if (searchSettings.search_setting_id) {
      update_one_searchSettings({ ...settings, bot_id: bot.bot_id }).then(
        () => {
          get_one_searchSettings(bot.bot_id);
        }
      );
    } else {
      create_one_searchSettings({ ...settings, bot_id: bot.bot_id }).then(
        () => {
          get_one_searchSettings(bot.bot_id);
        }
      );
    }
    loader?.complete()
  };

  const handleReset = () => {
    if (!isChanged) return;

    setSettings(searchSettings);
  };

  const isChanged = useMemo(() => {
    if (!searchSettings) return true;

    if (settings.engine !== searchSettings.engine) return true;
    if (settings.kb_type !== searchSettings.kb_type) return true;

    return false;
  }, [settings, searchSettings]);

  return (
    <FullModal
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      title="Search Settings"
    >
      <div className="flex flex-col gap-5">
        <div className="text-sm">
          You enabled GPT plugin, You can still use Searchat engine or use GPT
          for search
        </div>
        <div className="flex flex-col gap-4">
          <div>
            <input
              type="radio"
              id="searchat"
              name="engine"
              value="searchat"
              checked={settings.engine === "searchat"}
              onChange={() => {
                setSettings({ kb_type: "structured", engine: "searchat" });
              }}
              className="peer hidden [&:checked_+_label_svg]:block"
            />

            <label
              htmlFor="searchat"
              className="flex w-full h-24 cursor-pointer items-center  gap-2 rounded-md border border-l-8 border-white/25 py-1 px-3 text-white hover:border-primary hover:backdrop-brightness-200 peer-checked:border-primary  peer-checked:text-white"
            >
              <img className="h-10" src="./assets/backgrounds/logo.png"></img>
              <div>
                <p className="text-md font-medium">Searchat Engine</p>
                <p className="text-sm">
                  Use Searchat engine to look for your customers answers.
                </p>
              </div>
            </label>
          </div>
          <div>
            <input
              type="radio"
              id="gpt"
              name="engine"
              value="gpt"
              checked={settings.engine === "gpt"}
              onChange={() => {
                setSettings({ ...settings, engine: "gpt" });
              }}
              className="peer hidden [&:checked_+_label_svg]:block"
            />

            <label
              htmlFor="gpt"
              className="flex w-full h-24 cursor-pointer items-center  gap-2 rounded-md border border-l-8 border-white/25 py-1 px-3 text-white hover:border-primary hover:backdrop-brightness-200 peer-checked:border-primary  peer-checked:text-white"
            >
              <div className="px-1">
                <GPTSVG width="30px" />
              </div>
              <div>
                <p className="text-md font-medium">GPT Engine</p>
                <p className="text-sm">
                  Use GPT engine to look for your customers answers.
                </p>
              </div>
            </label>
          </div>
        </div>
        {settings.engine === "gpt" && (
          <div className="mt-4 flex gap-5">
            <div>Knowledege Base Type</div>
            <Radio
              name="kb_type"
              label="Structured KB"
              value="structured"
              checked={settings.kb_type === "structured"}
              onChange={() => {
                setSettings({ ...settings, kb_type: "structured" });
              }}
            />
            <Radio
              name="kb_type"
              label="Unstructured KB"
              value="unstructured"
              checked={settings.kb_type === "unstructured"}
              onChange={() => {
                setSettings({ ...settings, kb_type: "unstructured" });
              }}
            />
          </div>
        )}
        <hr className="text-white/25" />
        <div className="self-end flex gap-2">
          {searchSettings?.search_setting_id ? (
            <Button
              onClick={handleReset}
              disabled={!isChanged}
              variant="outline"
              className="flex gap-1"
            >
              <RefreshCwIcon size={15} /> Reset
            </Button>
          ) : null}
          <Button onClick={handleSubmit} loading={loading} disabled={!isChanged}>
            { !isChanged ? (
              "All Saved"
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </div>
    </FullModal>
  );
};
