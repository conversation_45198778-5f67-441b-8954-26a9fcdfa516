import { FullModal } from "common/components/modals";
import { <PERSON><PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Radio } from "common/ui/inputs/radio";
import {
  ChevronDown,
  Edit,
  ImageIcon,
  Link,
  PictureInPicture2,
  Plus,
  Upload,
  Video,
  X,
} from "lucide-react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { z } from "zod";
import helper from "../helper";
import { ITrigger } from "store/trigger/trigger.types";
import { getFile } from "apis/file.api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "common/ui/alertDialog";
import { Tooltip } from "common/ui/tooltip";
import { MediaInput } from "common/ui/inputs/mediaInput";
import { handleUploadImg, handleUploadVid } from "helpers/media";
import checkForErrors from "helpers/forms";
import { useLangStore } from "store/language/lang.store";
import { setValuesBasedOnLanguage } from "helpers/multiLang";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";
import { Textarea } from "common/ui/inputs/textarea";

interface CardEditorProps {
  updateCards: (cards: any) => void;
  uploadImageHandler: (data: any) => void;
  trigger?: ITrigger | string;
  isUpdate?: boolean;
  error?: string;
  title?: string;
  setIsCardChanged?: (value: boolean) => void;
  isDialog?: boolean;
  cardBlock?: any;
  handleSave?: ()=>void;
  entities?:any; 
  globals?:any;
}

export const CardEditor: React.FC<CardEditorProps> = ({
  updateCards,
  uploadImageHandler,
  error,
  trigger,
  isUpdate,
  title,
  setIsCardChanged,
  isDialog = false,
  cardBlock,
  handleSave,
  entities,
  globals
}) => {
  console.log("cardBlock", cardBlock);
  const { lang } = useLangStore();
  const bot = useBotStore((state) => state.bot) as IBot;
  const [showButtonModal, setShowButtonModal] = useState(false);
  const [originalCards, setOriginalCards] = useState([]);
  const [cards, setCards] = useState([]);
  const [actionToAdd, setActionToAdd] = useState(
    helper.initCardButtonData(isDialog)
  );
  const [buttonErrors, setButtonErrors] = useState<Record<string, string>>({});

  const [byLink, setByLink] = useState([]);

  const [cardToAddButtonsIndex, setCardToAddButtonsIndex] = useState(null);

  var textarea = document.getElementById("cardheading");
  textarea?.addEventListener("input", function () {
    textarea.style.height = "auto";
    var textHeight = textarea.scrollHeight;
    textarea.style.height = textHeight + "px";
  });

  var textarea2 = document.getElementById("cardsubheading");
  textarea2?.addEventListener("input", function () {
    textarea2.style.height = "auto";
    var textHeight = textarea2.scrollHeight;
    textarea2.style.height = textHeight + "px";
  });

  // SECTION === Card Editor
  useEffect(() => {
    console.log("trigger", isUpdate, isDialog, trigger);

    if (isUpdate) {
      if (trigger) {
        console.log("heere");

        const path = typeof trigger === "object" ? trigger.url : trigger;
        getFile(path).then((data) => {
          if (data) {
            console.log(data);

            const json_data = JSON.parse(data.file_data);
            const fetchedcards = json_data.attachments.map((a, index) => {
              return {
                body: [...a.body],
                actions: [...a.actions],
                order: index + 1,
              };
            });
            const fetchedCardstoUpdate = JSON.parse(
              JSON.stringify(fetchedcards)
            );
            setOriginalCards([...fetchedCardstoUpdate]);
          }
        });
      } else if (isDialog) {
        console.log("cardBlock", cardBlock);

        setOriginalCards([
          ...cardBlock.attachments.map((a, index) => {
            return {
              body: [...a.body],
              actions: [...a.actions],
              order: index + 1,
            };
          }),
        ]);
      }
    } else {
      addCard();
    }
  }, [trigger]);

  useEffect(() => {
    const fetchedCardstoUpdate = JSON.parse(JSON.stringify(originalCards));
    setCards([...fetchedCardstoUpdate]);

    fillCardBlocks([...fetchedCardstoUpdate]);
  }, [originalCards]);

  useEffect(() => {
    console.log(cards);

    const hasImageAndHeadingOrSubheading = cards.every((atch) => {
      const imageObj = atch.body.find((item) => item.id === "image");
      const videoObj = atch.body.find((item) => item.id === "video");
      const headingObj = atch.body.find((item) => item.id === "heading");
      const subheadingObj = atch.body.find((item) => item.id === "subheading");
      const voiceDataObj = atch?.body?.find((item) => item?.id === "voiceData");
      console.log(isDialog);

      const hasImage =
        imageObj && isDialog
          ? imageObj.url[lang].trim() !== ""
          : imageObj && imageObj.url.trim() !== "";
      console.log(imageObj);
      const hasVideo =
        videoObj && isDialog
          ? videoObj.sources[0].url[lang].trim() !== ""
          : videoObj.sources[0].url.trim() !== "";
      console.log(videoObj);

      console.log(headingObj);
      const hasHeading =
        headingObj && isDialog
          ? headingObj.text[lang].trim() !== ""
          : headingObj.text.trim() !== "";
      console.log(headingObj);
      const hasSubheading =
        subheadingObj && isDialog
          ? subheadingObj.text[lang].trim() !== ""
          : headingObj.text.trim() !== "";
      const hasVoiceDataObj =
          voiceDataObj && isDialog
          ? voiceDataObj
          : "";

      return (hasImage || hasVideo) && (hasHeading || hasSubheading) && hasVoiceDataObj;
    });
    console.log(
      "hasImageAndHeadingOrSubheading",
      hasImageAndHeadingOrSubheading
    );

    if (setIsCardChanged) {
      if (
        JSON.stringify(helper.preUpdateCards(originalCards)) !==
          JSON.stringify(helper.preUpdateCards(cards)) &&
        hasImageAndHeadingOrSubheading
      ) {
        console.log(true);

        setIsCardChanged(true);
      } else {
        console.log(false);
        setIsCardChanged(false);
      }
    }
    if (Boolean(cards.length)) {
      updateCards(helper.preUpdateCards(cards));
    }
  }, [cards]);

  const addCard = () => {
    const all_cards = [...cards];
    const initialCards = helper.initCardData(bot, all_cards, isDialog);
    all_cards.push(initialCards);
    setOriginalCards([...all_cards]);
  };

  const cardtemplate = {
    body: [
      {
        id: "video",
        type: "Media",
        horizontalAlignment: "Center",
        poster: "",
        height: "250px",
        width: "250px",
        sources: [
          {
            mimeType: "video/mp4",
            url: isDialog ? { en: "", ar: "" } : "",
          },
        ],
      },
      {
        id: "image",
        type: "Image",
        url: isDialog ? { en: "", ar: "" } : "",
        horizontalAlignment: "Center",
        height: "250px",
        width: "250px",
      },
      {
        id: "heading",
        type: "TextBlock",
        text: isDialog ? { en: "", ar: "" } : "",
        weight: "Bolder",
        horizontalAlignment: "Center",
        wrap: true,
      },
      {
        id: "subheading",
        type: "TextBlock",
        text: isDialog ? { en: "", ar: "" } : "",
        horizontalAlignment: "Center",
        wrap: true,
      },
      {
        id: "voiceData",
        type: "TextBlock",
        voiceData: isDialog ? {voice_path_male:"",voice_path_female:""} : "",
      },
    ],
    actions: [],
  };

  function isImageUrl(url) {
    const img = new Image();
    img.src = url;
    return new Promise((resolve) => {
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
    });
  }

  function isVideoUrl(url) {
    const video = document.createElement("video");
    video.src = url;
    return new Promise((resolve) => {
      video.addEventListener("loadedmetadata", () => resolve(true));
      video.addEventListener("error", () => resolve(false));
    });
  }

  const fillCardBlocks = (fetchedcards) => {
    const new_cards_set = [...fetchedcards];
    const filled = new_cards_set.map((card) => {
      const newCard = { ...card };
      cardtemplate.body.forEach((block) => {
        if (!newCard?.body?.find((a) => a?.id === block?.id)) {
          newCard.body.push({
            ...block,
            horizontalAlignment: bot.language === "en" ? "left" : "right",
          });
        }
      });
      return newCard;
    });
    setCards(filled);
  };

  const addCardText = (card_order, block_id, text) => {
    const new_cards_set = [...cards];
    if (isDialog) {
      new_cards_set
        .find((a) => a.order === card_order)
        .body.find((a) => a.id === block_id).text[lang] = text;
    } else {
      new_cards_set
        .find((a) => a.order === card_order)
        .body.find((a) => a.id === block_id).text = text;
    }
    setCards([...new_cards_set]);
  };
  const addCardVoice = (card_order, block_id, voiceData) => {

    const new_cards_set = [...cards];
    if (isDialog) {
      new_cards_set
        .find((a) => a.order === card_order)
        .body.find((a) => a.id === block_id).voiceData= voiceData;
    } else {
      new_cards_set
        .find((a) => a.order === card_order)
        .body.find((a) => a.id === block_id).voiceData = voiceData;
    }

    setCards([...new_cards_set]);
  };

  const handlePreviewURL = async (id, type: "video" | "image", url) => {
    if (type === "image") {
      const imgElement = document.getElementById(id) as HTMLImageElement;
      imgElement.src = url;
    } else {
      const vidElement = document.getElementById(id) as HTMLVideoElement;
      vidElement.src = url;
    }
  };

  useEffect(() => {
    console.log("cards", cards);
  }, [cards]);

  const uploadImg = (file, card_index) => {
    const formData = new FormData();
    formData.append("file", file);

    if (file) {
      if (file.type.includes("video")) {
        uploadImageHandler({
          path: `Bots/${bot.file_name}/Videos/${file.name}`,
          formData,
        });

        const card_to_modify = { ...cards[card_index] };
        isDialog
          ? (card_to_modify.body.find((a) => a.id === "image").url = {
              en: "",
              ar: "",
            })
          : (card_to_modify.body.find((a) => a.id === "image").url = "");
        setTimeout(() => {
          handleUploadVid(`card-video-${card_index}`, file);
          handleUploadImg(`card-poster-${card_index}`, "");
        }, 100);

        if (card_to_modify.body.find((a) => a.id === "video")) {
          isDialog
            ? (card_to_modify.body.find((a) => a.id === "video").sources[0].url[
                lang
              ] =
                "https://infotointell.fra1.digitaloceanspaces.com/" +
                `Bots/${bot.file_name}/Videos/${file.name}`)
            : (card_to_modify.body.find(
                (a) => a.id === "video"
              ).sources[0].url =
                "https://infotointell.fra1.digitaloceanspaces.com/" +
                `Bots/${bot.file_name}/Videos/${file.name}`);
        } else {
          card_to_modify.body.push({
            id: "video",
            type: "Media",
            horizontalAlignment: "Center",
            poster: "",
            height: "250px",
            width: "250px",
            sources: [
              {
                mimeType: "video/mp4",
                url: "",
              },
            ],
          });
          isDialog
            ? (card_to_modify.body.find((a) => a.id === "video").sources[0].url[
                lang
              ] =
                "https://infotointell.fra1.digitaloceanspaces.com/" +
                `Bots/${bot.file_name}/Videos/${file.name}`)
            : (card_to_modify.body.find(
                (a) => a.id === "video"
              ).sources[0].url =
                "https://infotointell.fra1.digitaloceanspaces.com/" +
                `Bots/${bot.file_name}/Videos/${file.name}`);
        }

        const new_cards_set = [...cards];
        new_cards_set[card_index] = { ...card_to_modify };
        setCards([...new_cards_set]);
      } else if (file.type.includes("image")) {
        uploadImageHandler({
          path: `Bots/${bot.file_name}/cardsImages/${file.name}`,
          formData,
        });
        setTimeout(() => {
          handleUploadVid(`card-video-${card_index}`, "");
          handleUploadImg(`card-poster-${card_index}`, file);
        }, 100);
        const card_to_modify = { ...cards[card_index] };
        isDialog
          ? (card_to_modify.body.find((a) => a.id === "image").url[lang] =
              "https://infotointell.fra1.digitaloceanspaces.com/" +
              `Bots/${bot.file_name}/cardsImages/${file.name}`)
          : (card_to_modify.body.find((a) => a.id === "image").url =
              "https://infotointell.fra1.digitaloceanspaces.com/" +
              `Bots/${bot.file_name}/cardsImages/${file.name}`);
        isDialog
          ? (card_to_modify.body.find((a) => a.id === "video").sources[0].url[
              lang
            ] = "")
          : (card_to_modify.body.find((a) => a.id === "video").sources[0].url =
              "");
        const new_cards_set = [...cards];
        new_cards_set[card_index] = { ...card_to_modify };
        setCards([...new_cards_set]);
      }
    }
  };

  const addMediaUrl = async (card_index, url) => {
    console.log(url);

    const card_to_modify = { ...cards[card_index] };
    console.log(card_to_modify);

    if (url) {
      if (await isVideoUrl(url)) {
        isDialog
          ? (card_to_modify.body.find((a) => a.id === "image").url[lang] = "")
          : (card_to_modify.body.find((a) => a.id === "image").url = "");

        isDialog
          ? (card_to_modify.body.find((a) => a.id === "video").sources[0].url[
              lang
            ] = url)
          : (card_to_modify.body.find((a) => a.id === "video").sources[0].url =
              url);
        const new_cards_set = [...cards];
        new_cards_set[card_index] = { ...card_to_modify };
        setCards([...new_cards_set]);

        setTimeout(() => {
          handlePreviewURL(`card-video-${card_index}`, "video", url);
          handlePreviewURL(`card-poster-${card_index}`, "image", "");
        }, 100);
      } else if (await isImageUrl(url)) {
        isDialog
          ? (card_to_modify.body.find((a) => a.id === "image").url[lang] = url)
          : (card_to_modify.body.find((a) => a.id === "image").url = url);
        isDialog
          ? (card_to_modify.body.find((a) => a.id === "video").sources[0].url[
              lang
            ] = "")
          : (card_to_modify.body.find((a) => a.id === "video").sources[0].url =
              "");
        const new_cards_set = [...cards];
        new_cards_set[card_index] = { ...card_to_modify };
        setCards([...new_cards_set]);

        setTimeout(() => {
          handlePreviewURL(`card-video-${card_index}`, "video", "");
          handlePreviewURL(`card-poster-${card_index}`, "image", url);
        }, 100);
      }
    }
  };

  const CreateButtonSchema = z
    .object(
      isDialog
        ? {
            title: z
              .object(
                lang === "en"
                  ? {
                      en: z.string().min(1, "Button display label is required"),
                      ar: z.string().optional(),
                    }
                  : {
                      en: z.string().optional(),
                      ar: z.string().min(1, "Button display label is required"),
                    }
              )
              .refine(
                (value) => (lang === "en" ? Boolean(value.en.trim()) : true),
                {
                  message: "Button display label is required",
                  path: ["en"],
                }
              )
              .refine(
                (value) => (lang === "ar" ? Boolean(value.ar.trim()) : true),
                {
                  message: "Button display label is required",
                  path: ["ar"],
                }
              ),
            type: z.enum(["Action.Submit", "Action.OpenUrl"]),
            data: z
              .object({
                en: z.string().optional(),
                ar: z.string().optional(),
              })
              .optional(),
            url: z
              .object({
                en: z.string().optional(),
                ar: z.string().optional(),
              })
              .optional(),
          }
        : {
            title: z
              .string()
              .min(1, "Button display label is required")
              .refine((value) => Boolean(value.trim()), {
                message: "Button display label is required",
              }),
            type: z.enum(["Action.Submit", "Action.OpenUrl"]),
            data: z.string().optional(),
            url: z.string().url().optional(),
          }
    )
    .refine(
      (data) => {
        if (isDialog) {
          if (data.type === "Action.Submit") {
            return Boolean(data.data[lang].trim());
          }
          return true;
        } else if (typeof data.data === "string") {
          if (data.type === "Action.Submit") {
            return Boolean(data.data.trim());
          }
          return true;
        }
      },
      {
        path: ["data"],
        message: "Prompt is required",
      }
    )
    .refine(
      (data) => {
        if (isDialog) {
          if (data.type === "Action.OpenUrl") {
            return Boolean(data.url[lang].trim());
          }
          return true;
        } else if (typeof data.url === "string") {
          if (data.type === "Action.OpenUrl") {
            if (isDialog) return Boolean(data.url[lang].trim());
            return Boolean(data.url.trim());
          }
          return true;
        }
      },
      {
        path: ["url"],
        message: "URL is required",
      }
    );

  const onChangeHandler = (key, value) => {
    setActionToAdd({
      ...actionToAdd,
      [key]: value,
    });
  };

  const AddButton = (card_index) => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateButtonSchema,
        data: actionToAdd,
      },
      setButtonErrors
    );
    console.log(isErrors)

    if (isErrors) return;

    const card_to_modify = { ...cards[card_index] };
    console.log(actionToAdd);

    card_to_modify.actions.push({
      ...actionToAdd,
      card_index: undefined,
    });
    const new_cards_set = [...cards];
    new_cards_set[card_index] = { ...card_to_modify };
    setCards([...new_cards_set]);

    setActionToAdd(helper.initCardButtonData(isDialog));

    setShowButtonModal(false);
  };

  const UpdateButton = () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateButtonSchema,
        data: actionToAdd,
      },
      setButtonErrors
    );

    if (isErrors) return;

    const card_to_modify = { ...cards[actionToAdd.card_index] };
    card_to_modify.actions[actionToAdd.action_index] = { ...actionToAdd };

    const new_cards_set = [...cards];
    new_cards_set[actionToAdd.card_index] = { ...card_to_modify };
    setCards([...new_cards_set]);

    setActionToAdd(helper.initCardButtonData(isDialog));
    setShowButtonModal(false);
  };

  const removeCardAction = (card_index, action_index) => {
    const card_to_modify = { ...cards[card_index] };
    const modified_card_actions = card_to_modify.actions.filter(
      (a, index) => index !== action_index
    );
    card_to_modify.actions = [...modified_card_actions];
    const new_cards_set = [...cards];
    new_cards_set[card_index] = { ...card_to_modify };
    setCards([...new_cards_set]);
  };

  const handleSwitchMedia = (card_index, link) => {
    const card_to_modify = { ...cards[card_index] };
    if (link) {
      setByLink([...byLink, card_index]);
      if (isDialog) {
        card_to_modify.body.find((a) => a.id === "image").url[lang] = "";
        card_to_modify.body.find((a) => a.id === "video").sources[0].url[lang] =
          "";
      } else {
        card_to_modify.body.find((a) => a.id === "image").url = "";
        card_to_modify.body.find((a) => a.id === "video").sources[0].url = "";
      }
      const new_cards_set = [...cards];
      new_cards_set[card_index] = { ...card_to_modify };
      setCards([...new_cards_set]);
    } else {
      setByLink(byLink.filter((a) => a !== card_index));
    }
  };

  const deleteCard = (index) => {
    var all_cards = [...cards];
    if (all_cards.length === 1) {
      all_cards = [];
    } else {
      all_cards.splice(index, index);
    }
    setCards([...all_cards]);
  };

  return (
    <>
      <div
        className={`space-y-2 max-h-full overflow-y-auto ${
          isDialog ? "" : "pr-3"
        } `}
      >
        <FullModal
          isOpen={showButtonModal}
          title="Create Card Button"
          onClose={() => {
            setShowButtonModal(false);
            setCardToAddButtonsIndex(null);
            setActionToAdd(helper.initCardButtonData(isDialog));
          }}
          className="!w-11/12"
        >
          <div className="flex flex-col gap-5">
            <div className="flex flex-col">

            <Input
              value={isDialog ? actionToAdd.title[lang] : actionToAdd.title}
              onChange={(e) => {
                console.log(actionToAdd.title);

                onChangeHandler(
                  "title",
                  isDialog
                    ? setValuesBasedOnLanguage(
                        e.target.value,
                        actionToAdd.title as any,
                        lang
                      )
                    : e.target.value
                );
              }}
              title={
                <label className="flex gap-5 items-end">
                  Button Label
                  <small className="text-white/50">
                    - Characters Limit: WhatsApp: 20, Facebook: 20
                  </small>
                </label>
              }
              name={`card-button-label-${cardToAddButtonsIndex}`}
              error={buttonErrors.title}
            />
                <small className="self-end text-white/50">
            {  isDialog ? actionToAdd.title[lang]?.length : 0} characters
          </small>
            </div>
            <fieldset className="flex flex-wrap gap-3 self-center">
              <legend className="sr-only">Action Type</legend>
              <Radio
                name="ActionType"
                value="Action.Submit"
                label="Prompt"
                checkMark
                checked={actionToAdd.type === "Action.Submit" ? true : false}
                onChange={(e) => {
                  setActionToAdd({
                    ...actionToAdd,
                    data: isDialog ? { en: "", ar: "" } : "",
                    title: isDialog ? { en: "", ar: "" } : "",
                    url: undefined,
                    type: "Action.Submit",
                  });
                }}
              />
              <Radio
                name="ActionType"
                value="Action.OpenUrl"
                label="URL"
                checkMark
                checked={actionToAdd.type === "Action.OpenUrl" ? true : false}
                onChange={(e) => {
                  setActionToAdd({
                    ...actionToAdd,
                    url: isDialog ? { en: "", ar: "" } : "",
                    title: isDialog ? { en: "", ar: "" } : "",
                    data: undefined,
                    type: "Action.OpenUrl",
                  } as any);
                }}
              />
            </fieldset>

            {actionToAdd.type === "Action.Submit" ? (
              <div className="flex flex-col">
              <Input
                value={actionToAdd.data[lang]}
                title={
                  <label className="flex gap-5 items-end">
                  Button Payload
                  <small className="text-white/50">
                    - Characters Limit: WhatsApp: 256, Facebook: 1000
                  </small>
                </label>
                }
                name="data"
                error={buttonErrors.data}
                onChange={(e) =>
                  onChangeHandler(
                    "data",
                    isDialog
                      ? setValuesBasedOnLanguage(
                          e.target.value,
                          actionToAdd.data as any,
                          lang
                        )
                      : e.target.value
                  )
                }
              />
              <small className="self-end text-white/50">
              {  isDialog ? actionToAdd.data[lang]?.length : 0} characters
            </small>
              </div>
            ) : (
              <Input
                value={actionToAdd.url[lang]}
                title={
                  <label className="flex gap-5 items-end">
                  URL
                  <small className="text-white/50">
                    - Not supported in WhatsApp
                  </small>
                </label>
                }
                name="url"
                error={buttonErrors.url}
                onChange={(e) =>
                  onChangeHandler(
                    "url",
                    isDialog
                      ? setValuesBasedOnLanguage(
                          e.target.value,
                          actionToAdd.url,
                          lang
                        )
                      : e.target.value
                  )
                }
              />
            )}
            {actionToAdd.action_index === undefined ? (
              <Button
                onClick={() => AddButton(cardToAddButtonsIndex)}
                className="self-end"
              >
                Add
              </Button>
            ) : (
              <div className="flex justify-between items-center">
                <small className="text-yellow-300">
                  You still need to save the card to save these changes
                </small>
                <Button onClick={UpdateButton}>Update</Button>
              </div>
            )}
          </div>
        </FullModal>

        <div className="sticky top-0 bg-accent">
          {title && title}{" "}
          {error && <small className="text-red-500">{error}</small>}{" "}
        </div>
        {cards.map((card, card_index) =>
          isDialog ? (
            <details
              key={card_index}
              className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden"
            >
              <summary className="flex cursor-pointer items-center justify-between gap-2 p-4 bg-secondary transition">
                <span className="text-sm font-medium"> Card </span>

                <span className="transition group-open:-rotate-180">
                  <ChevronDown className="h-4 w-4" />
                </span>
              </summary>
              <CardForm
                card_index={card_index}
                card={card}
                isDialog={isDialog}
                deleteCard={deleteCard}
                byLink={byLink}
                setByLink={setByLink}
                addCardText={addCardText}
                addCardVoice={addCardVoice}
                removeCardAction={removeCardAction}
                setShowButtonModal={setShowButtonModal}
                setActionToAdd={setActionToAdd}
                setCardToAddButtonsIndex={setCardToAddButtonsIndex}
                isUpdate={isUpdate}
                uploadImg={uploadImg}
                handleSwitchMedia={handleSwitchMedia}
                addMediaUrl={addMediaUrl}
                handleSave={handleSave}
                entities={entities}
                globals={globals}
              />
            </details>
          ) : (
            <CardForm
              card_index={card_index}
              card={card}
              isDialog={isDialog}
              deleteCard={deleteCard}
              byLink={byLink}
              setByLink={setByLink}
              addCardText={addCardText}
              addCardVoice={addCardVoice}
              removeCardAction={removeCardAction}
              setShowButtonModal={setShowButtonModal}
              setActionToAdd={setActionToAdd}
              setCardToAddButtonsIndex={setCardToAddButtonsIndex}
              isUpdate={isUpdate}
              uploadImg={uploadImg}
              handleSwitchMedia={handleSwitchMedia}
              addMediaUrl={addMediaUrl}
              key={card_index}
              handleSave={handleSave}
              entities={entities}
              globals={globals}
            />
          )
        )}
      </div>
      {isDialog && (
        <div className="flex justify-between items-center bg-secondary mt-2 p-2 rounded ">
          <label htmlFor="">Add New Card</label>
          <Button
            onClick={() => addCard()}
            className="rounded-full w-5 h-5 p-1"
          >
            <Plus />
          </Button>
        </div>
      )}
    </>
  );
};

const CardForm = ({
  card_index,
  card,
  isDialog,
  deleteCard,
  byLink,
  setByLink,
  addCardText,
  addCardVoice,
  removeCardAction,
  setShowButtonModal,
  setActionToAdd,
  setCardToAddButtonsIndex,
  isUpdate,
  uploadImg,
  handleSwitchMedia,
  addMediaUrl,
  handleSave,
  entities,
  globals
}) => {
  const { lang } = useLangStore();
  const [showModal, setShowModal] = useState(false);
  const handleUpdateVoice = (data: TVoiceBody) => {
    const updatedVoices = data;
    delete updatedVoices.bot_id;
    delete updatedVoices.user_id;
   const  voiceData = card.body.find((a) => a.id === "voiceData").voiceData;
    const prevVoices = { ...voiceData, ...updatedVoices };
    addCardVoice(card.order, "voiceData", prevVoices)
    
  };
  return (
    <div
      className={`flex flex-col gap-2 ${
        isDialog ? "relative  p-2 rounded" : ""
      } `}
      key={card_index}
    >
      {isDialog && (
        <i
          className="absolute -top-2 right-0 bg-red-500 rounded-full cursor-pointer "
          onClick={() => deleteCard(card_index)}
        >
          <X size={19} />
        </i>
      )}
      <div className="flex justify-between items-center mr-5">
        <div className="text-sm">Card Media</div>
        {byLink.includes(card_index) ? (
          <Tooltip text="By Upload">
            <Upload
              size={18}
              className="cursor-pointer"
              onClick={() => setByLink(byLink.filter((a) => a !== card_index))}
            />
          </Tooltip>
        ) : (
          <Tooltip text="Add by url">
            <Link
              size={18}
              className="cursor-pointer"
              onClick={() => handleSwitchMedia(card_index, true)}
            />
          </Tooltip>
        )}
      </div>
      <div className="flex items-center justify-center gap-2 w-full">
        {byLink.includes(card_index) ? (
          <>
            <img
              id={`card-poster-${card_index}`}
              src={
                isDialog
                  ? card.body.find((a) => a.id === "image").url[lang]
                  : card.body.find((a) => a.id === "image").url
              }
              alt="Preview"
              className={`w-28 h-28 object-cover ${
                isDialog
                  ? card.body.find((a) => a.id === "image").url[lang]
                    ? "block"
                    : "hidden"
                  : card.body.find((a) => a.id === "image").url
                  ? "block"
                  : "hidden"
              }`}
            />
            <video
              id={`card-video-${card_index}`}
              src={
                isDialog
                  ? card.body.find((a) => a.id === "video")?.sources[0].url[
                      lang
                    ]
                  : card.body.find((a) => a.id === "video")?.sources[0].url
              }
              className={`w-28 h-28 object-cover ${
                isDialog
                  ? card.body.find((a) => a.id === "video")?.sources[0].url[
                      lang
                    ]
                    ? "block"
                    : "hidden"
                  : card.body.find((a) => a.id === "video")?.sources[0].url
                  ? "block"
                  : "hidden"
              }`}
              controls
            ></video>
            <Input
              placeholder="URL"
              name="url"
              value={
                isDialog
                  ? card.body.find((a) => a.id === "image").url[lang] ||
                    card.body.find((a) => a.id === "video")?.sources[0].url[
                      lang
                    ] ||
                    ""
                  : card.body.find((a) => a.id === "image").url ||
                    card.body.find((a) => a.id === "video")?.sources[0].url ||
                    ""
              }
              onChange={(e) => addMediaUrl(card_index, e.target.value)}
            />
          </>
        ) : (
          <MediaInput
            card_index={card_index}
            imgSrc={
              isDialog
                ? card.body.find((a) => a.id === "image").url[lang]
                : card.body.find((a) => a.id === "image").url
            }
            videoSrc={
              isDialog
                ? card.body.find((a) => a.id === "video")?.sources[0].url[lang]
                : card.body.find((a) => a.id === "video")?.sources[0].url
            }
            video
            uploadImg={uploadImg}
          />
        )}
      </div>
      <VoiceModalWrapper
       card_Key={card_index + card?.order}
       card_index={card_index}
       handleSave={handleSave}
       handleUpdateVoice={handleUpdateVoice}
       setShowModal={setShowModal}
       showModal={showModal}
       text={   isDialog
        ? `${card.body.find((a) => a.id === "heading")?.text[lang]}, ${card.body.find((a) => a.id === "subheading")?.text[lang]}`
        : `${card.body.find((a) => a.id === "heading")?.text}, ${card.body.find((a) => a.id === "subheading")?.text}`}
        voice_path_female={card?.body?.find((a) => a?.id === "voiceData")?.voiceData?.voice_path_female || ''}
        voice_path_male={card?.body?.find((a) => a?.id === "voiceData")?.voiceData?.voice_path_male || ''}
       />
      <div className="text-sm">
        <div>
          <label htmlFor="cardheading" className="flex gap-5 items-end">Card Heading
          <small className="text-white/50">
            - Characters Limit: WhatsApp: 60, Facebook: 80

          </small>
          </label>
          <Textarea
            id="cardheading"
            // maxLength={25}
            placeholder="Card Heading..."
            tabIndex={0}
            dir="auto"
            value={
              isDialog
                ? card.body.find((a) => a.id === "heading")?.text[lang]
                : card.body.find((a) => a.id === "heading")?.text 
                
            }
            className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 `}
            rows={1}
            onChange={(event) =>
              addCardText(card.order, "heading", event.target.value)
            }
            entities={entities}
            globals={globals}
          ></Textarea>
              <small className="float-right text-white/50">
            {  isDialog
                ? card.body.find((a) => a.id === "heading")?.text[lang]?.length
                : card.body.find((a) => a.id === "heading")?.text?.length} characters
          </small>
        </div>
        <div className="flex flex-col w-full">



          <label htmlFor="cardsubheading" className="flex gap-5 items-end">Card Subheading
          <small className="text-white/50">
            - Characters Limit: WhatsApp: 1024, Facebook: 80
          </small>
          </label>
          <Textarea
            id="cardsubheading"
            // maxLength={25}
            placeholder="Card Subheading..."
            tabIndex={0}
            dir="auto"
            value={
              isDialog
                ? card.body.find((a) => a.id === "subheading")?.text[lang]
                : card.body.find((a) => a.id === "subheading")?.text
            }
            className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 `}
            rows={1}
            onChange={(event) =>
              addCardText(card.order, "subheading", event.target.value)
            }
            entities={entities}
            globals={globals}
          ></Textarea>
               <small className="self-end text-white/50">
            {  isDialog
                ? card.body.find((a) => a.id === "subheading")?.text[lang]?.length
                : card.body.find((a) => a.id === "subheading")?.text?.length} characters
          </small>
        </div>

        <div>
          <div className="space-y-5 h-full">
            <div className="space-y-2 py-2">
              <div className="flex justify-between items-center">
                <label htmlFor="">Card Buttons
                <small className="text-white/50">
                - Buttons Limit: WhatsApp: 3, Facebook: 3
               </small>
                </label>
                <Button
                  onClick={() => {
                    setShowButtonModal(true);
                    setCardToAddButtonsIndex(card_index);
                  }}
                  className="rounded-full w-5 h-5 p-1"
                >
                  <Plus />
                </Button>
              </div>
              <div className="max-h-32 h-32  overflow-y-auto p-2 border-2 border-gray-300 border-dashed rounded">
                <div className="space-y-1 py-2">
                  {card.actions?.map((a, action_index) => {
                    return (
                      <div
                        className="flex items-center gap-3"
                        key={action_index}
                      >
                        <textarea
                          disabled={true}
                          maxLength={25}
                          placeholder="Write a suggestion button..."
                          tabIndex={0}
                          dir="auto"
                          value={a.title[lang]}
                          className={`rounded bg-transparent border border-white/25  h-full break-words w-full  p-3  text-white max-h-[200px] overflow-y-hidden resize-none focus:border-white/50 focus:outline-none focus:ring-0 `}
                          rows={1}
                        ></textarea>
                        {card.actions.length > 0 ? (
                          <>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <X
                                  size={15}
                                  className=" bg-red-500 rounded-full cursor-pointer"
                                />
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Are you sure you want to delete this?
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() =>
                                      removeCardAction(card_index, action_index)
                                    }
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                            <Edit
                              onClick={() => {
                                setActionToAdd({
                                  ...a,
                                  card_index,
                                  action_index,
                                });
                                setShowButtonModal(true);
                              }}
                              size={16}
                              className="rounded-full cursor-pointer hover:text-primary"
                            />
                          </>
                        ) : null}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
