import { createTips } from "apis/faq.api";
import { Textarea } from "common/ui/inputs/textarea";
import { isArabic } from "helpers/helper";
import { ShieldClose } from "lucide-react";
import { useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { IFAQ } from "store/faq/faq.types";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";
import { ITrigger } from "store/trigger/trigger.types";

interface BotAnswerProps {
  onChange: (e) => void;
  value: string;
  error: string;
}

export const BotAnswer: React.FC<BotAnswerProps> = ({
  onChange,
  value,
  error,
}) => {

  return (
    <div className="space-y-1 relative">
      <label htmlFor="question" className="text-right">
        Bot Answer *

      </label>
      <Textarea className="h-32"  dir={isArabic(value) ? "rtl"  :"ltr"}  value={value} onChange={onChange}></Textarea>
      {error ? (
        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
          <ShieldClose size={12} />
          {error}
        </span>
      ) : null}
    </div>
  );
};
