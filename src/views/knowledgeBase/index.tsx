import {
  Clipboard<PERSON>ist,
  <PERSON>,
  FlaskConical,
  InfoIcon,
  Mail,
  MessageCircle,
  Milestone,
  Settings2,
} from "lucide-react";
import { useEffect, useState } from "react";
import { ViewCard } from "common/components/cards";
import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { MainPageHeader } from "common/components/headers";
import useGPTStore from "store/gpt/gpt.store";
import useSearchSettingsStore from "store/search/search.store";
import useBotStore from "store/bot/bot.store";
import { SearchSettings } from "./components/searchSettings";
import { Alert } from "common/ui/alert";
import views from "views";
import useUserPrivilegeStore from "store/priviliges/UserPrivilege.store";
import { IEditorPrivilges } from "store/priviliges/UserPrivilege.types";
import useUserStore from "store/user/user.store";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { botConfigType } from "types/botconfig.types";
import { DefaultsView } from "./subviews/defaults";
import { FAQsView } from "./subviews/faqs";

import { UnstructuredView } from "./subviews/unstructured";
import { DialogsView } from "./subviews/dialogs";
import { SmallTalkView } from "./subviews/smalltalk";
import { BadWordView } from "./subviews/badword";
import TestingSheet from "./components/testingSheet";
import { Button } from "common/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import useTOPICStore from "store/topic/topic.store";
import { FullModal } from "common/components/modals";
import { Card } from "common/ui/card";
import TopicsModal from "./components/topicsModal";
import { extractLangInOneLine } from "helpers/multiLang";
import useLLMStore from "store/llmIntegration/llm.store";

interface KnowledgeBaseViewProps {
  setView: (view: string) => void;
  botConfig: botConfigType;
  setBotConfig: (botConfig: botConfigType) => void;
}

export const KnowledgeBaseView: React.FC<KnowledgeBaseViewProps> = ({
  setView,
  botConfig,
  setBotConfig,
}) => {
  const user = useUserStore((state) => state.user);
  const { topics, topic, set_topic } = useTOPICStore();
  const bot = useBotStore((state) => state.bot);
  const llm = useLLMStore((state) => state.llm);
  // const { get_one_searchSettings, searchSettings } = useSearchSettingsStore();

  const { priviliges, isAdmin } = useUserPrivilegeStore();
  const hasEditorAccess = (privilege: keyof IEditorPrivilges | undefined) => {
    return Boolean(priviliges[privilege] || user.user_id === 17 || isAdmin);
  };

  const [showTip, setShowTip] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showTestSidebar, setShowTestSidebar] = useState(false);

  const [settings, setSettings] = useState({
    engine: "searchat",
    kb_type: "structured",
  });

  // useEffect(() => {
  //   // if (gpt?.openai_key) {
  //   get_one_searchSettings(bot.bot_id);
  //   // }
  // }, [llm]);

  // useEffect(() => {
  //   if (searchSettings?.search_setting_id) {
  //     setSettings({
  //       engine: searchSettings.engine,
  //       kb_type: searchSettings.kb_type,
  //     });
  //   } else {
  //     setSettings({
  //       engine: "searchat",
  //       kb_type: "structured",
  //     });
  //   }
  // }, [searchSettings]);
  const [topicsModal, setTopicsModal] = useState(false);
  return (
    <div className="relative">
      {showTestSidebar && <TestingSheet setShowTest={setShowTestSidebar} />}

      <div
        className={`space-y-5 h-screen ${showTestSidebar ? "pl-[350px]" : ""} `}
      >
        <MainPageHeader
          title="Add to your bot Knowledge base"
          description="Grow your chatbot knowledge and abilities."
          // btn={
          //   llm?.llm_key &&
          //   llm.status_active &&
          //   hasEditorAccess("builder_privilege")
          // }
          // btnText="Search Settings"
          // btnIcon={Settings2}
          // btnOnClick={() => setShowModal(true)}
        />
        <Button
          onClick={() => setShowTestSidebar(true)}
          variant="outline"
          className="mt-4 group w-full"
        >
          <FlaskConical size={15} className="mr-2 group-hover:rotate-12" />
          Test Your Bot
        </Button>
        {bot.bot_id !== 666 && 
        <div className="border border-white/25 p-5 rounded-md space-y-3">
          <p className="text-3xl font-bold">Topics</p>
          <FullModal
            key={"broadcasts"}
            title={"Topics Settings"}
            isOpen={topicsModal}
            onClose={() => {
              setTopicsModal(false);
            }}
          >
            <TopicsModal />
          </FullModal>
          <p>
            Topics are used to organize your bot knowledge base, You can use
            them to optimize the search results, minimize the response time, and
            improve the building experience.
          </p>
          <div className="w-full flex items-end justify-between">
            <div className="flex items-end gap-3">
              <Select
                value={topic?.topic_id?.toString() || ""}
                onValueChange={(value) => {
                  set_topic(value);
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select a type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={""}>All</SelectItem>
                  {topics.map((topic, i) => {
                    return (
                      <SelectItem key={i} value={topic.topic_id.toString()}>
                        {extractLangInOneLine(topic.display_name)}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              <p>Select a topic to filter the FAQs & Dialogs</p>
            </div>
            <div>
              <Button
                onClick={() => {
                  setTopicsModal(true);
                }}
              >
                Manage Topics
              </Button>
            </div>
          </div>
        </div>
        }
        {/* {showTip ? (
          <Alert
            title={
              llm?.status_active
                ? "You can choose between Searchat and GPT engine for your bot"
                : "By default your bot will use Searchat engine to search for answers."
            }
            content={
              llm?.status_active
                ? "If GPT engine was used you can either add structered knowledge base or import documents"
                : " You can also use GPT engine. To do so you need to enable GPT plugin in the plugins page. "
            }
            setShowAlert={setShowTip}
          />
        ) : (
          <InfoIcon
            size={15}
            className="text-amber-300 float-right cursor-pointer"
            onClick={() => setShowTip(true)}
          />
        )} */}
        {/* {llm?.llm_key && llm.status_active && (
          <SearchSettings
            showModal={showModal}
            setShowModal={setShowModal}
            settings={settings}
            setSettings={setSettings}
          />
        )} */}

        <Tabs defaultValue="defaults" className="w-full">
          <TabsList className="flex justify-stretch w-full">
            {hasEditorAccess("builder_privilege") && (
              <>
                <TabsTrigger className="grow" value="defaults">
                  Defaults
                </TabsTrigger>
                <TabsTrigger className="grow" value="faqs">
                  FAQs
                </TabsTrigger>
              </>
            )}
            {hasEditorAccess("dialog_privilege") && (
              <TabsTrigger className="grow" value="dialogs">
                Dialogs
              </TabsTrigger>
            )}
            <TabsTrigger className="grow" value="smallTalk">
              Small Talk
            </TabsTrigger>
            <TabsTrigger className="grow" value="badWord">
              Bad Word
            </TabsTrigger>
            {llm?.status_active &&
              // searchSettings?.engine === "gpt" &&
              hasEditorAccess("builder_privilege") && (
                <TabsTrigger className="grow" value="unstructured">
                  Unstructured
                </TabsTrigger>
              )}
          </TabsList>
          {hasEditorAccess("builder_privilege") && (
            <>
              <TabsContent value="defaults">
                <DefaultsView
                  botConfig={botConfig}
                  setBotConfig={setBotConfig}
                />
              </TabsContent>
              <TabsContent value="faqs">
                <FAQsView />
              </TabsContent>
            </>
          )}
          {hasEditorAccess("dialog_privilege") && (
            <TabsContent value="dialogs">
              <DialogsView />
            </TabsContent>
          )}
          <TabsContent value="smallTalk">
            <SmallTalkView />
          </TabsContent>
          <TabsContent value="badWord">
            <BadWordView
              botConfig={botConfig}
              setBotConfig={setBotConfig}
              />
          </TabsContent>
          {llm?.status_active &&
            // searchSettings?.engine === "gpt" &&
            hasEditorAccess("builder_privilege") && (
              <TabsContent value="unstructured">
                <UnstructuredView />
              </TabsContent>
            )}
        </Tabs>
        {/* <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {hasEditorAccess("builder_privilege") && (
          <>
          <ViewCard
          icon={Mail}
          title="Default Messages"
          description=" Define how to welcome customers and set fallback message"
              onClick={() => setView(views.botViews.DEFAULTS_VIEW)}
              btnText="Start Building"
              btnIcon={ToyBrick}
              />
            <ViewCard
            icon={ClipboardList}
              title="FAQs"
              description=" Define answers to your customers most asked questions"
              onClick={() => setView(views.botViews.FAQS_VIEW)}
              btnText="Start Building"
              btnIcon={ToyBrick}
              />
            <ViewCard
              icon={Milestone}
              title="Cards and Suggestions"
              description=" Create cards and suggest actions to your customers"
              onClick={() => setView(views.botViews.CARDS_AND_SUGGESTIONS_VIEW)}
              btnText="Start Building"
              btnIcon={ToyBrick}
              />
          </>
          )}
          {hasEditorAccess("dialog_privilege") && (
            <ViewCard
            icon={MessageCircle}
            title="Dialogs"
            description=" Create interactive dialogs between your bot and customers "
            onClick={() => setView(views.botViews.DIALOGS_VIEW)}
            btnText="Start Building"
            btnIcon={ToyBrick}
          />
        )}
        {gpt?.status_active &&
          searchSettings?.engine === "gpt" &&
          hasEditorAccess("builder_privilege") && (
            <ViewCard
            icon={Files}
            title="Unstructured Files"
            description=" Import documents to your bot knowledge base"
            onClick={() => setView(views.botViews.UNSTRUCTURED_VIEW)}
              btnText="Start Building"
              btnIcon={ToyBrick}
              />
              )}
            </div> */}
      </div>
    </div>
  );
};
