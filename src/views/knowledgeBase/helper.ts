import { IBot } from "store/bot/bot.types";

const initCardData = (bot: IBot, all_cards: any, isDialog = false) => {
  return {
    body: [
      {
        id: "video",
        type: "Media",
        horizontalAlignment: "Center",
        poster: "",
        height: "250px",
        width: "250px",
        sources: [
          {
            mimeType: "video/mp4",
            url: isDialog ? { en: "", ar: "" } : "",
          },
        ],
      },
      {
        id: "image",
        type: "Image",
        url: isDialog ? { en: "", ar: "" } : "",
        horizontalAlignment: "Center",
        height: "250px",
        width: "250px",
      },
      {
        id: "heading",
        type: "TextBlock",
        horizontalAlignment: bot.language === "en" ? "Left" : "Right",
        text: isDialog ? { en: "", ar: "" } : "",
        weight: "Bolder",
        wrap: true,
      },
      {
        id: "subheading",
        type: "TextBlock",
        horizontalAlignment: bot.language === "en" ? "Left" : "Right",
        text: isDialog ? { en: "", ar: "" } : "",
        wrap: true,
      },
    ],
    actions: [],
    order: all_cards.length + 1,
  };
};

const initCardButtonData = (isDialog = false) => {
  return {
    action_index: undefined,
    card_index: undefined,
    type: "Action.Submit",
    title: isDialog ? { en: "", ar: "" } : "",
    data: isDialog ? { en: "", ar: "" } : "",
    url: undefined,
  };
};

const preUpdateCards = (cards) => {
  const jsonBody = {
    attachmentLayout: "carousel",
    attachments: [
      ...cards.map((a) => {
        return {
          $schema: "https://adaptivecards.io/schemas/adaptive-card.json",
          type: "AdaptiveCard",
          version: "1.1",
          body: [...a.body],
          actions: [...a.actions],
        };
      }),
    ],
  };

  return jsonBody;
};

export default {
  initCardData,
  initCardButtonData,
  preUpdateCards,
};
