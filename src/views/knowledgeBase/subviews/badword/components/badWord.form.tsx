import { FC, memo, useState } from "react";
import {
  CreateBadWordSchema,
  CreateBadWordType,
  IBadWord,
} from "store/badword/badword.types";
import helper from "../helper";
import useBotStore from "store/bot/bot.store";
import { But<PERSON> } from "common/ui/button";
import useBadWordStore from "store/badword/badword.store";
import { toast } from "react-hot-toast";
import { useLoaderContext } from "context/loaderContext";
import checkForErrors from "helpers/forms";
import { Textarea } from "common/ui/inputs/textarea";
import { ShieldClose } from "lucide-react";
import useBotConfigStore from "store/botConfig/botConfig.store";

interface BadWordFormProps {
  setOpen: (open: boolean) => void;
  isUpdate?: boolean;
  badWord?: IBadWord;
}

const BadWordForm: FC<BadWordFormProps> = ({ setOpen, isUpdate, badWord }) => {
  const bot = useBotStore((state) => state.bot);
  const { create_one_badword, update_one_badword, loading } = useBadWordStore();
  const { get_bot_config, botConfig } = useBotConfigStore();
  const [formData, setFormData] = useState(
    helper.initBadWordFormData(bot.bot_id, badWord ? badWord : null)
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { loader } = useLoaderContext();

  const validateField =
    (field: keyof CreateBadWordType) =>
    (value: unknown): string => {
      const parsedResult = CreateBadWordSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key: keyof CreateBadWordType, value: string) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleCreate = () => {
    loader?.continuousStart();
    create_one_badword(formData).then((data) => {
      // toast.success("Bad-Word created successfully");
      // setOpen(false);
      loader?.complete();
    });
  };

  const handleUpdate = () => {
    loader?.continuousStart();
    update_one_badword(formData).then(() => {
      toast.success("Small Talk updated successfully");
      setOpen(false);
      loader?.complete();
    });
  };

  const handleSubmit = () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateBadWordSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;

    if (isUpdate) {
      handleUpdate();
    } else {
      handleCreate();
    }
  };

  return (
    <div className="flex flex-col gap-1 py-4 text-white/70">
      <label htmlFor="bad_word" className="text-md whitespace-nowrap">
        Add New Bad Word:
      </label>
      <p className="text-xs text-primary">
        {isUpdate
          ? ""
          : "Write A Bad Word You Want To Include In The Badword List "}
      </p>

      <Textarea
        id="bad_word"
        title="Bad Word"
        name="bad_word"
        // badWord={badWord}
        onChange={(e) => onChangeHandler("bad_word", e.target.value)}
        value={formData.bad_word}
      />

      {errors?.bad_word && (
        <p className="w-full">
          <span className="text-red-500 text-xs flex gap-1 items-center ">
            <ShieldClose size={12} />
            {errors?.bad_word}
          </span>
        </p>
      )}

      <div className="self-end">
        <Button loading={loading} onClick={handleSubmit}>
          {isUpdate ? "Save Changes" : "Create Bad Word"}
        </Button>
      </div>
    </div>
  );
};

export default memo(BadWordForm);
