import { FC, memo, useState } from "react";

import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";
import { IChildren } from "types/common.types";
import BadWordForm from "../badWord.form";
import { IBadWord } from "store/badword/badword.types";

interface UpdateSTProps extends IChildren {
    badWord: IBadWord;
}

const UpdateST: FC<UpdateSTProps> = ({ children, badWord }) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Update BadWord</SheetTitle>
          <SheetDescription>
            Update your BadWord here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <BadWordForm setOpen={setOpen} isUpdate badWord={badWord} />
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default memo(UpdateST);
