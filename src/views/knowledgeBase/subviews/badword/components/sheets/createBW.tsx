import { FC, memo, useState } from "react";

import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { IChildren } from "types/common.types";
import ImportBadWords from "../importBadWords";
import BadWordForm from "../badWord.form";
import BadWordConfigForm from "../badWordConfig.form";

interface CreateBWProps extends IChildren {}

const CreateBW: FC<CreateBWProps> = ({ children }) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Add a BadWord</SheetTitle>
          <SheetDescription>
            Create your BadWord here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <Tabs defaultValue="manage" className="w-full mt-3">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="manage">Manage Bad Word</TabsTrigger>
            <TabsTrigger value="create">Create Bad Word</TabsTrigger>
            <TabsTrigger value="import">Import Bad Words</TabsTrigger>
          </TabsList>
          <TabsContent value="manage">
            <BadWordConfigForm setOpen={setOpen} />
          </TabsContent>
          <TabsContent value="create">
            <BadWordForm setOpen={setOpen} />
          </TabsContent>
          <TabsContent value="import" className="h-[560px]">
            <ImportBadWords />
          </TabsContent>
        </Tabs>

        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default memo(CreateBW);
