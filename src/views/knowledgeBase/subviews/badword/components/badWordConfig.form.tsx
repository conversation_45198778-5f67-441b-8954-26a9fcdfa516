import { FC, memo, useEffect, useRef, useState } from "react";
import { IBadWord, IBadWordConfig } from "store/badword/badword.types";
import useBotStore from "store/bot/bot.store";
import RichTextEditor from "common/ui/inputs/richtextedtor";
import { <PERSON><PERSON> } from "common/ui/button";
import useBadWordStore from "store/badword/badword.store";
import { toast } from "react-hot-toast";
import { useLoaderContext } from "context/loaderContext";
import { botConfigType } from "types/botconfig.types";
import { getConfig, updateConfig } from "apis/config.api";
import { Switch } from "common/ui/inputs/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import {
  extractMultiLangFromString,
  concatenateURLs,
} from "store/voice/voice.helper";
import VoiceModalWrapper from "common/components/voice/voice-wrapper/VoiceModalWrapper";
import { TVoiceBody } from "common/components/voice/voiceEditor";
import useBotConfigStore from "store/botConfig/botConfig.store";

interface BadWordConfigFormProps {
  setOpen: (open: boolean) => void;
}

const BadWordConfigForm: FC<BadWordConfigFormProps> = ({
  setOpen,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { create_one_badword, update_one_badword, loading } = useBadWordStore();
  const { get_bot_config,botConfig,update_bot_config ,loading:configLoding } = useBotConfigStore();

  const [showModal, setShowModal] = useState(false);
  const voiceDataRef = useRef({
    voice_path_male: "",
    voice_path_female: "",
  });


  const [lang, setLang] = useState<"ar" | "en">("en");

  const [formData, setFormData] = useState<IBadWordConfig>(
    botConfig?.bad_word_config
  );


  const onChangeHandler = (
    key: keyof IBadWordConfig,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };


  const handleSubmit = () => {
      update_bot_config({ bot_id: bot.bot_id, bad_word_config: formData })
  };
  const handleUpdateVoice = (data: TVoiceBody) => {
    const updatedVoices = data;
    delete updatedVoices.bot_id;
    delete updatedVoices.user_id;
    voiceDataRef.current = {...voiceDataRef.current,...updatedVoices};
  };

  const handleSaveVoice = ()=>{
    setFormData((prev)=>{
      return{
        ...prev,
        ...voiceDataRef.current
      }
    })
    setShowModal(false)
  }

  return (
    <div className="flex flex-col gap-4 py-4 text-white/70">
      <div className="flex items-center justify-between">
        <Switch
          label="Activate Badwords"
          name="includeSearchatBadwords"
          checked={formData?.active || false}
          onChange={(e) => {
            onChangeHandler("active", e.target.checked);
          }}
        />

        <Button
          onClick={() => {
            const language = lang === "en" ? "ar" : "en";
            setLang(language);
          }}
        >
          {lang?.toUpperCase()}
        </Button>
      </div>

      <div className="flex flex-col justify-start items-start gap-1 w-full">
        <label htmlFor="pool" className="text-md whitespace-nowrap">
          Select Bad Words List for Filtering:
        </label>
        <Select
          name="pool"
          key={"pool"}
          value={formData?.pool || 'bot'}
          onValueChange={(value: "both" | "bot") => {
            onChangeHandler("pool", value);
          }}
        >
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Select a type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="both">Include Searchat Default Bad Words </SelectItem>
            <SelectItem value="bot">Include Custom Bad Words Only</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-1 relative">
        <label htmlFor="question" className="text-right flex items-center justify-start gap-2">
        <VoiceModalWrapper
          handleSave={() =>handleSaveVoice()}
          handleUpdateVoice={handleUpdateVoice}
          setShowModal={setShowModal}
          showModal={showModal}
          text={extractMultiLangFromString(formData?.answer || "")?.[lang] || ''}
          voice_path_female={voiceDataRef?.current?.voice_path_female || ""}
          voice_path_male={voiceDataRef?.current?.voice_path_male || ""}
        />

          Bot Answer *
        </label>

 
  
        <div className="!text-black">
          <RichTextEditor
            value={extractMultiLangFromString(formData?.answer || "")?.[lang] || ''}
            onChangeRichText={(value) => {
              const languages = extractMultiLangFromString(
                formData?.answer || ""
              );
              languages[lang] = value;
              onChangeHandler(
                "answer",
                concatenateURLs(languages?.ar || "", languages?.en || "")
              );
            }}
            lang={bot?.language || 'en'}
          />
        </div>
      </div>
      <div className="self-end">
        <Button loading={loading || configLoding} onClick={handleSubmit}>
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default memo(BadWordConfigForm);
