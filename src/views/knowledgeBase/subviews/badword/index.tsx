import { SubPageHeader } from "common/components/headers";
import { MainTable } from "common/components/tables/main.table";
import useConfirmModal from "common/hooks/useConfirmModal";
import searchedArray from "helpers/search";
import { Edit, FileAudio, PlusCircle, Trash2, UploadIcon } from "lucide-react";
import { FC, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useBadWordStore from "store/badword/badword.store";
import createBW from "./components/sheets/createBW";
import UpdateBW from "./components/sheets/updateBW";
import { toast } from "react-hot-toast";
import { CSVLink } from "react-csv";
import { Button } from "common/ui/button";
import { Settings } from "lucide-react";
import { botConfigType } from "types/botconfig.types";

interface BadWordViewProps {
  botConfig: botConfigType;
  setBotConfig: (botConfig: botConfigType) => void;
}

export const BadWordView: FC<BadWordViewProps> = ({botConfig,setBotConfig}) => {
  const {
    delete_one_badword,
    badWords,
    delete_many_badwords,
    fetchLoading,
  } = useBadWordStore();
  const bot = useBotStore((state) => state.bot);

  const confirmModal = useConfirmModal();
  const [keySearch, setKeySearch] = useState("");

  const [renderedBadWords, setRenderedBadWords] = useState([]);
  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  
 

  useEffect(() => {
    setRenderedBadWords(
      searchedArray(keySearch, badWords, ["question", "answer"]) || []
    );
  }, [badWords, keySearch]);




  const badWordsTableActions = [
    {
      label: "Edit",
      component: ({ item }) => (
        <UpdateBW badWord={item}>
          <Edit size={15} className="hover:text-primary cursor-pointer" />
        </UpdateBW>
      ),
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(() =>
          delete_one_badword(item.bad_word_id).then(() => {
            toast.success("Bad Word deleted successfully");
          })
        );
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
  ];

  useEffect(() => {
    if (selected?.length === renderedBadWords?.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selected, renderedBadWords]);

  const onSelect = (e) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(renderedBadWords?.map((st) => st.bad_word_id.toString()));
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(selected.filter((id) => id !== name.split("-")[1]));
        setSelectAll(false);
      }
    }
  };

  const handleDeleteSelected = async () => {
    await delete_many_badwords({
      bot_id: bot.bot_id,
      badwords: [...selected],
    });
    toast.success("Bad Words deleted successfully");

    setSelected([]);
  };

  const exportFile = () => {
    const headers = [
      { label: "Bad Word", key: "bad_word" },
      // { label: "Answer", key: "answer" },
    ];
    let data = [];
    if (badWords?.length > 0) {
      data = [
        ...badWords?.map((a) => {
          return {
            bad_word: a?.bad_word,
            // answer: a?.answer,
          };
        }),
      ];
    }

    const csvReport = {
      data: data,
      headers: headers,
      filename: `${bot.bot_name}-badwords`,
    };

    return csvReport;
  };

  return (
    <div className="space-y-3">
      <SubPageHeader
        title="Bad Word"
        description="Create and manage your chatbot BadWord!"
        search
        searchPlaceholder="Find a Badword..."
        setKeySearch={setKeySearch}
        btn
        btnText="Manage Bad Words"
        btnIcon={Settings}
        btnSheet={createBW}
      />
      
      {badWords?.length !== 0 && (
        <div className="float-right pb-5">
          <CSVLink {...exportFile()}>
            <Button variant="outline" className="glex gap-2 items-center">
              <UploadIcon className="h-4 w-4" /> Export Bad Words
            </Button>
          </CSVLink>
        </div>
      )}
      {selected?.length > 0 && (
        <Button
          onClick={() => {
            confirmModal.setOnConfirm(async () => await handleDeleteSelected());
            confirmModal.setType("delete");
            confirmModal.onOpen();
          }}
          variant="destructive"
          className="float-right mb-3 mr-2"
        >
          <Trash2 size={15} className="mr-2" />
          Delete Selected ({selected?.length})
        </Button>
      )}
      <div className="pt-5">
        <div className=" w-full">
          <MainTable
            loading={fetchLoading}
            data={renderedBadWords?.length > 0 ? [...renderedBadWords] : []}
            columns={[
              {
                name: "Bad Word",
                key: "bad_word",
              },
              // {
              //   name: "Answer",
              //   key: "answer",
              // },
            ]}
            actions={badWordsTableActions}
            checkBoxes
            onSelect={onSelect}
            isSelectAll={selectAll}
            isSelectOne={(id) => selected.includes(id)}
            idKey="bad_word_id"
            itemsPerPage={20}
          />
        </div>
      </div>
    </div>
  );
};
