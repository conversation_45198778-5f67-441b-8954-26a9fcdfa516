import { SubPageHeader } from "common/components/headers";
import { PlusCircle } from "lucide-react";
import { CreateDialog, DialogCard } from "./components";
import useDialogStore from "store/dialog/dialog.store";
import { useEffect, useMemo, useState } from "react";
import searchedArray from "helpers/search";
import Pagination from "common/ui/pagination";
import useTOPICStore from "store/topic/topic.store";

export const DialogsView: React.FC = ({}) => {
  const [keySearch, setKeySearch] = useState("");
  const { dialogs, dialog, loading } = useDialogStore();
  const [showModal, setShowModal] = useState(false);
  const [dialogToUpdate, setDialogToUpdate] = useState(null);
  const [dialogToDuplicate, setDialogToDuplicate] = useState(null);
  const itemsPerPage = 16;
  const [renderedDialogs, setrenderedDialogs] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const { topic, topics } = useTOPICStore();
  useEffect(() => {
    setrenderedDialogs(searchedArray(keySearch, dialogs));
  }, [keySearch, dialogs]);

  useEffect(() => {
    if (
      currentPage > 1 &&
      renderedDialogs.length <= (currentPage - 1) * itemsPerPage
    ) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage, renderedDialogs, itemsPerPage]);

  const currentData = useMemo(() => {
    const firstPageIndex = (currentPage - 1) * itemsPerPage;
    const lastPageIndex = firstPageIndex + itemsPerPage;
    const crd = renderedDialogs?.slice(firstPageIndex, lastPageIndex);

    if (topics?.length && Object.keys(topic || {})?.length) {
      return crd?.filter((dialog) => dialog.topic_id === topic.topic_id);
    }

    return crd;
  }, [currentPage, renderedDialogs, keySearch, topic, topics]);

  return (
    <>
      {showModal && (
        <CreateDialog
          showModal={showModal}
          setShowModal={setShowModal}
          dialogToUpdate={dialogToUpdate}
          dialogToDuplicate={dialogToDuplicate}
          setDialogToUpdate={setDialogToUpdate}
          setDialogToDuplicate={setDialogToDuplicate}
        />
      )}
      <div className="flex flex-col gap-4">
        <SubPageHeader
          title="Dialogs Composer"
          description="Make your processes conversational."
          search
          searchPlaceholder="Find a dialog..."
          setKeySearch={setKeySearch}
          btn
          btnText="Compose New Dialog"
          btnIcon={PlusCircle}
          btnOnClick={() => setShowModal(true)}
        />
        <div className="grid grid-cols-1 md:grid-cols-2  xl:grid-cols-4 gap-4">
          {currentData?.map((dialog, i) => (
            <DialogCard
              key={i}
              dialog={dialog}
              setDialogToUpdate={setDialogToUpdate}
              setDialogToDuplicate={setDialogToDuplicate}
              setShowModal={setShowModal}
            />
          ))}
        </div>
        <Pagination
          currentPage={currentPage}
          totalCount={renderedDialogs?.length}
          pageSize={itemsPerPage}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </>
  );
};
