import React, { useEffect, useState } from "react";
import { MoreHorizontal, Pencil } from "lucide-react";
import { But<PERSON> } from "common/ui/button";
import { IDialog } from "store/dialog/dialog.types";
import useDialogStore from "store/dialog/dialog.store";
import useBotStore from "store/bot/bot.store";
import useConfirmModal from "common/hooks/useConfirmModal";
import DialogContextMenu from "./dialogContextMenu";
import DialogDropDown from "./dialogDropDown";
import { extractLangInOneLine } from "helpers/multiLang";
import useTriggerStore from "store/trigger/trigger.store";
import { FullModal } from "common/components/modals";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import useTOPICStore from "store/topic/topic.store";
import { toast } from "react-hot-toast";
import { getIsLiveURL } from "apis/dialog.api";
interface Props {
  dialog: IDialog;
  setDialogToUpdate: (dialog: IDialog) => void;
  setDialogToDuplicate: (dialog: IDialog) => void;
  setShowModal: (value: boolean) => void;
}

const DialogCard: React.FC<Props> = ({
  dialog,
  setDialogToUpdate,
  setDialogToDuplicate,
  setShowModal,
}) => {
  const { updateOne, getAll, deleteOne } = useDialogStore();
  const bot = useBotStore((state) => state.bot);
  const confirmModal = useConfirmModal();
  const [loading, setLoading] = useState(false);
  const { topics } = useTOPICStore();

  const { triggers, update_many_triggers } = useTriggerStore();
  const onDelete = async () => {
    await deleteOne(dialog.dialog_id);
    getAll(bot.bot_id);
  };
  const [editTopicModal, setEditTopicModal] = useState(false);
  const [selectedTopic, setSlectedTopic] = useState<string | null>(null);
  useEffect(() => {
    setSlectedTopic(dialog?.topic_id ? String(dialog?.topic_id) : null);
  }, [dialog]);
  const menusProps = () => {
    const handleRename = () => {
      setDialogToUpdate(dialog);
      setShowModal(true);
    };

    const handleDuplicate = async () => {
      const temp = await getIsLiveURL(dialog.dialog_id)
      setDialogToDuplicate(temp);
      setShowModal(true);
    };

    const handleDelete = () => {
      confirmModal.onOpen();
      confirmModal.setOnConfirm(async () => await onDelete());
    };

    return {
      handleRename,
      handleDuplicate,
      handleDelete,
    };
  };

  const handleChangeTopic = async () => {
    setLoading(true);
    const allTriggerForDialog = triggers.filter((t) => t.url === dialog.url);
    await update_many_triggers(
      allTriggerForDialog.map((t) => ({
        ...t,
        topic_id: selectedTopic ? +selectedTopic : null,
      }))
    );
    await updateOne({
      ...dialog,
      topic_id: selectedTopic ? +selectedTopic : null,
    });
    getAll(bot.bot_id);
    setEditTopicModal(false);
    setLoading(false);
    toast.success("Topic Changed");
  };

  return (
    <>
      <FullModal
        title={"Manage Topic For" + ' "' + dialog.dialog_name + '" ' + "Dialog"}
        isOpen={editTopicModal}
        onClose={() => {
          setEditTopicModal(false);
        }}
      >
        <div className="w-full flex justify-between items-end">
          <div className="">
            <p>Select </p>
            <Select
              value={selectedTopic}
              onValueChange={(value) => {
                // set_topic(value);
                setSlectedTopic(value);
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={null}>No Topic</SelectItem>
                {topics.map((topic, i) => {
                  return (
                    <SelectItem key={i} value={topic.topic_id.toString()}>
                      {extractLangInOneLine(topic.display_name)}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={handleChangeTopic}
            disabled={
              !selectedTopic
                ? +selectedTopic != dialog.topic_id
                  ? false
                  : true
                : +selectedTopic == dialog.topic_id
            }
            loading={loading}
          >
            Change Topic
          </Button>
        </div>
      </FullModal>
      <DialogContextMenu {...menusProps()}>
        <div className="group w-full mb-5 bg-secondary border-2 border-white/25 shadow-2xl rounded-lg">
          <div className="space-y-2 p-3">
            <div className="font-bold  overflow-auto w-full">
              {dialog?.dialog_name ? dialog.dialog_name : "Untitled"}
            </div>
            <div key={dialog?.topic_id} className="flex gap-3 items-center">
              {dialog?.topic_id
                ? extractLangInOneLine(
                    topics.find((t) => t.topic_id == dialog.topic_id)
                      ?.display_name
                  )
                : "No Topic"}
              <Pencil
                onClick={() => {
                  setEditTopicModal(true);
                }}
                className="cursor-pointer"
                size={17}
              />
            </div>
          </div>
          <div className="border-t-2 border-white/50 p-2">
            <img
              src={
                dialog?.dialog_image
                  ? dialog.dialog_image.replace("&image=true", "")
                  : "https://wallpapers.com/images/hd/high-resolution-black-background-6803kw3b5utzklpr.jpg"
              }
              alt=""
              className="w-full h-48 object-cover"
            />
          </div>
          <div className="bg-primary/50 p-2 flex justify-between items-center rounded-b-lg">
            <Button
              variant="outline"
              className="hover:backdrop-brightness-110"
              size="sm"
              onClick={() =>
                window.open("/composer/" + dialog.dialog_id, "_blank")
              }
            >
              Open
            </Button>
            <div className=" flex gap-3">
              <DialogDropDown {...menusProps()}>
                <MoreHorizontal />
              </DialogDropDown>
            </div>
          </div>
        </div>
      </DialogContextMenu>
    </>
  );
};

export default DialogCard;
