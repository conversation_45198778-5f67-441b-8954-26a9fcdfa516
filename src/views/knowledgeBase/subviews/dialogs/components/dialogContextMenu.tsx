import { FC, memo } from "react";
import {
  ContextMenu,
  ContextMenuCheckboxItem,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuLabel,
  ContextMenuRadioGroup,
  ContextMenuRadioItem,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
} from "common/ui/contextMenu";
import { IChildren } from "types/common.types";

interface DialogContextMenuProps extends IChildren {
  handleRename: () => void;
  handleDuplicate: () => void;
  handleDelete: () => void;
}

const DialogContextMenu: FC<DialogContextMenuProps> = ({
  children,
  handleRename,
  handleDuplicate,
  handleDelete,
}) => {
  return (
    <>
      <ContextMenu>
        <ContextMenuTrigger asChild className="pointer-events-auto">{children}</ContextMenuTrigger>
        <ContextMenuContent className="w-32">
          <ContextMenuItem inset>Open</ContextMenuItem>
          <ContextMenuItem onClick={handleRename} inset>Rename</ContextMenuItem>
          <ContextMenuItem onClick={handleDuplicate} inset>Duplicate</ContextMenuItem>
          <ContextMenuItem onClick={handleDelete} inset>Delete</ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </>
  );
};

export default memo(DialogContextMenu);
