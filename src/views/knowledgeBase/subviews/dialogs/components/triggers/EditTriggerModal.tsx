import { FullModal } from "common/components/modals";
import React, { useEffect, useState } from "react";
import { IExtendedTrigger } from "./TriggersTable";
import { Input } from "common/ui/inputs/input";
import { updateOne } from "apis/trigger.api";
import toast from "react-hot-toast";
import { getTriggerbydialoId } from "apis/dialog.api";
type EditTriggerModalProps = {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  selectedTrigger: IExtendedTrigger | null;
  setUpdatedTriggers: React.Dispatch<React.SetStateAction<IExtendedTrigger[]>>;
};
const EditTriggerModal: React.FC<EditTriggerModalProps> = ({
  showModal,
  setShowModal,
  selectedTrigger,
  setUpdatedTriggers,
}) => {
  const [formData, setFormData] = useState<Partial<IExtendedTrigger>>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedTrigger?.dialog_id) {
      setFormData((prev) => {
        return { ...selectedTrigger };
      });
    }
  }, [selectedTrigger]);
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();

    const { name, value } = e.target;

    setFormData((prev) => {
      return {
        ...prev,
        [name]: value,
      };
    });
  };

  const onSaveHandler = async () => {
    if (selectedTrigger.trigger === formData.trigger || !formData.trigger) {
      toast.error(`please select different trigger name`);
      return;
    }
    setLoading(true);

    setUpdatedTriggers((prev) => {
      const updatedTriggers = prev.map((trigger) => {
        if (trigger.trigger_id === selectedTrigger?.trigger_id) {
          return {
            ...trigger,
            trigger: formData.trigger,
          };
        } else {
          return trigger;
        }
      });
      return updatedTriggers;
    });

    try {
      await updateOne({
        ...formData,
        trigger_id: selectedTrigger.trigger_id,
        trigger: formData?.trigger,
      });
      toast.success(`Updated successfully`);
    } catch (error) {
    }
    setShowModal(false)
    setLoading(false);
  };

  return (
    <FullModal
    className="text-white  min-w-[60%] "
      title={
        "Dialog (" +
        selectedTrigger?.dialog_name +
        ") trigger (" +
        selectedTrigger?.trigger +
        ")"
      }
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      //   disabled={!isChanged}
      onSave={onSaveHandler}
      loading={loading}
      footer
    >
      <div className="flex flex-col justify-center items-center   w-full gap-2 text-white">
        <div className="w-[100%] mb-4">
          <p className="text-lg my-2 text-white">
            Update the Trigger here. Click save when you&apos;re done.

          </p>
          <Input
            name="trigger"
            title={"Trigger:"}
            value={formData?.trigger || ""}
            onChange={handleChange}
          />
        </div>
      </div>
    </FullModal>
  );
};

export default EditTriggerModal;
