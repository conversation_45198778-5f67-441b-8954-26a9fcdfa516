import React from 'react'
import { <PERSON><PERSON> } from "common/ui/button"


import { Sheet,<PERSON><PERSON><PERSON>rigger,<PERSON>etC<PERSON>nt,<PERSON><PERSON><PERSON><PERSON>er,SheetTitle,SheetDescription,SheetFooter } from 'common/ui/sheet'
import TriggersTable from './TriggersTable'

const TriggersSheet = () => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline">Triggers</Button>
      </SheetTrigger>
      <SheetContent className='w-4/6'>
        <SheetHeader>
          <SheetTitle>Edit triggers</SheetTitle>
          <SheetDescription>
            Make changes to your triggers here.
          </SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 py-4 w-[100%]">
          <TriggersTable/>
        </div>

      </SheetContent>
    </Sheet>
  )
}

export default TriggersSheet