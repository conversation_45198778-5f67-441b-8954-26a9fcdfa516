import { getOne, getOneByUrl } from "apis/dialog.api";
import { getFile } from "apis/file.api";
import { MainTable } from "common/components/tables/main.table";
import { Card } from "common/ui/card";
import { Edit, View } from "lucide-react";

import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useTriggerStore from "store/trigger/trigger.store";
import { ITrigger } from "store/trigger/trigger.types";
import EditTriggerModal from "./EditTriggerModal";
import { CustomSearch } from "common/ui/inputs/search";
import useDialogStore from "store/dialog/dialog.store";
import { IDialog } from "store/dialog/dialog.types";
import { getBotTriggers } from "apis/trigger.api";
export interface IExtendedTrigger extends ITrigger {
  dialog_name: string;
  dialog_id: number;
  dialog: IDialog;
}
const TriggersTable = () => {
  const { get_all_triggers, triggers } = useTriggerStore();
  const { getAll ,dialogs } = useDialogStore();
  const { bot } = useBotStore();
  const [keySearch, setKeySearch] = useState("");

  const [updatedTriggers, setUpdatedTriggers] = useState<IExtendedTrigger[]>(
    []
  );
  const [triggersToRender, setTriggersToRender] = useState<IExtendedTrigger[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedTrigger, setSelectedTrigger] =
    useState<IExtendedTrigger | null>(null);
    
  useEffect(() => {
    getTableTriggers();
  }, [bot, get_all_triggers, getBotTriggers]);

  const getTableTriggers =async()=>{
    const temp = await getBotTriggers(bot);
    console.log("first", temp)
    const triggers = temp.map(tr => {
      return {
        ...tr,
        dialog_name: tr.dialog?.dialog_name
      }
    })
    setUpdatedTriggers(triggers)
    setTriggersToRender(triggers)
    console.log(triggers);
    return triggers;
  }
  
  const TableActions = [
    {
      label: "Edit",
      onClick: (item) => {
        setSelectedTrigger(item);
        setShowModal(true);
      },
      icon: Edit,
    },
    {
      label: "View",
      onClick: (item: IExtendedTrigger) => {
        setSelectedTrigger(item);
        window.open("/composer/" + item.dialog_id, "_blank");
      },
      icon: View,
    },
  ];

  

  useEffect(() => {
    if (updatedTriggers?.length > 0) {
      setTriggersToRender((prev) => {
        const triggersToRender = [...updatedTriggers];
        console.log("bgfdsa", triggersToRender)
        console.log("fghjk", updatedTriggers)
        triggersToRender.sort((a, b) => {
          return a.dialog.dialog_name?.localeCompare(b.dialog.dialog_name);
      });
      return triggersToRender?.filter((trigger) => {
          console.log("dialog.name", trigger.dialog.dialog_name)
          return (
            trigger?.trigger?.includes(keySearch) ||
            trigger?.trigger_name?.includes(keySearch) ||
            trigger?.dialog.dialog_name?.includes(keySearch)
          );
        });
      }); 
      console.log("123",triggersToRender)
    }
  }, [updatedTriggers, keySearch]);

  return (
    <div className="">
      <EditTriggerModal
        selectedTrigger={selectedTrigger}
        setShowModal={setShowModal}
        showModal={showModal}
        setUpdatedTriggers={setUpdatedTriggers}
      />
      <div className="flex w-52 my-2">
        <CustomSearch
          placeholder="Find a trigger..."
          onChange={(value) => setKeySearch(value)}
        />
      </div>
        <MainTable
          key={"Triggers"}
          loading={loading}
          data={triggersToRender}
          columns={[
            {
              name: "Dialog Name",
              key: "dialog_name",
            },
            {
              name: "Trigger",
              key: "trigger",
            },
          ]}
          itemsPerPage={10}
          actions={TableActions}
        />
    </div>
  );
};

export default TriggersTable;