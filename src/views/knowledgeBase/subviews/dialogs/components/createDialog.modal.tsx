import { createOne } from "apis/dialog.api";
import { getFile, uploadFile } from "apis/file.api";
import { FullModal } from "common/components/modals";
import { Input } from "common/ui/inputs/input";
import generateStorageId from "helpers/generateStorageId";
import { FC, memo, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useDialogStore from "store/dialog/dialog.store";
import { IDialog } from "store/dialog/dialog.types";

interface CreateDialogProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  dialogToUpdate: IDialog;
  dialogToDuplicate: IDialog;
  setDialogToUpdate: (dialog: IDialog) => void;
  setDialogToDuplicate: (dialog: IDialog) => void;
}

const CreateDialog: FC<CreateDialogProps> = ({
  showModal,
  setShowModal,
  dialogToUpdate,
  dialogToDuplicate,
  setDialogToUpdate,
  setDialogToDuplicate,
}) => {
  const handleClose = () => {
    setShowModal(false);
    setDialogName("");
    setDialogToUpdate(null);
    setDialogToDuplicate(null);
  };
  const { getAll, updateOne } = useDialogStore();
  const bot = useBotStore((state) => state.bot);
  const [dialogName, setDialogName] = useState("");
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (dialogToUpdate) {
      setDialogName(dialogToUpdate.dialog_name?.slice(0, 50));
    }
    if (dialogToDuplicate) {
      setDialogName("Copy of " + dialogToDuplicate.dialog_name?.slice(0, 42));
    }
  }, [dialogToUpdate, dialogToDuplicate]);

  const handleCreate = () => {
    const path = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;

    setLoading(true);
    if (Boolean(dialogToDuplicate)) {
      getFile(dialogToDuplicate.url).then((res) => {
        if (res) {
          uploadFile({
            path,
            file_body: res.file_data,
          }).then(() => {
            createOne({
              dialog_name: dialogName,
              url: path,
              bot_id: bot.bot_id,
            }).then(() => {
              getAll(bot.bot_id);
              handleClose();
              setLoading(false);
            });
          });
        }
      });
    } else {
      uploadFile({
        path,
        file_body: JSON.stringify({
          entities: {},
          steps: [],
          edges: [],
        }),
      }).then(() => {
        createOne({
          dialog_name: dialogName,
          url: path,
          bot_id: bot.bot_id,
        }).then((data) => {
          getAll(bot.bot_id);
          handleClose();
          if (data && !data.message) {
            setLoading(false);
            window.open(`/composer/${data.dialog_id}`, "_blank");
          }
        });
      });
    }
  };

  const handleUpdate = () => {
    setLoading(true);
    updateOne({
      ...dialogToUpdate,
      dialog_name: dialogName,
      bot_id: bot.bot_id,
    }).then(() => {
      getAll(bot.bot_id);
      setLoading(false);
      handleClose();
    });
  };

  const onSave = () => {
    if (dialogToUpdate) {
      handleUpdate();
    } else {
      handleCreate();
    }
  };

  return (
    <FullModal
      title={
        dialogToUpdate
          ? "Update Dialog"
          : dialogToDuplicate
          ? "Duplicate Dialog"
          : "Create Dialog"
      }
      onClose={handleClose}
      isOpen={showModal}
      footer
      disabled={
        loading || dialogToUpdate
          ? dialogToUpdate?.dialog_name === dialogName
          : dialogName.trim() === ""
      }
      onSave={onSave}
      loading={loading}
    >
      <Input
        title="Dialog Name"
        placeholder="Dialog Name"
        name="dialog_name"
        value={dialogName}
        onChange={(e) => setDialogName(e.target.value.slice(0, 50))}
      />
    </FullModal>
  );
};

export default memo(CreateDialog);
