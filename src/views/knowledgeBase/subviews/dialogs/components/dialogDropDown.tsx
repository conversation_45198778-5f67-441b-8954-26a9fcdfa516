import React, { FC, memo } from "react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "common/ui/dropdownMenu";
import { IChildren } from "types/common.types";

interface DialogDropDownProps extends IChildren {
  handleRename: () => void;
  handleDuplicate: () => void;
  handleDelete: () => void;
}

const DialogDropDown: FC<DialogDropDownProps> = ({
  children,
  handleRename,
  handleDuplicate,
  handleDelete,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="pointer-events-auto">
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem>Open</DropdownMenuItem>
        <DropdownMenuItem onClick={handleRename}>Rename</DropdownMenuItem>
        <DropdownMenuItem onClick={handleDuplicate}>Duplicate</DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete}>Delete</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default memo(DialogDropDown);
