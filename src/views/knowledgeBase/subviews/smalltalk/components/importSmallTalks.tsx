import { Button } from "common/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { useLoaderContext } from "context/loaderContext";
import { Loader2, Upload } from "lucide-react";
import { FC, memo, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useCSVReader } from "react-papaparse";
import useBotStore from "store/bot/bot.store";
import useSmallTalkStore from "store/smalltalk/smalltalk.store";

interface ImportSmallTalksProps {}

const ImportSmallTalks: FC<ImportSmallTalksProps> = ({}) => {
  const bot = useBotStore((state) => state.bot);
  const {create_many_smalltalks, delete_all_smalltalks} = useSmallTalkStore();
  const [CSVdata, setCSVdata] = useState([]);
  const [showMapperModel, setshowMapperModel] = useState(false);
  const { CSVReader } = useCSVReader();

  const convertCSVToJSON = (file) => {
    const data_json = [];

    if (file[0].length < 2) {
      toast.error(
        "Faild to import. Your csv file should include both question and answer columns."
      );
      return [];
    }
    const headerRow = file[0];
    
    file.slice(1).forEach((entity) => {
      const obj = {};
      headerRow.forEach((columnName, index) => {
        obj[columnName] = entity[index];
      });
      data_json.push(obj);
    });

    return data_json;
  };

  const handleOnFileLoad = (data) => {
    const json_Data = convertCSVToJSON(data);

    if (json_Data.length !== 0) {
      setCSVdata(json_Data);

      setshowMapperModel(true);
    } else {
      toast.error("No data found in the file");
    }
  };

  const [loading, setLoading] = useState(false);
  const {loader} = useLoaderContext();

  const [mapper, setMapper] = useState({
    Question: "",
    Answer: "",
  });
  const [addOrDelete, setAddOrDeleteOprtion] = useState({
    type: "add",
  });

  useEffect(() => {
    if (CSVdata && CSVdata.length) {
      setMapper({
        Question: Object.keys(CSVdata[0])[0],
        Answer: Object.keys(CSVdata[0])[0],
      });
    }
  }, [CSVdata]);

  const onSaveHandler = async () => {
    if (addOrDelete.type === "delete_all") {
      setLoading(true);
      loader?.continuousStart();
      await delete_all_smalltalks(bot.bot_id);
      const smallTalksFromMapping = [
        ...CSVdata.map((a) => {
          return {
            question: a[mapper["Question"]],
            answer: a[mapper["Answer"]],
          };
        }),
      ];
      if (smallTalksFromMapping.length) {
        const ST_JSON = smallTalksFromMapping;
        setTimeout(() => {
          const smalltalks_size = smallTalksFromMapping.length;
          const patchesNum = Math.ceil(smalltalks_size / 50);

          if (patchesNum >= 1) {
            var patches = [];

            patches.push([...ST_JSON]);
            patches.map((patch, index) =>
              create_many_smalltalks([
                ...patch
                  .filter(
                    (a) =>
                      typeof a.question === "string" &&
                      typeof a.answer === "string" &&
                      a.question !== "" &&
                      a.answer !== ""
                  )
                  .map((a) => {
                    return {
                      ...a,
                      bot_id: bot.bot_id,
                    };
                  }),
              ])
            );
          }
          toast.success(`${ST_JSON.length - 1} have been imported successfully.`);

          setLoading(false);
          loader?.complete();
          setshowMapperModel(false);
        }, 3000);
      }
    } else {
      setLoading(true);
      loader?.continuousStart();
      const smallTalksFromMapping = [
        ...CSVdata.map((a) => {
          return {
            question: a[mapper["Question"]],
            answer: a[mapper["Answer"]],
          };
        }),
      ];
      if (smallTalksFromMapping.length) {
        const ST_JSON = smallTalksFromMapping;
        setTimeout(() => {
          const smalltalks_size = smallTalksFromMapping.length;
          const patchesNum = Math.ceil(smalltalks_size / 50);

          if (patchesNum >= 1) {
            var patches = [];
            for (var i = 1; i < patchesNum; i++) {
              patches.push([...ST_JSON].slice((i - 1) * 50, i * 50));
            }
            patches.push(
              [...ST_JSON].slice((patchesNum - 1) * 50, smalltalks_size)
            );
            patches.map((patch, index) =>
              create_many_smalltalks([
                ...patch
                  .filter(
                    (a) =>
                      typeof a.question === "string" &&
                      typeof a.answer === "string" &&
                      a.question !== "" &&
                      a.answer !== ""
                  )
                  .map((a) => {
                    return {
                      ...a,
                      bot_id: bot.bot_id,
                    };
                  }),
              ])
            );
          }
          toast.success(`${ST_JSON.length - 1} have been imported successfully.`);

          setLoading(false);
          loader?.complete();
          setshowMapperModel(false);
        }, 3000);
      }
    }
  };

  return (
    <div className="text-white space-y-5">
      <div className="p-2 bg-secondary rounded">
        Import Small Talks from CSV file that has two columns, one for the question and
        the other for the answer.
      </div>
      <CSVReader
        onUploadAccepted={(results: any) => {
          handleOnFileLoad(results.data);
        }}
      >
        {({
          getRootProps,
          acceptedFile,
          ProgressBar,
          getRemoveFileProps,
        }: any) => (
          <div {...getRootProps()}>
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" /> Browse Files
            </Button>
          </div>
        )}
      </CSVReader>
      {showMapperModel && (
        <div className="flex flex-col space-y-5 border border-white/50 rounded p-5">
          {Object.keys(mapper).map((entity, i) => (
            <div
              key={i}
              className="w-full flex justify-between gap-2 items-center "
            >
              <label htmlFor="Unit" className="block  font-medium pb-1">
                {entity}
              </label>
              <div className="w-1/2">
                <Select
                  value={mapper[entity]}
                  onValueChange={(value) => {
                    const mapper_to_update = { ...mapper };
                    mapper_to_update[entity] = value;
                    setMapper({ ...mapper_to_update });
                  }}
                >
                  <SelectTrigger className="w-full ">
                    <SelectValue placeholder="Unit" />
                  </SelectTrigger>
                  <SelectContent>
                    {CSVdata.length > 0
                      ? Object.keys(CSVdata[0]).map((a, index) => (
                          <SelectItem key={index} value={a}>
                            {a}
                          </SelectItem>
                        ))
                      : null}
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
          <div className="w-full flex justify-between gap-2 items-center">
            <label htmlFor="Unit" className="block  font-medium pb-1">
              Option
            </label>
            <div className="w-1/2">
              <Select
                value={addOrDelete.type}
                onValueChange={(value) => {
                  if (value == "delete_all") {

                    setAddOrDeleteOprtion({
                      ...addOrDelete,
                      type: "delete_all",
                    });
                  }
                  if (value == "add") {
                    setAddOrDeleteOprtion({ ...addOrDelete, type: "add" });
                  }
                }}
              >
                <SelectTrigger className="w-full ">
                  <SelectValue placeholder="Unit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">Add To Previous SmallTalks</SelectItem>
                  <SelectItem value="delete_all">
                    Delete All Previous SmallTalks{" "}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <hr className="text-white/25" />
          <div className="flex gap-3 self-end">
            <Button
              variant="outline"
              onClick={() => {
                setshowMapperModel(false);
                setCSVdata([]);
              }}
            >
              Cancel
            </Button>
            <Button disabled={loading} onClick={onSaveHandler}>
              {loading ? <Loader2 className="animate-spin mr-2 h-4 w-4" /> : ""}
              Import
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(ImportSmallTalks);
