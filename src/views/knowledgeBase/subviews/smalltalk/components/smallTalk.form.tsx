import { FC, memo, useState } from "react";
import {
  CreateSmallTalkSchema,
  CreateSmallTalkType,
  ISmallTalk,
} from "store/smalltalk/smalltalk.types";
import { UserQuestion } from "views/knowledgeBase/components";
import helper from "../helper";
import useBotStore from "store/bot/bot.store";
import RichTextEditor from "common/ui/inputs/richtextedtor";
import { ShieldClose } from "lucide-react";
import { Button } from "common/ui/button";
import useSmallTalkStore from "store/smalltalk/smalltalk.store";
import { toast } from "react-hot-toast";
import { useLoaderContext } from "context/loaderContext";
import checkForErrors from "helpers/forms";
import useVoiceStore from "store/voice/voice.store";
import { generateVoicePath } from "store/voice/voice.calls";

interface SmallTalkFormProps {
  setOpen: (open: boolean) => void;
  isUpdate?: boolean;
  smallTalk?: ISmallTalk;
}

const SmallTalkForm: FC<SmallTalkFormProps> = ({
  setOpen,
  isUpdate,
  smallTalk,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { create_one_smalltalk, update_one_smalltalk, loading } =
    useSmallTalkStore();
  const [formData, setFormData] = useState(
    helper.initSmallTalkFormData(bot.bot_id, smallTalk ? smallTalk : null)
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { loader } = useLoaderContext();
  const { selectedVoices } = useVoiceStore();
  const [generatingAudio, setGeneratingAudio] = useState(false);

  const validateField =
    (field: keyof CreateSmallTalkType) =>
    (value: unknown): string => {
      const parsedResult = CreateSmallTalkSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key: keyof CreateSmallTalkType, value: string) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleCreate = async () => {
    loader?.continuousStart();
    setGeneratingAudio(true);
    const voice_path_male = await generateVoicePath(
      selectedVoices,
      formData.answer,
      "male",
      bot.file_name
    );
    const voice_path_female = await generateVoicePath(
      selectedVoices,
      formData.answer,
      "female",
      bot.file_name
    );
    setGeneratingAudio(false);

    create_one_smalltalk({
      ...formData,
      voice_path_male,
      voice_path_female,
    }).then(() => {
      toast.success("Small Talk created successfully");
      setOpen(false);
      loader?.complete();
    });
  };

  const handleUpdate = () => {
    loader?.continuousStart();
    update_one_smalltalk(formData).then(() => {
      toast.success("Small Talk updated successfully");
      setOpen(false);
      loader?.complete();
    });
  };

  const handleSubmit = () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateSmallTalkSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;

    if (isUpdate) {
      handleUpdate();
    } else {
      handleCreate();
    }
  };

  return (
    <div className="flex flex-col gap-4 py-4 text-white/70">
      <UserQuestion
        smallTalk={smallTalk}
        onChange={(e) => onChangeHandler("question", e.target.value)}
        value={formData.question}
        error={errors.question}
      />
      <div className="space-y-1 relative">
        <label htmlFor="question" className="text-right">
          Bot Answer *
        </label>

        <div className="!text-black">
          <RichTextEditor
            value={formData.answer}
            onChangeRichText={(value) => onChangeHandler("answer", value)}
            lang={bot.language}
          />
        </div>
        {errors.answer ? (
          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
            <ShieldClose size={12} />
            {errors.answer}
          </span>
        ) : null}
      </div>
      <div className="self-end">
        <Button loading={loading || generatingAudio} onClick={handleSubmit}>
          {isUpdate ? "Save Changes" : "Create Small Talk"}
        </Button>
      </div>
    </div>
  );
};

export default memo(SmallTalkForm);
