import { FC, memo, useState } from "react";

import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";
import { IChildren } from "types/common.types";
import SmallTalkForm from "../smallTalk.form";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";

interface UpdateSTProps extends IChildren {
    smallTalk: ISmallTalk;
}

const UpdateST: FC<UpdateSTProps> = ({ children, smallTalk }) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Update SmallTalk</SheetTitle>
          <SheetDescription>
            Update your SmallTalk here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <SmallTalkForm setOpen={setOpen} isUpdate smallTalk={smallTalk} />
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default memo(UpdateST);
