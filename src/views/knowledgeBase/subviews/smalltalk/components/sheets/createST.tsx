import { FC, memo, useState } from "react";

import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { IChildren } from "types/common.types";
import ImportSmallTalks from "../importSmallTalks";
import SmallTalkForm from "../smallTalk.form";

interface CreateSTProps extends IChildren {}

const CreateST: FC<CreateSTProps> = ({ children }) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Add a SmallTalk</SheetTitle>
          <SheetDescription>
            Create your SmallTalk here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <Tabs defaultValue="create" className="w-full mt-3">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">Create SmallTalk</TabsTrigger>
            <TabsTrigger value="import">Import SmallTalks</TabsTrigger>
          </TabsList>
          <TabsContent value="create">
            <SmallTalkForm setOpen={setOpen} />
          </TabsContent>
          <TabsContent value="import" className="h-[560px]">
            <ImportSmallTalks />
          </TabsContent>
        </Tabs>

        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default memo(CreateST);
