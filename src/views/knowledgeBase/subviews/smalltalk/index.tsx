import { SubPageHeader } from "common/components/headers";
import { MainTable } from "common/components/tables/main.table";
import useConfirmModal from "common/hooks/useConfirmModal";
import searchedArray from "helpers/search";
import { Edit, FileAudio, PlusCircle, Trash2, UploadIcon } from "lucide-react";
import { FC, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useSmallTalkStore from "store/smalltalk/smalltalk.store";
import createST from "./components/sheets/createST";
import UpdateST from "./components/sheets/updateST";
import { toast } from "react-hot-toast";
import { CSVLink } from "react-csv";
import { Button } from "common/ui/button";
import VoiceSheetWrapper from "common/components/voice/voice-wrapper/VoiceSheetWrapper";
import { ISmallTalk } from "store/smalltalk/smalltalk.types";

interface SmallTalkViewProps {}

export const SmallTalkView: FC<SmallTalkViewProps> = ({}) => {
  const {
    get_all_smalltalks,
    delete_one_smalltalk,
    smallTalks,
    delete_many_smalltalks,
    fetchLoading,
  } = useSmallTalkStore();
  const bot = useBotStore((state) => state.bot);
  const confirmModal = useConfirmModal();
  const [keySearch, setKeySearch] = useState("");

  const [renderedSmallTalks, setRenderedSmallTalks] = useState([]);
  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  
  useEffect(() => {
    get_all_smalltalks(bot.bot_id);
  }, [bot]);

  useEffect(() => {
    setRenderedSmallTalks(
      searchedArray(keySearch, smallTalks, ["question", "answer"]) || []
    );
  }, [smallTalks, keySearch]);


  const { update_one_smalltalk } = useSmallTalkStore();

  const handleUpdate = (data:{voice_path_female:string,voice_path_male:string},smallTalk:ISmallTalk) => {
    update_one_smalltalk({...data,small_talk_id:smallTalk.small_talk_id})
  };

  const smallTalksTableActions = [
    {
      label: "Edit Voice",
      component: ({ item }) => (
        <VoiceSheetWrapper handleUpdate={handleUpdate} item={item} answer={item?.answer}/>
      ),
    },
    {
      label: "Edit",
      component: ({ item }) => (
        <UpdateST smallTalk={item}>
          <Edit size={15} className="hover:text-primary cursor-pointer" />
        </UpdateST>
      ),
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(() =>
          delete_one_smalltalk(item.small_talk_id).then(() => {
            toast.success("Small Talk deleted successfully");
          })
        );
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
  ];

  useEffect(() => {
    if (selected?.length === renderedSmallTalks?.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selected, renderedSmallTalks]);

  const onSelect = (e) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(
          renderedSmallTalks?.map((st) => st.small_talk_id.toString())
        );
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(selected.filter((id) => id !== name.split("-")[1]));
        setSelectAll(false);
      }
    }
  };

  const handleDeleteSelected = async () => {
    await delete_many_smalltalks({
      bot_id: bot.bot_id,
      smalltalks: [...selected],
    });
    toast.success("Small Talks deleted successfully");

    setSelected([]);
  };

  const exportFile = () => {
    const headers = [
      { label: "Question", key: "question" },
      { label: "Answer", key: "answer" },
    ];

    const data = [
      ...smallTalks?.map((a) => {
        return {
          question: a?.question,
          answer: a?.answer,
        };
      }),
    ];

    const csvReport = {
      data: data,
      headers: headers,
      filename: `${bot.bot_name}-samlltalks`,
    };

    return csvReport;
  };

  return (
    <div className="space-y-3">
      <SubPageHeader
        title="Small Talk"
        description="Create and manage your chatbot SmallTalk!"
        search
        searchPlaceholder="Find a smalltalk..."
        setKeySearch={setKeySearch}
        btn
        btnText="Add SmallTalk"
        btnIcon={PlusCircle}
        btnSheet={createST}
      />
      {smallTalks.length !== 0 && (
        <div className="float-right pb-5">
          <CSVLink {...exportFile()}>
            <Button variant="outline" className="glex gap-2 items-center">
              <UploadIcon className="h-4 w-4" /> Export SmallTalks
            </Button>
          </CSVLink>
        </div>
      )}
      {selected?.length > 0 && (
        <Button
          onClick={() => {
            confirmModal.setOnConfirm(async () => await handleDeleteSelected());
            confirmModal.setType("delete");
            confirmModal.onOpen();
          }}
          variant="destructive"
          className="float-right mb-3 mr-2"
        >
          <Trash2 size={15} className="mr-2" />
          Delete Selected ({selected?.length})
        </Button>
      )}
      <div className="pt-5">
        <div className=" w-full">
          <MainTable
            loading={fetchLoading}
            data={renderedSmallTalks?.length > 0 ? [...renderedSmallTalks] : []}
            columns={[
              {
                name: "Question",
                key: "question",
              },
              {
                name: "Answer",
                key: "answer",
              },
            ]}
            actions={smallTalksTableActions}
            checkBoxes
            onSelect={onSelect}
            isSelectAll={selectAll}
            isSelectOne={(id) => selected.includes(id)}
            idKey="small_talk_id"
            itemsPerPage={20}
          />
        </div>
      </div>
    </div>
  );
};
