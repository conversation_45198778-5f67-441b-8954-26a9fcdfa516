import { ISmallTalk } from "store/smalltalk/smalltalk.types";

const initSmallTalkFormData = (bot_id: number, smallTalk?: ISmallTalk) => {
  return {
    question: smallTalk?.question ? smallTalk.question : "",
    answer: smallTalk?.answer ? smallTalk.answer : "",
    bot_id: bot_id,
    small_talk_id: smallTalk?.small_talk_id ? smallTalk.small_talk_id : 0,
  };
};

export default {
  initSmallTalkFormData,
};
