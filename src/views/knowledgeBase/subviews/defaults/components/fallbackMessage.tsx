import { But<PERSON> } from "common/ui/button";
import { Card } from "common/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import toast from "react-hot-toast";
import { Trash2 } from "lucide-react";
import { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import useTriggerStore from "store/trigger/trigger.store";
import { useLoaderContext } from "context/loaderContext";
import {getFallback, removeFallback, setFallback} from "apis/dialog.api"
import useDialogStore from "store/dialog/dialog.store";
import { updateOne } from "apis/bot.api";

export const FallbackMessage = () => {
  const bot = useBotStore((state) => state.bot);
  const dialogsFromStore = useDialogStore((state) => state.dialogs);
  const triggers = useTriggerStore((state) => state.triggers);

  const [loading, setLoading] = useState(false);
  const {loader} = useLoaderContext();

  const [dialogs, setDialogs] = useState([]);
  const [fallbackDialog, setFallbackDialog] = useState({
    id: 0,
    url: ""
  });
  const [isChanged, setIsChanged] = useState(false);
  const [isChangedDialog, setIsChangedDialog] = useState(false);


  useEffect(() => {
    var tiggers_to_view = triggers
      .filter((trigger) => trigger.trigger_type === "dialog")
      .reduce((acc, current) => {
        const x = acc.find((item) => item.url === current.url);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, []);

    setDialogs(tiggers_to_view);
  }, [triggers]);
  
  useEffect(() => {
    fetchFallback();
  }, [bot]);
  
  const fetchFallback = async () => {
    const fallback = await getFallback(bot.bot_id);
    if (fallback?.dialog_id) {
      setFallbackDialog({ id: fallback.dialog_id, url: fallback.url });
    } else if (bot?.fallback_dialog) {
      const url = bot.fallback_dialog;
      const matchingDialog = dialogsFromStore.find(dialog => dialog.url === url);
      if (matchingDialog?.dialog_id) {
        // await updateOne({bot_id: bot.bot_id, fallback_dialog: "", user_id: bot.user_id, bot_name: bot.bot_name})
        // await setFallback(matchingDialog.dialog_id);
        // await handleFallbackDialog(fallback.id);
        setFallbackDialog({ id: matchingDialog.dialog_id, url: matchingDialog.url });
      }
    }
  };

  const handleFallbackDialog = async (dialog_id) => {
      setFallbackDialog((prev) => ({ ...prev, id: dialog_id }));
      const currentFallback = await getFallback(bot.bot_id);
      if (!currentFallback || parseInt(dialog_id) !== currentFallback.dialog_id) {
        setIsChangedDialog(true);
      } else {
        setIsChangedDialog(false);
      }
    }

  const handleSubmit = async() =>{
    const temp = await setFallback(fallbackDialog.id);
    if(temp?.dialog_id){
      setFallbackDialog({id: temp.dialog_id, url:temp.url});
      setIsChanged(false);
      setIsChangedDialog(false);
      setLoading(false);
      loader?.complete()
      toast.success("Fallback dialog updated successfully");
    }
  }

  const handleReset = async() => {
    setFallbackDialog({id:0, url:""});
    handleFallbackDialog("");
    await removeFallback(bot.bot_id)
    setIsChanged(false);
    setIsChangedDialog(false);
    setLoading(false);
    console.log(fallbackDialog)
    
    if(bot?.fallback_dialog){
      await updateOne({bot_id: bot.bot_id, fallback_dialog: "", user_id: bot.user_id, bot_name: bot.bot_name})
    }
    toast.success("Fallback dialog removed successfully");
  };


  return (
    <Card title="Fallback Message" hr>
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3">
          <p>
            Initiate a dialog when the user says something the bot don&apos;t
            understand
          </p>
        </div>
        {dialogsFromStore.length > 0 ? (
          <>
            <label htmlFor="">Select Dialog Name</label>
            <div>
              <Select
                value={fallbackDialog.id !==0 ? String(fallbackDialog.id) : undefined}
                onValueChange={(value) => handleFallbackDialog(value)}
                >
                <SelectTrigger>
                  <SelectValue placeholder="Select a Dialog" />
                </SelectTrigger>
                <SelectContent>
                  {dialogsFromStore.map((dialog, i) => (
                    <SelectItem 
                    value={dialog.dialog_id.toString()} key={i}>
                      {dialog.dialog_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>
        ) : (
          <p className="p-3 text-center bg-yellow-200/50 rounded text-black my-2">
            No Dialogs Found
          </p>
        )}
        <div className="self-end flex gap-2">
          <Button
            onClick={handleReset}
            disabled={fallbackDialog.url === "" || fallbackDialog.id === 0}
            variant="outline"
            className="flex gap-1"
          >
            <Trash2 size={15} /> 
            Remove
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isChanged && !isChangedDialog}
            loading={loading}
          >
            { !isChanged && !isChangedDialog ? (
              "All Saved"
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
};
