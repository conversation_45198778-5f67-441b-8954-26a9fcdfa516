import { Card } from "common/ui/card";
import { Trash2 } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import useTriggerStore from "store/trigger/trigger.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import useBotStore from "store/bot/bot.store";
import { updateConfig } from "apis/config.api";
import { Button } from "common/ui/button";
import { botConfigType } from "types/botconfig.types";
import { useLoaderContext } from "context/loaderContext";
import { toast } from "react-hot-toast";
import useDialogStore from "store/dialog/dialog.store";
import { getWelcome, removeWelcome, setWelcome } from "apis/dialog.api";
interface WelcomeMessageProps {
  botConfig: botConfigType;
  setBotConfig: (botConfig: botConfigType) => void;
}
export const WelcomeMessage: React.FC<WelcomeMessageProps> = ({
  botConfig,
  setBotConfig,
}) => {
  const bot = useBotStore((state) => state.bot);
  const dialogsFromStore = useDialogStore((state) => state.dialogs);
  const triggers = useTriggerStore((state) => state.triggers);
  const [isChanges, setIsChanges] = useState(false);

  const [welcomeDialog, setWelcomeDialog] = useState({
    url: "",
    id: 0,
  });

  const [loading, setLoading] = useState(false);
  const {loader} = useLoaderContext();

  const [dialogs, setDialogs] = useState([]);

  useEffect(() => {
    var tiggers_to_view = triggers
    .filter((trigger) => trigger.trigger_type === "dialog")
    .reduce((acc, current) => {
      const x = acc.find((item) => item.url === current.url);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    
    setDialogs(tiggers_to_view);
  }, [triggers]);
  
  useEffect(() => {
    fetchWelcome();
  }, [bot, botConfig]);
  
  const fetchWelcome = async () => {
    const welcome = await getWelcome(bot.bot_id);
    console.log(welcome, "welcome");
    if (welcome?.dialog_id) {
      setWelcomeDialog({ id: welcome.dialog_id, url: welcome.url });
    } else if (botConfig?.welcome_dialog) {
      const url = botConfig.welcome_dialog;
      const matchingDialog = dialogsFromStore.find(dialog => dialog.url === url);
      if(matchingDialog?.dialog_id) {
        // await updateConfig({bot_id: bot.bot_id, welcome_dialog: null});
        // await setWelcome(matchingDialog.dialog_id);
        // await handleWelcomeDialog(matchingDialog.dialog_id);
        setWelcomeDialog({ id: matchingDialog.dialog_id, url: matchingDialog.url });
      }
    }
  };

  const handleWelcomeDialog = async (dialog_id) => {
      setWelcomeDialog((prev) => ({ ...prev, id: dialog_id }));
      const currentWelcome = await getWelcome(bot.bot_id);
      if (!currentWelcome || parseInt(dialog_id) !== currentWelcome.dialog_id) {
        setIsChanges(true);
      } else {
        setIsChanges(false);
      } 
  }
  
  const handleSubmit =async()=>{
    const temp = await setWelcome(welcomeDialog.id);
    if(temp?.dialog_id){
      setWelcomeDialog({id: temp.dialog_id, url:temp.url});
      setLoading(false);
      setIsChanges(false);
      loader?.complete()
      toast.success("Welcome dialog updated successfully");
    }
  }
  const handleReset = async() => {
    setWelcomeDialog({id:0, url:""});
    handleWelcomeDialog("");
    await removeWelcome(bot.bot_id);
    setIsChanges(false);
    setLoading(false);
    if(botConfig?.welcome_dialog){
      await updateConfig({bot_id: bot.bot_id, welcome_dialog: null});
    }
    toast.success("Welcome Message Removed successfully");
  };


  return (
    <Card title="Welcome Message" hr>
      <div className="flex flex-col gap-3 container">
        <p>
          Initiate a dialog to greet your users when they open the
          chatbot.
        </p>
        {dialogsFromStore.length > 0 ? (
          <>
            <label htmlFor="">Select Dialog Name</label>
            <div>
              <Select
                value={welcomeDialog.id !== 0 ? String(welcomeDialog.id) : undefined}
                onValueChange={(value) => handleWelcomeDialog(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a Dialog" />
                </SelectTrigger>
                <SelectContent>
                  {dialogsFromStore.map((dialog, i) => (
                    <SelectItem 
                    value={dialog.dialog_id.toString()} key={i}>
                      {dialog.dialog_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>
        ) : (
          <p className="p-3 text-center bg-yellow-200/50 rounded text-black my-2">
            No Dialogs Found
          </p>
        )}
        <div className="self-end flex gap-2">
          <Button
            onClick={handleReset}
            disabled={!welcomeDialog.url}
            variant="outline"
            className="flex gap-1"
          >
            <Trash2 size={15} /> 
            Remove
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isChanges}
            loading={loading}
          >
            {!isChanges ? "All Saved" : "Save Changes"}
          </Button>
        </div>
      </div>
    </Card>
  );
};
