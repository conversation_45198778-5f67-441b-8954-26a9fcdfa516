import { Card } from "common/ui/card";
import React from "react";
import { FallbackMessage, WelcomeMessage } from "./components";
import { botConfigType } from "types/botconfig.types";
import { SubPageHeader } from "common/components/headers";

interface DefaultsViewProps {
  botConfig: botConfigType;
  setBotConfig: (botConfig: botConfigType) => void;
}

export const DefaultsView: React.FC<DefaultsViewProps> = ({
  botConfig,
  setBotConfig,
}) => {
  return (
    <div className="space-y-3">
      <SubPageHeader
        title="Default Messages"
        description="Welcome your custormers and set a fallback message from here!"
      />
      <div className="grid grid-cols-2 pt-5 gap-5">
        <WelcomeMessage botConfig={botConfig} setBotConfig={setBotConfig} />
        <FallbackMessage />
      </div>
    </div>
  );
};
