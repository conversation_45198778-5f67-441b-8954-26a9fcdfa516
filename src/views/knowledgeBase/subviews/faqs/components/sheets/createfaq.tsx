"use client";

import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "common/ui/sheet";
import { useState } from "react";
import { QuickResponse } from "../quickresponse";
import { IChildren } from "types/common.types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { Importfaqs } from "..";

interface CreateFAQSheetProps extends IChildren {}

export const CreateFAQSheet: React.FC<CreateFAQSheetProps> = ({ children }) => {
  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Add an FAQ</SheetTitle>
          <SheetDescription>
            Create your FAQ here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <Tabs defaultValue="create" className="w-full mt-3">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">Create FAQ</TabsTrigger>
            <TabsTrigger value="import">Import FAQs</TabsTrigger>
          </TabsList>
          <TabsContent value="create">
            <QuickResponse setOpen={setOpen} />
          </TabsContent>
          <TabsContent value="import" className="h-[560px]">
            <Importfaqs />
          </TabsContent>
        </Tabs>

        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
