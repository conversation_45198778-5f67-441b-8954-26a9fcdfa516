"use client";

import { createTips } from "apis/faq.api";
import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import RichTextEditor from "common/ui/inputs/richtextedtor";
import { Textarea } from "common/ui/inputs/textarea";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "common/ui/sheet";
import { useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useFAQStore from "store/faq/faq.store";
import useUserStore from "store/user/user.store";
import { ShieldClose } from "lucide-react";
import defaults from "defaults";
import helper from "../../helper";
import { CreateFAQInfo, CreateFAQSchema, IFAQ } from "store/faq/faq.types";
import { QuickResponse } from "../quickresponse";

interface UpdateFAQSheetProps {
  children: React.ReactNode;
  faq: IFAQ;
}

export const UpdateFAQSheet: React.FC<UpdateFAQSheetProps> = ({
  children,
  faq,
}) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto z-40" position="right" size="lg">
        <SheetHeader>
          <SheetTitle>Update FAQ</SheetTitle>
          <SheetDescription>
            Modify your FAQ here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <QuickResponse setOpen={setOpen} isUpdate faq={faq} />
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
