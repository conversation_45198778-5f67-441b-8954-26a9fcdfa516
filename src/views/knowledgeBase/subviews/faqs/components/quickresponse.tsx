import { createTips, getAlters } from "apis/faq.api";
import { <PERSON><PERSON> } from "common/ui/button";
import RichTextEditor from "common/ui/inputs/richtextedtor";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useFAQStore from "store/faq/faq.store";
import useUserStore from "store/user/user.store";
import { InfoIcon, ShieldClose } from "lucide-react";
import { CreateFAQInfo, CreateFAQSchema, IFAQ } from "store/faq/faq.types";
import helper from "../helper";
import { getOne } from "apis/voice.api";

import toast from "react-hot-toast";
import {
  AlternativesInputs,
  UserQuestion,
} from "views/knowledgeBase/components";
import checkForErrors from "helpers/forms";
import { VoiceType } from "types/addons.types";
import { isArabic } from "helpers/helper";

import useTOPICStore from "store/topic/topic.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { extractLangInOneLine } from "helpers/multiLang";
import useVoiceStore from "store/voice/voice.store";
import { generateVoicePath } from "store/voice/voice.calls";

interface QuickResponseProps {
  setOpen: (open: boolean) => void;
  isUpdate?: boolean;
  faq?: IFAQ;
}

export const QuickResponse: React.FC<QuickResponseProps> = ({
  setOpen,
  isUpdate,
  faq,
}) => {
  const bot = useBotStore((state) => state.bot) as IBot;
  const user_id = useUserStore((state) => state.user).user_id;
  const { create_one_faq, delete_one_faq, update_many_faqs, create_many_faqs } =
    useFAQStore();
  const [selectedTopic, setSelectedTopic] = useState<string | null>(
    faq?.topic_id ? String(faq?.topic_id) : null
  );
  const [formData, setFormData] = useState(
    helper.initFAQFormData(user_id, bot.bot_id, faq ? faq : null)
  );

  const [alternatives, setAlternatives] = useState([]);
  const [copiedAlternatives, setCopiedAlternatives] = useState([]);
  const [editedAlternatives, setEditedAlternatives] = useState<boolean>(false);
  const [newAlternatives, setNewAlternatives] = useState([]);

  const [errors, setErrors] = useState<Record<string, string>>({});

  const [loading, setLoading] = useState<boolean>(false);

  const [voice, setVoice] = useState<VoiceType>();

  const selectedVoices = useVoiceStore((state) => state.selectedVoices);

  useEffect(() => {
    getOne(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setVoice({
          ...data,
        });
      }
    });
  }, []);

  // SECTION get data

  const getAllAlters = () => {
    getAlters(bot.bot_id, faq.faq_id).then((data) => {
      if (data) {
        setAlternatives([...data]);
      }
    });
  };

  useEffect(() => {
    if (faq && isUpdate) {
      getAllAlters();
    }
  }, []);

  useEffect(() => {
    setCopiedAlternatives([...alternatives]);
  }, [alternatives]);

  const validateField =
    (field: keyof CreateFAQInfo) =>
    (value: unknown): string => {
      const parsedResult = CreateFAQSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  // SECTION handle submit form
  const handleUpdate = async () => {
    if (
      formData.answer === faq.answer &&
      formData.question === faq.question &&
      newAlternatives?.every(
        (input) => input.question?.replace(/ /g, "") == ""
      ) &&
      !editedAlternatives &&
      selectedTopic == String(faq.topic_id)
    ) {
      toast("No changes made", {
        icon: <InfoIcon className="text-blue-500" />,
      });
      return;
    }
    setLoading(true);
    if (formData.answer && formData.question) {
      const voice_path_male = await generateVoicePath(
        selectedVoices,
        formData.answer,
        "male",
        bot.file_name
      );
      const voice_path_female = await generateVoicePath(
        selectedVoices,
        formData.answer,
        "female",
        bot.file_name
      );

      const trimmedFormData = {
        ...formData,
        topic_id: selectedTopic ? +selectedTopic : null,
        question: formData.question.trim().replace(/\s+/g, " "),
        answer: formData.answer,
        voice_path_male: voice_path_male,
        voice_path_female: voice_path_female,
        lemmatized_question: isArabic(formData.question)
          ? formData.question
          : "",
      };
      const all_faqs_to_update = [
        {
          ...trimmedFormData,
          topic_id: selectedTopic ? +selectedTopic : null,
          faq_id: faq.faq_id,
        },
      ];
      copiedAlternatives.map((a) =>
        all_faqs_to_update.push({
          ...a,
          topic_id: selectedTopic ? +selectedTopic : null,
          bot_id: bot.bot_id,
          answer: formData.answer,
          voice_path_male: voice_path_male,
          voice_path_female: voice_path_female,
          lemmatized_question: isArabic(a.question) ? a.question : "",
        })
      );

      update_many_faqs([...all_faqs_to_update]).then((res) => {
        if (newAlternatives.length) {
          const alternativesToCreate = newAlternatives?.filter(
            (input) => input.question?.replace(/ /g, "") != ""
          );
          create_many_faqs([
            ...alternativesToCreate.map((alter) => {
              return {
                question: alter,
                bot_id: bot.bot_id,
                answer: formData.answer,
                voice_path_male: voice_path_male,
                voice_path_female: voice_path_female,
                lemmatized_question: isArabic(alter) ? alter : "",
              };
            }),
          ]).then(() => {
            toast.success("FAQ updated with alternatives");
          });
        } else {
          toast.success("FAQ updated successfully");
        }
        setLoading(false);
        setOpen(false);
      });
    }
  };

  const handleCreate = async () => {
    setLoading(true);
    const voice_path_male = await generateVoicePath(
      selectedVoices,
      formData.answer,
      "male",
      bot.file_name
    );
    const voice_path_female = await generateVoicePath(
      selectedVoices,
      formData.answer,
      "female",
      bot.file_name
    );
    if (formData.answer && formData.question) {
      const trimmedFormData = {
        ...formData,
        topic_id: selectedTopic ? +selectedTopic : null,
        question: formData.question.trim().replace(/\s+/g, " "),
        answer: formData.answer,
        voice_path_male: voice_path_male,
        voice_path_female: voice_path_female,
        lemmatized_question: isArabic(formData.question)
          ? formData.question
          : "",
      };
      const all_faqs_to_create = [
        {
          ...trimmedFormData,
        },
      ];
      const alternativesToCreate = newAlternatives?.filter(
        (input) => input.question?.replace(/ /g, "") != ""
      );

      alternativesToCreate?.map((alter) => {
        all_faqs_to_create.push({
          topic_id: selectedTopic ? +selectedTopic : null,
          question: alter,
          bot_id: bot.bot_id,
          answer: formData.answer,
          user_id: user_id,
          voice_path_male: voice_path_male,
          voice_path_female: voice_path_female,
          lemmatized_question: isArabic(alter) ? alter : "",
          is_main: alter.is_main,
        });
      });
      create_many_faqs([...all_faqs_to_create]).then((res) => {
        if (alternativesToCreate?.length) {
          toast.success("FAQ created with alternatives");
        } else {
          toast.success("FAQ created successfully");
        }
        setLoading(false);
        setErrors({});
        setFormData(helper.initFAQFormData(user_id, bot.bot_id));
        setOpen(false);
      });
    }
  };

  const handleSubmit = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateFAQSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;

    if (isUpdate) {
      await handleUpdate();
    } else {
      await handleCreate();
    }
  };

  const { topics } = useTOPICStore();

  return (
    <div className="flex flex-col gap-4 py-4 text-white/70">
      <UserQuestion
        faq={faq}
        onChange={(e) => onChangeHandler("question", e.target.value)}
        value={formData.question}
        error={errors.question}
      />
      <AlternativesInputs
        faq={faq}
        setEditedAlternatives={setEditedAlternatives}
        alternatives={alternatives}
        newAlternatives={newAlternatives}
        setNewAlternatives={setNewAlternatives}
        copiedAlternatives={copiedAlternatives}
        setCopiedAlternatives={setCopiedAlternatives}
        setAlternatives={setAlternatives}
      />
      <div className="space-y-1 relative">
        <label htmlFor="question" className="text-right">
          Bot Answer *
        </label>

        <div className="!text-black">
          <RichTextEditor
            value={formData.answer}
            onChangeRichText={(value) => onChangeHandler("answer", value)}
            lang={bot.language}
          />
        </div>
        {errors.answer ? (
          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
            <ShieldClose size={12} />
            {errors.answer}
          </span>
        ) : null}
      </div>

      <div className="">
        <label htmlFor="topic">Topic</label>
        <Select
          value={selectedTopic}
          onValueChange={(value) => {
            console.log(value);

            setSelectedTopic(value);
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a topic" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={null}>No Topic</SelectItem>
            {topics.map((t, i) => {
              return (
                <SelectItem key={i} value={String(t.topic_id)}>
                  {extractLangInOneLine(t.display_name)}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <div className="self-end">
        <Button loading={loading} onClick={handleSubmit}>
          {isUpdate ? "Save Changes" : "Create FAQ"}
        </Button>
      </div>
    </div>
  );
};
