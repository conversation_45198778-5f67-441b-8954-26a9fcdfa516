import { getAlters } from "apis/faq.api";
import useConfirmModal from "common/hooks/useConfirmModal";
import searchedArray from "helpers/search";
import { Edit, PlusCircle, Trash2, UploadIcon, FileAudio } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useFAQStore from "store/faq/faq.store";
import { CreateFAQSheet, UpdateFAQSheet } from "./components";
import { SubPageHeader } from "common/components/headers";
import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { toast } from "react-hot-toast";
import { CSVLink } from "react-csv";
import useTOPICStore from "store/topic/topic.store";
import { extractLangInOneLine } from "helpers/multiLang";
import VoiceSheetWrapper from "common/components/voice/voice-wrapper/VoiceSheetWrapper";
import { IFAQ } from "store/faq/faq.types";

export const FAQsView = () => {
  const { get_all_faqs, FAQs, fetchLoading, delete_many_faqs } = useFAQStore();
  const bot = useBotStore((state) => state.bot) as IBot;
  const { topic, topics } = useTOPICStore();
  const confirmModal = useConfirmModal();
  const [keySearch, setKeySearch] = useState("");
  const [renderedFAQs, setRenderedFAQs] = useState([]);

  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    var faq_to_view = FAQs?.reduce((acc, current) => {
      const x = acc.find((item) => item.answer === current.answer);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    setRenderedFAQs(
      searchedArray(keySearch, faq_to_view, ["question", "answer"])
    );
  }, [FAQs, keySearch]);

  useEffect(() => {
    get_all_faqs(bot.bot_id);
  }, [bot]);

  useEffect(() => {
    if (selected?.length === renderedFAQs?.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selected, renderedFAQs]);

  const handleDelete = async (faq_id) => {
    let alternatives = [];
    const data = await getAlters(bot.bot_id, faq_id);
    const faqs_to_delete = [faq_id];
    if (data) {
      alternatives = [...data];
      alternatives?.map((a) => {
        faqs_to_delete.push(a.faq_id);
      });
    }
    await delete_many_faqs({
      bot_id: bot.bot_id,
      faqs: [...faqs_to_delete],
    });
    toast.success("FAQ deleted successfully");
  };

  const { update_one_faq_voice } = useFAQStore();

  const handleUpdate = (data:{voice_path_female:string,voice_path_male:string},faq:IFAQ) => {
    update_one_faq_voice({...data,faq_id:faq.faq_id})
  };

  const faqTableActions = [
    {
      label: "Edit Voice",
      component: ({ item }) => (
        <VoiceSheetWrapper handleUpdate={handleUpdate} item={item} answer={item?.answer}/>
      ),
    },
    {
      label: "Edit",
      component: ({ item }) => (
        <UpdateFAQSheet faq={item}>
          <Edit size={15} className="hover:text-primary cursor-pointer" />
        </UpdateFAQSheet>
      ),
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(async () => await handleDelete(item.faq_id));
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
  ];

  const onSelect = (e) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(renderedFAQs?.map((faq) => faq.faq_id.toString()));
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(selected.filter((id) => id !== name.split("-")[1]));
        setSelectAll(false);
      }
    }
  };

  const handleDeleteSelected = async () => {
    let alts = [];

    await Promise.all(
      selected.map(async (faq_id) => {
        const res = await getAlters(bot.bot_id, faq_id);
        if (res) {
          alts.push(...res.map((a) => a.faq_id));
        }
      })
    );

    await delete_many_faqs({
      bot_id: bot.bot_id,
      faqs: [...selected, ...alts],
    });
    toast.success("FAQs deleted successfully");

    setSelected([]);
  };
  const exportFile = () => {
    const headers = [
      { label: "Question", key: "question" },
      { label: "Answer", key: "answer" },
    ];
    let data = [];
    if (renderedFAQs) {
      data = [
        ...renderedFAQs.map((a) => {
          return {
            question: a.question,
            answer: a.answer,
          };
        }),
      ];
    }

    const csvReport = {
      data: data,
      headers: headers,
      filename: `${bot.bot_name}-faqs`,
    };

    return csvReport;
  };
  const tableData = useMemo(() => {
    if (renderedFAQs) {
      if (Object.keys(topic || {})?.length) {
        console.log("renderedFAQs", topic);
        if (topics?.length) {
          return renderedFAQs
            .map((f) => {
              return {
                ...f,
                topic_name: extractLangInOneLine(
                  topics.find((t) => t.topic_id === f.topic_id)?.display_name
                ),
              };
            })
            .filter((faq) => faq.topic_id === topic.topic_id);
        }
        return renderedFAQs.filter((faq) => faq.topic_id === topic.topic_id);
      }
      if (topics?.length) {
        return renderedFAQs.map((f) => {
          return {
            ...f,
            topic_name: extractLangInOneLine(
              topics.find((t) => t.topic_id === f.topic_id)?.display_name
            ),
          };
        });
      }
      return renderedFAQs;
    }
    return [];
  }, [renderedFAQs, topic]);
  return (
    <div className="space-y-3">
      <SubPageHeader
        title="FAQs"
        description="Create and manage your chatbot FAQs!"
        search
        searchPlaceholder="Find an faq..."
        setKeySearch={setKeySearch}
        btn
        btnText="Add FAQs"
        btnIcon={PlusCircle}
        btnSheet={CreateFAQSheet}
      />
      {renderedFAQs?.length !== 0 && (
        <div className="float-right pb-5">
          <CSVLink {...exportFile()}>
            <Button variant="outline" className="glex gap-2 items-center">
              <UploadIcon className="h-4 w-4" /> Export FAQs
            </Button>
          </CSVLink>
        </div>
      )}
      {selected?.length > 0 && (
        <Button
          onClick={() => {
            confirmModal.setOnConfirm(async () => await handleDeleteSelected());
            confirmModal.setType("delete");
            confirmModal.onOpen();
          }}
          variant="destructive"
          className="float-right mb-3 mr-2"
        >
          <Trash2 size={15} className="mr-2" />
          Delete Selected ({selected?.length})
        </Button>
      )}
      <div className="pt-5">
        <div className=" w-full">
          <MainTable
            loading={fetchLoading}
            data={tableData}
            columns={[
              {
                name: "Question",
                key: "question",
              },
              {
                name: "Answer",
                key: "answer",
              },
              {
                name: "Topic",
                key: "topic_name",
              },
            ]}
            itemsPerPage={20}
            actions={faqTableActions}
            checkBoxes
            onSelect={onSelect}
            isSelectAll={selectAll}
            isSelectOne={(id) => selected.includes(id)}
            idKey="faq_id"
          />
        </div>
      </div>
    </div>
  );
};
