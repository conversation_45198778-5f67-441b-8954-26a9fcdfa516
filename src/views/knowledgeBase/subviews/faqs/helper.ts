import { IFAQ } from "store/faq/faq.types";





const initFAQFormData = (user_id: number, bot_id: number, faq?: IFAQ) => {
  return {
    question: faq?.question ? faq.question : "",
    answer: faq?.answer ? faq.answer : "",
    user_id: user_id,
    bot_id: bot_id,
    voice_path_male: faq?.voice_path_male ? faq?.voice_path_male : "",
    voice_path_female: faq?.voice_path_female ? faq?.voice_path_female : "",
    is_main: true,
  };
};
function getRandomInt(max: number) {
  return Math.floor(Math.random() * max);
}

export default {
  initFAQFormData,

};
