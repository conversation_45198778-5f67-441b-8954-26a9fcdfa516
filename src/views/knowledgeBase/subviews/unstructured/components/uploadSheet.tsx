import React, { useState, useEffect } from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "common/ui/sheet";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "common/ui/tabs";
import { createMany } from "apis/unstructuredResources.api";
import generateStorageId from "helpers/generateStorageId";
import constant from "constant";
import useBotStore from "store/bot/bot.store";
import { Button } from "common/ui/button";
import { FilesInput } from "./filesInput";
import { uploadFormData } from "apis/file.api";
import toaster, { toast } from "react-hot-toast";
import useUnstructuredResourcesStore from "store/unstructuredResources/unstructuredResources.store";
import { Clapperboard, Loader2, X } from "lucide-react";
import { Configuration, OpenAIApi } from "openai";
import useGPTStore from "store/gpt/gpt.store";
import { transcribe } from "apis/openai.api";
import { Input } from "common/ui/inputs/input";
import { z } from "zod";
import { useLoaderContext } from "context/loaderContext";
import checkForErrors from "helpers/forms";
import { FullModal } from "common/components/modals";
import RenderForm from "./DynamicForm";

export const UploadSheet = ({ children }) => {
  const [open, setOpen] = useState(false);

  const bot = useBotStore((state) => state.bot);
  const gpt = useGPTStore((state) => state.gpt);
  const {
    create_many_unstructuredResources,
    get_all_unstructuredResources,
    create_one_unstructuredResource,
    loading,
  } = useUnstructuredResourcesStore();

  const [filesData, setFilesData] = useState([]);
  const [mediaToTranscribe, setMediaToTranscribe] = useState(null);
  const [transcription, setTranscription] = useState("");
  const [transcriptionLoading, setTranscriptionLoading] = useState(false);
  const [website, setWebsite] = useState({
    url: "",
    name: "",
  });
  const [chunk, setChunk] = useState(false);
  const [submit, setSubmit] = useState("");

  const { loader } = useLoaderContext();

  const [errors, setErrors] = useState<Record<string, string>>({});

  const websiteSchema = z.object({
    name: z.string().trim(),
    url: z.string().url().trim(),
  });

  type websiteSchemaType = z.infer<typeof websiteSchema>;

  const validateField =
    (field: keyof websiteSchemaType) =>
    (value: unknown): string => {
      const parsedResult = websiteSchema
        .pick({
          [field]: true,
        } as any)
        .safeParse({
          [field]: value,
        });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const handleOnWebsiteChange = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setWebsite({
      ...website,
      [key]: value,
    });
  };

  const handleOnWebsiteSubmit = async () => {
    setOpen(false);
    setChunk(true);
    setSubmit("website");
    // const isErrors = checkForErrors({
    //   zodSchema: websiteSchema,
    //    data:website,
    // }, setErrors);

    // if (isErrors) return;

    // await create_one_unstructuredResource({
    //   bot_id: bot.bot_id,
    //   resource_name: website.name,
    //   resource_type: "website",
    //   resource_url: website.url,
    // });

    // toaster.success("Website added successfully");
    // setOpen(false);
    // setWebsite({
    //   url: "",
    //   name: "",
    // });
  };

  const handleFilesChange = (files) => {
    const files_arr = [...filesData];

    for (const file of files) {
      const formData = new FormData();
      formData.append("file", file);
      const extension = file?.name?.split(".")?.[1];
      const path = `Bots/${bot.file_name}/unstructuredResources/${
        file?.name?.split(".")?.[0]
      }${generateStorageId(4)}.${extension}`;

      files_arr.push({
        url: constant.MEDIA_STORAGE_URL + path,
        path,
        formData,
        file: file,
      });
    }

    setFilesData([...files_arr]);
  };

  const handleDeleteFile = (index) => {
    const files_arr = [...filesData];
    files_arr.splice(index, 1);
    setFilesData([...files_arr]);
  };

  const handleUpload = () => {
    setOpen(false);
    setChunk(true);
    setSubmit("file");
    // loader?.continuousStart()
    // if (filesData.length) {
    //   for (const file of filesData) {
    //     console.log(file, "file");
    //     uploadFormData({
    //       path: file.path,
    //       formData: file.formData,
    //     });
    //   }

    //   create_many_unstructuredResources([
    //     ...filesData.map((file) => {
    //       return {
    //         resource_url: file.url,
    //         bot_id: bot.bot_id,
    //         resource_name: file.file.name,
    //         resource_type: file.file.name.split(".")[1],
    //       };
    //     }),
    //   ]).finally(() => {
    //     setFilesData([]);
    //     setTimeout(() => {
    //       get_all_unstructuredResources(bot.bot_id);
    //     }, 1000);
    //   })
    // }
    // loader?.complete()
  };

  const handleTranscribe = async () => {
    //add to chunk
    setTranscriptionLoading(true);
    console.log(mediaToTranscribe[0], "mediaToTranscribe");

    const response = await transcribe(gpt.openai_key, mediaToTranscribe[0]);
    console.log(response, "response");
    if (response.error) {
      toaster.error(response.error.message);
      setTranscriptionLoading(false);
      return;
    }

    setTranscription(response.text);
    setTranscriptionLoading(false);
  };

  const uploadTranscription = async (transcription) => {
    loader?.continuousStart();
    const blob = new Blob([transcription], {
      type: "text/plain;charset=utf-8",
    });

    const formData = new FormData();
    formData.append("file", blob, "transcription.txt");
    const path = `Bots/${
      bot.file_name
    }/unstructuredResources/${generateStorageId()}.txt`;

    await uploadFormData({
      path: path,
      formData: formData,
    });

    create_one_unstructuredResource({
      resource_url: constant.MEDIA_STORAGE_URL + path,
      bot_id: bot.bot_id,
      resource_name: mediaToTranscribe[0]?.name + "transcription.txt",
      resource_type: "txt",
    }).then((res) => {
      toaster.success("Transcription uploaded successfully!");
      setOpen(false);
      setTranscription("");
      setMediaToTranscribe(null);
    });
    loader?.complete();
  };

  interface DynamicFormI {
    form: any;
    tempState: Record<string, string | number>;
  }
  const formObj = [
    {
      name: "chunking_strategy",
      type: "select",
      values: [
        {
          semantic: [
            {
              type: "select",
              name: "breakpoint_threshold_type",
              values: [
                "percentile",
                "standard_deviation",
                "interquartile",
                "gradient",
              ],
            },
          ],
          CharacterTextSplitter: [
            {
              type: "number",
              name: "chunk_size",
              value: 5000,
            },
            {
              type: "number",
              name: "chunk_overlap",
              value: 500,
            },
            {
              type: "string",
              name: "separator",
              value: "\\n\\n",
            },
          ],
          RecursiveCharacterTextSplitter: [
            {
              type: "number",
              name: "chunk_size",
              value: 1000,
            },
            {
              type: "number",
              name: "chunk_overlap",
              value: 200,
            },
            {
              type: "multi-select",
              name: "separators",
              value: ["\\n", "\\t", "space", ","],
            },
            {
              type: "select",
              name: "keep_separator",
              values: [true, false, "start", "end"],
            },
          ],
          SentenceTransformersTokenTextSplitter: [
            {
              type: "number",
              name: "chunk_size",
              value: 1000,
            },
            {
              type: "number",
              name: "chunk_overlap",
              value: 50,
            },
            {
              type: "number",
              name: "tokens_per_chunk",
              value: 0,
            },
            {
              type: "select",
              name: "model_name",
              values: ["sentence-transformers/all-mpnet-base-v2"],
            },
          ],
          TokenTextSplitter: [
            {
              type: "number",
              name: "chunk_size",
              value: 1000,
            },
            {
              type: "number",
              name: "chunk_overlap",
              value: 20,
            },
          ],
        },
      ],
    },
  ];
  const ChunkSettings = ({ tempState = {} }: DynamicFormI) => {
    const [state, setState] = React.useState(tempState);
    useEffect(() => {
      setState(tempState);
    }, []);
    const setMainState = (value: string | number, key: string) => {
      setState({
        ...state,
        [key]: value,
      });
    };

    const onSave = async () => {
      setChunk(false);
      if (submit === "file") {
        loader?.continuousStart();
        if (filesData.length) {
          for (const file of filesData) {
            // console.log(file, "file");
            uploadFormData({
              path: file.path,
              formData: file.formData,
            });
          }

          create_many_unstructuredResources([
            ...filesData.map((file) => {
              return {
                resource_url: file.url,
                bot_id: bot.bot_id,
                resource_name: file.file.name,
                resource_type: file.file.name.split(".")[1],
                chunkConfig: state,
              };
            }),
          ]).finally(() => {
            setFilesData([]);
            setTimeout(() => {
              get_all_unstructuredResources(bot.bot_id);
            }, 1000);
            setOpen(false);
          });
        }
        loader?.complete();
      } else if (submit === "website") {
        const isErrors = checkForErrors(
          {
            zodSchema: websiteSchema,
            data: website,
          },
          setErrors
        );

        if (isErrors) return;

        await create_one_unstructuredResource({
          bot_id: bot.bot_id,
          resource_name: website.name,
          resource_type: "website",
          resource_url: website.url,
          chunkConfig: state,
        });

        toaster.success("Website added successfully");
        setOpen(false);
        setWebsite({
          url: "",
          name: "",
        });
      }
    };

    return (
      <FullModal
        className="text-white"
        isOpen={chunk}
        onClose={() => setChunk(false)}
        title="Chunk Config"
        saveButtonText="Upload"
        onSave={onSave}
        footer
      >
        <div className="grid grid-cols-1">
          <RenderForm arrObj={formObj} setState={setMainState} state={state} />
          {state?.chunking_strategy && (
            <div className="flex py-5">
              <Button
                onClick={async () => {
                  await setChunk(false);
                  setChunk(true);
                }}
                className="ml-2"
              >
                Reset to Defaults
              </Button>
            </div>
          )}
        </div>
      </FullModal>
    );
  };
  return (
    <>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>{children}</SheetTrigger>
        <SheetContent
          className="overflow-y-auto"
          position="right"
          size="default"
        >
          <SheetHeader>
            <SheetTitle>Upload Unstructured Knowledge Base</SheetTitle>
            <SheetDescription>
              Upload files to your knowledge base!
            </SheetDescription>
          </SheetHeader>
          <Tabs defaultValue="files" className="w-full mt-3">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="files">Files</TabsTrigger>
              <TabsTrigger value="media" disabled={true}>
                Video / Audio
              </TabsTrigger>
              <TabsTrigger value="website">Website</TabsTrigger>
            </TabsList>
            <TabsContent value="files">
              <div className="flex flex-col gap-3">
                <span className="text-white text-sm">
                  Upload (pdf, ppt, doc, txt, CSV) files to your knowledge base!
                </span>
                <FilesInput
                  ACCEPTED_FILE_TYPES={[
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-powerpoint",
                    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    "text/plain",
                    // "text/csv",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    ".xlsx",
                  ]}
                  onChange={handleFilesChange}
                />
                {filesData.length ? (
                  <>
                    <div className="max-h-[300px] overflow-y-auto border rounded border-white text-white">
                      {filesData.map((file, i) => (
                        <div
                          key={i}
                          className="flex items-center justify-between p-2"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-300 text-gray-600 text-xs tracking-tighter ">
                              {file.file.name.split(".")[1].toUpperCase()}
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm font-semibold">
                                {file.file.name}
                              </span>
                              <span className="text-xs text-gray-500">
                                {file.file.size} bytes
                              </span>
                            </div>
                          </div>
                          <button
                            onClick={() => handleDeleteFile(i)}
                            className="text-sm text-red-500"
                          >
                            Delete
                          </button>
                        </div>
                      ))}
                    </div>
                    <Button
                      className="self-end"
                      loading={loading}
                      onClick={handleUpload}
                    >
                      Upload
                    </Button>
                  </>
                ) : null}
              </div>
            </TabsContent>
            <TabsContent value="media" className="h-[560px]">
              <div className="space-y-3">
                <span className="text-white text-sm">
                  Upload video or audio files to transcribe them into text and
                  upload them.{" "}
                  <span className="text-amber-500">
                    Your openai key is being used for this.
                  </span>
                </span>
                <FilesInput
                  ACCEPTED_FILE_TYPES={[
                    "video/mp4",
                    "video/x-m4v",
                    "video/*",
                    "audio/mpeg",
                    "audio/wav",
                    "audio/*",
                  ]}
                  icon={Clapperboard}
                  onChange={(file) => {
                    setMediaToTranscribe(file);
                  }}
                />
                {mediaToTranscribe ? (
                  <div className="flex justify-between items-center">
                    <div className="text-white text-sm bg-secondary rounded-lg p-3 relative">
                      {mediaToTranscribe[0].name}
                      <span className="absolute -top-1 right-0 bg-red-500 rounded-full cursor-pointer ">
                        <X
                          size={15}
                          onClick={() => setMediaToTranscribe(null)}
                        />
                      </span>
                    </div>
                    <Button onClick={handleTranscribe}>
                      {transcriptionLoading ? (
                        <>
                          <Loader2 className="mr-1 w-3 h-3 animate-spin" />
                        </>
                      ) : (
                        "Transcribe"
                      )}
                    </Button>
                  </div>
                ) : null}
                {transcription ? (
                  <div className="flex flex-col gap-3">
                    <span className="text-white text-sm">Transcription</span>
                    <div className="p-5 border rounded max-h-[180px] overflow-y-auto text-white">
                      {transcription}
                    </div>
                    <Button
                      onClick={() => {
                        uploadTranscription(transcription);
                      }}
                      loading={loading}
                    >
                      Upload Text
                    </Button>
                  </div>
                ) : null}
              </div>
            </TabsContent>
            <TabsContent value="website">
              <div className="flex flex-col gap-5 text-white">
                <span className=" text-sm">
                  Get data from a website and upload it to your knowledge base!
                </span>
                <Input
                  placeholder="Enter website name"
                  value={website.name}
                  onChange={(e) =>
                    handleOnWebsiteChange("name", e.target.value)
                  }
                  name="websiteName"
                  title="Website Name"
                  error={errors.name}
                />
                <Input
                  placeholder="Enter website url"
                  value={website.url}
                  onChange={(e) => handleOnWebsiteChange("url", e.target.value)}
                  name="websiteUrl"
                  title="Website Url"
                  error={errors.url}
                />
                {website.name.trim() && website.url.trim() ? (
                  <Button loading={loading} onClick={handleOnWebsiteSubmit}>
                    Upload
                  </Button>
                ) : null}
              </div>
            </TabsContent>
          </Tabs>
          <SheetFooter></SheetFooter>
        </SheetContent>
      </Sheet>
      <ChunkSettings form={{}} tempState={{}} />
    </>
  );
};
