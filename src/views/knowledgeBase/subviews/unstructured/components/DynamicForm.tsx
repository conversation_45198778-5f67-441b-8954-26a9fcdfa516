"use client";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Input } from "common/ui/input";
import { useEffect, useState, useCallback } from "react";

const RenderForm = ({
  arrObj,
  setState,
  state,
}: {
  arrObj: any[];
  state: Record<string, string | number>;
  setState: (value: string | number, key: string) => void;
}) => {
  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = useCallback(
    (e, obj) => {
      if (e.key === "Enter" && inputValue.trim() !== "") {
        const currentValues =
          String(state[obj.name])?.split("~~").filter(Boolean) || [];
        const updatedValues = [...currentValues, inputValue];
        setState(updatedValues.join("~~"), obj.name);
        setInputValue("");
      }
    },
    [inputValue, state, setState]
  );

  const handleRemove = useCallback(
    (index, obj) => {
      const currentValues =
        String(state[obj.name])?.split("~~").filter(Boolean) || [];
      const updatedValues = currentValues.filter((_, i) => i !== index);
      setState(updatedValues.join("~~"), obj.name);
    },
    [state, setState]
  );

  const getDefaultValue = useCallback(
    (obj) => {
      if (!state[obj.name]) {
        if (obj.type === "string" || obj.type === "number") {
          return obj?.value;
        } else if (obj.type === "multi-select") {
          return obj?.value[0];
        } else if (obj.type === "select" && typeof obj.values[0] !== "object") {
          return obj?.values[0];
        }
      }
      return null;
    },
    [state]
  );

  useEffect(() => {
    arrObj.forEach((obj) => {
      const newValue = getDefaultValue(obj);
      if (newValue !== null) {
        setState(newValue, obj.name);
      }
    });
  }, [arrObj, getDefaultValue, setState]);

  if (!arrObj || !arrObj.length) return null;

  return (
    <div>
      {arrObj.map((obj, i) => {
        const currentValues =
          String(state[obj.name])?.split("~~").filter(Boolean) || [];

        if (
          obj.type === "select" &&
          "values" in obj &&
          typeof obj?.values[0] === "object"
        ) {
          return (
            <div key={i} className="w-full">
              <label>{obj.name.replaceAll("_", " ").toUpperCase()}</label>
              <Select
                value={state?.[obj.name] as any}
                onValueChange={(value) => setState(value, obj.name)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={"Your Default Values"} />
                </SelectTrigger>
                <SelectContent className="w-full">
                  <SelectGroup>
                    {Object.keys(obj.values[0])?.map((val, idx) => (
                      <SelectItem key={idx} value={val}>
                        {val}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              {state[obj.name] && (
                <RenderForm
                  arrObj={obj.values[0]?.[state[obj.name] || ""] || []}
                  setState={setState}
                  state={state}
                />
              )}
            </div>
          );
        }

        if (obj.type === "select" && typeof obj.values[0] !== "object") {
          return (
            <div key={i}>
              <label>{obj.name.replaceAll("_", " ").toUpperCase()}</label>
              <Select
                value={state[obj.name] as any}
                onValueChange={(value) => setState(value, obj.name)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={`Select ${obj.name.replaceAll("_", " ")}`}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>
                      {obj.name.replaceAll("_", " ").toUpperCase()}
                    </SelectLabel>
                    {obj.values?.map((val: any, idx: number) => (
                      <SelectItem key={idx} value={val}>
                        {val.toString().replaceAll("_", " ")}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          );
        }

        if (obj.type === "string" || obj.type === "number") {
          return (
            <div key={i}>
              <label>{obj.name.replaceAll("_", " ").toUpperCase()}</label>
              <Input
                className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25"
                type={obj.type}
                value={state[obj.name] || obj?.value}
                onChange={(e) => setState(e.target.value, obj.name)}
              />
            </div>
          );
        }

        if (obj.type === "multi-select" && Array.isArray(obj.value)) {
          return (
            <div key={i} className="space-y-3">
              <label className="block text-sm font-semibold">
                {obj.name.replaceAll("_", " ").toUpperCase()}
              </label>
              <input
                type="text"
                value={inputValue || null}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, obj)}
                className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25"
                placeholder={`Press Enter to add ${obj.name.replaceAll(
                  "_",
                  " "
                )}`}
              />
              <ul className="space-y-1">
                {currentValues.map((value, idx) => (
                  <li key={idx} className="flex items-center space-x-2">
                    <span className="px-2 py-1 bg-gray-200 text-gray-800 rounded-md">
                      {value.trim() === "" ? "[Space]" : value || null}
                    </span>
                    <button
                      onClick={() => handleRemove(idx, obj)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          );
        }
      })}
    </div>
  );
};

export default RenderForm;
