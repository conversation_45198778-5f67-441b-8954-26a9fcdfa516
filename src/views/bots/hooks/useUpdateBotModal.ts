import { IBot } from "store/bot/bot.types";
import { create } from "zustand";

interface UpdateBotModalStore {
  isOpen: boolean;
  bot: IBot | {};
  setBot: (bot: IBot) => void;
  onOpen: () => void;
  onClose: () => void;
}

const useUpdateBotModal = create<UpdateBotModalStore>((set) => ({
  isOpen: false,
  bot: {},
  onOpen: () => set({ isOpen: true }),
  onClose: () => set({ isOpen: false }),
  setBot: (bot) => set({ bot: bot }),
}));

export default useUpdateBotModal;
