import {
  Bo<PERSON><PERSON>ard,
  BotLoadingSkeleton,
  UpdateBotModal,
} from "views/bots/components";
import { CustomSearch } from "common/ui/inputs/search";
import { <PERSON><PERSON> } from "common/ui/button";
import { <PERSON><PERSON>, Loader2, PlusCircle } from "lucide-react";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import useCreateBotModal from "views/bots/hooks/useCreateBotModal";
import searchedArray from "helpers/search";
import useFAQStore from "store/faq/faq.store";
import { DuplicateBotModal } from "./components/modals/duplicatebot.modal";
import useTriggerStore from "store/trigger/trigger.store";
import useGPTStore from "store/gpt/gpt.store";
import useSearchSettingsStore from "store/search/search.store";
import { IBot } from "store/bot/bot.types";
import useTrelloStore from "store/trello/trello.store";
import useLiveChatStore from "store/livechat/livechat.store";
import views from "views";
import Empty from "common/components/empty";
import { useLoaderContext } from "context/loaderContext";
import useLLMStore from "store/llmIntegration/llm.store";

interface BotsViewProps {
  showView?: boolean;
  setView: (view: string) => void;
}

export const BotsView: React.FC<BotsViewProps> = ({ setView }) => {
  const { bots, get_all_bots, loading, get_one_bot } = useBotStore();
  const { get_one_LLM } = useLLMStore();
  const { get_all_faqs } = useFAQStore();
  const { get_all_triggers } = useTriggerStore();
  const { get_one_gpt } = useGPTStore();
  const { get_one_searchSettings } = useSearchSettingsStore();
  const { get_one_trello } = useTrelloStore();
  const { get_one_livechat } = useLiveChatStore();
  const { user } = useUserStore();
  const createBotModal = useCreateBotModal();

  const [renderedBots, setRenderedBots] = useState(bots);
  const [keySearch, setKeySearch] = useState("");

  const [loadingBot, setLoadingBot] = useState(false);
  const { loader } = useLoaderContext();
  useEffect(() => {
    setRenderedBots(bots);
  }, [bots]);

  useEffect(() => {
    get_all_bots(user.user_id);
  }, [user]);

  const getBotData = useCallback(async (bot: IBot) => {
    loader?.continuousStart();
    await get_one_bot(bot.bot_id);
    get_one_LLM(bot.bot_id)
    loader?.complete();
  }, []);

  const botsToView = useMemo(() => {
    const filtered = searchedArray(keySearch, renderedBots, [
      "bot_name",
      "description",
      "bot_id",
    ]);
    return filtered;
  }, [keySearch, renderedBots]);
  return (
    <div className={` p-10 flex flex-col gap-5 relative`}>
      {loadingBot && (
        <div className="fixed inset-0 w-full h-screen bg-black/20 flex justify-center items-center z-50">
          <Loader2 className="animate-spin h-32 w-32 text-primary " />
        </div>
      )}
      <div className="p-5 flex flex-wrap justify-between items-center">
        <div className=" flex flex-col gap-3">
          <h1 className="text-white text-3xl">Your Bots</h1>
          <p className="capitalize">Create and manage your chatbots!</p>
        </div>
        <div className="w-5/12">
          <CustomSearch
            placeholder="Find a bot..."
            onChange={(value) => setKeySearch(value)}
          />
        </div>
        <Button onClick={createBotModal.onOpen}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create New Bot
        </Button>
      </div>
      <hr className="text-white/50" />
      {loading ? (
        <BotLoadingSkeleton />
      ) : botsToView?.length ? (
        <div className="p-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-5">
          {botsToView?.map((bot, i) => {
            return (
              <BotCard
                key={i}
                bot={bot}
                setRenderedBots={setRenderedBots}
                renderedBots={renderedBots}
                onClick={async () => {
                  setLoadingBot(true);
                  await getBotData(bot);
                  // setTimeout(() => {
                  setView(views.botViews.OVERVIEW_VIEW);
                  setLoadingBot(false);
                  // }, 1000);
                }}
              />
            );
          })}
        </div>
      ) : (
        <div className="flex flex-col justify-center items-center gap-5 p-20">
          <Empty text="Get Started By" />
          <Button onClick={createBotModal.onOpen}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create New Bot
          </Button>
        </div>
      )}
      <UpdateBotModal />
      <DuplicateBotModal setView={setView} />
    </div>
  );
};
