import { Modal } from "common/components/modals";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import constant from "constant";
import { useEffect, useMemo, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { ShieldClose, User } from "lucide-react";
import { IBot } from "store/bot/bot.types";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import { SelectCategories } from "../selectCategories";
import useDuplicateBotModal from "views/bots/hooks/useDuplicateBotModal";
import z from "zod";
import helper from "views/bots/helper";
import { useLoaderContext } from "context/loaderContext";
import views from "views";

export const DuplicateBotModal = ({ setView }) => {
  const bot = useDuplicateBotModal((state) => state.bot) as IBot;
  const onClose = useDuplicateBotModal((state) => state.onClose);
  const isOpen = useDuplicateBotModal((state) => state.isOpen);
  const { create_bot, get_all_bots, duplicate_bot } = useBotStore();
  const bots = useBotStore((state) => state.bots) as IBot[];
  const user_id = useUserStore((state) => state.user.user_id);

  const [loading, setLoading] = useState(false);
  const { loader } = useLoaderContext();

  const isBotNameUnique = () =>
    !bots.find((a) => a.bot_name === formData.bot_name);

  const [formData, setFormData] = useState(
    helper.initBotFormData(user_id, 5, bot)
  );
  useEffect(() => {
    const botData = helper.initBotFormData(user_id, 5, bot);
    botData.bot_name = bot.bot_name + "Duplicate";
    botData.bot_category = bot.bot_category || "Accounting & Tax Services";
    setFormData(botData);
  }, [bot]);

  const [errors, setErrors] = useState({
    bot_name: "",
    language: "",
    description: "",
    bot_category: "",
  });

  useEffect(() => {
    if (!isBotNameUnique()) {
      setErrors({ ...errors, bot_name: "Bot name should be unique" });
    } else {
      setErrors({ ...errors, bot_name: "" });
    }
  }, [formData]);

  const disabled = useMemo(() => {
    if (
      Boolean(
        formData.bot_name && formData.bot_category && formData.language
      ) &&
      isBotNameUnique()
    ) {
      return false;
    } else {
      return true;
    }
  }, [formData]);

  const onSubmit = async (data) => {
    setLoading(true);
    loader?.continuousStart();
    await duplicate_bot({
      ...data,
      bot_id: bot.bot_id,
      fallback_dialog: bot.fallback_dialog,
      icon: bot.icon,
      plan_id: bot.plan_id,
    });
    await get_all_bots(bot.bot_id);
    setTimeout(() => {
      setView(views.botViews.OVERVIEW_VIEW);
    }, 1000);
    setLoading(false);
    loader?.complete();
    setFormData(helper.initBotFormData(user_id, 5));
    onClose();
  };

  let bodyContent = (
    <div className="space-y-5">
      <Input
        title="Bot Name"
        subtitle="Only English is Allowed, No Spaces"
        type="text"
        name="bot_name"
        value={formData.bot_name}
        onChange={(event) => {
          const value = event.target.value;
          if (value.match(constant.IS_ENG_REGEX) || !Boolean(value)) {
            setFormData({
              ...formData,
              bot_name: value,
            });
          }
        }}
        error={errors.bot_name}
      />
      <div className="space-y-1">
        <label className="block" htmlFor="description">
          Bot Description
        </label>
        <Textarea
          value={formData.description}
          onChange={(event) => {
            setFormData({
              ...formData,
              description: event.target.value,
            });
          }}
          id="description"
          placeholder="Describe your bot!"
        />
        {errors.description ? (
          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
            <ShieldClose size={12} />
            {errors.description}
          </span>
        ) : null}
      </div>
      <Select
        onValueChange={(e) =>
          setFormData({
            ...formData,
            language: e,
          })
        }
        value={formData.language}
      >
        <SelectTrigger className="w-1/2 ">
          <SelectValue placeholder="Bot Language" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="ar">Arabic</SelectItem>
          <SelectItem value="en">English</SelectItem>
        </SelectContent>
      </Select>
      <SelectCategories
        onChange={(e) =>
          setFormData({
            ...formData,
            bot_category: e,
          })
        }
        value={formData.bot_category}
      />
    </div>
  );
  return (
    <Modal
      title="Duplicate Bot"
      onClose={onClose}
      isOpen={isOpen}
      actionLabel="Duplicate"
      onSubmit={() => onSubmit(formData)}
      body={bodyContent}
      disabled={disabled}
      loading={loading}
    />
  );
};
