import { Modal } from "common/components/modals";
import useUpdateBotModal from "views/bots/hooks/useUpdateBotModal";
import { Textarea } from "common/ui/inputs/textarea";
import React, { useEffect, useState } from "react";
import { IBot, UpdateBotInfo, UpdateBotSchema } from "store/bot/bot.types";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import { restartOne } from "apis/bot.api";
import { Input } from "common/ui/inputs/input";
import toast from "react-hot-toast";
import { z } from "zod";
import constant from "constant";
import { ShieldClose } from "lucide-react";
import checkForErrors from "helpers/forms";
import { useLoaderContext } from "context/loaderContext";

export const UpdateBotModal = ({}) => {
  const bot = useUpdateBotModal((state) => state.bot) as IBot;
  const onClose = useUpdateBotModal((state) => state.onClose);
  const isOpen = useUpdateBotModal((state) => state.isOpen);
  const { update_one_bot, bots, get_all_bots } = useBotStore();
  const { user } = useUserStore();

  const [errors, setErrors] = useState<Record<string, string>>();
  const [loading, setLoading] = useState(false);
  const { loader } = useLoaderContext();

  const [updBot, setUpdBot] = useState({ ...bot });
  useEffect(() => {
    setUpdBot({ ...bot });
  }, [bot]);

  const UpdateBotSchema = z.object({
    // user_id: z.number(),
    bot_name: z
      .string()
      .min(1, "Name is required")
      .regex(constant.IS_ENG_REGEX, "Only English is Allowed, No Spaces")
      .refine(
        (value) =>
          !bots
            .filter((a) => a.bot_id !== bot.bot_id)
            .find((a) => a.bot_name === value),
        {
          message: "Bot name is reserved, Try another one",
        }
      )
      .refine((value) => Boolean(value.trim()), {
        message: "This Field Cannot Be Blank.",
      }),
    description: z.string(),
    language: z.string().min(1, "Language is required"),
  });

  const validateField =
    (field: keyof z.infer<typeof UpdateBotSchema>) =>
    (value: unknown): string => {
      const parsedResult = UpdateBotSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setUpdBot({
      ...updBot,
      [key]: value,
    });
  };

  const onSubmit = async () => {
    if (JSON.stringify(bot) === JSON.stringify(updBot)) {
      return;
    }

    const isErrors = checkForErrors(
      {
        zodSchema: UpdateBotSchema,
        data: updBot,
      },
      setErrors
    );

    if (isErrors) return;

    setLoading(true);
    loader?.continuousStart();
    await update_one_bot({ user_id: user.user_id, ...updBot });
    toast.success("Updated Successfully");
    setTimeout(() => {
      restartOne({
        bot_id: bot.bot_id,
      });
      get_all_bots(user.user_id);
      setLoading(false);
      loader?.complete();
    }, 2000);
    onClose();
  };

  const bodyContent = (
    <div className="space-y-3">
      <Input
        title="Bot Name"
        subtitle="Only English is Allowed, No Spaces"
        type="text"
        name="bot_name"
        value={updBot.bot_name}
        onChange={(event) => {
          onChangeHandler("bot_name", event.target.value);
        }}
        error={errors?.bot_name}
      />
      <div className="space-y-1 relative">
        <label className="block" htmlFor="">
          Bot Description
        </label>
        <Textarea
          placeholder="Describe your bot!"
          value={updBot.description}
          onChange={(event) => {
            onChangeHandler("description", event.target.value);
          }}
        />
        {errors?.description && (
          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
            {errors?.description ? (
              <>
                <ShieldClose size={12} />
                {errors?.description}
              </>
            ) : null}
          </span>
        )}
      </div>
      <div className="space-y-1">
        <label className="block" htmlFor="">
          Bot Language
        </label>

        <fieldset className="flex flex-wrap gap-3">
          <legend className="sr-only">Language</legend>

          <div>
            <input
              type="radio"
              name="Language"
              value="ar"
              id="ar"
              className="peer hidden"
              checked={updBot.language === "ar"}
              onChange={() => onChangeHandler("language", "ar")}
            />

            <label
              htmlFor="ar"
              className="flex cursor-pointer items-center justify-center rounded-md border border-gray-100 py-2 px-3 text-white hover:border-gray-200 peer-checked:border-black peer-checked:bg-white peer-checked:text-black"
            >
              <p className="text-sm font-medium">Arabic</p>
            </label>
          </div>

          <div>
            <input
              type="radio"
              name="Language"
              value="en"
              id="en"
              className="peer hidden"
              checked={updBot.language === "en"}
              onChange={() => onChangeHandler("language", "en")}
            />

            <label
              htmlFor="en"
              className="flex cursor-pointer items-center justify-center rounded-md border border-gray-100 py-2 px-3 text-white hover:border-gray-200 peer-checked:border-black peer-checked:bg-white peer-checked:text-black"
            >
              <p className="text-sm font-medium">English</p>
            </label>
          </div>
        </fieldset>
      </div>
    </div>
  );

  return (
    <Modal
      title="Update Bot"
      onClose={onClose}
      isOpen={isOpen}
      actionLabel="Update"
      onSubmit={onSubmit}
      body={bodyContent}
      disabled={JSON.stringify(bot) === JSON.stringify(updBot)}
      loading={loading}
    />
  );
};
