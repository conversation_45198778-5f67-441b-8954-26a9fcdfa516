import { Modal } from "common/components/modals";
import useCreateBotModal from "views/bots/hooks/useCreateBotModal";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import { useMemo, useState } from "react";
import helper from "views/bots/helper";
import constant from "constant";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { ShieldClose, User } from "lucide-react";
import { IBot } from "store/bot/bot.types";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import { SelectCategories } from "../selectCategories";
import { z } from "zod";
import views from "views";
import Plans from "views/configure/subviews/plans/components/plans";
import checkForErrors from "helpers/forms";
import { toast } from "react-hot-toast";
import { useLoaderContext } from "context/loaderContext";
import Link from "next/link";


enum STEPS {
  INFO = 0,
  TYPE = 1,
  TEMPLATE = 2,
  PLAN = 3,
}

export const CreateBotModal = ({ setView }) => {
  const createBotModal = useCreateBotModal();
  const bots = useBotStore((state) => state.bots) as IBot[];
  const { create_bot } = useBotStore();

  const user_id = useUserStore((state) => state.user.user_id);

  const [step, setStep] = useState(STEPS.INFO);

  const [isTemplate, setIsTemplate] = useState(false);

  const [formData, setFormData] = useState(helper.initBotFormData(user_id, 3));
  const [selectedPlan, setSelectedPlan] = useState(3);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [loading, setLoading] = useState(false);
  const {loader} = useLoaderContext()

  const CreateBotSchema = z.object({
    user_id: z.number(),
    bot_name: z
      .string()
      .min(1, "Name is required")
      .regex(constant.IS_ENG_REGEX, "Only English is Allowed, No Spaces")
      .refine((value) => !bots.find((a) => a.bot_name === value), {
        message: "Bot name is reserved, Try another one",
      })
      .refine((value) => Boolean(value), {
        message: "This Field Cannot Be Blank.",
      }),
    description: z.string(),
    language: z.string().min(1, "Language is required"),
    icon: z.string(),
    bot_category: z.string().min(1, "Category is required"),
    bot_type: z.string(),
    plan_id: z.number(),
  });

  type CreateBotInfo = z.infer<typeof CreateBotSchema>;
  const actionLabel = useMemo(() => {
    // if (step === STEPS.PLAN) {
      return "Create";
    // }

    // return "Next";
  }, [step]);

  const secondaryActionLabel = useMemo(() => {
    if (step === STEPS.INFO) {
      return undefined;
    }

    return "Back";
  }, [step]);
  const onBack = () => {
    // if (!isTemplate && step === STEPS.PLAN) {
    //   return setStep((value) => value - 2);
    // }
    // setStep((value) => value - 1);
    setStep((value) => value - 3);
  };
  const onNext = () => {
    // if (!isTemplate && step === STEPS.TYPE) {
    //   return setStep((value) => value + 2);
    // }
    // setStep((value) => value + 1);
    setStep((value) => value + 3);
  };

  const onSubmit = async (data) => {
    if (step === STEPS.INFO) {
      loader?.continuousStart()
      setLoading(true);
      await toast.promise(
       create_bot({
        ...data,
        plan_id: 3})
        ,
        {
          loading: "Creating Bot...",
          success: "Bot Created Successfully",
          error: "Error Creating Bot",
        }
      );
      setFormData(helper.initBotFormData(user_id, 3));
      setSelectedPlan(3);
      setStep(STEPS.INFO);

      loader?.complete()
      setLoading(false);
      createBotModal.onClose();

      setTimeout(() => {
        setView(views.botViews.OVERVIEW_VIEW);
      }, 1000);
    } else {
      const isErrors = checkForErrors({
        zodSchema: CreateBotSchema,
        data,
      }, setErrors);
      if (isErrors) return
      onNext();
    }
  };

  const validateField =
    (field: keyof CreateBotInfo) =>
    (value: unknown): string => {
      const parsedResult = CreateBotSchema.pick({ [field]: true } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const handleClose = () => {
    setErrors({});
    setFormData(helper.initBotFormData(user_id, 3));
    createBotModal.onClose();
  };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  let bodyContent = (
    <div className="space-y-5">
      <Input
        title="Bot Name"
        subtitle="Only English is Allowed, No Spaces"
        type="text"
        name="bot_name"
        value={formData.bot_name}
        onChange={(event) => onChangeHandler("bot_name", event.target.value)}
        error={errors?.bot_name}
      />
      <div className="space-y-1">
        <label className="block" htmlFor="description">
          Bot Description
        </label>
        <Textarea
          value={formData.description}
          onChange={(event) =>
            onChangeHandler("description", event.target.value)
          }
          id="description"
          placeholder="Describe your bot!"
        />
        {errors.description ? (
          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
            <ShieldClose size={12} />
            {errors.description}
          </span>
        ) : null}
      </div>
      <div>
        <Select onValueChange={(value) => onChangeHandler("language", value)}>
          <SelectTrigger className="w-1/2">
            <SelectValue placeholder="Bot Language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ar">Arabic</SelectItem>
            <SelectItem value="en">English</SelectItem>
            <div className="relative group">
              <SelectItem value="both">
                User Choice
                <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-black text-white text-xs rounded py-1 px-2 group-hover:block">
                  Prompt user for preferred language.
                </div>
              </SelectItem>
            </div>
            <div className="relative group">
              <SelectItem value="webDefault">
                Web Default
                <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-black text-white text-xs rounded py-1 px-2 group-hover:block">
                  Start dialogs with website&apos;s default language
                </div>
              </SelectItem>
            </div>
          </SelectContent>
        </Select>
        {errors.language ? (
          <span className="text-red-500 text-xs flex gap-1 items-center pt-0">
            <ShieldClose size={12} />
            {errors.language}
          </span>
        ) : null}
      </div>
      <div>
        <SelectCategories
          onChange={(value) => onChangeHandler("bot_category", value)}
        />
        {errors.bot_category ? (
          <span className="text-red-500 text-xs flex gap-1 items-center pt-0">
            <ShieldClose size={12} />
            {errors.bot_category}
          </span>
        ) : null}
      </div>
    </div>
  );

  if (step === STEPS.TYPE) {
    bodyContent = (
      <div className="space-y-5">
        <p>Choose Bot Type</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <input
              type="radio"
              name="Type"
              value="blank"
              id="blank"
              className="peer hidden"
              checked

            />
            <label
              htmlFor="blank"
              className="flex flex-col w-full h-24 cursor-pointer rounded-md border border-l-8 border-white/25 p-5 text-white hover:border-primary hover:backdrop-brightness-200 peer-checked:border-primary  peer-checked:text-white"
            >
              <p className="text-lg text-white">Blank ChatBot</p>
              <span className="text-sm">Design your chatbot from scratch</span>
            </label>
          </div>
          <div>
            <input
              type="radio"
              name="Type"
              value="blank"
              id="blank"
              className="peer hidden"
              disabled

            />
            <label
              htmlFor="blank"
              className="flex flex-col w-full h-24 cursor-pointer rounded-md border border-l-8 border-white/25 p-5 text-white hover:border-primary hover:backdrop-brightness-200 peer-checked:border-primary  peer-checked:text-white peer-disabled:hover:border-white/25 peer-disabled:hover:backdrop-brightness-100 peer-disabled:cursor-not-allowed"
            >
        <p className="text-lg text-white">Choose a template</p>
            <span className="text-sm">Design your chatbot over a template</span>
            </label>
          </div>
        </div>
      </div>
    );
  }

  if (step === STEPS.TEMPLATE) {
    bodyContent = <div>template</div>;
  }
  if (step === STEPS.PLAN) {
    bodyContent = (
      <div className="space-y-3">
        <p>
          Choose a plan that suits your needs, you can upgrade or downgrade your
          plan at any time, {" "}
          <Link href="https://searchat.com/pricing" target="_blank" className="text-primary hover:underline hover:underline-offset-2">
            See Plans Details
          </Link>
        </p>
        <Plans 
          selectedPlan={selectedPlan}
          setSelectedPlan={setSelectedPlan}
        />
      </div>
    );
  }

  return (
    <Modal
      title="Create New Bot"
      onClose={handleClose}
      isOpen={createBotModal.isOpen}
      actionLabel={actionLabel}
      secondaryActionLabel={secondaryActionLabel}
      onSubmit={() => onSubmit(formData)}
      secondaryAction={step === STEPS.INFO ? undefined : onBack}
      body={bodyContent}
      disabled={false}
      loading={loading}
    />
  );
};
