"use client";

import constant from "constant";
import { restartOne } from "apis/bot.api";
import useUpdateBotModal from "views/bots/hooks/useUpdateBotModal";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "common/ui/dropdownMenu";
import toast from "react-hot-toast";
import {
  Copy,
  Globe,
  Link,
  PinOff,
  PlayCircle,
  RefreshCcw,
  Save,
  Settings,
  StopCircle,
  Trash2,
} from "lucide-react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useUserStore from "store/user/user.store";
import useConfirmModal from "common/hooks/useConfirmModal";
import useDuplicateBotModal from "views/bots/hooks/useDuplicateBotModal";
import { copyToClipboard } from "helpers/helper";

interface BotCardMenuProps {
  bot: IBot;
  setRenderedBots?: (bots: IBot[]) => void;
  renderedBots?: IBot[];
  children: React.ReactNode;
}

export const BotCardMenu: React.FC<BotCardMenuProps> = ({
  bot,
  setRenderedBots,
  renderedBots,
  children,
}) => {
  const botURL = `${constant.STORAGE_SERVER_URL}${bot.file_name}`;
  const { user } = useUserStore();
  const { update_one_bot, delete_one_bot, get_all_bots } = useBotStore();
  const updateBotModal = useUpdateBotModal();
  const duplicatebotModal = useDuplicateBotModal();
  const confirmModal = useConfirmModal();



  const openInNewTab = (url: string, e) => {
    e.stopPropagation();
    window.open(url, "_blank");
  };

  const stopBot = (e) => {
    e.stopPropagation();
    update_one_bot({
      user_id: user.user_id,
      ...bot,
      status: "stopped",
    });
    const botsToUpdate = [...renderedBots];
    const index = botsToUpdate.findIndex((a) => a.bot_id === bot.bot_id);
    botsToUpdate[index].status = "stopped";
    setRenderedBots([...botsToUpdate]);
    toast.success("Bot Stopped!");
  };

  const startBot = (e) => {
    e.stopPropagation();
    update_one_bot({
      user_id: user.user_id,
      ...bot,
      status: "running",
    });
    const botsToUpdate = [...renderedBots];
    const index = botsToUpdate.findIndex((a) => a.bot_id === bot.bot_id);
    botsToUpdate[index].status = "running";
    setRenderedBots([...botsToUpdate]);
    toast.success("Bot is running!");
  };

  const saveAsTemplate = (e) => {
    e.stopPropagation();
    update_one_bot({
      user_id: user.user_id,
      ...bot,
      is_template: true,
    });
    const botsToUpdate = [...renderedBots];
    const index = botsToUpdate.findIndex((a) => a.bot_id === bot.bot_id);
    botsToUpdate[index].is_template = true;
    setRenderedBots([...botsToUpdate]);

    toast.success("Bot Saved as a template!");
  };
  const removeTemplate = (e) => {
    e.stopPropagation();
    update_one_bot({
      user_id: user.user_id,
      ...bot,
      is_template: false,
    });
    const botsToUpdate = [...renderedBots];
    const index = botsToUpdate.findIndex((a) => a.bot_id === bot.bot_id);
    botsToUpdate[index].is_template = false;
    setRenderedBots([...botsToUpdate]);

    toast.success("Bot removed from templates!");
  };

  const restartBot = (e) => {
    e.stopPropagation();
    restartOne({
      bot_id: bot.bot_id,
    }).then((data) => {
      update_one_bot({
        user_id: user.user_id,
        ...bot,
        status: "restarting",
      });
    });
    const botsToUpdate = [...renderedBots];
    const index = botsToUpdate.findIndex((a) => a.bot_id === bot.bot_id);
    botsToUpdate[index].status = "restarting";
    setRenderedBots([...botsToUpdate]);

    toast.success("Bot restarted successfully!");
  };

  const handleDelete = async () => {
   await delete_one_bot({ user_id: user.user_id, bot_id: bot.bot_id });
   await get_all_bots(user.user_id)
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
        <DropdownMenuContent className="ml-8 -mb-5 w-56 bg-secondary border-white/25 text-white">
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={(e) => copyToClipboard(e, botURL)}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Link className="mr-2 h-4 w-4" />
              <span>Copy Link</span>
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={(e) => openInNewTab(botURL, e)}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Globe className="mr-2 h-4 w-4" />
              <span>Open in browser</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator className=" bg-white/25" />
          <DropdownMenuGroup>
            {bot.status === "running" || bot.status === "restarting" ? (
              <DropdownMenuItem
                onClick={(e) => stopBot(e)}
                className="cursor-pointer hover:!bg-white/25"
              >
                <StopCircle className="mr-2 h-4 w-4" />
                <span>Stop Bot</span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem
                onClick={(e) => startBot(e)}
                className="cursor-pointer hover:!bg-white/25"
              >
                <PlayCircle className="mr-2 h-4 w-4" />
                <span>Start Bot</span>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                duplicatebotModal.setBot(bot);
                setTimeout(() => duplicatebotModal.onOpen(), 200);
              }}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Copy className="mr-2 h-4 w-4" />
              <span>Duplicate Bot</span>
            </DropdownMenuItem>
            {bot?.is_template ? (
              <DropdownMenuItem
                onClick={(e) => removeTemplate(e)}
                className="cursor-pointer hover:!bg-white/25"
              >
                <PinOff className="mr-2 h-4 w-4" />
                <span>Remove from templates</span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem
                onClick={(e) => saveAsTemplate(e)}
                className="cursor-pointer hover:!bg-white/25"
              >
                <Save className="mr-2 h-4 w-4" />
                <span>Save as template</span>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={(e) => restartBot(e)}
              className="cursor-pointer hover:!bg-white/25"
            >
              <RefreshCcw className="mr-2 h-4 w-4" />
              <span>Restart Bot</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                updateBotModal.setBot(bot);
                setTimeout(() => updateBotModal.onOpen(), 200);
              }}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Update Bot</span>
            </DropdownMenuItem>{" "}
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                confirmModal.setOnConfirm(async () => await handleDelete());
                confirmModal.setType("delete");
                confirmModal.onOpen();
              }}
              className="cursor-pointer hover:!bg-white/25"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              <span>Delete Bot</span>
            </DropdownMenuItem>
            <br/>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
