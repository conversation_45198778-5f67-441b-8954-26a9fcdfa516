import React from "react";

export const BotLoadingSkeleton = () => {
  return (
    <div className="p-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
      <div className="animate-pulse min-h-[170px] p-5 rounded-lg bg-white/10 text-white "></div>
    </div>
  );
};
