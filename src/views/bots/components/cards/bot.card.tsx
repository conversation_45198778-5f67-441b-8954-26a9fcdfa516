"use client";
import { MoreHorizontal } from "lucide-react";
import { IBot } from "store/bot/bot.types";
import { BotCardMenu } from "../bot.card.menu";
import React, { useState } from "react";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";

interface BotCardProps {
  bot: IBot;
  setRenderedBots: (bots: IBot[]) => void;
  renderedBots: IBot[];
  onClick: () => void;
}

export const BotCard: React.FC<BotCardProps> = ({
  bot,
  setRenderedBots,
  renderedBots,
  onClick,
}) => {
  return (
    <div
      onClick={onClick}
      className="text-white hover:cursor-pointer relative flex flex-col gap-9 p-6 rounded-lg shadow-xl border border-gray-500"
    >
      <HoverCard>
        <HoverCardTrigger asChild>
          <div className="flex flex-col gap-4 items-center">
            {bot?.icon ? (
              <img
                className="w-32 h-32 object-cover rounded-full"
                src={bot?.icon}
                alt=""
              />
            ) : (
              <img
                className="w-32 h-32 object-cover rounded-full"
                src="https://cdn-icons-png.flaticon.com/512/4712/4712139.png"
                alt=""
              />
            )}
            <div className="flex flex-col break-words whitespace-break-spaces overflow-hidden">
              <span className="max-h-[25px] max-w-[65px] lg:max-h-fit lg:max-w-[200px] truncate">
                {bot?.bot_name}
              </span>
            </div>
          </div>
        </HoverCardTrigger>
        <HoverCardContent
          align="start"
          className="w-80 bg-black border-2 border-gray-800 shadow-xl"
        >
          <div className="">
            <p>{bot?.description}</p>
            <p>Language: {bot?.language}</p>
          </div>
        </HoverCardContent>
      </HoverCard>

      <BotCardMenu
        bot={bot}
        setRenderedBots={setRenderedBots}
        renderedBots={renderedBots}
      >
        <div className="hover:backdrop-brightness-200 h-5 flex items-center self-center justify-center rounded-lg px-1 py-2">
          <MoreHorizontal size={30} />
        </div>
      </BotCardMenu>
    </div>
  );
};
