import React from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

const categories = [
  "Accounting & Tax Services",
  "Arts, Culture & Entertainment",
  "Auto Sales & Service",
  "Banking & Finance",
  "Business Services",
  "Community Organizations",
  "Dentists & Orthodontists",
  "Education",
  "Fashion",
  "Grocery",
  "Health & Wellness",
  "Health Care",
  "Home Improvement",
  "Insurance",
  "Internet & Web Services",
  "Legal Services",
  "Lodging & Travel",
  "Marketing & Advertising",
  "News & Media",
  "Pet Services",
  "Real Estate",
  "Restaurants",
  "Shopping & Retail",
  "Sports & Recreation",
  "Transportation",
  "Travel",
  "Utilities",
  "Pharmacy",
  "Wedding, Events & Meetings",
];

interface SelectCategoriesProps {
  onChange: (value: string) => void;
  value?: string;
}

export const SelectCategories: React.FC<SelectCategoriesProps> = ({
  onChange,
  value,
}) => {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="w-1/2 ">
        <SelectValue placeholder="Bot Category" />
      </SelectTrigger>
      <SelectContent>
        {categories.map((cat, i) => {
          return (
            <SelectItem key={i} value={cat}>
              {cat}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};
