import { FC, memo, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";

interface TestingProps {
  connectionString: string;
}
const IsJsonString = (str) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
const Testing: FC<TestingProps> = ({ connectionString }) => {
  const [assets, setAssets] = useState(null);
  const [showChatBot, setShowChatBot] = useState(false);
  const bot = useBotStore((state) => state.bot);
  if (typeof window !== "undefined") {
    window.addEventListener("message", (e) => {
      if (IsJsonString(e.data)) {
        const data = JSON.parse(e.data);
        if (data.type !== "local_storage") {
          const assetsData = JSON.parse(e.data)?.data;
          if (assetsData) {
            setAssets(assetsData);
          }
        }
      }

      if (e.data === "close") {
        setShowChatBot(false);
      }
    });
  }

  useEffect(() => {
    console.log(assets);
  }, [assets]);

  return (
    // assets && (
    <>
      <iframe
        className={`h-full bottom-0 right-0 min-w-[400px] fixed z-[22222] border-none ${
          showChatBot ? "block" : "hidden"
        } `}
        src={`https://chatbot-incubator-refactor-g668y.ondigitalocean.app?bot_id=${bot.bot_id}&is_appearance=true`}
        allow="camera; microphone;clipboard-read; clipboard-write;geolocation"
      ></iframe>
      <img
        src={assets?.botIcon}
        alt=""
        id="searchat-open-chatbot-button"
        className="fixed bottom-[30px] right-[30px] border-none rounded-[80px] h-[80px] w-[80px] cursor-pointer z-40 shadow-[rgba(0,0,0,0.1)_0px_4px_6px_-1px,rgba(0,0,0,0.06)_0px_2px_4px_-1px] animate-[3s_chatbot_infinite_ease-in-out]"
        onClick={() => setShowChatBot(true)}
        style={{
          // animation: assets?.BotIconAnimation
          //   ? `3s ${assets?.BotIconAnimationType} infinite ease-in-out`
          //   : "",
          display: assets ? "block" : "none",
        }}
      />

      {assets?.tooltip && (
        <div
          id="searchat-chatbot-bubble-tooltip"
          className="fixed bottom-[90px] right-[120px] border-none rounded-[80px] rounded-br-none cursor-pointer z-40 p-[15px] max-w-[200px] shadow-[rgba(0,0,0,0.1)_0px_4px_6px_-1px,rgba(0,0,0,0.06)_0px_2px_4px+-1px] border-[1px] border-[#eee] inline-block bg-white text-black"
        >
          {assets?.tooltipText}
        </div>
      )}
    </>
  );
  // );
};

export default memo(Testing);
