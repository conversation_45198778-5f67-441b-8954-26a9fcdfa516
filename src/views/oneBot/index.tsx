import { BotSidebar } from "common/components/sidebars";
import { CardsAndSuggestionsView } from "views/cardsAndSuggestions";
import { KnowledgeBaseView } from "views/knowledgeBase";
import { OverviewView } from "views/overview";
import { useEffect, useState, useCallback, useMemo } from "react";
import { IBot } from "store/bot/bot.types";
import useBotStore from "store/bot/bot.store";
import { getConfig } from "apis/config.api";
import { IntegrationsView } from "views/integrations";
import { botConfigType } from "types/botconfig.types";
import { StoreView } from "views/store";
import { ConfigureView } from "views/configure";
import views from "views";
import { UnstructuredView } from "views/unstructured";
import useFAQStore from "store/faq/faq.store";
import useTriggerStore from "store/trigger/trigger.store";
import useGPTStore from "store/gpt/gpt.store";
import useSearchSettingsStore from "store/search/search.store";
import useTrelloStore from "store/trello/trello.store";
import useLiveChatStore from "store/livechat/livechat.store";
import DashboardView from "views/dashboard";
import useCartStore from "store/cart/cart.store";
import { EditorsView } from "views/editors";
import { getEditorPrivilages } from "apis/editors.api";
import useUserStore from "store/user/user.store";
import helper from "./helper";
import useUserPrivilegeStore from "store/priviliges/UserPrivilege.store";
import { IEditorPrivilges } from "store/priviliges/UserPrivilege.types";
import Empty from "common/components/empty";
import { FlaskConical } from "lucide-react";
import Testing from "./components/testing";
import useCalendlyStore from "store/calendly/calendly.store";
import useSendgridStore from "store/sendgrid/sendgrid.store";
import { publishFile } from "apis/file.api";
import { useTestingBot } from "common/hooks/useTestingBot";
import useSentimentAddonStore from "store/sentimentAddon/sentimentAddon.store";
import useDialogStore from "store/dialog/dialog.store";
import useItemStore from "store/item/item.store";
import useCategoryStore from "store/category/category.store";
import useOfferStore from "store/offers/offers.store";
import useLeadsAndReportStore from "store/leadsAndReports/leadsAndReports.store";
import { BroadcastView } from "views/broadcast";
import useTOPICStore from "store/topic/topic.store";
import { LogsView } from "views/logs";
import { LiveChatsView } from "views/internalLiveChat";
import useBadWordStore from "store/badword/badword.store";
import useBotConfigStore from "store/botConfig/botConfig.store";
import useInternalLiveChatStore from "store/internalLivechat/internalLivechat.store";
import useTicketingStore from "store/ticketing/ticketing.store";


interface OneBotViewProps {
  view: string;
  setView: (view: string) => void;
}

export const OneBotView: React.FC<OneBotViewProps> = ({ view, setView }) => {
  const { bot, get_one_bot } = useBotStore();
  const user = useUserStore((state) => state.user);
  const { get_all_faqs } = useFAQStore();
  const { get_all_topics } = useTOPICStore();
  const { get_all_triggers } = useTriggerStore();
  const { get_one_gpt } = useGPTStore();
  const { get_one_searchSettings } = useSearchSettingsStore();
  const { get_one_trello } = useTrelloStore();
  const { get_one_livechat } = useLiveChatStore();
  const { get_one_cart } = useCartStore();
  const { get_one_calendly } = useCalendlyStore();
  const { get_one_sendgrid } = useSendgridStore();
  const { getSentimentAddon } = useSentimentAddonStore();
  const { get_all_items } = useItemStore();
  const { get_all_categories } = useCategoryStore();
  const { get_all_offers } = useOfferStore();
  const { get_leads_status, get_reports_status } = useLeadsAndReportStore();
  const { get_agent, get_internal_livechat, agent } =
    useInternalLiveChatStore();

  const { get_one_ticketing, get_support_agent, support_agent } =
    useTicketingStore();

  // useTestingBot();
  const getAllDialogs = useDialogStore((state) => state.getAll);

  const { set_user_priviliges, set_is_admin } = useUserPrivilegeStore();
  const [editorPrivilages, setEditorPrivilages] = useState(
    helper.initEditorPriviliges(false)
  );
  const [connectionString, setConnectionString] = useState(undefined);

  const { priviliges, isAdmin } = useUserPrivilegeStore();
  const hasEditorAccess = (privilege: keyof IEditorPrivilges | undefined) => {
    return Boolean(priviliges[privilege] || user.user_id === 17 || isAdmin);
  };

  useEffect(() => {
    set_user_priviliges(helper.initEditorPriviliges(false));
    set_is_admin(false);
    if (bot) {
      getEditorPrivilages(bot.bot_id || 1, user.user_id).then((data) => {
        if (!data?.message) {
          set_user_priviliges({
            appearance_privilege: data?.appearance_privilege,
            builder_privilege: data?.builder_privilege,
            store_privilege: data?.store_privilege,
            deployment_privilege: data?.deployment_privilege,
            sales_dashboard_privilege: data?.sales_dashboard_privilege,
            transaction_dashboard_privilege:
              data?.transaction_dashboard_privilege,
            editor_privilege: data?.editor_privilege,
            lead_privilege: data?.lead_privilege,
            report_privilege: data?.report_privilege,
            addon_privilege: data?.addon_privilege,
            dialog_privilege: data?.dialog_privilege,
            agent_privilege: data?.agent_privilege,
            ticketing_privilege: data?.ticketing_privilege,
          });
        } else if (data?.message === "User is an admin") {
          set_user_priviliges(helper.initEditorPriviliges(true));
          set_is_admin(true);
        } else if (data?.message === "User is not authorizated") {
          set_user_priviliges(helper.initEditorPriviliges(false));
        }
      });
    }
  }, [bot]);

  const [botConfig, setBotConfig] = useState<botConfigType>();

  const { get_bot_config } = useBotConfigStore();
  const { get_all_badwords } = useBadWordStore();

  const getBotData = useCallback(
    async (bot: IBot) => {
      try {
        await Promise.all([
          // get_one_bot(bot.bot_id),
          get_all_faqs(bot.bot_id),
          get_all_topics(bot.bot_id),
          get_all_triggers(bot.bot_id),
          get_one_gpt(bot.bot_id),
          get_one_searchSettings(bot.bot_id),
          get_one_trello(bot.bot_id),
          get_one_livechat(bot.bot_id),
          get_one_cart(bot.bot_id),
          get_one_calendly(bot.bot_id),
          get_one_sendgrid(bot.bot_id),
          getAllDialogs(bot.bot_id),
          getSentimentAddon(bot.bot_id),
          get_all_items(bot.bot_id),
          get_all_categories(bot.bot_id),
          get_all_offers(bot.bot_id),
          get_leads_status(bot.bot_id),
          get_reports_status(bot.bot_id),
          get_all_badwords(bot.bot_id),
          get_agent(bot.bot_id, user.user_id),
          get_internal_livechat(bot.bot_id),
          get_one_ticketing(bot.bot_id),
          get_support_agent(user.user_id, bot.bot_id),
        ]);
      } catch (error) {
        console.error("Error fetching bot data:", error);
      }
    },
    [bot]
  );

  const hasAgentAccess = () => {
    const isAgentPrivilege = Boolean(priviliges["agent_privilege"]);
    return isAgentPrivilege && agent?.agent_id;
  };

  useEffect(() => {
    getBotData(bot);
    get_bot_config(bot.bot_id);
    getConfig(bot.bot_id).then((data) => {
      if (data) {
        setBotConfig({
          ...data,
        });
      }
    });
    publishFile({ file_name: bot.file_name }).then((data) => {
      setConnectionString(data.url);
    });
  }, [bot]);

  return (
    <div>
      <BotSidebar view={view} setView={setView} />
      <div className="pl-64 pb-32">
        <div className="p-12">
          {view === views.botViews.OVERVIEW_VIEW ? (
            <OverviewView setView={setView} botConfig={botConfig} />
          ) : view === views.botViews.KNOWLEDGE_BASE_VIEW &&
            (hasEditorAccess("builder_privilege") ||
              hasEditorAccess("dialog_privilege")) ? (
            <KnowledgeBaseView
              setView={setView}
              botConfig={botConfig}
              setBotConfig={setBotConfig}
            />
          ) : view === views.botViews.INTEGRATIONS_VIEW &&
            hasEditorAccess("addon_privilege") ? (
            <IntegrationsView setView={setView} />
          ) : view === views.botViews.CONFIGURE_VIEW &&
            (hasEditorAccess("appearance_privilege") ||
              hasEditorAccess("deployment_privilege")) ? (
            <ConfigureView setView={setView} />
          ) : view === views.botViews.STORE_VIEW &&
            hasEditorAccess("store_privilege") ? (
            <StoreView />
          ) : view === views.botViews.DASHBOARD_VIEW &&
            (hasEditorAccess("sales_dashboard_privilege") ||
              hasEditorAccess("transaction_dashboard_privilege")) ? (
                <DashboardView />
          ) : view === views.botViews.EDITORS_VIEW &&
            hasEditorAccess("editor_privilege") ? (
            <EditorsView />
          ) : view === views.botViews.BROADCAST_VIEW ? (
            <BroadcastView />
          ) : view === views.botViews.LOG_VIEW ? (
            <LogsView />
          ) : view === views.botViews.AGENT_VIEW && hasAgentAccess() ? (
            <LiveChatsView />
          ) : (
            <div className="flex flex-col justify-center items-center h-full mt-[20%]">
              <Empty />
              <span>Page Not Found</span>
              <span>404</span>
            </div>
          )}
        </div>
      </div>

      {/* <Testing connectionString={connectionString} /> */}
    </div>
  );
};
