import { uploadBotImages } from "apis/file.api";
import { BotIcon } from "common/components/botIcon";
import { MainPageHeader } from "common/components/headers";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import constant from "constant";
import generateStorageId from "helpers/generateStorageId";
import { handleUploadImg } from "helpers/media";
import { X } from "lucide-react";
import { FC, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useUserStore from "store/user/user.store";

interface ProfileViewProps {}

export const ProfileView: FC<ProfileViewProps> = ({}) => {
  const { user, update_profile } = useUserStore();

  const [enableSave, setEnableSave] = useState(false);

  const [imageToUpload, setImageToUpload] = useState({
    path: "",
    formData: null,
  });

  const [userData, setUserData] = useState({
    ...user,
  });

  const uploadImageHandler = (event) => {
    const file = event.target.files[0];
    const name = event.target.name;
    console.log(name, "===>", file);

    if (!file) return;

    const formData = new FormData();
    formData.append("file", file);

    const path = `userAssets/${generateStorageId()}.${
      file?.type?.split("/")[1]
    }`;

    setImageToUpload({
      path,
      formData,
    });

    setTimeout(() => {
      onChangeHandler("photo", constant.MEDIA_STORAGE_URL + path);
    }, 100);
    setTimeout(() => {
      handleUploadImg(`img-${name}`, file);
    }, 200);
  };

  useEffect(() => {
    if (
      JSON.stringify(userData) !== JSON.stringify(user) &&
      userData?.user_name?.trim() !== ""
    ) {
      setEnableSave(true);
    } else {
      setEnableSave(false);
    }
  }, [userData]);

  useEffect(() => {
    setUserData({
      ...user,
    });
  }, [user]);

  const onChangeHandler = (key, value) => {
    setUserData({
      ...userData,
      [key]: value,
    });
  };

  const onSave = async () => {
    if (userData?.user_name.trim() === "") {
      return;
    }

    if (userData?.photo && imageToUpload?.path !== "") {
      await uploadBotImages({
        formData: imageToUpload?.formData,
        path: imageToUpload?.path,
      });
    }

    update_profile(userData);
  };

  return (
    <div className="p-10 px-16 space-y-5">
      <MainPageHeader
        title={`Welcome ${userData?.user_name}!`}
        description="Overview and edit your profile."
      />
      <div className="flex justify-center">
        <BotIcon
          key="profilepic"
          icon={userData?.photo}
          isUpload
          name="profilepic"
          uploadImg={uploadImageHandler}
          className="h-32 w-32 rounded-full"
        />
        {userData?.photo && (
          <X
            className="h-4 w-4 cursor-pointer bg-red-900 rounded-full"
            onClick={() => {
              onChangeHandler("photo", "");
              setImageToUpload({
                path: "",
                formData: null,
              });
            }}
          />
        )}
      </div>
      <div className="flex flex-col mx-auto gap-5 border rounded-lg w-2/3 p-5">
        <Input
          name="name"
          title="Name"
          placeholder="Name"
          value={userData?.user_name}
          onChange={(e) => onChangeHandler("user_name", e.target.value)}
        />
        <Input
          name="company_name"
          title="Company Name"
          placeholder="Company"
          value={userData?.company_name}
          onChange={(e) => onChangeHandler("company_name", e.target.value)}
        />
        <Input
          name="email"
          title="Email"
          placeholder="email"
          disabled
          value={userData?.email}
        />
        <div className="self-end flex gap-5">
          <Button
            variant="outline"
            onClick={() => {
              setUserData({
                ...user,
              });
            }}
            disabled={!enableSave && userData?.user_name.trim() !== ""}
          >
            Reset
          </Button>
          <Button
            onClick={() => {
              toast.promise(onSave(), {
                loading: "Saving...",
                success: <b>Profile saved!</b>,
                error: <b>Could not save.</b>,
              });
            }}
            disabled={!enableSave}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};
