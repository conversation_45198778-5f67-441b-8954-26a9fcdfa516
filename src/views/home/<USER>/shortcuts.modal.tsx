import { FullModal } from "common/components/modals";
import { FC, memo, useEffect, useState } from "react";
import BotsMenu from "./botsMenu";
import useBotStore from "store/bot/bot.store";
import { createOne } from "apis/shortcuts.api";
import useUserStore from "store/user/user.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import views from "views";
import { Trash2 } from "lucide-react";
import { toast } from "react-hot-toast";
import { getOne } from "apis/bot.api";

interface ShortcutsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  shortcuts: any[];
  setShortcuts: (shortcuts: any[]) => void;
}

const ShortcutsModal: FC<ShortcutsModalProps> = ({
  showModal,
  setShowModal,
  shortcuts,
  setShortcuts,
}) => {
  const user = useUserStore((state) => state.user);
  const { bots, get_one_bot, bot } = useBotStore();
  const handleClose = () => {
    setShortcutsToEdit([]);
    setShowModal(false);
  };

  const [shortcutsToEdit, setShortcutsToEdit] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (shortcuts) {
      setShortcutsToEdit(shortcuts);
    } else {
      setShortcutsToEdit([]);
    }
  }, [shortcuts, showModal]);

  const addShortcut = (bot_id) => {
    const newShortcut = {
      bot_id: +bot_id,
      view: "OVERVIEW_VIEW",
    };
    setShortcutsToEdit([...shortcutsToEdit, newShortcut]);
  };

  const removeShortcut = (index) => {
    const newShortcuts = [...shortcutsToEdit];
    newShortcuts.splice(index, 1);
    setShortcutsToEdit(newShortcuts);
  };

  const onPageChangeHandler =  (value, index) => {
    const newShortcuts = JSON.parse(JSON.stringify([...shortcutsToEdit]));
    newShortcuts[index].view = value;
    setShortcutsToEdit(newShortcuts);
  }

  const onSave = async () => {
    setLoading(true);
    const res = await createOne({
      user_id: user.user_id,
      shortcuts: shortcutsToEdit,
    });

    if (res) {
      setShowModal(false);
      setShortcuts(JSON.parse(res.shortcuts));
      toast.success("Shortcuts updated successfully");
    } else {
      toast.error("Something went wrong");
    }
    setLoading(false);
  };

  return (
    <FullModal
      title="Configure Shortcuts"
      isOpen={showModal}
      onClose={handleClose}
      disabled={JSON.stringify(shortcuts) === JSON.stringify(shortcutsToEdit)}
      onSave={onSave}
      footer
      loading={loading}
    >
      <div className="relative">
        <div className="bg-secondary py-3 space-y-2 w-full mb-3 sticky -top-5">
          <div>Add Shortcut</div>
          <BotsMenu
            bots={[
              ...bots.map((a) => {
                return {
                  value: a.bot_id.toString(),
                  label: a.bot_name,
                };
              }),
            ]}
            onSelect={(value) => {
              addShortcut(value);
            }}
          />
        </div>
        <div className="h-[300px]">
          {shortcutsToEdit?.map((shortcut, index) => {
            return (
              <div
                className=" mb-2 bg-accent/50 rounded-lg p-2 pb-3"
                key={index}
              >
                <div className="pb-3 capitalize text-primary font-bold flex justify-between items-center">
                  {
                    bots?.find((bot) => bot.bot_id === +shortcut.bot_id)
                      ?.bot_name
                  }
                  <Trash2
                    size={18}
                    className="text-white/50 hover:text-red-500 cursor-pointer"
                    onClick={() => {
                      removeShortcut(index);
                    }}
                  />
                </div>
                <div className="flex gap-2 items-center">
                  <div>Page:</div>
                  <Select
                    value={shortcut.view}
                    onValueChange={(value) => {
                      onPageChangeHandler(value, index)
                    }}
                  >
                    <SelectTrigger className="min-w-[180px] capitalize">
                      <SelectValue placeholder="Select a page" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.keys(views.botViews)
                        .filter((viewKey) => {
                          return bots?.find(
                            (bot) => bot.bot_id === +shortcut.bot_id
                          )?.plan_id !== 3
                            ? viewKey !== "DASHBOARD_VIEW" &&
                                viewKey !== "PLUGINS_VIEW"
                            : viewKey;
                        })
                        .map((view, index) => {
                          return (
                            <SelectItem
                              value={view}
                              key={index}
                              className="capitalize"
                            >
                              {view
                                .replaceAll("_", " ")
                                .replaceAll(" VIEW", "")}
                            </SelectItem>
                          );
                        })}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </FullModal>
  );
};

export default memo(ShortcutsModal);
