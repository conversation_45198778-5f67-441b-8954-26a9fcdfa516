import useCreateBotModal from "views/bots/hooks/useCreateBotModal";
import { <PERSON><PERSON> } from "common/ui/button";
import { PlusCircle } from "lucide-react";
import React from "react";

export const HomeCard = () => {
  const createBotModal = useCreateBotModal();
  return (
    <div className="border-t-4 border-primary min-h-[170px] p-5 rounded-lg bg-white/10 text-white/60 flex flex-col gap-5">
      <div className="flex flex-col gap-1 mb-3">
        <h1 className="text-white text-xl">Create your next chatbot</h1>
        <span className="capitalize">Add another one to the mix!</span>
      </div>

      <hr className="text-white/50" />

      <Button onClick={createBotModal.onOpen} className="!self-end">
        <PlusCircle className="mr-2 h-4 w-4" />
        Create New Bot
      </Button>
    </div>
  );
};
