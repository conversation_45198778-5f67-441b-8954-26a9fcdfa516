import { <PERSON><PERSON> } from "common/ui/button";
import { Card } from "common/ui/card";
import { MessageSquare } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { HomeCard, ShortcutsModal } from "./components";
import views from "views";
import useContactModal from "views/contact/hooks/useContactModal";
import useUserStore from "store/user/user.store";
import { getAll } from "apis/shortcuts.api";
import useBotStore from "store/bot/bot.store";
import { BotIcon } from "common/components/botIcon";
import useFAQStore from "store/faq/faq.store";
import useTriggerStore from "store/trigger/trigger.store";
import useGPTStore from "store/gpt/gpt.store";
import useSearchSettingsStore from "store/search/search.store";
import useTrelloStore from "store/trello/trello.store";
import useLiveChatStore from "store/livechat/livechat.store";
import { IBot } from "store/bot/bot.types";
import useTOPICStore from "store/topic/topic.store";

interface HomeViewProps {
  showView?: boolean;
  setView: (view: string) => void;
}

export const HomeView: React.FC<HomeViewProps> = ({ setView }) => {
  const { bots, get_one_bot, get_all_bots } = useBotStore();
  const user = useUserStore((state) => state.user);
  const { get_all_faqs } = useFAQStore();
  const { get_all_topics } = useTOPICStore();
  const { get_all_triggers } = useTriggerStore();
  const { get_one_gpt } = useGPTStore();
  const { get_one_searchSettings } = useSearchSettingsStore();
  const { get_one_trello } = useTrelloStore();
  const { get_one_livechat } = useLiveChatStore();
  const contactModal = useContactModal();

  const [showModal, setShowModal] = useState(false);
  const [shortcuts, setShortcuts] = useState([]);

  useEffect(() => {
    getAll(user.user_id).then((res) => {
      if (res) {
        setShortcuts(JSON.parse(res.shortcuts));
      } else {
        setShortcuts([]);
      }
    });

    get_all_bots(user.user_id);
  }, []);

  const getBotData = useCallback(async (bot: IBot) => {
    await get_one_bot(bot.bot_id);
    await get_all_faqs(bot.bot_id);
    await get_all_topics(bot.bot_id);
    await get_all_triggers(bot.bot_id);
    await get_one_gpt(bot.bot_id);
    await get_one_searchSettings(bot.bot_id);
    await get_one_trello(bot.bot_id);
    await get_one_livechat(bot.bot_id);
  }, []);

  useEffect(() => {
    setTimeout(() => setView(views.mainViews.HOME_VIEW), 500);
  }, []);
  return (
    <>
      <ShortcutsModal
        showModal={showModal}
        setShowModal={setShowModal}
        shortcuts={shortcuts}
        setShortcuts={setShortcuts}
      />
      <div className={`flex`}>
        <div className="p-10 flex flex-col gap-16 md:10/12 lg:w-11/12">
          <div className="flex flex-col gap-5">
            <div className="p-5 flex flex-wrap justify-between items-center">
              <div className=" flex flex-col gap-3">
                <h1 className="text-white text-4xl font-bold capitalize">
                  Welcome {user.user_name}!
                </h1>
                <p className="capitalize">Start your journey here!</p>
              </div>
              <Button variant="outline" onClick={() => contactModal.onOpen()}>
                <MessageSquare className="mr-2 h-4 w-4" />
                Leave Your Feedback
              </Button>
            </div>
            <hr className="text-white/50" />
            <div className="p-5 grid grid-cols-3 gap-2">
              <HomeCard />
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between pl-5">
              <h1 className="text-white font-bold text-xl">Shortcuts!</h1>
              <Button variant="link" onClick={() => setShowModal(true)}>
                Configure Shortcuts
              </Button>
            </div>
            <hr className="text-white/25" />
            {shortcuts.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 p-5">
                {shortcuts.map((shortcut, index) => {
                  const bot = bots?.find(
                    (bot) => bot.bot_id === +shortcut.bot_id
                  );
                  return (
                    <Card
                      key={index}
                      className="cursor-pointer"
                      onClick={async () => {
                        await getBotData(bot);
                        // setTimeout(
                        setView(views.botViews[shortcut.view]);
                        //   500
                        // );
                      }}
                    >
                      <div className="flex flex-col gap-3">
                        <div className="font-bold text-lg capitalize flex gap-2 items-center">
                          <BotIcon icon={bot?.icon} />
                          <div className="flex flex-col">
                            {bot?.bot_name}

                            <span className="text-white/50 text-sm font-normal">
                              {shortcut.view
                                .replaceAll("_", " ")
                                .replaceAll(" VIEW", "")}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
