import { FC, memo } from "react";
import Select from "react-select";

interface BotsMenuProps {
  bots: Record<"value" | "label", string>[];
  onSelect: (value: string) => void;
}

const BotsMenu: FC<BotsMenuProps> = ({ bots, onSelect }) => {
  const value = { value: "", label: "Select a bot" };
  return (
    <Select
      value={value}
      options={bots}
      onChange={(e) => {
        if (e) onSelect(e.value);
      }}
      // isClearable
      styles={{
        control: (provided, state) => ({
          ...provided,
          backgroundColor: "#1F1F1F",
          border: "none",
          borderRadius: "0.5rem",
          boxShadow: "none",
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          minHeight: "2.5rem",
          padding: "0.3rem 1rem",
          "&:hover": {
            borderColor: "#fff",
          },
          "&:placeholder": {
            color: "#fff",
          },
        }),
        menu: (provided, state) => ({
          ...provided,
          backgroundColor: "#1F1F1F",
          border: "none",
          borderRadius: "0.5rem",
          boxShadow: "none",
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          padding: "0.5rem 1rem",
          zIndex: 9999,
          "&:hover": {
            borderColor: "#fff",
          },
        }),
        option: (provided, state) => ({
          ...provided,
          backgroundColor: state.isSelected ? "#1F1F1F" : "transparent",
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          padding: "0.5rem 1rem",
          "&:hover": {
            backgroundColor: "#1F1F1F",
            color: "#fff",
          },
        }),
        singleValue: (provided, state) => ({
          ...provided,
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
        }),
        valueContainer: (provided, state) => ({
          ...provided,
          padding: "0.3rem 1rem",
        }),
        input: (provided, state) => ({
          ...provided,
          color: "#fff",
          fontSize: "1rem",
          fontWeight: 400,
          border: "none",
          boxShadow: "none !important",
          "&:focus": {
            borderColor: "#fff",
            boxShadow: "none !important",
          },
        }),
      }}
    />
  );
};

export default memo(BotsMenu);
