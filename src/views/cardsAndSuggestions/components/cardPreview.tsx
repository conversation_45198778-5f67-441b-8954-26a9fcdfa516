import { getFile } from "apis/file.api";
import constant from "constant";
import { FC, useEffect, useState } from "react";
import { ITrigger } from "store/trigger/trigger.types";
import {
  Navigation,
  Pagination,
  Scrollbar,
  A11y,
  EffectFade,
  EffectCards,
  EffectCoverflow,
} from "swiper";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/effect-cards";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { useLangStore } from "store/language/lang.store";

type CardPreviewProps = {
  trigger?: ITrigger;
  isDialog?: boolean;
  dialogCard?: any;
};

export const CardPreview: FC<CardPreviewProps> = ({
  trigger,
  isDialog = false,
  dialogCard,
}) => {
  const [cards, setCards] = useState([]);

  useEffect(() => {
    if (trigger) {
      getFile(trigger.url).then((data) => {
        if (data) {
          const json_data = JSON.parse(data.file_data);
          setCards(
            json_data.attachments.map((a, index) => {
              return {
                body: [...a.body],
                actions: [...a.actions],
                order: index + 1,
              };
            })
          );
        }
      });
    } else if (isDialog) {
      setCards(
        dialogCard.attachments.map((a, index) => {
          return {
            body: [...a.body],
            actions: [...a.actions],
            order: index + 1,
          };
        })
      );
    }
  }, [trigger, isDialog]);

  return (
    <>
      {cards.length > 1 ? (
        <Swiper
          effect={"coverflow"}
          coverflowEffect={{
            slideShadows: false,
          }}
          grabCursor={true}
          pagination={{
            clickable: true,
          }}
          slidesPerView={"auto"}
          spaceBetween={10}
          modules={[Pagination, Navigation, EffectFade, EffectCoverflow]}
          className="mySwiper w-full flex justify-center items-center !text-red-500"
        >
          {cards?.map((card, index) => {
            console.log(card);

            return (
              <SwiperSlide className="!w-[85%]" key={index}>
                {" "}
                <OneCard card={card} isDialog={isDialog} />
              </SwiperSlide>
            );
          })}
        </Swiper>
      ) : cards.length === 1 ? (
        <OneCard card={cards[0]} isDialog={isDialog} />
      ) : null}
    </>
  );
};

const OneCard = ({ card, isDialog }) => {
  const { lang } = useLangStore();
  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
      className={`${
        isDialog ? "w-48 h-auto " : "absolute right-20 -top-5 w-56"
      } flex max-w-xs flex-col overflow-hidden rounded-lg border border-gray-100 bg-white bg-opacity-90 backdrop-blur-lg shadow-md  z-50`}
    >
      <div
        className={`relative mx-3 mt-3 flex pb-3 rounded-xl ${
          isDialog ? "w-48 h-auto" : ""
        }`}
      >
        <div className="flex flex-col w-full">
          {Boolean(
            isDialog
              ? card?.body.find((a) => a.id === "image")?.url[lang]
              : card?.body.find((a) => a.id === "image")?.url
          ) ? (
            <img
              className={`rounded-lg shadow-lg max-w-full ${
                isDialog ? "h-32 w-40" : "h-48"
              }  object-cover`}
              src={
                isDialog
                  ? card?.body.find((a) => a.id === "image")?.url[lang]
                  : card?.body.find((a) => a.id === "image")?.url
              }
            />
          ) : null}

          {Boolean(
            isDialog
              ? card?.body.find((a) => a.id === "video")?.sources[0]?.url[lang]
              : card?.body.find((a) => a.id === "video")?.sources[0]?.url
          ) ? (
            <div style={{ width: "100%" }}>
              <video controls autoPlay muted className="max-w-full">
                <source
                  src={
                    isDialog
                      ? card?.body.find((a) => a.id === "video")?.sources[0]
                          ?.url[lang]
                      : card?.body.find((a) => a.id === "video")?.sources[0]
                          ?.url
                  }
                ></source>
              </video>
            </div>
          ) : null}

          {Boolean(
            isDialog
              ? card?.body.find((a) => a.id === "heading")?.text[lang]
              : card?.body.find((a) => a.id === "heading")?.text
          ) ? (
            <div
              className={`font-bold text-black ${
                isDialog ? "text-sm w-40" : "text-xl my-2"

              }`}
              style={{
                textAlign: constant.IS_AR_REGEX.test(
                  isDialog
                    ? card?.body.find((a) => a.id === "heading").text[lang]
                    : card?.body.find((a) => a.id === "heading").text
                )
                  ? "right"
                  : "left",
                direction: constant.IS_AR_REGEX.test(
                  isDialog
                    ? card?.body.find((a) => a.id === "heading").text[lang]
                    : card?.body.find((a) => a.id === "heading").text
                )
                  ? "rtl"
                  : "ltr",
              }}
              dangerouslySetInnerHTML={{
                __html: isDialog
                  ? card?.body.find((a) => a.id === "heading").text[lang]
                  : card?.body.find((a) => a.id === "heading").text,
              }}
            ></div>
          ) : null}
          {Boolean(
            isDialog
              ? card?.body.find((a) => a.id === "subheading")?.text[lang]
              : card?.body.find((a) => a.id === "subheading")?.text
          ) ? (
            <div
              className={` ${
                isDialog ? "text-xs w-40" : "max-w-[250px] mt-3 text-sm"
              } text-black`}
              style={{
                marginBottom: "10px",
                textAlign: constant.IS_AR_REGEX.test(
                  isDialog
                    ? card?.body.find((a) => a.id === "subheading")?.text[lang]
                    : card?.body.find((a) => a.id === "subheading")?.text
                )
                  ? "right"
                  : "left",
                direction: constant.IS_AR_REGEX.test(
                  isDialog
                    ? card?.body.find((a) => a.id === "subheading")?.text[lang]
                    : card?.body.find((a) => a.id === "subheading")?.text
                )
                  ? "rtl"
                  : "ltr",
              }}
              dangerouslySetInnerHTML={{
                __html: isDialog
                  ? card?.body.find((a) => a.id === "subheading")?.text[lang]
                  : card?.body.find((a) => a.id === "subheading")?.text,
              }}
            ></div>
          ) : null}
          <div
            className={`${
              isDialog ? "w-9/12 mb-5 " : "w-full"
            }  flex flex-col gap-2 `}
          >
            {card?.actions.map((action: any, i: number) => {
              return (
                <button
                  key={i}
                  className=" text-sm text-white rounded-md px-2  py-1 shadow-lg shadow-black/50 hover:brightness-110 "
                  style={{
                    backgroundImage: `linear-gradient(to left, rgb(0 0 0 / 0.5) ,  rgb(0 0 0 / 0.5))`,
                  }}
                >
                  {isDialog ? action.title[lang] : action.title}
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
