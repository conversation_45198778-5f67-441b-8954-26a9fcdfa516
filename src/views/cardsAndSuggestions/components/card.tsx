import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { useEffect, useState } from "react";
import {
  CreateTriggerInfo,
  CreateTriggerSchema,
  ITrigger,
} from "store/trigger/trigger.types";
import {
  AlternativesInputs,
  CardEditor,
  UserQuestion,
} from "views/knowledgeBase/components";
import helper from "../helper";
import useUserStore from "store/user/user.store";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useTriggerStore from "store/trigger/trigger.store";
import generateStorageId from "helpers/generateStorageId";
import { getFile, uploadBotImages, uploadFile } from "apis/file.api";
import toast from "react-hot-toast";
import { getAlternative } from "apis/trigger.api";
import { InfoIcon } from "lucide-react";
import checkForErrors from "helpers/forms";

interface CardActionProps {
  trigger?: ITrigger;
  isUpdate?: boolean;
  setOpen: (open: boolean) => void;
}

export const CardAction: React.FC<CardActionProps> = ({
  trigger,
  isUpdate,
  setOpen,
}) => {
  const user_id = useUserStore((state) => state.user).user_id;
  const bot = useBotStore((state) => state.bot) as IBot;
  const { update_many_triggers, create_many_triggers, delete_one_trigger } =
    useTriggerStore();

  const [cardData, setCardData] = useState([]);

  const [cardsToUpload, setCardsToUpload] = useState({
    attachments: [],
    attachmentLayout: "",
  });
  const [imagesToUplaod, setImagesToUpload] = useState([]);

  const [alternatives, setAlternatives] = useState([]);
  const [copiedAlternatives, setCopiedAlternatives] = useState([]);
  const [editedAlternatives, setEditedAlternatives] = useState<boolean>(false);
  const [newAlternatives, setNewAlternatives] = useState([]);

  const [formData, setFormData] = useState(
    helper.initTriggerFormData(user_id, bot.bot_id, trigger ? trigger : null)
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  var textarea = document.getElementById("myInput2");
  textarea?.addEventListener("input", function () {
    textarea.style.height = "auto";
    var textHeight = textarea.scrollHeight;
    textarea.style.height = textHeight + "px";
  });

  const getAllAlters = () => {
    getAlternative(bot.bot_id, trigger.trigger_id, trigger.url).then((data) => {
      if (data) {
        const alts = data.filter((a) => a.trigger_id !== trigger.trigger_id);
        setAlternatives([...alts]);
      }
    });
  };

  useEffect(() => {
    if (trigger && isUpdate) {
      getAllAlters();
    }
  }, []);

  useEffect(() => {
    setCopiedAlternatives([...alternatives]);
  }, [alternatives]);

  useEffect(() => {
    if (isUpdate && trigger) {
      getFile(trigger.url).then((data) => {
        if (data) {
          const json_data = JSON.parse(data.file_data);
          setCardData([
            ...json_data.attachments.map((a, index) => {
              return {
                body: [...a.body],
                actions: [...a.actions],
                order: index + 1,
              };
            }),
          ]);
        }
      });
    }
  }, [trigger]);

  //   SECTION handle form

  const validateField =
    (field: keyof CreateTriggerInfo) =>
    (value: unknown): string => {
      const parsedResult = CreateTriggerSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  const updateCards = (cards) => setCardsToUpload(cards);

  const uploadImageHandler = (image) => {
    const allImg = [...imagesToUplaod];
    allImg.push(image);
    setImagesToUpload([...allImg]);
  };

  const handleCreate = () => {
    const card_path = `Bots/${
      bot.file_name
    }/CardsConfig/${generateStorageId()}.json`;

    uploadFile({
      path: card_path,
      file_body: JSON.stringify({
        ...cardsToUpload,
      }),
    }).then(async (data) => {
      imagesToUplaod.map(
        async (image) =>
          await uploadBotImages({ ...image, path: image?.path + "&image=true" })
      );
      const triggers_to_create = [
        {
          trigger: formData.trigger.trim().replace(/\s+/g, " "),
          bot_id: bot.bot_id,
          url: card_path,
          trigger_name: formData.trigger_name.trim().replace(/\s+/g, " "),
          trigger_type: "card",
        },
      ];
      const alternativesToCreate = newAlternatives?.filter(
        (input) => input.trigger?.replace(/ /g, "") != ""
      );
      alternativesToCreate?.map((alter) => {
        triggers_to_create.push({
          trigger: alter,
          bot_id: bot.bot_id,
          url: card_path,
          trigger_name: formData.trigger_name.trim().replace(/\s+/g, " "),
          trigger_type: "card",
        });
      });
      create_many_triggers(triggers_to_create).then((res) => {
        if (alternativesToCreate?.length) {
          toast.success("Card created with alternatives");
        } else {
          toast.success("Card created successfully");
        }
        setErrors({});
        setFormData(helper.initTriggerFormData(user_id, bot.bot_id));
        setOpen(false);
      });
    });
  };

  const handleUpdate = () => {
    if (
      cardData.every((card, index) => {
        return (
          JSON.stringify(card.body) ===
            JSON.stringify(cardsToUpload.attachments[index].body) &&
          JSON.stringify(card.actions) ===
            JSON.stringify(cardsToUpload.attachments[index].actions)
        );
      }) &&
      trigger?.trigger === formData.trigger &&
      trigger?.trigger_name === formData.trigger_name &&
      newAlternatives?.every(
        (input) => input.trigger?.replace(/ /g, "") == ""
      ) &&
      !editedAlternatives
    ) {
      toast("No changes made", {
        icon: <InfoIcon className="text-blue-500" />,
      });
      return;
    } else {
      const card_path = trigger.url;
      uploadFile({
        path: card_path,
        file_body: JSON.stringify({
          ...cardsToUpload,
        }),
      }).then(async (data) => {
        imagesToUplaod.map(
          async (image) =>
            await uploadBotImages({
              ...image,
              path: image?.path + "&image=true",
            })
        );

        const alternativesToCreate = newAlternatives?.filter(
          (input) => input.trigger?.replace(/ /g, "") != ""
        );

        if (alternativesToCreate?.length) {
          create_many_triggers([
            ...alternativesToCreate.map((alter) => {
              return {
                trigger: alter,
                bot_id: bot.bot_id,
                url: trigger.url,
                trigger_name: trigger.trigger_name.trim().replace(/\s+/g, " "),
                trigger_type: trigger.trigger_type,
              };
            }),
          ]);
        }

        const triggers_to_update = [
          {
            trigger: formData.trigger.trim().replace(/\s+/g, " "),
            bot_id: bot.bot_id,
            url: card_path,
            trigger_name: formData.trigger_name.trim().replace(/\s+/g, " "),
            trigger_type: "card",
            trigger_id: trigger.trigger_id,
          },
        ];

        copiedAlternatives?.map((alter) => {
          triggers_to_update.push({
            ...alter,
            bot_id: bot.bot_id,
          });
        });

        await update_many_triggers([...triggers_to_update]);
        toast.success("Card updated successfully");

        setErrors({});

        setOpen(false);
      });
    }
  };

  const handleSubmit = () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateTriggerSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;
    const hasImageAndHeadingOrSubheading = cardsToUpload.attachments?.every(
      (atch) => {
        const imageObj = atch.body.find((item) => item.id === "image");
        const videoObj = atch.body.find((item) => item.id === "video");
        const headingObj = atch.body.find((item) => item.id === "heading");
        const subheadingObj = atch.body.find(
          (item) => item.id === "subheading"
        );

        const hasImage = imageObj && imageObj.url.trim() !== "";
        const hasVideo = videoObj && videoObj.sources[0].url.trim() !== "";
        const hasHeading = headingObj && headingObj.text.trim() !== "";
        const hasSubheading = subheadingObj && subheadingObj.text.trim() !== "";

        return (hasImage || hasVideo) && (hasHeading || hasSubheading);
      }
    );
    if (isUpdate) {
      if (hasImageAndHeadingOrSubheading) {
        handleUpdate();
      } else {
        setErrors({
          card: "Please add media and heading or subheading",
        });
      }
    } else {
      if (hasImageAndHeadingOrSubheading) {
        handleCreate();
      } else {
        setErrors({
          card: "Please add media and heading or subheading",
        });
      }
    }
  };

  return (
    <div className="flex flex-col justify-between gap-10">
      <div className="text-white grid grid-cols-2 gap-8 h-96 items-start">
        <div className="space-y-5">
          <UserQuestion
            trigger={trigger}
            onChange={(e) => onChangeHandler("trigger", e.target.value)}
            value={formData.trigger}
            error={errors.trigger}
          />
          <div className="">
            <Input
              title="Card Name"
              subtitle="For dashboard (optional)"
              name="trigger_name"
              value={formData.trigger_name}
              onChange={(e) => onChangeHandler("trigger_name", e.target.value)}
            />
          </div>
          <AlternativesInputs
            trigger={trigger}
            setEditedAlternatives={setEditedAlternatives}
            alternatives={alternatives}
            newAlternatives={newAlternatives}
            setNewAlternatives={setNewAlternatives}
            copiedAlternatives={copiedAlternatives}
            setCopiedAlternatives={setCopiedAlternatives}
            setAlternatives={setAlternatives}
          />
        </div>
        <CardEditor
          uploadImageHandler={uploadImageHandler}
          updateCards={updateCards}
          error={errors.card}
          isUpdate={isUpdate}
          trigger={trigger}
          title="Card Details"
        />
      </div>
      <div className="self-end mt-14">
        <Button onClick={handleSubmit}>
          {isUpdate ? "Save Changes" : "Create Card"}
        </Button>
      </div>{" "}
    </div>
  );
};
