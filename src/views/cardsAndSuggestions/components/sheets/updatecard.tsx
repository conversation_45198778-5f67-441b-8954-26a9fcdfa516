import React, { useState } from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";

import { ITrigger } from "store/trigger/trigger.types";
import { CardAction } from "../card";

interface UpdateCardSheetProps {
  children: React.ReactNode;
  trigger: ITrigger;
}

export const UpdateCardSheet: React.FC<UpdateCardSheetProps> = ({
  children,
  trigger,
}) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="xl">
        <SheetHeader>
          <SheetTitle>Update your card</SheetTitle>
          <SheetDescription>
            Update your card here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <div className="mt-10">
          <CardAction setOpen={setOpen} isUpdate trigger={trigger} />
        </div>

        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
