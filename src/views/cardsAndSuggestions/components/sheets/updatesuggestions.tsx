import React, { useState } from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "common/ui/sheet";
import { SuggestionAction } from "../suggestion";
import { ITrigger } from "store/trigger/trigger.types";

interface UpdateSuggestionSheetProps {
  children: React.ReactNode;
  trigger: ITrigger;
}

export const UpdateSuggestionSheet: React.FC<UpdateSuggestionSheetProps> = ({
  children,
  trigger,
}) => {
  const [open, setOpen] = useState(false);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="xl">
        <SheetHeader>
          <SheetTitle>Update your suggested actions</SheetTitle>
          <SheetDescription>
            Update your suggested Actions here. Click save when you&apos;re
            done.
          </SheetDescription>
        </SheetHeader>
        <div className="mt-10">
          <SuggestionAction setOpen={setOpen} isUpdate trigger={trigger} />
        </div>

        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
