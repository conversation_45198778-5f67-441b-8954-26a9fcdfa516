"use client";

import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
} from "common/ui/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { useState } from "react";
import { CardAction } from "../card";
import { SuggestionAction } from "../suggestion";

interface CreateTriggerSheetProps {
  children: React.ReactNode;
}

export const CreateTriggerSheet: React.FC<CreateTriggerSheetProps> = ({
  children,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="xl">
        <SheetHeader>
          <SheetTitle>Add a Trigger</SheetTitle>
          <SheetDescription>
            Create your Smart Actions here. Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>
        <Tabs defaultValue="cards" className="w-full mt-3">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="cards">Cards</TabsTrigger>
            <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          </TabsList>
          <TabsContent value="cards">
            <CardAction setOpen={setOpen} />
          </TabsContent>
          <TabsContent value="suggestions" className="h-[560px]">
            <SuggestionAction setOpen={setOpen} />
          </TabsContent>
        </Tabs>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
