import toast from "react-hot-toast";
import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { InfoIcon } from "lucide-react";
import React, { useState, useEffect } from "react";
import helper from "../helper";
import useUserStore from "store/user/user.store";
import { IBot } from "store/bot/bot.types";
import useBotStore from "store/bot/bot.store";
import { getFile, uploadFile } from "apis/file.api";
import {
  CreateTriggerInfo,
  CreateTriggerSchema,
  ITrigger,
} from "store/trigger/trigger.types";
import useTriggerStore from "store/trigger/trigger.store";
import { getAlternative } from "apis/trigger.api";
import {
  AlternativesInputs,
  SuggestionButtons,
  UserQuestion,
} from "views/knowledgeBase/components";
import generateStorageId from "helpers/generateStorageId";
import checkForErrors from "helpers/forms";

interface SuggestionActionProps {
  trigger?: ITrigger;
  isUpdate?: boolean;
  setOpen: (open: boolean) => void;
}
// TODO -- check for 3 suggestions only -- max letters in suggestion
// BUG delete suggestion alternative not working

export const SuggestionAction: React.FC<SuggestionActionProps> = ({
  trigger,
  isUpdate,
  setOpen,
}) => {
  const user_id = useUserStore((state) => state.user).user_id;
  const bot = useBotStore((state) => state.bot) as IBot;
  const { update_many_triggers, create_many_triggers, delete_one_trigger } =
    useTriggerStore();

  const [suggestedActions, setSuggestedActions] = useState([]);
  const [copiedSuggestions, setCopiedSuggestions] = useState([]);
  //   const [addMore, setAddMore] = useState(false);

  const [alternatives, setAlternatives] = useState([]);
  const [copiedAlternatives, setCopiedAlternatives] = useState([]);
  const [editedAlternatives, setEditedAlternatives] = useState<boolean>(false);
  const [newAlternatives, setNewAlternatives] = useState([]);

  const [formData, setFormData] = useState(
    helper.initTriggerFormData(user_id, bot.bot_id, trigger ? trigger : null)
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  var textarea = document.getElementById("myInput2");
  textarea?.addEventListener("input", function () {
    textarea.style.height = "auto";
    var textHeight = textarea.scrollHeight;
    textarea.style.height = textHeight + "px";
  });

  //   SECTION get data

  const getAllAlters = () => {
    getAlternative(bot.bot_id, trigger.trigger_id, trigger.url).then((data) => {
      if (data) {
        const alts = data.filter((a) => a.trigger_id !== trigger.trigger_id);
        setAlternatives([...alts]);
      }
    });
  };

  useEffect(() => {
    if (trigger && isUpdate) {
      getAllAlters();
    }
  }, []);

  useEffect(() => {
    setCopiedAlternatives([...alternatives]);
  }, [alternatives]);

  //   SECTION handle form

  const validateField =
    (field: keyof CreateTriggerInfo) =>
    (value: unknown): string => {
      const parsedResult = CreateTriggerSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  //   SECTION handle submit

  const handleCreate = () => {
    const suggestion_path = `Bots/${
      bot.file_name
    }/ActionsConfig/action-${generateStorageId()}.json`;

    if (copiedSuggestions.length > 0) {
      const suggestionsToCreate = copiedSuggestions.filter(
        (suggestion) => suggestion.trim() !== ""
      );

      if (suggestionsToCreate.length > 0) {
        uploadFile({
          path: suggestion_path,
          file_body: JSON.stringify({
            actions: suggestionsToCreate.map((suggestion) =>
              suggestion.trim().replace(/\s+/g, " ")
            ),
          }),
        }).then((data) => {
          if (data) {
            const triggers_to_create = [
              {
                trigger: formData.trigger.trim().replace(/\s+/g, " "),
                bot_id: bot.bot_id,
                url: suggestion_path,
                trigger_name: formData.trigger_name.trim().replace(/\s+/g, " "),
                trigger_type: "suggestion",
              },
            ];
            const alternativesToCreate = newAlternatives?.filter(
              (input) => input.trigger?.replace(/ /g, "") != ""
            );
            alternativesToCreate?.map((alter) => {
              triggers_to_create.push({
                trigger: alter,
                bot_id: bot.bot_id,
                url: suggestion_path,
                trigger_name: formData.trigger_name.trim().replace(/\s+/g, " "),
                trigger_type: "suggestion",
              });
            });
            create_many_triggers(triggers_to_create).then((res) => {
              if (alternativesToCreate?.length) {
                toast.success("Trigger created with alternatives");
              } else {
                toast.success("Trigger created successfully");
              }
              setErrors({});
              setFormData(helper.initTriggerFormData(user_id, bot.bot_id));
              setOpen(false);
            });
          }
        });
      } else {
        setErrors({
          ...errors,
          suggestions: "You need to add at least one suggestion",
        });
      }
    } else {
      setErrors({
        ...errors,
        suggestions: "You need to add at least one suggestion",
      });
    }
  };

  const handleUpdate = async () => {
    const suggestionsToUpdate = copiedSuggestions.filter(
      (suggestion) => suggestion.trim() !== ""
    );
    if (
      formData.trigger === trigger.trigger &&
      formData.trigger_name === trigger.trigger_name &&
      newAlternatives?.every(
        (input) => input.trigger?.replace(/ /g, "") == ""
      ) &&
      !editedAlternatives &&
      suggestionsToUpdate?.every((suggestion, index) => {
        return suggestion === suggestedActions[index];
      })
    ) {
      toast("No changes made", {
        icon: <InfoIcon className="text-blue-500" />,
      });
      return;
    }

    const suggestion_path = trigger.url;

    if (suggestionsToUpdate.length > 0) {
      uploadFile({
        path: suggestion_path,
        file_body: JSON.stringify({
          actions: suggestionsToUpdate.map((suggestion) =>
            suggestion.trim().replace(/\s+/g, " ")
          ),
        }),
      });
      const triggers_to_update = [
        {
          trigger: formData.trigger.trim().replace(/\s+/g, " "),
          bot_id: bot.bot_id,
          url: suggestion_path,
          trigger_name: formData.trigger_name.trim().replace(/\s+/g, " "),
          trigger_type: "suggestion",
          trigger_id: trigger.trigger_id,
        },
      ];

      const alternativesToCreate = newAlternatives?.filter(
        (input) => input.trigger?.replace(/ /g, "") != ""
      );

      if (alternativesToCreate?.length) {
        create_many_triggers([
          ...alternativesToCreate.map((alter) => {
            return {
              trigger: alter,
              bot_id: bot.bot_id,
              url: trigger.url,
              trigger_name: trigger.trigger_name.trim().replace(/\s+/g, " "),
              trigger_type: trigger.trigger_type,
            };
          }),
        ]);
      }

      copiedAlternatives?.map((alter) => {
        triggers_to_update.push({
          ...alter,
          bot_id: bot.bot_id,
        });
      });
      await update_many_triggers([...triggers_to_update]);
      toast.success("Trigger updated successfully");

      setErrors({});

      setOpen(false);
    } else {
      setErrors({
        ...errors,
        suggestions: "You need to add at least one suggestion",
      });
    }
  };

  const handleSubmit = () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateTriggerSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;

    const suggestionsToUpdate = copiedSuggestions.filter(
      (suggestion) => suggestion.trim() !== ""
    );

    if (isUpdate) {
      if (suggestionsToUpdate.length > 0) {
        handleUpdate();
      } else {
        setErrors({
          ...errors,
          suggestions: "You need to add at least one suggestion",
        });
      }
    } else {
      handleCreate();
    }
  };

  return (
    <div className="flex flex-col justify-between gap-10">
      <div className="text-white grid grid-cols-2 gap-8 h-96 items-start">
        <div className="space-y-5">
          <UserQuestion
            trigger={trigger}
            onChange={(e) => onChangeHandler("trigger", e.target.value)}
            value={formData.trigger}
            error={errors.trigger}
          />
          <div className="">
            <Input
              title="Action Name"
              subtitle="For dashboard (optional)"
              name="trigger_name"
              value={formData.trigger_name}
              onChange={(e) => onChangeHandler("trigger_name", e.target.value)}
            />
          </div>
          <AlternativesInputs
            trigger={trigger}
            setEditedAlternatives={setEditedAlternatives}
            alternatives={alternatives}
            newAlternatives={newAlternatives}
            setNewAlternatives={setNewAlternatives}
            copiedAlternatives={copiedAlternatives}
            setCopiedAlternatives={setCopiedAlternatives}
            setAlternatives={setAlternatives}
          />
        </div>
        <SuggestionButtons
          title="Suggestions"
          suggestedActions={suggestedActions}
          setSuggestedActions={setSuggestedActions}
          trigger={trigger}
          copiedSuggestions={copiedSuggestions}
          setCopiedSuggestions={setCopiedSuggestions}
          error={errors.suggestions}
          required
        />
      </div>
      <div className="self-end mt-10">
        <Button onClick={handleSubmit}>
          {isUpdate ? "Save Changes" : "Create Suggestions"}
        </Button>
      </div>{" "}
    </div>
  );
};
