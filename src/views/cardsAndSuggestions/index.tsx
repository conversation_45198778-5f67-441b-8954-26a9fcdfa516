import searchedArray from "helpers/search";
import { <PERSON>, Eye, PlusCircle, Trash2 } from "lucide-react";
import { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useTriggerStore from "store/trigger/trigger.store";
import { CreateTriggerSheet, UpdateSuggestionSheet } from "./components";
import { getAlternative } from "apis/trigger.api";
import useConfirmModal from "common/hooks/useConfirmModal";
import toast from "react-hot-toast";
import { UpdateCardSheet } from "./components/sheets/updatecard";
import { CardPreview } from "./components/cardPreview";
import { SubPageHeader } from "common/components/headers";
import { MainTable } from "common/components/tables/main.table";

export const CardsAndSuggestionsView = ({ botConfig }) => {
  const [keySearch, setKeySearch] = useState("");
  const bot = useBotStore((state) => state.bot) as IBot;
  const { get_all_triggers, triggers, delete_many_triggers } =
    useTriggerStore();
  const confirmModal = useConfirmModal();
  const [cards, setCards] = useState([]);
  const [suggestions, setSuggestions] = useState([]);

  const [triggerToPreview, setTriggerToPreview] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    get_all_triggers(bot.bot_id);
  }, []);
  useEffect(() => {
    var tiggers_to_view = triggers?.reduce((acc, current) => {
      const x = acc.find((item) => item.url === current.url);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    tiggers_to_view = tiggers_to_view?.filter(
      (a) =>
        !((botConfig && botConfig.suggested_actions) || []).includes(a.trigger)
    );
    setCards(
      tiggers_to_view?.filter((trigger) => trigger.trigger_type === "card")
    );
    setSuggestions(
      tiggers_to_view?.filter(
        (trigger) => trigger.trigger_type === "suggestion"
      )
    );
  }, [triggers]);

  const handleSuggDelete = (trigger) => {
    let alternatives = [];
    getAlternative(bot.bot_id, trigger.trigger_id, trigger.url).then((data) => {
      if (data) {
        alternatives = [...data];
        const triggers_to_delete = [
          {
            trigger_id: trigger.trigger_id,
            bot_id: bot.bot_id,
          },
        ];
        alternatives?.map((a) => {
          triggers_to_delete.push({
            trigger_id: a.trigger_id,
            bot_id: bot.bot_id,
          });
        });
        delete_many_triggers([...triggers_to_delete]).then((data) => {
          toast.success("Deleted successfully");
        });
      }
    });
  };

  const cardTableActions = [
    {
      label: "Preview",
      component: ({ item }) => (
        <>
          {showPreview && triggerToPreview === item && (
            <CardPreview trigger={triggerToPreview} />
          )}
          <Eye
            onMouseDown={(e) => {
              e.preventDefault();
              setTriggerToPreview(item);
              setShowPreview(true);
            }}
            onMouseUp={(e) => {
              e.preventDefault();
              if (showPreview) setShowPreview(false);
            }}
            onMouseOut={(e) => {
              e.preventDefault();
              if (showPreview) setShowPreview(false);
            }}
            size={15}
            className="hover:text-primary cursor-pointer"
          />
        </>
      ),
    },
    {
      label: "Edit",
      component: ({ item }) => (
        <UpdateCardSheet trigger={item}>
          <Edit size={15} className="hover:text-primary cursor-pointer" />
        </UpdateCardSheet>
      ),
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(() => handleSuggDelete(item));
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
  ];

  const suggTableActions = [
    {
      label: "Edit",
      component: ({ item }) => (
        <UpdateSuggestionSheet trigger={item}>
          <Edit size={15} className="hover:text-primary cursor-pointer" />
        </UpdateSuggestionSheet>
      ),
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(() => handleSuggDelete(item));
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
  ];

  return (
    <>
      <div className="space-y-3">
        <SubPageHeader
          title="Cards And Suggestions"
          description="Create and manage your chatbot smart actions!"
          search
          searchPlaceholder="Find a trigger..."
          setKeySearch={setKeySearch}
          btn
          btnText="Create New Trigger"
          btnIcon={PlusCircle}
          btnSheet={CreateTriggerSheet}
        />
        <div className="grid grid-cols-2 pt-5 gap-5">
          <div className="">
            <MainTable
              data={[...searchedArray(keySearch, cards, ["trigger"])]}
              columns={[{ name: "Card", key: "trigger" }]}
              actions={cardTableActions}
            />
          </div>
          <div>
            <MainTable
              data={[...searchedArray(keySearch, suggestions, ["trigger"])]}
              columns={[{ name: "Suggestions", key: "trigger" }]}
              actions={suggTableActions}
            />
          </div>
        </div>
      </div>
    </>
  );
};
