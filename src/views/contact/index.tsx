import { Modal } from "common/components/modals";
import { FC, memo, useEffect, useState } from "react";
import useContactModal from "./hooks/useContactModal";
import { contactUS } from "apis/contact.api";
import { toast } from "react-hot-toast";
import { Input } from "common/ui/inputs/input";
import { PhoneInput } from "common/ui/inputs/phoneInput";
import { Textarea } from "common/ui/inputs/textarea";
import { z } from "zod";
import { Mail, Phone, ShieldClose } from "lucide-react";
import PhoneSVG from "common/icons/PhoneSVG";
import MailSVG from "common/icons/MailSVG";
import checkForErrors from "helpers/forms";

export const ContactModal: FC = () => {
  const contactModal = useContactModal();

  const [contact, setContact] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    subject: "",
    body: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>();

  const ContactSchema = z.object({
    first_name: z
      .string()
      .min(1, { message: "First name is required" })
      .refine((data) => data.trim() !== "", {
        message: "First name is required",
      }),
    last_name: z.string().optional(),
    email: z
      .string()
      .email()
      .refine((data) => data.trim() !== "", {
        message: "Email is required",
      }),
    phone_number: z
      .string()
      .min(1, { message: "Phone number is required" })
      .min(10, { message: "Invalid phone number" }),
    body: z
      .string()
      .min(1, { message: "Message is required" })
      .refine((data) => data.trim() !== "", {
        message: "Message is required",
      }),
  });

  const validateField =
    (field: keyof z.infer<typeof ContactSchema>) =>
    (value: unknown): string => {
      const parsedResult = ContactSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const [submitLoading, setSubmitLoading] = useState(false);

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });

    setContact({ ...contact, [key]: value });

    if (key === "first_name") {
      setContact({ ...contact, subject: value, [key]: value });
    }
  };

  const onSendEmail = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: ContactSchema,
        data: contact,
      },
      setErrors
    );

    if (isErrors) return;

    setSubmitLoading(true);
    await contactUS(contact);
    setSubmitLoading(false);
    toast.success("Email sent successfully");
    handleClose();
  };

  const handleClose = () => {
    contactModal.onClose();
    setContact({
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      subject: "",
      body: "",
    });
    setErrors({});
  };

  const bodyContent = (
    <div className="space-y-4">
      <div className="p-5 bg-gray-400/25 rounded flex justify-between">
        <div className="flex items-center gap-3">
          {/* <Phone size={16} /> */}
          <PhoneSVG />
          <a href="tel:+962795853190">+962 79 585 3190</a>
        </div>
        <div className="flex items-center gap-3">
          {/* <Mail size={16} />{" "} */}
          <MailSVG />
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-5">
        <div className="flex flex-col gap-5">
          <div className="flex gap-3">
            <Input
              name="first_name"
              title="First Name"
              value={contact.first_name}
              onChange={(event) =>
                onChangeHandler("first_name", event.target.value)
              }
              error={errors?.first_name}
            />
            <Input
              name="last_name"
              title="Last Name"
              value={contact.last_name}
              onChange={(event) =>
                onChangeHandler("last_name", event.target.value)
              }
              error={errors?.last_name}
            />
          </div>
          <Input
            name="email"
            title="Email"
            value={contact.email}
            onChange={(event) => onChangeHandler("email", event.target.value)}
            error={errors?.email}
          />
          <PhoneInput
            value={contact.phone_number}
            onChange={(value) => onChangeHandler("phone_number", value)}
            error={errors?.phone_number}
          />
        </div>
        <div className="h-full space-y-1 relative">
          <label htmlFor="">Your Message</label>
          <Textarea
            className="h-56"
            value={contact.body}
            onChange={(event) => onChangeHandler("body", event.target.value)}
          />
          {errors?.body && (
            <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
              {errors?.body ? (
                <>
                  <ShieldClose size={12} />
                  {errors?.body}
                </>
              ) : null}
            </span>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Modal
      title="Get in Touch"
      onClose={handleClose}
      isOpen={contactModal.isOpen}
      actionLabel="Send"
      secondaryActionLabel="Cancel"
      secondaryAction={() => handleClose()}
      onSubmit={() => onSendEmail()}
      body={bodyContent}
      loading={submitLoading}
    />
  );
};
