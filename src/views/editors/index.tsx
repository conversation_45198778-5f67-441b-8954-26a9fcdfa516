import { deleteMany, deleteOne, getAll } from "apis/editors.api";
import { SubPageHeader } from "common/components/headers";
import { MainTable } from "common/components/tables/main.table";
import searchedArray from "helpers/search";
import { PlusCircle, Trash2, Edit } from "lucide-react";
import { FC, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import EditorSheet from "./components/editor.sheet";
import { Button } from "common/ui/button";
import useConfirmModal from "common/hooks/useConfirmModal";
import { toast } from "react-hot-toast";

interface EditorsViewProps {}

export const EditorsView: FC<EditorsViewProps> = ({}) => {
  const confirmModal = useConfirmModal();
  const { bot, planfunction } = useBotStore();
  const [editors, setEditors] = useState([]);

  const [keySearch, setKeySearch] = useState("");

  const [renderedEditors, setRenderedEditors] = useState([]);
  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  const [disabledAddEditor, setdisabledAddEditor] = useState(false);
  const [numberEditors, setnumberEditors] = useState(0);

  const [fetchLoading, setFetchLoading] = useState(false);

  function setAbilityToAddEditors() {
    if (planfunction.plan_id == 5) {
      setdisabledAddEditor(true);
    } else if (planfunction.plan_id == 1 && numberEditors >= 2) {
      setdisabledAddEditor(true);
    } else if (planfunction.plan_id == 2 && numberEditors >= 5) {
      setdisabledAddEditor(true);
    } else if (planfunction.plan_id == 3) {
      setdisabledAddEditor(false);
    }
  }

  useEffect(() => {
    setRenderedEditors(
      searchedArray(keySearch, editors, ["email"])
    );
  }, [editors, keySearch]);

  const getEditorsData = async () => {
    setFetchLoading(true);
    const data = await getAll(bot.bot_id);
    if (data && !data.message && data.length > 0) {
      setEditors([...data]);
      setnumberEditors(data.length);
    }
    setAbilityToAddEditors();
    setFetchLoading(false);
  }

  useEffect(() => {
    getEditorsData();
  }, []);

  const onDeleteUser = async (editorToDelete) => {
    await deleteOne({
      email: editorToDelete.email,
      bot_id: bot.bot_id,
      editor_id: editorToDelete.editor_id,
    });
    var editors_to_change = [...editors];
    editors_to_change = editors_to_change.filter(
      (a) => a.editor_id !== editorToDelete.editor_id
    );
    setEditors([...editors_to_change]);
    toast.success("Editor deleted successfully");
    setnumberEditors(numberEditors - 1);
  };

  const updateUserState = (editor: any, isUpdate: boolean) => {
    var users_arr = [...editors];
    if (isUpdate) {
      users_arr[users_arr.findIndex((a) => a.editor_id === editor.editor_id)] =
        { ...editor };
    } else {
      users_arr.push({
        ...editor,
      });
    }

    setEditors([...users_arr]);
  };

  const tableData = () => {
    const data = renderedEditors?.map((editor) => {
      return {
        ...editor,
        email: editor.email,
        appearance: editor.appearance_privilege ? "✅" : "❌",
        builder: editor.builder_privilege ? "✅" : "❌",
        store: editor.store_privilege ? "✅" : "❌",
        connect: editor.deployment_privilege ? "✅" : "❌",
        transaction_dashboard: editor.transaction_dashboard_privilege
          ? "✅"
          : "❌",
        sales_dashboard: editor.sales_dashboard_privilege ? "✅" : "❌",
        dialog: editor.dialog_privilege ? "✅" : "❌",
        editor: editor.editor_privilege ? "✅" : "❌",
        lead: editor.lead_privilege ? "✅" : "❌",
        report: editor.report_privilege ? "✅" : "❌",
        addons: editor.addon_privilege ? "✅" : "❌",
        agent: editor.agent_privilege ? "✅" : "❌",
        ticketing: editor.ticketing_privilege ? "✅" : "❌",
      };
    });

    // const data = searchedArray(keySearch, modifiedEditors, ["email"]);

    const columns = [
      {
        name: "Email",
        key: "email",
      },
      {
        name: "Appearance",
        key: "appearance",
      },
      {
        name: "Builder",
        key: "builder",
      },
      {
        name: "Store",
        key: "store",
      },
      {
        name: "Connect",
        key: "connect",
      },
      {
        name: "General Dashboard",
        key: "transaction_dashboard",
      },
      {
        name: "Sales Dashboard",
        key: "sales_dashboard",
      },
      {
        name: "Dialog",
        key: "dialog",
      },
      {
        name: "Editor",
        key: "editor",
      },
      {
        name: "Lead",
        key: "lead",
      },
      {
        name: "Report",
        key: "report",
      },
      {
        name: "Addons",
        key: "addons",
      },
      {
        name: "Agent",
        key: "agent",
      },
    ];

    const actions = [
      {
        label: "Edit",
        component: ({ item }) => (
          <EditorSheet
            editor={item}
            updateUserState={updateUserState}
            numberEditors={numberEditors}
            setnumberEditors={setnumberEditors}
          >
            <Edit size={15} className="hover:text-primary cursor-pointer" />
          </EditorSheet>
        ),
      },
      {
        label: "Delete",
        icon: Trash2,
        onClick: (item) => {
          confirmModal.onOpen();
          confirmModal.setOnConfirm(async () => await onDeleteUser(item));
        },
      },
    ];

    return {
      columns,
      data,
      actions,
    };
  };

  useEffect(() => {
    if (selected?.length === renderedEditors?.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selected, renderedEditors]);

  const onSelect = (e) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(
          renderedEditors?.map((editor) =>
            editor.editor_id.toString()
          )
        );
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(
          selected.filter((id) => id !== name.split("-")[1])
        );
        setSelectAll(false);
      }
    }
  };

  const handleDeleteSelected = async () => {
    await deleteMany({
      editors: selected,
      bot_id: bot.bot_id,
    });
    setEditors(
      editors.filter((editor) => !selected.includes(editor.editor_id.toString()))
    );

    toast.success("Editors deleted successfully");
    setSelected([]);
  }


  return (
    <div className="space-y-5">
      <SubPageHeader
        title="Editors"
        description="Editors can help you manage your bot."
        search
        searchPlaceholder="Find an editor..."
        setKeySearch={setKeySearch}
      >
        <EditorSheet
          updateUserState={updateUserState}
          numberEditors={numberEditors}
          setnumberEditors={setnumberEditors}
        >
          <Button disabled={disabledAddEditor}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Editors
          </Button>
        </EditorSheet>
      </SubPageHeader>
      <div className="w-full overflow-x-auto">
      {selected?.length > 0 && (
        <Button onClick={()=>{
          confirmModal.setOnConfirm(async () => await handleDeleteSelected());
          confirmModal.setType("delete");
          confirmModal.onOpen();
        }} variant="destructive" className="float-right mb-3">
          <Trash2 size={15} className="mr-2" />
          Delete Selected ({selected.length})
        </Button>
        )}
        <MainTable {...tableData()} className="text-xs"   checkBoxes
            onSelect={onSelect}
            isSelectAll={selectAll}
            isSelectOne={
              (id) => selected.includes(id)
            }
            idKey="editor_id"
            loading={fetchLoading} 
            />
      </div>
    </div>
  );
};
