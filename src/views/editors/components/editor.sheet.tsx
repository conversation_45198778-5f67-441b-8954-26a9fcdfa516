"use client";

import { createOne, updateOne } from "apis/editors.api";
import { <PERSON><PERSON> } from "common/ui/button";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from "common/ui/sheet";
import { Loader2 } from "lucide-react";
import { FC, memo, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useBotStore from "store/bot/bot.store";
import { IChildren } from "types/common.types";
import { z } from "zod";
import helper from "../helper";
import { useLoaderContext } from "context/loaderContext";
import checkForErrors from "helpers/forms";

interface EditorSheetProps extends IChildren {
  editor?: any;
  updateUserState: (editor: any, isUpdate: boolean) => void;
  numberEditors: number;
  setnumberEditors: (number: number) => void;
}

const EditorSheet: FC<EditorSheetProps> = ({
  editor,
  children,
  updateUserState,
  numberEditors,
  setnumberEditors,
}) => {
  const bot = useBotStore((state) => state.bot);
  const [loading, setLoading] = useState(false);
  const {loader} = useLoaderContext();
  const [editorDataToChange, setEditorDataToChange] = useState(
    helper.initEditorData()
  );

  const [errors, setErrors] = useState<Record<string, string>>();
  const schema = z.object({
    email: z.string().email(),
  });

  const onEmailChangeHandler = (e) => {
    const parsedResult = schema.safeParse({ email: e.target.value });
    if (parsedResult.success === false) {
      setErrors({ email: parsedResult.error.errors[0].message });
    } else {
      setErrors({});
    }

    setEditorDataToChange({
      ...editorDataToChange,
      email: e.target.value,
    });
  };

  const [selectAll, setSelectAll] = useState(false);
  const onSelect = (e) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      setEditorDataToChange({
        ...editorDataToChange,
        appearance_privilege: checked,
        builder_privilege: checked,
        store_privilege: checked,
        deployment_privilege: checked,
        transaction_dashboard_privilege: checked,
        sales_dashboard_privilege: checked,
        editor_privilege: checked,
        lead_privilege: checked,
        report_privilege: checked,
        addon_privilege: checked,
        dialog_privilege: checked,
        agent_privilege: checked,
        ticketing_privilege: checked,
      });
    } else {
      setEditorDataToChange({
        ...editorDataToChange,
        [name]: checked,
      });
      if (!checked) setSelectAll(false);
    }
  };

  const onSave = async () => {
    const isErrors = checkForErrors({
      zodSchema: schema,
       data:editorDataToChange,
    }, setErrors);

    if (isErrors) return;

    setLoading(true);
    loader?.continuousStart()
    if (editorDataToChange.editor_id) {
      const update_data = await updateOne({
        bot_id: bot.bot_id,
        ...editorDataToChange,
      });
      if (update_data) {
        console.table(update_data);
        setLoading(false);
        loader?.complete()
        updateUserState(
          {
            ...update_data,
          },
          true
        );
        toast.success("Editor updated successfully");
        setOpen(false);
      }
    } else {
      const create_data = await createOne({
        bot_id: bot.bot_id,
        ...editorDataToChange,
      });
      if (create_data) {
        setLoading(false);
        loader?.complete()
        updateUserState(
          {
            ...create_data,
          },
          false
        );
        toast.success("Editor created successfully");
        setOpen(false);
      }
    }
    setnumberEditors(numberEditors + 1);
    setEditorDataToChange(helper.initEditorData());
  };

  useEffect(() => {
    if (editor) {
      setEditorDataToChange({
        ...editor,
      });
    } else {
      setEditorDataToChange(helper.initEditorData());
    }
  }, [editor]);

  const [open, setOpen] = useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent
        className="overflow-y-auto text-white"
        position="right"
        size="sm"
      >
        <SheetHeader>
          <SheetTitle>Editor Privileges</SheetTitle>
          <SheetDescription>
            Choose the privileges you want to give to this editor.
          </SheetDescription>
        </SheetHeader>
        <div className="py-3 flex flex-col gap-5">
          <Input
            name="email"
            value={editorDataToChange.email}
            onChange={onEmailChangeHandler}
            placeholder="Email"
            error={errors?.email}
          />
          <div className="flex justify-between gap-3 text-sm border-b pb-2 border-white/25">
            Priviliges
            <div className="flex gap-1 text-white/50">
              <Checkbox
                checked={selectAll}
                onChange={onSelect}
                name={`select-all`}
              />
              Select All
            </div>
          </div>
          <div className="space-y-2 text-md">
            {Object.keys(editorDataToChange).map((key, i) => {
              return (
                key.includes("privilege") && (
                  <div key={i} className="flex gap-2 capitalize">
                    <Checkbox
                      checked={editorDataToChange[key]}
                      onChange={onSelect}
                      name={key}
                    />
                    {key.replaceAll("_", " ")}
                  </div>
                )
              );
            })}
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button loading={loading} onClick={() => onSave()}>
              Save
            </Button>
          </div>
        </div>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default memo(EditorSheet);
