import { ICart } from "store/cart/cart.types";
import { ICategory } from "store/category/category.types";
import { IItem } from "store/item/item.types";

const initCreateItemData = (cart: ICart, categories: ICategory[]) => {
  return {
    item_title: "",
    item_description: "",
    item_price: "0",
    currency: cart.cart_currency || "JD",
    item_unit: "piece",
    item_qty: 1,
    category_id: categories[0]?.category_id || 0,
    item_condition: "new",
    item_icons: [],
    item_instock: true,
    price_hide: false,
    item_hide: false,
    show_url: false,
    item_url: null,
    lemmatized_item_title: "",
  };
};

const initCreateCategoryData = () => {
  return {
    category_id: null,
    category_name: "",
    category_description: "",
    category_icon: "",
  };
};

const initCreateOfferData = () => {
  return {
    offer_description: "",
    offer_type: "Percentage",
    offer_percentage: 0,
    item_unit: "Piece",
    offer_date_start: new Date().toLocaleDateString(),
    offer_date_end: new Date().toLocaleDateString(),
    offer_status: "active",
    offer_icon: "",
    fixed_value: "0",
    offer_id: null,
  };
};

export default {
  initCreateItemData,
  initCreateCategoryData,
  initCreateOfferData,
};
