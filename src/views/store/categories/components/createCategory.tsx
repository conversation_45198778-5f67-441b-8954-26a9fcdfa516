import { uploadBotImages } from "apis/file.api";
import { FullModal } from "common/components/modals";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { MediaInput } from "common/ui/inputs/mediaInput";
import { Textarea } from "common/ui/inputs/textarea";
import toast from "react-hot-toast";
import constant from "constant";
import generateStorageId from "helpers/generateStorageId";
import { handleUploadImg } from "helpers/media";
import { Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import useCategoryStore from "store/category/category.store";
import { ICategory } from "store/category/category.types";
import { CreateCategorySchema, CreateCategoryType } from "types/store.types";
import helper from "../../helper";
import checkForErrors from "helpers/forms";
import { isArabic } from "helpers/helper";
interface CreateCategoryModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  category?: ICategory;
  setCategortToEdit?: (category: ICategory) => void;
  canAddCategory?: boolean;
}

export const CreateCategoryModal: React.FC<CreateCategoryModalProps> = ({
  showModal,
  setShowModal,
  category,
  setCategortToEdit,
  canAddCategory,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { create_one_category, update_one_category } = useCategoryStore();
  const [imageToUpload, setImageToUpload] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isChanged, setIsChanged] = useState(false);
  const [categoryData, setCategoryData] = useState(
    helper.initCreateCategoryData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (category) {
      setCategoryData(category);
    } else {
      setCategoryData(helper.initCreateCategoryData());
    }
  }, [category]);

  const validateField =
    (field: keyof CreateCategoryType) =>
    (value: unknown): string => {
      const parsedResult = CreateCategorySchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });

    const category_to_change = { ...categoryData };
    category_to_change[key] = value;
    setCategoryData({ ...category_to_change });

    if (category) {
      if (category[key] === value) {
        setIsChanged(false);
      } else {
        setIsChanged(true);
      }
    } else {
      if (
        categoryData[key].trim().replace(/\s+/g, " ") ===
          value.trim().replace(/\s+/g, " ") ||
        value === ""
      ) {
        setIsChanged(false);
      } else {
        setIsChanged(true);
      }
    }
  };

  const uploadImg = (img, card_index) => {
    const formData = new FormData();
    formData.append("file", img);
    const path = `Bots/${bot.file_name}/itemsAssets/${generateStorageId()}.${
      img.type.split("/")[1]
    }`;
    onChangeHandler("category_icon", constant.MEDIA_STORAGE_URL + path);
    const imageToChange = {
      path,
      formData,
    };
    setImageToUpload({ ...imageToChange });
    setTimeout(() => {
      handleUploadImg(`card-poster-${card_index}`, img);
    }, 100);
  };

  const onSaveHandler = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: CreateCategorySchema,
        data: categoryData,
      },
      setErrors
    );

    if (isErrors) return;

    if (categoryData.category_id) {
      setLoading(true);
      await update_one_category({
        bot_id: bot.bot_id,
        ...categoryData,
        lemmatized_category_name: isArabic(categoryData.category_name)
          ? categoryData.category_name
          : "",
      });
      if (imageToUpload) {
        await uploadBotImages({
          ...imageToUpload,
          patg: imageToUpload.path + "&image=true",
        });
      }
      setErrors({});
      setLoading(false);
      setShowModal(false);
      setCategortToEdit(null);
      toast.success("Category updated successfully");
    } else {
      if (canAddCategory) {
        setLoading(true);
        await create_one_category({
          bot_id: bot.bot_id,
          ...categoryData,
          lemmatized_category_name: isArabic(categoryData.category_name)
            ? categoryData.category_name
            : "",
        });
        if (imageToUpload) {
          await uploadBotImages({
            ...imageToUpload,
            patg: imageToUpload.path + "&image=true",
          });
        }
        setLoading(false);
        setShowModal(false);
        setCategoryData(helper.initCreateCategoryData());
        setErrors({});
        setCategortToEdit(null);
        toast.success("Category created successfully");
      } else {
        toast.error(
          "You can not add more categories, please upgrade your plan"
        );
      }
    }
  };

  const handleClose = () => {
    setCategortToEdit(null);
    setShowModal(false);
    setErrors({});
  };

  return (
    <FullModal
      title={category ? "Update Category" : "Create Category"}
      isOpen={showModal}
      onClose={handleClose}
      disabled={!isChanged}
      onSave={onSaveHandler}
      loading={loading}
      footer
    >
      <div className="flex flex-col gap-5">
        <MediaInput
          card_index={0}
          imgSrc={categoryData?.category_icon}
          uploadImg={uploadImg}
        />
        <Input
          title="Category Name *"
          name="category_name"
          placeholder="Category Name"
          onChange={(e) => onChangeHandler("category_name", e.target.value)}
          value={categoryData?.category_name}
          error={errors?.category_name}
        />
        <div className="space-y-1">
          <label htmlFor="description">Category Description</label>
          <Textarea
            id="Description"
            name="category_description"
            onChange={(e) =>
              onChangeHandler("category_description", e.target.value)
            }
            value={categoryData?.category_description}
          />
        </div>
      </div>
    </FullModal>
  );
};
