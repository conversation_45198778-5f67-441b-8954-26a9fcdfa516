import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import searchedArray from "helpers/search";
import { Edit, PlusCircleIcon, PlusIcon, Trash2 } from "lucide-react";
import { useState, useEffect } from "react";
import useCategoryStore from "store/category/category.store";
import useItemStore from "store/item/item.store";
import { CreateCategoryModal } from "./components/createCategory";
import useConfirmModal from "common/hooks/useConfirmModal";
import toast from "react-hot-toast";
import planFunctionChecker from "helpers/planFunctionChecker";
import useBotStore from "store/bot/bot.store";

export const Categories = () => {
  const { bot, planfunction } = useBotStore();
  const { categories, delete_one_category } = useCategoryStore();
  const items = useItemStore((state) => state.items);
  const confirmModal = useConfirmModal();

  const [keySearch, setKeySearch] = useState("");
  const [renderedCategories, setRenderedCategories] = useState([]);
  const [showModal, setShowModal] = useState(false);

  const [canAddCategory, setCanAddCategory] = useState(true);

  const [categotyToEdit, setCategoryToEdit] = useState(null);

  useEffect(() => {
    const categoriesToRender = categories.map((category) => {
      return {
        ...category,
        items: getNumberOfItemsInCategory(category.category_id),
      };
    });
    console.log("changed");

    setRenderedCategories(
      searchedArray(keySearch, categoriesToRender, [
        "category_name",
        "category_description",
      ])
    );
  }, [categories, keySearch, items]);

  useEffect(() => {
    const canAdd = planFunctionChecker(
      planfunction,
      "max_categories",
      categories.length
    );
    setCanAddCategory(canAdd);
  }, [categories, bot]);

  const onClickCreateCategory = () => {
    if (canAddCategory) {
      setShowModal(true);
    }
  };

  const getNumberOfItemsInCategory = (cat_id) => {
    const itemsInCategory = items.filter((a) => a.category_id === cat_id);
    return itemsInCategory.length;
  };

  const onDeleteCategory = async (categoryToDelete) => {
    const item = items.find(
      (a) => a.category_id === categoryToDelete.category_id
    );
    if (item) {
      toast.error(
        "There are Items attached to this Category, please delete them  in order to delete the category."
      );
    } else {
      await delete_one_category(categoryToDelete.category_id);
      toast.success("Category deleted successfully");
    }
  };

  const tableData = () => {
    const data = renderedCategories;

    const columns = [
      { name: "Category", key: "category_name" },
      { name: "No. of Items", key: "items" },
    ];

    const actions = [
      {
        label: "Edit",
        onClick: (item) => {
          setCategoryToEdit(item);
          setShowModal(true);
        },
        icon: Edit,
      },
      {
        label: "Delete",
        onClick: (item) => {
          confirmModal.setOnConfirm(async () => await onDeleteCategory(item));
          confirmModal.setType("delete");
          confirmModal.onOpen();
        },
        icon: Trash2,
      },
    ];

    const itemsPerPage = 10;

    return { data, columns, itemsPerPage, actions };
  };

  return (
    <>
      <CreateCategoryModal
        showModal={showModal}
        setShowModal={setShowModal}
        category={categotyToEdit}
        setCategortToEdit={setCategoryToEdit}
        canAddCategory={canAddCategory}
      />
      <div className="flex flex-col gap-3">
        <div className="flex justify-between">
          <div>
            <CustomSearch
              placeholder="Find a category..."
              onChange={(value) => setKeySearch(value)}
            />
          </div>

          <div className="group relative">
            <Button
              className="peer"
              onClick={onClickCreateCategory}
              disabled={!canAddCategory}
            >
              <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Category
            </Button>
            {!canAddCategory && (
              <div className="hidden group-hover:block w-52 absolute z-50 bg-secondary border border-white/50 text-white  p-2 break-words whitespace-break-spaces rounded">
                You reached the maximum number of categories allowed in your
                plan. If you want to add more categories, please upgrade your
                plan.
              </div>
            )}
          </div>
        </div>
        <MainTable {...tableData()} />
      </div>
    </>
  );
};
