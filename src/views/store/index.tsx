import { Main<PERSON>ageHeader } from "common/components/headers";
import { Alert } from "common/ui/alert";
import { InfoIcon } from "lucide-react";
import { useState, useEffect } from "react";
import useCartStore from "store/cart/cart.store";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "common/ui/tabs";

import useCategoryStore from "store/category/category.store";
import useBotStore from "store/bot/bot.store";
import useItemStore from "store/item/item.store";
import { getAll as getAllCartLogs } from "apis/cartLog.api";
import { getAll as getAllFeatures } from "apis/itemFeature.api";
import { getAll as getAllOfferItems } from "apis/itemsOffers.api";
import useItemSpecificationStore from "store/itemSpecifications/itemSpecifications.store";
import { Categories } from "./categories";
import { Items } from "./items";
import useOfferStore from "store/offers/offers.store";
import { Promotions } from "./promotions";
import { Orders } from "./orders";
import { Specifications } from "./specifications";

export const StoreView = () => {
  const bot = useBotStore((state) => state.bot);
  const isCart = useCartStore((state) => state.cart.cart_active);
  const { get_all_categories } = useCategoryStore();
  const { get_all_items } = useItemStore();
  const { get_all_item_specifications } = useItemSpecificationStore();
  const { get_all_offers } = useOfferStore();

  const [showTip, setShowTip] = useState(false);

  const [allItemFeatures, setAllItemFeatures] = useState([]);
  const [offerItems, setOfferItems] = useState([]);

  const [logs, setLogs] = useState([]);

  useEffect(() => {
    get_all_categories(bot.bot_id);
    get_all_items(bot.bot_id);
    get_all_item_specifications(bot.bot_id);
    get_all_offers(bot.bot_id);

    getAllCartLogs(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setLogs([...data]);
      }
    });

    getAllFeatures(bot.bot_id).then((data) => {
      if (data) {
        return setAllItemFeatures(data);
      }
    });

    getAllOfferItems(bot.bot_id)
      .then((data) => {
        if (data) {
          return setOfferItems([...data]);
        }
      })
      .catch((err) => {
        console.error(err);
      });
  }, [bot]);

  return (
    <div className="space-y-5">
      <MainPageHeader
        title="Store"
        description="Display your store items for your customers."
      />
      {showTip ? (
        <Alert
          title={
            isCart
              ? "The customer can view and order items from your store!"
              : "The customer will only be able to view items in your store!"
          }
          content={
            isCart
              ? "You can disable E-commerce from Addons to stop customers from ordering items from your store."
              : "You can enable E-commerce from Addons to allow customers to order items from your store."
          }
          setShowAlert={setShowTip}
        />
      ) : (
        <InfoIcon
          size={15}
          className="text-amber-300 float-right cursor-pointer"
          onClick={() => setShowTip(true)}
        />
      )}
      <Tabs defaultValue="categories" className="w-full mt-3">
        <TabsList className="grid w-full sm:grid-cols-2 lg:grid-cols-5">
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
          <TabsTrigger value="promotions">Promotions</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>
        <TabsContent value="categories">
          <Categories />
        </TabsContent>
        <TabsContent value="items">
          <Items
            logs={logs}
            allItemFeatures={allItemFeatures}
            setAllItemFeatures={setAllItemFeatures}
          />
        </TabsContent>
        <TabsContent value="specifications">
          <Specifications allItemFeatures={allItemFeatures} />
        </TabsContent>
        <TabsContent value="promotions">
          <Promotions offerItems={offerItems} setOfferItems={setOfferItems} />
        </TabsContent>
        <TabsContent value="orders">
          <Orders setLogs={setLogs} logs={logs} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
