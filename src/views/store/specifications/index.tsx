import { MainTable } from "common/components/tables/main.table";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import toast from "react-hot-toast";
import { Edit, PlusCircleIcon, Trash2 } from "lucide-react";
import React, { use, useEffect, useState } from "react";

import useItemSpecificationStore from "store/itemSpecifications/itemSpecifications.store";
import { AddUpdateSpecificationModal } from "./components";
import planFunctionChecker from "helpers/planFunctionChecker";
import useBotStore from "store/bot/bot.store";
import useItemStore from "store/item/item.store";
import searchedArray from "helpers/search";

export const Specifications = ({ allItemFeatures }) => {
  const { bot, planfunction } = useBotStore();
  const items = useItemStore((state) => state.items);
  const { delete_one_item_specification, SPECIFICATIONS } =
    useItemSpecificationStore();

  const confirmModal = useConfirmModal();

  const [keySearch, setKeySearch] = useState("");

  const [selectedspecification, setSelectedspecification] = useState();
  const [showAddUpdateModal, setShowAddUpdateModal] = useState(false);

  const [canAddSpecification, setCanAddSpecification] = useState(true);
  const [loading, setLoading] = useState(false);

  const [renderedSpecifications, setRenderedSpecifications] = useState([]);

  useEffect(() => {
    setRenderedSpecifications(
      searchedArray(keySearch, SPECIFICATIONS, ["feature_name"])
    );
  }, [SPECIFICATIONS, keySearch]);

  useEffect(() => {
    const canAdd = planFunctionChecker(
      planfunction,
      "max_features",
      SPECIFICATIONS.length
    );
    setCanAddSpecification(canAdd);
  }, [SPECIFICATIONS, bot]);

  const onClickCreateSpecification = () => {
    if (canAddSpecification) {
      setShowAddUpdateModal(true);
    }
  };

  const onDeletespecification = (specificationToDelete) => {
    const specification = allItemFeatures.find(
      (a) => a.feature_id === specificationToDelete.feature_id
    );
    if (specification) {
      toast.error(
        "There are Items attached to this Spesification, please delete the Specification from the Item/s in order to delete it."
      );
    } else {
      delete_one_item_specification(specificationToDelete.feature_id).then(
        () => {
          toast.success("Specification deleted successfully");
        }
      );
    }
  };

  const getItemsNumber = (specId) => {
    let number = allItemFeatures.filter((ele) => {
      return ele.feature_id === specId;
    });
    return number.length;
  };

  const tableData = () => {
    const data = renderedSpecifications?.map((specification) => {
      return {
        ...specification,
        items: getItemsNumber(specification.feature_id),
      };
    });

    const columns = [
      {
        name: "Specification",
        key: "feature_name",
      },
      {
        name: "No. of Items",
        key: "items",
      },
    ];

    const actions = [
      {
        label: "Edit",
        onClick: (specification) => {
          setSelectedspecification(specification);
          setShowAddUpdateModal(true);
        },
        icon: Edit,
      },
      {
        label: "Delete",
        onClick: (specification) => {
          confirmModal.onOpen();
          confirmModal.setType("delete");
          confirmModal.setOnConfirm(() => onDeletespecification(specification));
        },
        icon: Trash2,
      },
    ];
    const itemsPerPage = 10;

    return {
      columns,
      data,
      actions,
      itemsPerPage,
    };
  };

  return (
    <>
      <AddUpdateSpecificationModal
        showModal={showAddUpdateModal}
        setShowModal={setShowAddUpdateModal}
        specification={selectedspecification}
        setSpecificationToEdit={setSelectedspecification}
      />
      <div className="flex flex-col gap-3">
        <div className="flex justify-between">
          <div>
            <CustomSearch
              placeholder="Find a specification..."
              onChange={(value) => setKeySearch(value)}
            />
          </div>

          <div className="group relative">
            <Button
              className="peer"
              onClick={onClickCreateSpecification}
              disabled={!canAddSpecification || !items.length}
            >
              <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Specification
            </Button>
            {!items.length ? (
              <div className="hidden group-hover:block absolute z-50 bg-secondary border border-white/50 text-white  p-2 break-words whitespace-break-spaces rounded">
                You need to create an item first.
              </div>
            ) : !canAddSpecification ? (
              <div className="hidden group-hover:block w-52 absolute z-50 bg-secondary border border-white/50 text-white  p-2 break-words whitespace-break-spaces rounded">
                You reached the maximum number of specifications allowed in your
                plan. If you want to add more specifications, please upgrade
                your plan.
              </div>
            ) : null}
          </div>
        </div>
        <MainTable {...tableData()} />
      </div>
    </>
  );
};
