import { FullModal } from "common/components/modals";
import { Input } from "common/ui/inputs/input";
import toast from "react-hot-toast";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useItemSpecificationStore from "store/itemSpecifications/itemSpecifications.store";

export const AddUpdateSpecificationModal = ({
  showModal,
  setShowModal,
  specification,
  setSpecificationToEdit,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { update_one_item_specification, create_one_item_specification, loading} =
    useItemSpecificationStore();

  const handleClose = () => {
    setShowModal(false);
    setSpecificationToEdit(null);
  };

  const [specificationData, setSpecificationData] = useState({
    feature_name: "",
    bot_id: bot.bot_id,
    feature_id: null,
  });

  useEffect(() => {
    if (specification) {
      setSpecificationData({
        ...specification,
      });
    }
  }, [specification]);

  const validation = () => {
    const missing = [];
    Object.keys(specificationData).map((a) => {
      if (typeof specificationData[a] === "string") {
        if (!specificationData[a].trim().replace(/\s+/g, " ").length) {
          missing.push(a);
        }
      }
    });

    return missing.length;
  };

  const onSaveHandler = async () => {
    if (specificationData.feature_id) {
     await update_one_item_specification({
        bot_id: bot.bot_id,
        ...specificationData,
      })
        toast.success("Specification updated successfully");
        handleClose();
      setSpecificationToEdit(null);
    } else {
      await create_one_item_specification({
        bot_id: bot.bot_id,
        ...specificationData,
      })
        toast.success("Specification created successfully");
        handleClose();
      setSpecificationToEdit(null);
    }
  };

  return (
    <FullModal
      title="Create Specification"
      isOpen={showModal}
      onClose={handleClose}
      disabled={Boolean(validation())}
      onSave={onSaveHandler}
      loading={loading}
      footer
    >
      <Input
        name="feature_name"
        title="Specification Name"
        value={specificationData.feature_name}
        onChange={(e) =>
          setSpecificationData({
            ...specificationData,
            feature_name: e.target.value,
          })
        }
      />
    </FullModal>
  );
};
