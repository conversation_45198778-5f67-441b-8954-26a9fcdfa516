import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { ICategory } from "store/category/category.types";
import { Checkbox } from "common/ui/inputs/checkbox";
import { ChevronDown } from "lucide-react";

interface ItemsFiltersProps {
  sortOption: string;
  setSortOption: (sortOption: string) => void;
  handleResetAll: () => void;
  setSortInStock: (sortInStock: string) => void;
  sortInStock: string;
  categories: any[];
  categoriesFilter: string[];
  setCategoriesFilter: (categoriesFilter: string[]) => void;
  setPriceRange: (priceRange: any) => void;
  priceRange: any;
}

export const ItemsFilters: React.FC<ItemsFiltersProps> = ({
  sortOption,
  setSortOption,
  handleResetAll,
  setSortInStock,
  sortInStock,
  categories,
  categoriesFilter,
  setCategoriesFilter,
  setPriceRange,
  priceRange,
}) => {
  return (
    <div className=" space-y-4 col-span-1 sticky top-10 h-fit border border-white/25 p-2 rounded-md mt-8">
      <button
        type="button"
        className="text-sm whitespace-nowrap underline underline-offset-4 float-right"
        onClick={handleResetAll}
      >
        Reset All
      </button>

      <div className="w-full">
        <label htmlFor="SortBy" className="block text-xs font-medium pb-1">
          Sort By
        </label>
        <Select
          value={sortOption}
          onValueChange={(value) => {
            setSortOption(value);
          }}
        >
          <SelectTrigger className="w-full ">
            <SelectValue placeholder="Sort By" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Sort By</SelectItem>
            <SelectItem value="Title, DESC">Title, DESC</SelectItem>
            <SelectItem value="Title, ASC">Title, ASC</SelectItem>
            <SelectItem value="Price, DESC">Price, DESC</SelectItem>
            <SelectItem value="Price, ASC">Price, ASC</SelectItem>
            <SelectItem value="Category, DESC">Category, DESC</SelectItem>
            <SelectItem value="Category, ASC">Category, ASC</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <p className="block text-xs font-medium ">Filters</p>

        <div className="mt-1 space-y-2">
          <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
            <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
              <span className="text-sm font-medium"> Availability </span>

              <span className="transition group-open:-rotate-180">
                <ChevronDown className="h-4 w-4" />
              </span>
            </summary>

            <div className="border-t border-gray-200">
              <header className="flex items-center justify-end p-4">
                <button
                  type="button"
                  className="text-sm  underline underline-offset-4"
                  onClick={() => setSortInStock("")}
                >
                  Reset
                </button>
              </header>

              <ul className="space-y-1 border-t border-gray-200 p-4">
                <li>
                  <label
                    htmlFor="FilterInStock"
                    className="inline-flex items-center gap-2"
                  >
                    <input
                      name="stockFilter"
                      type="radio"
                      id="FilterInStock"
                      className="text-primary"
                      checked={sortInStock === "In Stock"}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSortInStock("In Stock");
                        }
                      }}
                    />

                    <span className="text-sm font-medium ">In Stock</span>
                  </label>
                </li>

                <li>
                  <label
                    htmlFor="FilterOutOfStock"
                    className="inline-flex items-center gap-2"
                  >
                    <input
                      name="stockFilter"
                      type="radio"
                      id="FilterOutOfStock"
                      className="text-primary"
                      checked={sortInStock === "Out of Stock"}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSortInStock("Out of Stock");
                        }
                      }}
                    />

                    <span className="text-sm font-medium ">Out of Stock</span>
                  </label>
                </li>

                <li>
                  <label
                    htmlFor="FilterHidden"
                    className="inline-flex items-center gap-2"
                  >
                    <input
                      name="stockFilter"
                      type="radio"
                      id="FilterHidden"
                      className="text-primary"
                      checked={sortInStock === "Hidden Items"}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSortInStock("Hidden Items");             
                        }
                      }
                      }
                    />
                    <span className="text-sm font-medium ">Hidden Items</span>
                  </label>
                </li>
              </ul>
            </div>
          </details>

          <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
            <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
              <span className="text-sm font-medium"> Price </span>

              <span className="transition group-open:-rotate-180">
                <ChevronDown className="h-4 w-4" />
              </span>
            </summary>

            <div className="border-t border-gray-200">
              <header className="flex items-center justify-end p-4">
                <button
                  type="button"
                  className="text-sm  underline underline-offset-4"
                  onClick={() => setPriceRange({ min: null, max: null })}
                >
                  Reset
                </button>
              </header>

              <div className="border-t border-gray-200 p-4">
                <div className="flex justify-between gap-4">
                  <label
                    htmlFor="FilterPriceFrom"
                    className="flex items-center gap-2"
                  >
                    <span className="text-sm text-gray-600">$</span>

                    <input
                      type="number"
                      min={0}
                      id="FilterPriceFrom"
                      placeholder="From"
                      className="w-full rounded-md border-gray-200 shadow-sm sm:text-sm bg-transparent"
                      value={priceRange.min ? priceRange.min : ""}
                      onChange={(e) =>
                        setPriceRange({
                          ...priceRange,
                          min: e.target.value,
                        })
                      }
                    />
                  </label>

                  <label
                    htmlFor="FilterPriceTo"
                    className="flex items-center gap-2"
                  >
                    <span className="text-sm text-gray-600">$</span>

                    <input
                      type="number"
                      min={0}
                      id="FilterPriceTo"
                      placeholder="To"
                      className="w-full rounded-md border-gray-200 shadow-sm sm:text-sm bg-transparent"
                      value={priceRange.max ? priceRange.max : ""}
                      onChange={(e) =>
                        setPriceRange({
                          ...priceRange,
                          max: e.target.value,
                        })
                      }
                    />
                  </label>
                </div>
              </div>
            </div>
          </details>

          <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
            <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
              <span className="text-sm font-medium"> Categories </span>

              <span className="transition group-open:-rotate-180">
                <ChevronDown className="h-4 w-4" />
              </span>
            </summary>

            <div className="border-t border-gray-200">
              <header className="flex items-center justify-between p-4">
                <span className="text-sm ">
                  {" "}
                  {categoriesFilter?.length} Selected{" "}
                </span>

                <button
                  type="button"
                  className="text-sm  underline underline-offset-4"
                  onClick={() => setCategoriesFilter([])}
                >
                  Reset
                </button>
              </header>

              <ul className="space-y-1 border-t border-gray-200 p-4">
                {categories?.map((category) => (
                  <li key={category.category_id}>
                    <Checkbox
                      name={category.category_name}
                      label={category.category_name}
                      checked={categoriesFilter.includes(category.category_id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setCategoriesFilter([
                            ...categoriesFilter,
                            category.category_id,
                          ]);
                        } else {
                          setCategoriesFilter(
                            categoriesFilter.filter(
                              (item) => item !== category.category_id
                            )
                          );
                        }
                      }}
                    />
                  </li>
                ))}
              </ul>
            </div>
          </details>
        </div>
      </div>
    </div>
  );
};
