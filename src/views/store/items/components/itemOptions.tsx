import { useState, useEffect } from "react";
import {
  createOne as createOneOption,
  updateOne as updateOneOption,
  deleteOne as deleteOneOption,
  getAll as getAllOptions,
} from "apis/itemsOptions";
import { FullModal } from "common/components/modals";
import { Button } from "common/ui/button";
import {
  ChevronDown,
  Loader2,
  PlusCircle,
  Save,
  Trash2,
  X,
} from "lucide-react";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import useConfirmModal from "common/hooks/useConfirmModal";
import { z } from "zod";
import { toast } from "react-hot-toast";
import checkForErrors from "helpers/forms";

export const ItemOptionsModal = ({
  item,
  showModal,
  setShowModal,
  setItemToEdit,
}) => {
  const [originalOptions, setOriginalOptions] = useState([]);
  const [options, setOptions] = useState([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [savingOptionIdx, setSavingOptionIdx] = useState(null); 

  const confirmModal = useConfirmModal();

  const getOptions = async () => {
    const data = await getAllOptions(item?.item_id);
    if (data) {
          setOriginalOptions([...data]);
    }
  }

  useEffect(() => {
    if (item) {
      getOptions();
    }
  }, [item]);

  useEffect(() => {
    if (originalOptions) {
      const data = JSON.parse(JSON.stringify(originalOptions));
      setOptions([...data]);
    }
  }, [originalOptions]);

  const addNewMainOption = () => {
    const data = {
      item_id: item.item_id,
      option_title: "",
      option_description: "",
      option: [
        {
          name: "",
          price: 0,
          currency: item.currency,
        },
      ],
    }
      const optionsToUpdate = [...options];
      optionsToUpdate.push(data);
      setOptions([...optionsToUpdate]);
  };

  const onChangeMainOption = (main_index, e) => {
    const optionsToUpdate = [...options];
    optionsToUpdate[main_index][e.target.name] = e.target.value;
    setOptions([...optionsToUpdate]);
  };

  const deleteMainOption = async (main_index, option) => {
    await deleteOneOption({
      item_option_id: option.item_option_id,
    })
    var optionsToUpdate = [...options];
    optionsToUpdate = optionsToUpdate.filter(
      (a, index) => index !== main_index
    );
    setOptions([...optionsToUpdate]);
  };

  const addChoice = (main_index) => {
    const optionsToUpdate = [...options];
    optionsToUpdate[main_index].option.push({
      name: "",
      price: "",
      currancy: item.currancy,
    });
    setOptions([...optionsToUpdate]);
  };

  const onChangeChoice = (main_index, choice_index, key, value) => {
    const optionsToUpdate = [...options];
    optionsToUpdate[main_index].option[choice_index][key] = value;
    setOptions([...optionsToUpdate]);
  };

  const deleteChoice = (main_index, choice_index) => {
    const optionsToUpdate = [...options];
    optionsToUpdate[main_index].option = optionsToUpdate[
      main_index
    ].option.filter((a, index) => index !== choice_index);
    setOptions([...optionsToUpdate]);
  };

  const isSaveDisabled = (option) => {
    const originalOption = originalOptions.find(
      (a) => a.item_option_id === option.item_option_id
    );

    if (originalOption) {
      if (JSON.stringify(originalOption) === JSON.stringify(option)) {
        return true;
      }
    }
    return false;
  };

  const optionSchema = z.object({
    option_title: z
      .string()
      .min(1)
      .refine((val) => val.trim() !== "", {
        message: "Option title cannot be empty",
      }),
    option: z
      .array(
        z.object({
          name: z.string().min(1),
          price: z.string().nonempty(),
        })
      )
      .nonempty("Option must have at least one choice"),
  });

  const saveOption =async (option, index) => {
    const isErrors = checkForErrors({
      zodSchema: optionSchema,
       data:option,
    }, setErrors);

    if (isErrors) return;

    setLoading(true);
    setSavingOptionIdx(index);
    if (option.item_option_id) {
    await updateOneOption({
      ...option,
    })
    } else {
      await createOneOption({
        ...option,
      })
    }
    await getOptions();
    setLoading(false);
    setSavingOptionIdx(null);
  };

  const handleClose = () => {
    setShowModal(false);
    setItemToEdit(null);
    setErrors({});
  };

  return (
    <FullModal title="Item Options" isOpen={showModal} onClose={handleClose}>
      <div className="space-y-3">
        <Button onClick={() => addNewMainOption()}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add New Option
        </Button>
        {options.length > 0 ? (
          <>
            <hr className="text-white/25" />
            {options.map((option, main_index) => (
              <details
                key={option.item_option_id}
                open
                className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden"
              >
                <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
                  <Trash2
                    size={15}
                    className="cursor-pointer text-red-500 hover:text-red-600 z-50"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      confirmModal.onOpen();
                      confirmModal.setType("delete");
                      confirmModal.setOnConfirm(async () =>
                       {await deleteMainOption(main_index, option)
                       toast.success("Option deleted successfully")}
                      );
                    }}
                  />
                  <span className="text-sm font-medium"> Option </span>
                  <span className="transition group-open:-rotate-180">
                    <ChevronDown className="h-4 w-4" />
                  </span>
                </summary>
                <div className="border-t space-y-3  ">
                  <div className="max-h-64 overflow-y-auto p-3 text-sm">
                    <Input
                      title="Option Title"
                      value={option.option_title}
                      name="option_title"
                      onChange={(e) => onChangeMainOption(main_index, e)}
                    />
                    <div className="space-y-1 relative">
                      <label htmlFor="description">Option Description</label>
                      <Textarea
                        id="Description"
                        name="option_description"
                        value={option?.option_description}
                        onChange={(e) => onChangeMainOption(main_index, e)}
                      />
                    </div>
                    <div className="flex gap-2 items-center">
                      <Button
                        variant="link"
                        onClick={() => addChoice(main_index)}
                      >
                        <PlusCircle className="mr-2 h-4 w-4" /> Add New Choice
                      </Button>
                      {errors?.option && (
                        <span className="text-red-500 text-xs ">
                          You must have at least one choice with a name and
                          price
                        </span>
                      )}
                    </div>
                    <div className="grid grid-cols-2 py-2 gap-2">
                      {option?.option?.map((choice, choice_index) => (
                        <div
                          key={choice_index}
                          className="flex items-center gap-2 p-2 border border-gray-300 rounded text-sm relative"
                        >
                          {option?.option?.length > 1 && (
                            <X
                              size={15}
                              className="cursor-pointer bg-red-500 hover:bg-red-600 rounded-full absolute -top-1 -right-1"
                              onClick={() => {
                                if (choice.name && choice.price) {
                                  confirmModal.onOpen();
                                  confirmModal.setType("delete");
                                  confirmModal.setOnConfirm(() =>
                                    deleteChoice(main_index, choice_index)
                                  );
                                } else {
                                  deleteChoice(main_index, choice_index);
                                }
                              }}
                            />
                          )}
                          <Input
                            title="Choice Name"
                            value={choice.name}
                            name="name"
                            onChange={(e) =>
                              onChangeChoice(
                                main_index,
                                choice_index,
                                e.target.name,
                                e.target.value
                              )
                            }
                          />
                          <Input
                            title="Price"
                            value={choice.price ? choice.price : ""}
                            name="price"
                            onChange={(e) => {
                              if (e.target.value === ".") {
                                onChangeChoice(
                                  main_index,
                                  choice_index,
                                  e.target.name,
                                  "0."
                                );
                              }
                              if (!isNaN(e.target.value)) {
                                onChangeChoice(
                                  main_index,
                                  choice_index,
                                  e.target.name,
                                  e.target.value
                                );
                              }
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="border-t p-2 flex flex-col">
                    <Button
                      disabled={isSaveDisabled(option)}
                      onClick={() => saveOption(option, main_index)}
                      className="self-end"
                    >
                      {loading && +savingOptionIdx === +main_index ? (
                        <>
                          {" "}
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Save{" "}
                        </>
                      ) : (
                        <>
                          {" "}
                          <Save className="mr-2 h-4 w-4" /> Save{" "}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </details>
            ))}
          </>
        ) : null}
      </div>
    </FullModal>
  );
};
