import { FullModal } from "common/components/modals";
import { Input } from "common/ui/inputs/input";
import { MediaInput } from "common/ui/inputs/mediaInput";
import { Textarea } from "common/ui/inputs/textarea";
import generateStorageId from "helpers/generateStorageId";
import { ImageIcon, Link, ShieldClose, Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import useCategoryStore from "store/category/category.store";
import { Checkbox } from "common/ui/inputs/checkbox";
import constant from "constant";
import { z } from "zod";
import useCartStore from "store/cart/cart.store";
import helper from "../../helper";
import useItemStore from "store/item/item.store";
import { uploadBotImages } from "apis/file.api";
import toast from "react-hot-toast";
import { Tooltip } from "common/ui/tooltip";
import { Button } from "common/ui/button";
import { IItem } from "store/item/item.types";
import { InfoIcon } from "lucide-react";
import checkForErrors from "helpers/forms";
import { isArabic } from "helpers/helper";

interface AddUpdateItemModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  item?: IItem;
  setItemToEdit?: (item: IItem) => void;
  canAddItem?: boolean;
}

export const AddUpdateItemModal: React.FC<AddUpdateItemModalProps> = ({
  showModal,
  setShowModal,
  item,
  setItemToEdit,
  canAddItem,
}) => {
  const [images, setImages] = useState([]);
  const [imageUrl, setImageUrl] = useState();

  const bot = useBotStore((state) => state.bot);
  const categories = useCategoryStore((state) => state.categories);
  const cart = useCartStore((state) => state.cart);
  const { items, create_one_item, update_one_item } = useItemStore();

  const [itemData, setItemData] = useState(
    helper.initCreateItemData(cart, categories)
  );

  const [loading, setLoading] = useState(false);

  const [byLink, setByLink] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (item) {
      setItemData(item);
      setImages([...item.item_icons]);
    } else {
      setItemData(helper.initCreateItemData(cart, categories));
      setImages([]);
    }
  }, [item]);

  const CreateItemSchema = z.object({
    // exclude item to be updated
    item_title: z
      .string()
      .min(1, { message: "Item name is required" })
      .refine(
        (value) =>
          !items.find(
            (a) =>
              a.item_title.trim().toLowerCase().replace(/\s+/g, " ") ===
                value.trim().replace(/\s+/g, " ").toLowerCase() &&
              a.item_id !== item?.item_id
          ),
        {
          message: "Item name is reserved, Try another one",
        }
      ),
    item_description: z
      .string()
      .min(1, { message: "Item description is required" }),
    item_price: z.union([z.string(), z.number()]).refine((val) => {
      return val !== "" && +val !== 0;
    }, "Item price is required"),
    category_id: z.number(),
    item_icons: z.array(z.string()),
    item_qty: z.number().nonnegative("Item quantity is required"),
    show_url: z.boolean(),
    item_url: z.string().url().nullable().optional(),
    // lemmatized_item_title: z.string().optional(),
    // item url is required if show_url is true
  });

  const RefinedCreateItemSchema = CreateItemSchema.refine(
    (data) => {
      if (data.show_url) {
        return data.item_url !== "" && data.item_url !== null;
      }
      return true;
    },
    {
      path: ["item_url"],
      message: "Item url is required",
    }
  );

  type CreateItemType = z.infer<typeof CreateItemSchema>;

  const validateField =
    (field: keyof CreateItemType) =>
    (value: unknown): string => {
      const parsedResult = CreateItemSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });

    const item_to_change = { ...itemData };
    item_to_change[key] = value;
    setItemData({ ...item_to_change });
  };

  function isImageUrl(url) {
    const img = new Image();
    img.src = url;
    return new Promise((resolve) => {
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
    });
  }

  const uploadMultipleImg = (imgs) => {
    const images_arr = [...images];

    for (let index = 0; index < imgs.length; index++) {
      const image = imgs[index];
      const formData = new FormData();
      formData.append("file", image);
      const path = `Bots/${bot.file_name}/itemsAssets/${generateStorageId()}.${
        image.type.split("/")[1]
      }`;

      images_arr.push({
        url: "https://infotointell.fra1.digitaloceanspaces.com/" + path,
        path,
        formData,
        file: image,
      });
    }
    setImages([...images_arr]);
  };

  const onDeleteImage = (image_index) => {
    const imagesToUpdate = [
      ...images.filter((a, index) => index !== image_index),
    ];
    setImages([...imagesToUpdate]);
  };

  const onSaveHandler = async () => {
    const item_data = { ...itemData };
    if (item_data.item_url === "") item_data.item_url = null;

    const isErrors = checkForErrors(
      {
        zodSchema: RefinedCreateItemSchema,
        data: item_data,
      },
      setErrors
    );

    if (isErrors) return;

    setLoading(true);
    let item_to_change = { ...itemData };
    const images_arr = [...images];

    item_to_change.item_icons = [
      ...images_arr.map((a) => (typeof a === "object" ? a.url : a)),
    ];

    if (item) {
      if (JSON.stringify(item_to_change) === JSON.stringify(item)) {
        toast("No changes made", {
          icon: <InfoIcon className="text-blue-500" />,
        });
        return;
      }
    }

    if (item) {
      const newImages = images_arr.filter((a) =>
        typeof a === "object" ? !item.item_icons.includes(a.url) : false
      );

      if (newImages.length > 0) {
        newImages.map(async (image) => {
          if (typeof image === "object") {
            await uploadBotImages({
              path: image.path + "&image=true",
              formData: image.formData,
            });
          }
        });
      }
    } else {
      if (images_arr.length > 0) {
        images_arr.map(async (image) => {
          if (typeof image === "object") {
            await uploadBotImages({
              path: image.path + "&image=true",
              formData: image.formData,
            });
          }
        });
      }
    }

    item_to_change = {
      ...item_to_change,
      item_title: item_to_change.item_title.trim().replace(/\s+/g, " "),
      item_description: item_to_change.item_description
        .trim()
        .replace(/\s+/g, " ")
        .substring(0, 400),
      lemmatized_item_title: isArabic(item_to_change.item_title)
        ? item_to_change.item_title
        : "",
    };

    if (item) {
      await update_one_item({
        ...item_to_change,
      });
      toast.success("Item updated successfully");
      setErrors({});
      setItemToEdit(null);
      handleClose();
    } else {
      if (canAddItem) {
        await create_one_item({
          bot_id: bot.bot_id,
          ...item_to_change,
        });
        toast.success("Item created successfully");
        setErrors({});
        setItemToEdit(null);
        setItemData(helper.initCreateItemData(cart, categories));
        setImages([]);
        handleClose();
      } else {
        toast.error("You can't add more items, please upgrade your plan");
      }
    }
    setLoading(false);
  };

  const handleClose = () => {
    setItemToEdit(null);
    setShowModal(false);
    setErrors({});
    setImages([]);
  };

  return (
    <FullModal
      title={item ? "Update Item" : "Create Item"}
      isOpen={showModal}
      onClose={handleClose}
      //   disabled={!isChanged}
      onSave={onSaveHandler}
      loading={loading}
      footer
    >
      <div className="grid grid-cols-4 gap-2">
        <div className="col-span-1">
          <div className="float-right mb-1">
            {byLink ? (
              <Tooltip text="By Upload">
                <Upload
                  size={18}
                  className="cursor-pointer"
                  onClick={() => setByLink(false)}
                />
              </Tooltip>
            ) : (
              <Tooltip text="Add by url">
                <Link
                  size={18}
                  className="cursor-pointer"
                  onClick={() => setByLink(true)}
                />
              </Tooltip>
            )}
          </div>
          {byLink ? (
            <div className="flex flex-col gap-2 mt-4 w-full">
              <Input
                name="add_img"
                value={imageUrl ? imageUrl : ""}
                onChange={(e) => {
                  setImageUrl(e.target.value);
                }}
              />
              <Button
                onClick={async (e) => {
                  if (await isImageUrl(imageUrl)) {
                    e.preventDefault();
                    const images_arr = [...images];
                    images_arr.push(imageUrl);
                    console.log(images_arr, "images_arr");

                    setImages([...images_arr]);
                    setImageUrl(undefined);
                  } else {
                    toast.error("Invalid image url");
                  }
                }}
              >
                Add
              </Button>
            </div>
          ) : (
            <MediaInput
              isMultiple
              uploadMultipleImg={(imgs) => uploadMultipleImg(imgs)}
              //   images={images}
              //   onDeleteImage={onDeleteImage}
            />
          )}
        </div>
        <div className="flex gap-1 flex-wrap pt-2 col-span-3 h-40 overflow-y-auto">
          {images.map((image, i) => (
            <div key={i} className="group relative">
              <img
                src={
                  typeof image === "object"
                    ? URL.createObjectURL(image.file)
                    : image
                }
                alt="Preview"
                className={`w-20 h-20 object-cover `}
              />
              <div>
                <X
                  size={15}
                  onClick={() => onDeleteImage(i)}
                  className="bg-red-500 rounded-full absolute -top-1 -right-1 hidden group-hover:block cursor-pointer"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-4">
        <div className="flex gap-2">
          <Input
            title="Item Name"
            name="name"
            error={errors?.item_title}
            onChange={(e) => onChangeHandler("item_title", e.target.value)}
            value={itemData?.item_title}
          />
          <div className="w-full">
            <label htmlFor="Category" className="block font-medium pb-1">
              Category
            </label>
            <Select
              value={itemData?.category_id.toString()}
              onValueChange={(value) => {
                onChangeHandler("category_id", +value);
              }}
            >
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem
                    key={category.category_id}
                    value={category.category_id.toString()}
                  >
                    {category.category_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="space-y-1 relative">
          <label htmlFor="description">Item Description</label>
          <Textarea
            id="Description"
            name="item_description"
            value={itemData?.item_description}
            onChange={(e) =>
              onChangeHandler("item_description", e.target.value)
            }
          />
          <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
            {errors?.item_description ? (
              <>
                <ShieldClose size={12} />
                {errors?.item_description}
              </>
            ) : null}
          </span>
        </div>
        <div className="flex gap-2">
          <Input
            title="Price"
            name="price"
            error={errors?.item_price}
            value={itemData?.item_price ? itemData?.item_price.toString() : ""}
            onChange={(e) => {
              if (e.target.value === ".") {
                onChangeHandler("item_price", "0.");
              }
              if (!isNaN(e.target.value)) {
                onChangeHandler("item_price", e.target.value);
              }
            }}
          />
          <Input
            title="Quantity"
            name="quantity"
            error={errors?.item_qty}
            value={itemData?.item_qty}
            onChange={(e) => {
              if (!isNaN(e.target.value)) {
                onChangeHandler("item_qty", +e.target.value);
              }
            }}
          />
        </div>
        <div className="flex gap-2">
          <div className="w-full">
            <label htmlFor="Unit" className="block text-xs font-medium pb-1">
              Unit
            </label>
            <Select
              value={itemData?.item_unit.toLowerCase()}
              onValueChange={(value) => {
                onChangeHandler("item_unit", value);
              }}
            >
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                {constant.UNITS.map((unit) => (
                  <SelectItem key={unit.value} value={unit.value}>
                    {unit.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full">
            <label
              htmlFor="Condition"
              className="block text-xs font-medium pb-1"
            >
              Condition
            </label>
            <Select
              value={itemData?.item_condition.toLowerCase()}
              onValueChange={(value) => {
                onChangeHandler("item_condition", value);
              }}
            >
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Condition" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="new">New</SelectItem>

                <SelectItem value="used">Used</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full">
            <label htmlFor="Status" className="block text-xs font-medium pb-1">
              Status
            </label>
            <Select
              value={itemData?.item_instock.toString()}
              onValueChange={(value) => {
                onChangeHandler("item_instock", value);
              }}
            >
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">In Stock</SelectItem>
                <SelectItem value="false">Out of Stock</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex gap-10 px-1 py-2 items-end">
          <Checkbox
            checked={itemData?.item_hide}
            onChange={(e) => onChangeHandler("item_hide", e.target.checked)}
            name="hide_item"
            label="Hide Item"
          />
          <Checkbox
            checked={itemData?.price_hide}
            onChange={(e) => onChangeHandler("price_hide", e.target.checked)}
            name="hide_price"
            label="Hide Price"
          />
          <Checkbox
            checked={itemData?.show_url}
            onChange={(e) => {
              if (!e.target.checked) {
                setItemData({ ...itemData, item_url: null });
                setErrors({ ...errors, item_url: "" });
              }
              onChangeHandler("show_url", e.target.checked);
            }}
            name="show_item_url"
            label="Show Item URL"
          />
          {itemData?.show_url && (
            <div className="w-1/3 text-sm">
              <Input
                title="Item URL"
                name="item_url"
                error={errors?.item_url}
                value={itemData?.item_url ? itemData?.item_url : ""}
                onChange={(e) => onChangeHandler("item_url", e.target.value)}
              />
            </div>
          )}
        </div>
      </div>
    </FullModal>
  );
};
