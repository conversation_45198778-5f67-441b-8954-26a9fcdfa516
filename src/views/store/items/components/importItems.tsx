import { FullModal } from "common/components/modals";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import toast from "react-hot-toast";
import planFunction<PERSON><PERSON>cker from "helpers/planFunctionChecker";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useCartStore from "store/cart/cart.store";
import useCategoryStore from "store/category/category.store";
import useItemStore from "store/item/item.store";
import { isArabic } from "helpers/helper";

interface ImportItemsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  importedItems: any[];
}

function extractPrice(string) {
  // Define a regular expression pattern to match the price format
  const pattern = /\d+(\.\d{1,2})?/g;

  // Find all matches in the string
  const matches = string.match(pattern);

  // Convert the matches to floats
  const prices = matches ? matches.map((match) => parseFloat(match)) : [];

  // Return the first price found, or null if no prices were found
  return prices.length ? prices[0] : null;
}

export const ImportItemsModal: React.FC<ImportItemsModalProps> = ({
  showModal,
  setShowModal,
  importedItems,
}) => {
  const { bot, planfunction } = useBotStore();
  const { items, create_many_items, get_all_items } = useItemStore();
  const cart = useCartStore((state) => state.cart);
  const { categories, create_many_categories } = useCategoryStore();

  const [loading, setLoading] = useState(false);

  const [mapper, setMapper] = useState({
    item_title: "",
    item_description: "",
    item_qty: "",
    category_name: "",
    item_icons: "",
    item_price: "",
  });

  useEffect(() => {
    if (importedItems && importedItems.length)
      setMapper({
        item_title: Object.keys(importedItems[0])[0],
        item_description: Object.keys(importedItems[0])[0],
        item_qty: Object.keys(importedItems[0])[0],
        category_name: Object.keys(importedItems[0])[0],
        item_icons: Object.keys(importedItems[0])[0],
        item_price: Object.keys(importedItems[0])[0],
      });
  }, [importedItems]);

  const validateBeforeImport = () => {
    var err = false;
    Object.keys(mapper).map((a) => {
      if (!Boolean(mapper[a])) {
        err = true;
      }
    });
    return err;
  };

  const onSaveHandler = async () => {
    setLoading(true);
    const all_categories = importedItems
      .map((a) => a[mapper["category_name"]])
      .reduce((acc, current) => {
        const x = acc.find((item) => item === current);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, []);

    const newCategories = all_categories.filter(
      (a) => categories.findIndex((b) => b.category_name === a) === -1
    );

    if (
      !planFunctionChecker(
        planfunction,
        "max_items",
        [...items, ...importedItems].length
      ) ||
      !planFunctionChecker(
        planfunction,
        "max_categories",
        [...categories, ...newCategories].length
      )
    ) {
      toast.error(
        "You have exceeded your max limit, please upgrade your plan."
      );
      return;
    }

    await create_many_categories({
      bot_id: bot.bot_id,
      categories: [
        ...newCategories.map((a) => {
          return {
            category_name: a,
            category_description: a,
            lemmatized_category_name: isArabic(a) ? a : "",
          };
        }),
      ],
    });

    const itemsToSend = [
      ...importedItems.map((a) => {
        const path = `Bots/${bot.file_name}/itemsAssets/${
          a[mapper["item_icons"]].split("/")[
            a[mapper["item_icons"]].split("/").length - 1
          ]
        }`;

        return {
          item_title: a[mapper["item_title"]],
          item_price: extractPrice(a[mapper["item_price"]]),
          item_name: a[mapper["item_title"]],
          item_description: a[mapper["item_description"]].substring(0, 400),
          item_qty: +a[mapper["item_qty"]],
          category_name: a[mapper["category_name"]],
          item_icons: `${[...a[mapper["item_icons"]].split(",")].slice(0, 2)}`,
          item_condition: "New",
          item_unit: "Piece",
          currency: cart ? cart.cart_currency : "",
          url: a[mapper["item_icons"]],
          path,
          lemmatized_item_title: isArabic(a[mapper["item_title"]]) ? a[mapper["item_title"]] : "",
        };
      }),
    ];

    const items_size = itemsToSend.length;
    const patchesNum = Math.ceil(items_size / 5);

    if (patchesNum > 1) {
      var patches = [];
      for (var i = 1; i < patchesNum; i++) {
        patches.push([...itemsToSend].slice((i - 1) * 5, i * 5));
      }
      patches.push([...itemsToSend].slice((patchesNum - 1) * 5, items_size));

      patches.map(async (patch, index) => {
       await create_many_items({
          bot_id: bot.bot_id,
          items: [...patch],
        })
      await get_all_items(bot.bot_id);
       
      });
    } else {
      await create_many_items({
        bot_id: bot.bot_id,
        items: [...itemsToSend],
      })
      await get_all_items(bot.bot_id);
    
    }

    toast.success(`${items_size} Items imported successfully`);
    setLoading(false);
    handleClose();
  };

  const handleClose = () => {
    setShowModal(false);
  };
  return (
    <FullModal
      title="Import Items"
      isOpen={showModal}
      onClose={handleClose}
      footer
      disabled={validateBeforeImport()}
      onSave={onSaveHandler}
      loading={loading}
    >
      {Object.keys(mapper).map((entity, i) => (
        <div
          key={i}
          className="w-full flex justify-between gap-2 items-center my-2"
        >
          <label htmlFor="Unit" className="block  font-medium pb-1">
            {entity}
          </label>
          <div className="w-1/2">
            <Select
              value={mapper[entity]}
              onValueChange={(value) => {
                const mapper_to_update = { ...mapper };
                mapper_to_update[entity] = value;
                setMapper({ ...mapper_to_update });
              }}
            >
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                {importedItems.length > 0
                  ? Object.keys(importedItems[0]).map((a, index) => (
                      <SelectItem key={index} value={a}>
                        {a}
                      </SelectItem>
                    ))
                  : null}
              </SelectContent>
            </Select>
          </div>
        </div>
      ))}
    </FullModal>
  );
};
