import { FullModal } from "common/components/modals";

import { useState, useEffect } from "react";
import { IItem } from "store/item/item.types";
import useItemSpecificationStore from "store/itemSpecifications/itemSpecifications.store";
import {
  createOne as createOneFeature,
  updateOne as updateOneFeature,
  deleteOne as deleteOneFeature,
} from "apis/itemFeature.api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Input } from "common/ui/inputs/input";
import { Edit, Loader2, Plus, PlusCircle, Save, Trash2 } from "lucide-react";
import { Button } from "common/ui/button";
import useBotStore from "store/bot/bot.store";
import useConfirmModal from "common/hooks/useConfirmModal";
import { toast } from "react-hot-toast";

interface ItemSpecificationsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  setItemToEdit: (item: IItem) => void;
  item?: IItem;
  allItemFeatures?: any;
  itemFeatures?: any;
  setAllItemFeatures?: any;
}

export const ItemSpecificationsModal: React.FC<
  ItemSpecificationsModalProps
> = ({
  showModal,
  setShowModal,
  setItemToEdit,
  item,
  allItemFeatures,
  itemFeatures,
  setAllItemFeatures,
}) => {
  const bot = useBotStore((state) => state.bot);
  const specifications = useItemSpecificationStore(
    (state) => state.SPECIFICATIONS
  );
  const confirmModal = useConfirmModal();
  const [isChanged, setIsChanged] = useState(false);
  const [changedValues, setChangedValues] = useState({});


  const [featureToAdd, setFeatureToAdd] = useState({
    item_id: item?.item_id,
    feature_value: "",
    feature_id: null,
  });

  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    if (item) {
      setFeatureToAdd({
        item_id: item?.item_id,
        feature_value: "",
        feature_id: null,
      });
    }
  }, [item]);

  const getUnAttachedFeatures = () => {
    return specifications
      .filter(
        (a) =>
          itemFeatures.findIndex((b) => b.feature_id === a.feature_id) === -1
      )
      .map((a) => {
        return {
          feature_name: a.feature_name,
          feature_id: a.feature_id,
        };
      });
  };

  const unAttachFeatureFromItem = async (item_feature_id) => {
    const data = await deleteOneFeature({
      item_feature_id,
    })
    if (data) {
      var itemFeaturestoUpd = [...itemFeatures];
      var allItemFeaturesToUpd = [...allItemFeatures];
      allItemFeaturesToUpd = allItemFeaturesToUpd.filter(
        (a) => a.item_id !== item.item_id
      );
      itemFeaturestoUpd = itemFeaturestoUpd.filter(
        (a) => a.item_feature_id !== item_feature_id
      );
      setAllItemFeatures([...allItemFeaturesToUpd, ...itemFeaturestoUpd]);
      toast.success("Specification removed successfully");
    }
  };

  const handleAddFeature = async () => {
    if (Boolean(isOneEmpty(featureToAdd))) return;
    setLoading(true);
    const create_data = await createOneFeature({
      bot_id: bot.bot_id,
      ...featureToAdd,
    })
    if (create_data) {
      const itemFeaturesToUpd = [...itemFeatures];
      var allItemFeaturesToUpd = [...allItemFeatures];
      allItemFeaturesToUpd = allItemFeaturesToUpd.filter(
        (a) => a.item_id !== item?.item_id
      );
      itemFeaturesToUpd.push({
        ...create_data,
      });

      setFeatureToAdd({
        item_id: item?.item_id,
        feature_value: "",
        feature_id: "",
      });
      setAllItemFeatures([...allItemFeaturesToUpd, ...itemFeaturesToUpd]);
    }  
    setLoading(false);
  };

  const isOneEmpty = (objectToValidate) => {
    const missing = [];
    Object.keys(objectToValidate).map((a) => {
      if (typeof objectToValidate[a] === "string") {
        if (!objectToValidate[a].length) {
          missing.push(a);
        }
      }
    });
    return missing.length;
  };

  const onChangeHandler = (e , index: number) => {    
    if (itemFeatures[index].feature_value !== e.target.value) {
      setIsChanged(true);
    } else {
      setIsChanged(false);
    }
    const itemFeaturestoUpd = JSON.parse(JSON.stringify(itemFeatures));

    let allItemFeaturesToUpd = JSON.parse(JSON.stringify([...allItemFeatures]));
    allItemFeaturesToUpd = allItemFeaturesToUpd.filter(
      (a) => a.item_id !== item.item_id
    );
    itemFeaturestoUpd[index].feature_value = e.target.value;
    setAllItemFeatures([
      ...allItemFeaturesToUpd,
      ...itemFeaturestoUpd,
    ]);
  }

  const onSaveHandler = async (ft) => {
    setSaveLoading(true);
    await updateOneFeature({
      ...ft,
    })
    setIsChanged(false);
    setSaveLoading(false);
  }

  const handleClose = () => {
    setShowModal(false);
    setItemToEdit(null);
  };

  return (
    <FullModal
      title="Item Specifications"
      isOpen={showModal}
      onClose={handleClose}
    >
      <div className="flex gap-3 items-end">
        <div className="w-full">
          <label
            htmlFor="Specification Name"
            className="block font-medium pb-1"
          >
            Specification Name
          </label>
          <Select
            value={
              featureToAdd?.feature_id
                ? featureToAdd?.feature_id.toString()
                : ""
            }
            onValueChange={(value) => {
              setFeatureToAdd({
                ...featureToAdd,
                feature_id: +value,
              });
            }}
          >
            <SelectTrigger className="w-full ">
              <SelectValue placeholder="Specification" />
            </SelectTrigger>
            <SelectContent>
              {[...getUnAttachedFeatures()].map((ft) => (
                <SelectItem
                  key={ft.feature_id}
                  value={ft.feature_id.toString()}
                >
                  {ft.feature_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Input
          title="Value"
          name="value"
          value={featureToAdd?.feature_value}
          onChange={(e) => {
            setFeatureToAdd({
              ...featureToAdd,
              feature_value: e.target.value,
            });
          }}
        />
        <Button
          disabled={Boolean(isOneEmpty(featureToAdd))}
          onClick={handleAddFeature}
          className="rounded-full w-10 h-10 p-3"
          loading={loading}
        >
          <Plus />
        </Button>
      </div>
      {
          [...getUnAttachedFeatures()].length === 0 && (
            <small className="text-red-500 text-xs">
            - Add a specification in the specifications tab first
          </small>
          )
      }
      {itemFeatures?.length ? (
        <>
          <h3 className="flex items-center my-8">
            <span
              aria-hidden="true"
              className="flex-grow bg-gray-200 rounded h-0.5"
            ></span>
            <span className="mx-3 text-md font-medium">
              Added Specifications
            </span>
            <span
              aria-hidden="true"
              className="flex-grow bg-gray-200 rounded h-0.5"
            ></span>
          </h3>
          <div className="space-y-3 text-sm">
            {itemFeatures?.map((ft, i) => (
              <div key={ft.feature_id} className="flex items-end gap-3">
                <Select disabled={true}>
                  <SelectTrigger className="w-full ">
                    <SelectValue placeholder={ft.feature_name} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      key={ft.feature_id}
                      value={ft.feature_id.toString()}
                    >
                      {ft.feature_name}
                    </SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  title="Value"
                  name="spec_value"
                  value={ft.feature_value}
                  onChange={(e) => onChangeHandler(e, i)}
                />
                <button
                  disabled={!isChanged}
                  className="mb-4 cursor-pointer disabled:cursor-default group"
                  onClick={() => {
                    onSaveHandler(ft);
                  }}
                >
                  {saveLoading ? (
                    <Loader2
                      size={15}
                      className="animate-spin text-primary"
                    />
                  ) :
                  <Save
                    size={15}
                    className="hover:text-primary group-disabled:hover:text-white/50 group-disabled:text-white/50"
                  />}
                </button>
                <div
                  onClick={() => {
                    confirmModal.onOpen();
                    confirmModal.setOnConfirm(async () => {
                      await unAttachFeatureFromItem(ft.item_feature_id);
                    });
                  }}
                  className="mb-4 cursor-pointer"
                >
                  <Trash2 size={15} className="hover:text-red-500 " />
                </div>
              </div>
            ))}
          </div>
        </>
      ) : null}
    </FullModal>
  );
};
