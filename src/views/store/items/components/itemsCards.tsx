import { LucideIcon } from "lucide-react";
import React, { useMemo, useState } from "react";
import constant from "constant";
import { IItem } from "store/item/item.types";
import Pagination from "common/ui/pagination";
import Empty from "common/components/empty";

type ActionType = {
  label: string;
  icon?: LucideIcon;
  onClick?: (item: any) => void;
  component?: (props: { item: any }) => React.ReactNode;
};

interface ItemCardProps {
  items: IItem[];
  actions?: Array<ActionType>;
}

export const ItemsCards: React.FC<ItemCardProps> = ({ items, actions }) => {
  const [currentPage, setCurrentPage] = useState(1);

  const itemsPerPage = 12;

  const currentData = useMemo(() => {
    const firstPageIndex = (currentPage - 1) * itemsPerPage;
    const lastPageIndex = firstPageIndex + itemsPerPage;
    return items?.slice(firstPageIndex, lastPageIndex);
  }, [currentPage, items]);
  return (
    <>
      {currentData?.length ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-2 gap-y-5">
          {currentData?.map((item, i) => (
            <div
              key={i}
              className="group block h-fit shadow-md hover:shadow-lg shadow-warmGray-800 hover:shadow-warmGray-800"
            >
              <div className="relative h-[250px]">
                <img
                  src={
                    item.item_icons.length > 0
                      ? item.item_icons[0].includes("http")
                        ? item.item_icons[0]
                        : constant.ASSETS_URL.concat("noimage.png")
                      : constant.ASSETS_URL.concat("noimage.png")
                  }
                  alt=""
                  className={`absolute inset-0 h-full w-full object-cover opacity-100 ${
                    item.item_icons.length > 1 ? "group-hover:opacity-0" : ""
                  } `}
                />
                {item.item_icons.length > 1 && (
                  <img
                    src={item.item_icons[1]}
                    alt=""
                    className="absolute inset-0 h-full w-full object-cover opacity-0 group-hover:opacity-100"
                  />
                )}
              </div>
              <div className="flex gap-2 my-2 p-1">
                <span className=" bg-primary px-2 rounded-xl text-xs">
                  {item.item_condition}
                </span>
                <span className=" bg-primary px-2 rounded-xl text-xs">
                  {item.item_instock ? "In Stock" : "Out of Stock"}
                </span>
                {item.item_hide &&
                <span className=" bg-red-500 px-2 rounded-xl text-xs">
                  Hidden
                </span>
                }
              </div>
              <div className=" flex justify-between text-sm p-1 h-28">
                <div>
                  <h3 className=" group-hover:underline group-hover:underline-offset-4">
                    {item.item_title}
                  </h3>

                  <p className="mt-1.5 max-w-[45ch] text-xs text-gray-500 line-clamp-3 min-h-[50px]">
                    {item.item_description}
                  </p>
                </div>

                <p className="whitespace-nowrap">
                  {item.item_price} {item.currency}
                </p>
              </div>
              <div className="flex gap-2 justify-center bg-accent p-1 mt-2">
                {actions?.map(
                  ({ label, icon: Icon, onClick, component }, i) => (
                    <React.Fragment key={label}>
                      {component ? (
                        component({ item })
                      ) : (
                        <Icon
                          key={i}
                          onClick={() => onClick(item)}
                          className={`${
                            label === "Delete"
                              ? "hover:text-red-500"
                              : "hover:text-primary"
                          } cursor-pointer`}
                          size={15}
                        />
                      )}
                    </React.Fragment>
                  )
                )}
                {/* <Trash2 size={15} />
           <Edit size={15} />
           <Menu size={15} />
           <Gem size={15} /> */}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Empty text="Add New Item" />
      )}
      <Pagination
        currentPage={currentPage}
        totalCount={items?.length}
        pageSize={itemsPerPage}
        onPageChange={(page) => setCurrentPage(page)}
      />
    </>
  );
};
