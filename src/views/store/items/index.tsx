import { MainTable } from "common/components/tables/main.table";
import { But<PERSON> } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import searchedArray from "helpers/search";
import { useCSVReader } from "react-papaparse";
import { CSVLink } from "react-csv";
import {
  Edit,
  Eye,
  EyeOff,
  Gem,
  Menu,
  PlusCircleIcon,
  Trash2,
  DownloadIcon,
  UploadIcon,
  FileAudio,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import useCategoryStore from "store/category/category.store";
import useItemStore from "store/item/item.store";

import toast from "react-hot-toast";
import useConfirmModal from "common/hooks/useConfirmModal";

import planFunctionChecker from "helpers/planFunctionChecker";
import useBotStore from "store/bot/bot.store";
import {
  AddUpdateItemModal,
  ImportItemsModal,
  ItemOptionsModal,
  ItemSpecificationsModal,
  ItemsCards,
  ItemsFilters,
} from "./components";
import GoogleDriveSVG from "common/icons/GoogleDriveSVG";
import { IItem } from "store/item/item.types";
import VoiceSheetWrapper from "common/components/voice/voice-wrapper/VoiceSheetWrapper";

export const Items = ({ logs, allItemFeatures, setAllItemFeatures }) => {
  const { bot, planfunction } = useBotStore();
  const { items, delete_one_item, delete_many_items,update_one_item } = useItemStore();
  const categories = useCategoryStore((state) => state.categories);
  const confirmModal = useConfirmModal();

  const [view, setView] = useState("cards");

  const [keySearch, setKeySearch] = useState("");
  const [sortOption, setSortOption] = useState("");
  const [sortInStock, setSortInStock] = useState("");
  const [categoriesFilter, setCategoriesFilter] = useState([]);
  const [priceRange, setPriceRange] = useState({ min: null, max: null });
  const [renderedItems, setRenderedItems] = useState([]);

  const [showModal, setShowModal] = useState(false);
  const [showSpecModal, setShowSpecModal] = useState(false);
  const [showOptionsModal, setShowOptionsModal] = useState(false);

  const [itemToEdit, setItemToEdit] = useState(null);
  const [canAddItem, setCanAddItem] = useState(true);

  const [importedFileData, setImpotedFileData] = useState([]);
  const [showImportModal, setShowImportModal] = useState(false);

  const [showHiddenItems, setShowHiddenItems] = useState(false);

  const { CSVReader } = useCSVReader();

  const convertCSVToJSON = (file) => {
    const data_json = [];

    const headerRow = file[0];

    file.slice(1).forEach((entity) => {
      const obj = {};
      headerRow.forEach((columnName, index) => {
        obj[columnName] = entity[index];
      });
      data_json.push(obj);
    });

    return data_json;
  };

  useEffect(() => {
    const canAdd = planFunctionChecker(planfunction, "max_items", items.length);
    setCanAddItem(canAdd);
  }, [items, bot]);

  useEffect(() => {
    let filteredItems = [...items];
    if (categoriesFilter.length > 0) {
      filteredItems = filteredItems.filter((item) =>
        categoriesFilter.includes(item.category_id)
      );
    }
    if (priceRange.min || priceRange.max) {
      filteredItems = filteredItems.filter((item) => {
        const price = item.item_price;
        return (
          (!priceRange.min || +price >= +priceRange.min) &&
          (!priceRange.max || +price <= +priceRange.max)
        );
      });
    }
    if (sortOption === "Title, DESC") {
      filteredItems.sort((a, b) => (a.item_title > b.item_title ? -1 : 1));
    }
    if (sortOption === "Title, ASC") {
      filteredItems.sort((a, b) => (a.item_title > b.item_title ? 1 : -1));
    }
    if (sortOption === "Price, DESC") {
      filteredItems.sort((a, b) => (a.item_price > b.item_price ? -1 : 1));
    }
    if (sortOption === "Price, ASC") {
      filteredItems.sort((a, b) => (a.item_price > b.item_price ? 1 : -1));
    }
    if (sortInStock === "In Stock") {
      filteredItems = filteredItems.filter((item) => item.item_instock);
    }
    if (sortInStock === "Out of Stock") {
      filteredItems = filteredItems.filter((item) => !item.item_instock);
    }
    if (sortInStock === "Hidden Items") {
      filteredItems = filteredItems.filter((item) => item.item_hide);
    }
    if (sortOption === "Category, ASC") {
      filteredItems.sort((a, b) => (a.category_id > b.category_id ? 1 : -1));
    }
    if (sortOption === "Category, DESC") {
      filteredItems.sort((a, b) => (a.category_id > b.category_id ? -1 : 1));
    }

    setRenderedItems(
      searchedArray(keySearch, filteredItems, [
        "item_title",
        "item_description",
      ])
    );
  }, [
    items,
    categoriesFilter,
    priceRange,
    sortOption,
    keySearch,
    sortInStock,
    showHiddenItems,
  ]);

  const handleResetAll = () => {
    setSortOption("");
    setSortInStock("");
    setCategoriesFilter([]);
    setPriceRange({ min: null, max: null });
  };

  const onDeleteItem = async (itemToDelete) => {
    const ids = [];
    logs.map((log) => {
      if (log.items) {
        log.items.map((a) => {
          ids.push(a.item_id);
        });
      }
    });
    const itemsIds = ids.reduce((acc, current) => {
      const x = acc.find((item) => item === current);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);

    const index = itemsIds.findIndex((a) => a === itemToDelete.item_id);
    if (index === -1) {
      await delete_one_item(itemToDelete.item_id);
      toast.success("Item deleted successfully.");
    } else {
      toast.error("You can not delete this item.");
    }
  };
  const handleUpdate = (data:{voice_path_female:string,voice_path_male:string},item:IItem) => {
     update_one_item({
      ...data,item_id:item.item_id
    })
  };
  const actionsArray = [
    {
      label: "Edit Voice",
      component: ({ item }) => (
      <VoiceSheetWrapper handleUpdate={handleUpdate} item={item} answer={item?.item_title + ", " + item?.item_description}  />
      ),
    },
    {
      label: "Edit",
      onClick: (item) => {
        setItemToEdit(item);
        setShowModal(true);
      },
      icon: Edit,
    },
    {
      label: "Delete",
      onClick: (item) => {
        confirmModal.setOnConfirm(async () => await onDeleteItem(item));
        confirmModal.setType("delete");
        confirmModal.onOpen();
      },
      icon: Trash2,
    },
    {
      label: "Options",
      onClick: (item) => {
        setItemToEdit(item);
        setShowOptionsModal(true);
      },
      icon: Menu,
    },
    {
      label: "Specifications",
      onClick: (item) => {
        setItemToEdit(item);
        setShowSpecModal(true);
      },
      icon: Gem,
    },
  ];

  const tableData = () => {
    const data = renderedItems;

    const columns = [
      { name: "Item", key: "item_title" },
      { name: "Description", key: "item_description" },
      { name: "Price", key: "item_price" },
    ];

    const actions = actionsArray;

    const itemsPerPage = 10;

    return { data, columns, itemsPerPage, actions };
  };

  const cardsData = () => {
    const items = renderedItems;

    const actions = actionsArray;

    return { items, actions };
  };

  const exportFile = () => {
    const headers = [
      { label: "item_title", key: "item_title" },
      { label: "item_description", key: "item_description" },
      { label: "item_qty", key: "item_qty" },
      { label: "category_name", key: "category_name" },
      { label: "item_icons", key: "item_icons" },
      { label: "item_price", key: "item_price" },
    ];

    const data = [
      ...items.map((a) => {
        return {
          item_title: a.item_title,
          item_description: a.item_description,
          item_qty: a.item_qty,
          category_name: categories.find((b) => b.category_id === a.category_id)
            ? categories.find((b) => b.category_id === a.category_id)
                .category_name
            : categories.find((b) => b.category_id === a.category_id),
          item_icons: [...a.item_icons.map((b) => b)],
          item_price: `${a.item_price} ${a.currency}`,
        };
      }),
    ];

    const csvReport = {
      data: data,
      headers: headers,
      filename: `${bot.bot_name}-items.csv`,
    };

    return csvReport;
  };

  const onClickCreateItem = () => {
    if (canAddItem) {
      setShowModal(true);
    }
  };

  return (
    <>
      {categories.length !== 0 && (
        <AddUpdateItemModal
          showModal={showModal}
          setShowModal={setShowModal}
          item={itemToEdit}
          setItemToEdit={setItemToEdit}
          canAddItem={canAddItem}
        />
      )}
      <ItemSpecificationsModal
        showModal={showSpecModal}
        setShowModal={setShowSpecModal}
        item={itemToEdit}
        allItemFeatures={allItemFeatures}
        setAllItemFeatures={setAllItemFeatures}
        itemFeatures={
          itemToEdit
            ? allItemFeatures.filter((a) => a.item_id === itemToEdit.item_id)
            : []
        }
        setItemToEdit={setItemToEdit}
      />
      <ItemOptionsModal
        showModal={showOptionsModal}
        setShowModal={setShowOptionsModal}
        item={itemToEdit}
        setItemToEdit={setItemToEdit}
      />
      <ImportItemsModal
        showModal={showImportModal}
        setShowModal={setShowImportModal}
        importedItems={importedFileData}
      />

      <div className="grid grid-cols-4 gap-3">
        <div>
          <div className="flex justify-center text-center flex-wrap">
            <div
              className={`min-w-[100px] items-center justify-center rounded-[0.185rem] px-3 py-1.5  text-sm font-medium text-slate-400 transition-all  disabled:pointer-events-none disabled:opacity-50
            ${
              view === "cards"
                ? "bg-gray-400 text-slate-900 shadow-sm"
                : "cursor-pointer"
            }`}
              onClick={() => setView("cards")}
            >
              Cards
            </div>
            <div
              className={`min-w-[100px] items-center justify-center rounded-[0.185rem] px-3 py-1.5  text-sm font-medium text-slate-400 transition-all  disabled:pointer-events-none disabled:opacity-50
            ${
              view === "table"
                ? "bg-gray-400 text-slate-900 shadow-sm"
                : "cursor-pointer"
            }
         `}
              onClick={() => setView("table")}
            >
              Table
            </div>
          </div>
          <ItemsFilters
            categories={categories}
            sortOption={sortOption}
            setSortOption={setSortOption}
            sortInStock={sortInStock}
            setSortInStock={setSortInStock}
            categoriesFilter={categoriesFilter}
            setCategoriesFilter={setCategoriesFilter}
            priceRange={priceRange}
            setPriceRange={setPriceRange}
            handleResetAll={handleResetAll}
          />
        </div>

        <div className="flex flex-col gap-3 col-span-3">
          <div className="flex justify-between">
            <div>
              <CustomSearch
                placeholder="Find an item..."
                onChange={(value) => setKeySearch(value)}
              />
            </div>
            <div className="flex gap-2">
              {renderedItems?.length !== 0 && (
                <CSVLink {...exportFile()}>
                  <Button>
                    <UploadIcon className="h-4 w-4" />
                  </Button>
                </CSVLink>
              )}
              <Button variant="outline" className="flex gap-2">
                <GoogleDriveSVG />
                Import Google Drive
              </Button>
              <CSVReader
                onUploadAccepted={(results: any) => {
                  if (results.data.length > 0) {
                    const data_json = convertCSVToJSON(results.data);
                    setImpotedFileData([...data_json]);
                    setShowImportModal(true);
                  } else {
                    toast.error("No data found in the file");
                  }
                }}
              >
                {({
                  getRootProps,
                  acceptedFile,
                  ProgressBar,
                  getRemoveFileProps,
                }: any) => (
                  <div {...getRootProps()}>
                    {/* {acceptedFile ? (
                      ""
                    ) : ( */}
                    <Button variant="outline">
                      <DownloadIcon className="mr-2 h-4 w-4" /> Import CSV
                    </Button>
                    {/* )} */}
                  </div>
                )}
              </CSVReader>
              <div className="group relative">
                <Button
                  onClick={onClickCreateItem}
                  disabled={!canAddItem || !categories.length}
                >
                  <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Item
                </Button>
                {!categories.length ? (
                  <div className="hidden group-hover:block absolute z-50 bg-secondary border border-white/50 text-white  p-2 break-words whitespace-break-spaces rounded">
                    You need to create a category first.
                  </div>
                ) : !canAddItem ? (
                  <div className="hidden group-hover:block w-52 absolute z-50 bg-secondary border border-white/50 text-white  p-2 break-words whitespace-break-spaces rounded">
                    You reached the maximum number of items allowed in your
                    plan. If you want to add more items, please upgrade your
                    plan.
                  </div>
                ) : null}
              </div>
            </div>
          </div>
          {view === "table" ? (
            <MainTable {...tableData()} />
          ) : (
            <ItemsCards {...cardsData()} />
          )}
        </div>
      </div>
    </>
  );
};
