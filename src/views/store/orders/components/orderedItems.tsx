import { FullModal } from "common/components/modals";
import { MainTable } from "common/components/tables/main.table";
import React from "react";
import useCartStore from "store/cart/cart.store";

interface OrderedItemsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  items: any[];
}

export const OrderedItemsModal: React.FC<OrderedItemsModalProps> = ({
  showModal,
  setShowModal,
  items,
}) => {
  const cart = useCartStore((state) => state.cart);
  const handleClose = () => {
    setShowModal(false);
  };

  const tableData = () => {
    const data = items.map((item) => {
      return {
        ...item,
        total_price: item.qty * item.price + " " + cart.cart_currency,
        price: item.price + " " + cart.cart_currency,
      };
    });

    const columns = [
      {
        name: "Item",
        key: "title",
      },
      {
        name: "Quantity",
        key: "qty",
      },
      {
        name: "Unit Price",
        key: "price",
      },
      {
        name: "Total Price",
        key: "total_price",
      },
      {
        name: "Note",
        key: "note",
      },
    ];

    return {
      data,
      columns,
    };
  };

  return (
    <FullModal
      title={`Ordered Items `}
      isOpen={showModal}
      onClose={handleClose}
    >
      <div>
        <MainTable {...tableData()} />
      </div>
    </FullModal>
  );
};
