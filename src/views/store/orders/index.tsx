import React, { useEffect, useState } from "react";
import { getAll as getAllCartLogs, updateItem } from "apis/cartLog.api";
import useBotStore from "store/bot/bot.store";
import { approximate, convertTZ, findTZ } from "helpers/date";
import useCartStore from "store/cart/cart.store";
import { MainTable } from "common/components/tables/main.table";
import { cleanPhoneNumber } from "helpers/phone";
import { CSVLink } from "react-csv";
import { Button } from "common/ui/button";
import searchedArray from "helpers/search";
import { CustomSearch } from "common/ui/inputs/search";
import {
  Package,
  Pen,
  RefreshCcw,
  RefreshCw,
  RotateCw,
  UploadIcon,
} from "lucide-react";
import { OrderedItemsModal } from "./components";
import { FullModal } from "common/components/modals";
import toast from "react-hot-toast";

export const Orders = ({ logs, setLogs }) => {
  const bot = useBotStore((state) => state.bot);
  const cart = useCartStore((state) => state.cart);

  const [keySearch, setKeySearch] = useState("");

  const [itemsToshow, setItemsToShow] = useState([]);
  const [showOrderedItemsModal, setShowOrderedItemsModal] = useState(false);
  const [deliveredTab, setDeliveredTab] = useState(false);
  const [markAsDelivered, setMarkAsDelivered] = useState(false);
  const [deliveredItem, setDeliveredItem] = useState([]);


  const getData = () =>
    getAllCartLogs(bot.bot_id).then((data) => {
      if (data && !data.message) {
        const logsData = data.map((a) => {
          return {
            ...a,
            date: new Date(convertTZ(a.createdAt, findTZ())).toDateString(),
            time: new Date(
              convertTZ(a.createdAt, findTZ())
            ).toLocaleTimeString(),
          };
        });
        setLogs(logsData);
      }
    });

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    console.table(logs);
  }, [logs]);

  const exportFile = () => {
    const headers = [
      { label: "Name", key: "username" },
      { label: "Phone", key: "phone" },
      { label: "Address", key: "address" },
      { label: "Total Price", key: "price_total" },
      { label: "Payment Type", key: "payment_type" },
      { label: "Order Status", key: "order_status"},
      { label: "date", key: "date" },
      { label: "time", key: "time" },
    ];

    const data = [
      ...logs.map((a) => {
        return {
          username: a.username,
          phone: a.phone?.toString(),
          address: a.address,
          price_total: a.price_total,
          payment_type: a.payment_type || "cash on deleviry",
          date: new Date(convertTZ(a.createdAt, findTZ())).toDateString(),
          time: new Date(convertTZ(a.createdAt, findTZ())).toLocaleTimeString(),
        };
      }),
    ];

    const csvReport = {
      data: data,
      headers: headers,
      filename: `${bot.bot_name}-order.csv`,
    };

    return csvReport;
  };

  const tableData = () => {
    let data = logs
    .filter((log) => log.order_status != "Delivered")
    .map((log) => {
      return {
        ...log,
        total_vat:
          approximate(
            log.price_total - log.price_total / (1 + cart.vat_ammount)
          ) /
            100 +
          " " +
          cart.cart_currency,
        order_status: log.order_status,
        price_total: log.price_total + " " + cart.cart_currency,
        payment_type: log.payment_type || "Cash on Delivery",
        phone: cleanPhoneNumber(log.phone),
      };
    });

    data = searchedArray(keySearch, data, ["username", "phone", "address"]);

    const columns = [
      {
        name: "Phone Number",
        key: "phone",
      },
      {
        name: "Address",
        key: "address",
      },
      {
        name: "Customer Name",
        key: "username",
      },
      {
        name: "Date",
        key: "date",
      },
      {
        name: "Time",
        key: "time",
      },
      {
        name: "Total Price",
        key: "price_total",
      },
      {
        name: "Total VAT",
        key: "total_vat",
      },
      {
        name: "Order Status",
        key: "order_status",
      },
      {
        name: "Payment Type",
        key: "payment_type",
      },
    ];

    const actions = [
      {
        label: "View",
        onClick: (log) => {
          setItemsToShow(log.items ? [...log.items] : []);
          setShowOrderedItemsModal(true);
        },
        icon: Package,
      },
      {
        label: "Mark As Delivered",
        onClick: (data) => {
          setMarkAsDelivered(true);
          setDeliveredItem(data);
        },
        onHover: "Mark As Delivered",
        icon: Pen,
      },
    ];
    const itemsPerPage = 10;

    return { data, columns, itemsPerPage, actions };
  };

  return (
    <>
      <OrderedItemsModal
        showModal={showOrderedItemsModal}
        setShowModal={setShowOrderedItemsModal}
        items={itemsToshow}
        />

      <Delivered
        logs={logs}
        deliveredTab={deliveredTab}
        setDeliveredTab={setDeliveredTab}
        />

      <MarkAsDeliveredModule
        setMarkAsDelivered = {setMarkAsDelivered}
        setDeliveredItem = {setDeliveredItem}
        markAsDelivered = {markAsDelivered}
        deliveredItem ={deliveredItem}
        getData={getData}
      />

      <div className="space-y-3">
        <div className="flex justify-between">
          <div>
            <CustomSearch
              placeholder="Find an Order"
              onChange={(value) => setKeySearch(value)}
            />
          </div>
          <div className="flex items-center gap-3">
            <RotateCw
              onClick={getData}
              className="cursor-pointer hover:text-primary"
            />
            <Button
              onClick={()=>{setDeliveredTab(true);}}
            >
              Delivered
            </Button>
            <CSVLink {...exportFile()}>
              <Button>
                {" "}
                <UploadIcon className="h-4 w-4 mr-2" />
                Export as CSV
              </Button>
            </CSVLink>
          </div>
        </div>
        <MainTable {...tableData()} />
      </div>
    </>
  );
};

const Delivered = ({
  logs,
  deliveredTab,
  setDeliveredTab,

}) => {
  const cart = useCartStore((state) => state.cart);
  const tableData=()=>{
    const data = logs
    .filter((log) => log.order_status === "Delivered")
    .map((log) => {
      return {
        ...log,
          total_vat:
            approximate(
              log.price_total - log.price_total / (1 + cart.vat_ammount)
            ) /
              100 +
            " " +
            cart.cart_currency,
          order_status: log.order_status,
          price_total: log.price_total + " " + cart.cart_currency,
          payment_type: log.payment_type || "Cash on Delivery",
          phone: cleanPhoneNumber(log.phone),
      };
    });
    const columns = [
      {
        name: "Phone Number",
        key: "phone",
      },
      {
        name: "Address",
        key: "address",
      },
      {
        name: "Customer Name",
        key: "username",
      },
      {
        name: "Date",
        key: "date",
      },
      {
        name: "Time",
        key: "time",
      },
      {
        name: "Total Price",
        key: "price_total",
      },
      {
        name: "Order Status",
        key: "order_status",
      },
      {
        name: "Payment Type",
        key: "payment_type",
      },
    ];
    return {
      data, 
      columns
    }
  }
  return (
    <FullModal
      isOpen={deliveredTab}
      onClose={() => setDeliveredTab(false)}
      title="Delivered Orders"
      className="h-fit w-fit"
    >
      <div>
        <MainTable {...tableData()} className="w-full"/>
      </div>
    </FullModal>
  )
}

const MarkAsDeliveredModule =({
  setMarkAsDelivered,
  setDeliveredItem,
  markAsDelivered,
  deliveredItem,
  getData,
})=>{
  const tableData = () => {
    const data = [
      {
        order_status: deliveredItem?.order_status || "",
        price_total: deliveredItem?.price_total || "",
        address: deliveredItem?.address || "",
        username: deliveredItem?.username || "",
      }
    ]
    const columns = [
      {
        key: "username",
        name: "Customer Name",
      },
      {
        key: "address",
        name: "Address",
      },
      {
        key: "price_total",
        name: "price",
      },
      {
        key: "order_status",
        name: "Status",
      },
    ]
    return {
      data,
      columns,
    };
  };
  return (
    <FullModal
      isOpen={markAsDelivered}
      onClose={() => setMarkAsDelivered(false)}
      title="Mark As Delivered"
    >
      <div>
        <MainTable {...tableData()} />
        <div className="flex justify-end px-6 py-3">

        <Button 
          title="Mark As Delivered"
          onClick={()=>{
            if(deliveredItem.order_status === "Delivered"){
              toast('Already  Marked As Delivered', {
                icon: '⚠️'
              });
              getData();
              setMarkAsDelivered(false);
            } else {
              updateItem({bot_id: deliveredItem.bot_id, cart_log_id: deliveredItem.cart_log_id, order_status: "Delivered"})
              setMarkAsDelivered(false);
              getData();
              toast.success('Changed Status.');
            }
          }}
          className="hover:text-white px-6 py-3 w-32 bg-black text-primary border-2   border-primary font-bold"
          >
            Mark As Delivered
        </Button>
        </div>
      </div>
    </FullModal>
  )
}

