import { FullModal } from "common/components/modals";
import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { Checkbox } from "common/ui/inputs/checkbox";
import { CustomSearch } from "common/ui/inputs/search";
import searchedArray from "helpers/search";
import React, { useEffect, useState } from "react";

export const SelectItemsModal = ({
  showModal,
  setShowModal,
  allItems,
  currentlyAssignedItems,
  setOfferItemsToChange,
  offer,
}) => {
  const handleClose = () => {
    setShowModal(false);
  };
  const [itemsCheckList, setItemsCheckList] = useState([]);
  const [keySearch, setKeySearch] = useState("");

  const [isAllSelected, setIsAllSelected] = useState(false);

  useEffect(() => {
    const itemsCheckListToUpdate = [];
    [...allItems()].map((a) => {
      itemsCheckListToUpdate.push({
        choosen: currentlyAssignedItems.find((b) => b.item_id === a.item_id)
          ? true
          : false,
        ...a,
      });
    });
    setItemsCheckList([...itemsCheckListToUpdate]);
  }, [offer, currentlyAssignedItems]);

  const onSelectItem = (item_id, value) => {
    const updateCheckList = [...itemsCheckList];
    updateCheckList.find((a) => a.item_id === item_id).choosen = value;
    setItemsCheckList([...updateCheckList]);
  };

  const onSelectAll = () => {
    if (isAllSelected) {
      const updateCheckList = [...itemsCheckList];
      updateCheckList.map((a) => (a.choosen = false));
      setItemsCheckList([...updateCheckList]);
      setIsAllSelected(false);
    } else {
      const updateCheckList = [...itemsCheckList];
      updateCheckList.map((a) => (a.choosen = true));
      setItemsCheckList([...updateCheckList]);
      setIsAllSelected(true);
    }
  };

  useEffect(() => {
    if (itemsCheckList.length) {
      const isAllSelected = itemsCheckList.every((a) => a.choosen === true);
      setIsAllSelected(isAllSelected);
    }
  }, [itemsCheckList]);

  const tableData = () => {
    const data = searchedArray(keySearch, itemsCheckList);

    const columns = [
      {
        name: "Item",
        key: "item_title",
      },
      {
        name: "Original Price",
        key: "item_price",
      },
    ];

    const actions = [
      {
        label: "Select",
        component: ({ item }) => (
          <Checkbox
            name="select"
            onChange={(event) =>
              onSelectItem(item.item_id, event.target.checked)
            }
            checked={item.choosen}
          />
        ),
      },
    ];

    return {
      columns,
      data,
      actions,
    };
  };

  const onSaveHandler = () => {
    var itemsStateToUpdate = [...itemsCheckList];
    itemsStateToUpdate = itemsStateToUpdate.filter((a) => a.choosen);
    setOfferItemsToChange([...itemsStateToUpdate]);
    handleClose();
  };

  return (
    <FullModal
      title={`Select Items `}
      isOpen={showModal}
      onClose={handleClose}
      onSave={onSaveHandler}
      footer
      className="!w-11/12"
    >
      <div className="space-y-3">
        <div className="whitespace-nowrap text-xs">
          {itemsCheckList.filter((a) => a.choosen).length} items selected
        </div>
        <div className="flex justify-between items-center gap-20">
          <div>
            <CustomSearch
              placeholder="Find an item..."
              onChange={(value) => setKeySearch(value)}
            />
          </div>

          <Button
            onClick={() => onSelectAll()}
            variant="link"
            className="whitespace-nowrap"
          >
            {isAllSelected ? "UnSelect All" : "Select All"}
          </Button>
        </div>
        <MainTable {...tableData()} />
      </div>
    </FullModal>
  );
};
