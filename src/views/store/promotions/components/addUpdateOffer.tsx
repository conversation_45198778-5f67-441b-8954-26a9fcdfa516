import { FullModal } from "common/components/modals";
import { MediaInput } from "common/ui/inputs/mediaInput";
import constant from "constant";
import generateStorageId from "helpers/generateStorageId";
import { handleUploadImg } from "helpers/media";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import helper from "../../helper";
import { Input } from "common/ui/inputs/input";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { DateRange } from "react-day-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { z } from "zod";
import useOfferStore from "store/offers/offers.store";
import { Button } from "common/ui/button";
import { MainTable } from "common/components/tables/main.table";
import { SelectItemsModal } from "./selectItems";
import { uploadBotImages } from "apis/file.api";
import { createMany } from "apis/itemsOffers.api";
import toast from "react-hot-toast";
import { createOne, updateOne } from "apis/offers.api";
import { InfoIcon } from "lucide-react";
import checkForErrors from "helpers/forms";
import { isArabic } from "helpers/helper";

export const AddUpdateOfferModal = ({
  offer,
  setOfferToEdit,
  showModal,
  setShowModal,
  offerItems,
  allItems,
  setOfferItems,
}) => {
  const bot = useBotStore((state) => state.bot);
  const { OFFERS: offers, get_all_offers } = useOfferStore();
  const [offerData, setOfferData] = useState(helper.initCreateOfferData());
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(offerData?.offer_date_start),
    to: new Date(offerData?.offer_date_end),
  });
  const [offerItemsToChange, setOfferItemsToChange] = useState([]);
  const [showSelectItemsModal, setShowSelectItemsModal] = useState(false);
  const [imageToUpload, setImageToUpload] = useState(null);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const updateOfferItemsState = (newItemsState, offer_id) => {
    const offer_items_to_update = [
      ...offerItems.filter((a) => a.offer_id !== offer_id),
    ];
    newItemsState.map((a) => {
      offer_items_to_update.push(a);
    });
    setOfferItems([...offer_items_to_update]);
  };

  useEffect(() => {
    if (offer) {
      setOfferData({
        ...offer,
      });
      setOfferItemsToChange([...offerItems]);
    } else {
      setOfferData(helper.initCreateOfferData());
      setOfferItemsToChange([]);
    }
  }, [offer, offerItems]);

  useEffect(() => {
    setOfferData({
      ...offerData,
      offer_date_start: date?.from.toLocaleDateString(),
      offer_date_end: date?.to.toLocaleDateString(),
    });
  }, [date]);

  const CreateOfferSchema = z.object({
    offer_description: z
      .string()
      .min(3, { message: "Offer name is required" })
      .refine(
        (value) =>
          !offers.find(
            (a) =>
              a.offer_description.trim().toLowerCase().replace(/\s+/g, " ") ===
                value.trim().replace(/\s+/g, " ").toLowerCase() &&
              a.offer_id !== offer?.offer_id
          ),
        {
          message: "Offer name is reserved, Try another one",
        }
      ),
    offer_date_start: z.string().nonempty("Offer start date is required"),
    offer_date_end: z.string().nonempty("Offer end date is required"),
    offer_type: z.string(),
    fixed_value: z.union([z.string(), z.number()]),
    offer_percentage: z.number(),
  });

  type CreateOfferType = z.infer<typeof CreateOfferSchema>;

  const validateField =
    (field: keyof CreateOfferType) =>
    (value: unknown): string => {
      const parsedResult = CreateOfferSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    const category_to_change = { ...offerData };
    category_to_change[key] = value;
    setOfferData({ ...category_to_change });
  };

  const uploadImg = (img, card_index) => {
    const formData = new FormData();
    formData.append("file", img);
    const path = `Bots/${bot.file_name}/itemsAssets/${generateStorageId()}.${
      img.type.split("/")[1]
    }`;
    onChangeHandler("offer_icon", constant.MEDIA_STORAGE_URL + path);
    const imageToChange = {
      path,
      formData,
    };
    setImageToUpload({ ...imageToChange });
    setTimeout(() => {
      handleUploadImg(`card-poster-${card_index}`, img);
    }, 100);
  };

  const isDisabled = () => {
    const missing = [];
    // if offerdata offer type is fixed value check for fixed value else check for percentage
    Object.keys(offerData).map((a) => {
      if (offerData.offer_type === "Fixed") {
        if (
          typeof offerData[a] === "string" &&
          a !== "offer_icon" &&
          a !== "offer_percentage"
        ) {
          if (!offerData[a].length) {
            missing.push(a);
          }
        }
      } else {
        if (
          typeof offerData[a] === "string" &&
          a !== "offer_icon" &&
          a !== "fixed_value"
        ) {
          if (!offerData[a].length) {
            missing.push(a);
          }
        }
      }
    });
    return missing.length;
  };

  const itemsWithOfferTableData = () => {
    const data = offerItemsToChange.map((a) => {
      return {
        item_title: a.item_title,
        item_price: a.item_price,
        item_discount:
          offerData.offer_type === "Fixed"
            ? offerData.fixed_value
            : offerData.offer_percentage * 100 + "%",
        item_new_price:
          offerData.offer_type === "Fixed"
            ? +(a.item_price - +offerData.fixed_value).toFixed(2)
            : a.item_price - a.item_price * offerData.offer_percentage,
      };
    });

    const columns = [
      {
        name: "Item",
        key: "item_title",
      },
      {
        name: "Price",
        key: "item_price",
      },
      {
        name: "Discount",
        key: "item_discount",
      },
      {
        name: "New Price",
        key: "item_new_price",
      },
    ];
    return { data, columns };
  };

  const handleClose = () => {
    setShowModal(false);
    setOfferToEdit(null);
    setOfferData(helper.initCreateOfferData());
    setErrors({});
  };

  const handleCreateOfferItems = async (offer_data, itemsToBeSet) => {
    const data = await createMany(
      offer_data.offer_id,
      bot.bot_id,
      itemsToBeSet.length ? [...itemsToBeSet] : [{}]
    );
    if (data) {
      await get_all_offers(bot.bot_id);
      updateOfferItemsState(data, offer_data.offer_id);
      handleClose();
    }
  };

  const handleUpdate = async () => {
    const update_data = await updateOne({
      ...offerData,
      lemmatized_offer_description: isArabic(offerData.offer_description)
        ? offerData.offer_description
        : "",
    });
    if (update_data) {
      const itemsToBeSet = offerItemsToChange.map((a) => {
        return {
          bot_id: bot.bot_id,
          offer_id: update_data.offer_id,
          item_id: a.item_id,
        };
      });

      await handleCreateOfferItems(update_data, itemsToBeSet);
      toast.success("Offer Updates Successfully");
    }
  };

  const handleCreate = async () => {
    const create_data = await createOne({
      bot_id: bot.bot_id,
      ...offerData,
      lemmatized_offer_description: isArabic(offerData.offer_description)
        ? offerData.offer_description
        : "",
    });
    if (create_data) {
      const itemsToBeSet = offerItemsToChange.map((a) => {
        return {
          bot_id: bot.bot_id,
          offer_id: create_data.offer_id,
          item_id: a.item_id,
        };
      });
      await handleCreateOfferItems(create_data, itemsToBeSet);
      toast.success("Offer Created Successfully");
    }
  };

  const handleNoChanges = () => {
    if (offer) {
      if (
        JSON.stringify(offerData) === JSON.stringify(offer) &&
        JSON.stringify(offerItemsToChange) === JSON.stringify(offerItems)
      ) {
        toast("No changes made", {
          icon: <InfoIcon className="text-blue-500" />,
        });
        return true;
      }
    }
    return false;
  };

  const onSaveHandler = async () => {
    const isNoChanges = handleNoChanges();

    if (isNoChanges) {
      return;
    }

    const isErrors = checkForErrors(
      {
        zodSchema: CreateOfferSchema,
        data: offerData,
      },
      setErrors
    );

    if (isErrors) {
      return;
    }

    setLoading(true);

    if (imageToUpload) {
      await uploadBotImages({
        ...imageToUpload,
        path: imageToUpload.path + "&image=true",
      });
    }
    if (offerData.offer_id) {
      await handleUpdate();
    } else {
      await handleCreate();
    }

    setLoading(false);
  };

  return (
    <>
      <FullModal
        title={offer ? "Update Offer" : "Create Offer"}
        isOpen={showModal}
        onClose={handleClose}
        // disabled={Boolean(isDisabled())}
        onSave={onSaveHandler}
        loading={loading}
        footer
      >
        <SelectItemsModal
          showModal={showSelectItemsModal}
          setShowModal={setShowSelectItemsModal}
          allItems={allItems}
          currentlyAssignedItems={offerItemsToChange}
          setOfferItemsToChange={setOfferItemsToChange}
          offer={offer}
        />
        <div className="flex flex-col gap-5">
          <MediaInput
            card_index={0}
            imgSrc={offerData?.offer_icon}
            uploadImg={uploadImg}
          />
          <Input
            name="offer_description"
            title="Offer"
            value={offerData?.offer_description}
            onChange={(e) =>
              onChangeHandler("offer_description", e.target.value)
            }
            error={errors?.offer_description}
          />
          <div className="flex items-center gap-2">
            <label htmlFor="">Start and End Dates</label>
            <DatePickerWithRange date={date} setDate={setDate} />
          </div>
          <div className="flex items-center gap-2">
            <div className="w-full">
              <label htmlFor="Category" className="block font-medium pb-1">
                Offer Type
              </label>
              <Select
                value={offerData?.offer_type}
                onValueChange={(value) => {
                  onChangeHandler("offer_type", value);
                }}
              >
                <SelectTrigger className="w-full ">
                  <SelectValue placeholder="Offer Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Percentage">Percentage</SelectItem>
                  <SelectItem value="Fixed">Fixed Value</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full">
              <label htmlFor="Category" className="block font-medium pb-1">
                Offer Status
              </label>
              <Select
                value={offerData?.offer_status}
                onValueChange={(value) => {
                  onChangeHandler("offer_status", value);
                }}
              >
                <SelectTrigger className="w-full ">
                  <SelectValue placeholder="Offer Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {offerData?.offer_type === "Percentage" ? (
              <Input
                name="offer_percentage"
                title="Offer percentage"
                value={offerData?.offer_percentage * 100}
                onChange={(e) => {
                  if (+e.target.value <= 100) {
                    onChangeHandler("offer_percentage", +e.target.value / 100);
                  }
                }}
                error={errors?.offer_percentage}
              />
            ) : (
              <Input
                name="fixed_value"
                title="Offer Value"
                value={offerData?.fixed_value}
                onChange={(e) => {
                  if (e.target.value === ".") {
                    onChangeHandler("fixed_value", "0.");
                  }
                  if (!isNaN(e.target.value)) {
                    onChangeHandler("fixed_value", e.target.value);
                  }
                }}
                error={errors?.fixed_value}
              />
            )}
          </div>

          <hr />
          <div className="flex justify-between">
            <span>Apply Offer to Items</span>
            <Button onClick={() => setShowSelectItemsModal(true)}>
              {offerItemsToChange.length > 0 ? "Edit Items" : "Select Items"}
            </Button>
          </div>
          <MainTable {...itemsWithOfferTableData()} />
        </div>
      </FullModal>{" "}
    </>
  );
};
