import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import toast from "react-hot-toast";
import searchedArray from "helpers/search";
import { Edit, PlusCircleIcon, Trash2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import useItemStore from "store/item/item.store";
import useOfferStore from "store/offers/offers.store";
import { AddUpdateOfferModal } from "./components";
import planFunctionChecker from "helpers/planFunctionChecker";
import useBotStore from "store/bot/bot.store";
import useConfirmModal from "common/hooks/useConfirmModal";

export const Promotions = ({ offerItems, setOfferItems }) => {
  const { bot, planfunction } = useBotStore();

  const [canAddOffer, setCanAddOffer] = useState(true);
  const [offerItemsToView, setOfferItemsToView] = useState([]);
  const [renderedOffers, setRenderedOffers] = useState([]);
  const [keySearch, setKeySearch] = useState("");
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [showAddUpdateModal, setShowAddUpdateModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const confirmModal = useConfirmModal();
  const items = useItemStore((state) => state.items);
  const { OFFERS: offers, delete_one_offer } = useOfferStore();

  useEffect(() => itemsToOffersMapper(), [offers, offerItems]);

  const itemsToOffersMapper = () => {
    const itemsToMap = [...offerItems];
    const offersToMap = [...offers];
    const offer_items_arr = [];
    offersToMap.map((offer) => {
      const itemsAttachedToOffer = itemsToMap.filter(
        (a) => a.offer_id === offer.offer_id
      );
      offer_items_arr.push({
        offer_id: offer.offer_id,
        items:
          itemsAttachedToOffer.length > 0
            ? itemsAttachedToOffer.map((a) => a.item_id)
            : [],
      });
    });

    setOfferItemsToView([...offer_items_arr]);
  };

  useEffect(() => {
    const offersToRender = offers.map((offer) => {
      return {
        ...offer,
        items_num: getOfferItems(offer.offer_id).length,
      };
    });
    setRenderedOffers(
      searchedArray(keySearch, offersToRender, ["offer_description"])
    );
  }, [offerItemsToView, keySearch]);

  const getOfferItems = (offer_id) => {
    const offer_items = offerItemsToView.find((a) => a.offer_id === offer_id);
    if (offer_items) {
      const itemsToReturn = [];
      offer_items.items.map((a) => {
        itemsToReturn.push({
          ...items.find((b) => b.item_id === a),
        });
      });
      return [...itemsToReturn];
    }
    return [];
  };

  const onDeleteOffer = async (offerToDelete) => {
    if (getOfferItems(offerToDelete ? offerToDelete.offer_id : null).length) {
      toast.error(
        "You can not delete this offer, because it is already attached to items."
      );
    } else {
      await delete_one_offer(offerToDelete.offer_id);
      toast.success("Offer deleted successfully.");
    }
  };

  useEffect(() => {
    const canAdd = planFunctionChecker(
      planfunction,
      "max_offers",
      offers.length
    );
    setCanAddOffer(canAdd);
  }, [offers, bot]);

  const onClickCreateOffer = () => {
    if (canAddOffer) {
      setShowAddUpdateModal(true);
    }
  };

  const tableData = () => {
    const data = renderedOffers;

    const columns = [
      { name: "Offer", key: "offer_description" },
      { name: "Offer Type", key: "offer_type" },
      { name: "Status", key: "offer_status" },
      { name: "Number Of Items", key: "items_num" },
    ];

    const actions = [
      {
        label: "Edit",
        onClick: (item) => {
          setSelectedOffer(item);
          setShowAddUpdateModal(true);
        },
        icon: Edit,
      },
      {
        label: "Delete",
        onClick: (item) => {
          confirmModal.onOpen();
          confirmModal.setType("delete");
          confirmModal.setOnConfirm(async () => await onDeleteOffer(item));
        },
        icon: Trash2,
      },
    ];

    const itemsPerPage = 10;

    return { data, columns, itemsPerPage, actions };
  };

  const allItems = () => {
    var allowedItems = [];
    const attachedToSelected = getOfferItems(
      selectedOffer ? selectedOffer.offer_id : null
    );
    attachedToSelected.map((a) => allowedItems.push(a));
    items.map((a) => {
      const notAttachedToAny = !offerItems.find((b) => b.item_id === a.item_id);
      if (notAttachedToAny) {
        allowedItems.push(a);
      }
    });
    return allowedItems;
  };

  return (
    <>
      <AddUpdateOfferModal
        showModal={showAddUpdateModal}
        setShowModal={setShowAddUpdateModal}
        offer={selectedOffer}
        setOfferToEdit={setSelectedOffer}
        offerItems={getOfferItems(
          selectedOffer ? selectedOffer.offer_id : null
        )}
        allItems={allItems}
        setOfferItems={setOfferItems}
      />
      <div className="flex flex-col gap-3">
        <div className="flex justify-between">
          <div>
            <CustomSearch
              placeholder="Find an offer..."
              onChange={(value) => setKeySearch(value)}
            />
          </div>

          <div className="group relative">
            <Button
              className="peer"
              onClick={onClickCreateOffer}
              disabled={!canAddOffer}
            >
              <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Offer
            </Button>
            {!canAddOffer && (
              <div className="hidden group-hover:block w-52 absolute z-50 bg-secondary border border-white/50 text-white  p-2 break-words whitespace-break-spaces rounded">
                You reached the maximum number of offers allowed in your plan.
                If you want to add more offers, please upgrade your plan.
              </div>
            )}
          </div>
        </div>
        <MainTable {...tableData()} />
      </div>
    </>
  );
};
