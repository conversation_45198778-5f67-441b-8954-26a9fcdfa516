import { Folders, LucideIcon } from "lucide-react";
import React from "react";

interface FilesInputProps {
  onChange: (files: any) => void;
  ACCEPTED_FILE_TYPES: string[];
  icon?: LucideIcon;
  isMultiple?: boolean;
}

export const FilesInput: React.FC<FilesInputProps> = ({
  onChange,
  ACCEPTED_FILE_TYPES,
  icon: Icon = Folders,
  isMultiple = true,
}) => {
  return (
    <label
      htmlFor="dropzone-file"
      className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer  hover:bg-gray-800 dark:border-gray-600 overflow-hidden"
      onDragEnter={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.add("bg-gray-200");
      }}
      onDragOver={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.add("bg-gray-200");
      }}
      onDragLeave={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.remove("bg-gray-200");
      }}
      onDrop={(event) => {
        event.preventDefault();
        event.stopPropagation();
        event.currentTarget.classList.remove("bg-gray-200");
        const files = event.dataTransfer.files;

        const acceptedFiles = Array.from(files).filter((file) =>
          ACCEPTED_FILE_TYPES.includes(file.type)
        );

        onChange(acceptedFiles);
      }}
    >
      <div
        className={`flex flex-col items-center justify-center text-center pb-5  gap-3 text-white`}
      >
        <Icon size={35} />
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Drag and drop your files here or click to browse
        </p>
      </div>
      <input
        id="dropzone-file"
        type="file"
        className="hidden"
        onChange={(e) => {
          onChange(e.target.files);
        }}
        accept={ACCEPTED_FILE_TYPES.join(",")}
        name="files"
        multiple={isMultiple}
      />
    </label>
  );
};
