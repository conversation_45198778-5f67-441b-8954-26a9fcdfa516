import { SubPageHeader } from "common/components/headers";
import React, { useEffect, useState } from "react";
import { FilesInput, UploadSheet } from "./components";
import generateStorageId from "helpers/generateStorageId";
import useBotStore from "store/bot/bot.store";
import constant from "constant";
import { Button } from "common/ui/button";
import { uploadFile } from "apis/file.api";
import { createMany, deleteOne, getAll } from "apis/unstructuredResources.api";
import useConfirmModal from "common/hooks/useConfirmModal";
import useUnstructuredResourcesStore from "store/unstructuredResources/unstructuredResources.store";
import { Checkbox } from "common/ui/inputs/checkbox";
import { toast } from "react-hot-toast";

export const UnstructuredView = () => {
  const bot = useBotStore((state) => state.bot);
  const confirmModal = useConfirmModal();
  const {
    unstructuredResources,
    get_all_unstructuredResources,
    delete_one_unstructuredResource,
    delete_many_unstructuredResources,
  } = useUnstructuredResourcesStore();

  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    get_all_unstructuredResources(bot.bot_id).then((res) => {
      console.log(res, "res.data");
    });
  }, []);

  const handleDeleteFile = (resource_id) => {
    delete_one_unstructuredResource(resource_id);
  };

  const onSelect = (e) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelectedFiles(
          unstructuredResources.map((resource) =>
            resource.resource_id.toString()
          )
        );
      } else {
        setSelectedFiles([]);
      }
    } else {
      if (checked) {
        setSelectedFiles([...selectedFiles, name.split("-")[1]]);
      } else {
        setSelectedFiles(
          selectedFiles.filter((id) => id !== name.split("-")[1])
        );
        setSelectAll(false);
      }
    }
  };

  return (
    <div className="space-y-5">
      <SubPageHeader
        title="Unstructured Knowledge Base"
        description="Upload files to your knowledge base!"
        btn
        btnText="Upload"
        btnSheet={UploadSheet}
      />
      {unstructuredResources?.length ? (
        <div className="space-y-5">
          <span className="text-xl uppercase flex justify-between items-center bg-accent rounded gap-5 pl-2">
            <Checkbox
              checked={selectAll}
              onChange={onSelect}
              name={`select-all`}
            />
            Your files
            <Button
              onClick={() => {
                confirmModal.onOpen();
                confirmModal.setOnConfirm(() => {
                  delete_many_unstructuredResources(
                    unstructuredResources.filter((resource) =>
                      selectedFiles.includes(resource.resource_id.toString())
                    )
                  ).then((res) => {
                    get_all_unstructuredResources(bot.bot_id);
                    toast.success("Files deleted successfully");

                    setSelectedFiles([]);
                  });
                });
              }}
              variant="destructive"
              className={`${selectedFiles.length ? "visible" : "invisible"}`}
            >
              Delete selected
            </Button>
          </span>
          {unstructuredResources?.map((resource, i) => {
            return (
              <div
                key={i}
                className="flex items-center justify-between p-2 pb-3 border-b border-gray-400 hover:backdrop-brightness-150"
              >
                <div className="flex items-center gap-3">
                  {" "}
                  <Checkbox
                    checked={selectedFiles.includes(
                      resource.resource_id.toString()
                    )}
                    onChange={onSelect}
                    name={`select-${resource.resource_id}`}
                    key={resource.resource_id}
                  />
                  <div className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-300 text-gray-600 text-xs tracking-tighter uppercase">
                    {" "}
                    {resource?.resource_type}
                  </div>
                  <a
                    href={resource?.resource_url}
                    target="_blank"
                    className="flex flex-col"
                  >
                    <span className="text-sm font-semibold">
                      {resource?.resource_name}
                    </span>
                  </a>
                </div>
                <button
                  onClick={() => {
                    confirmModal.onOpen();
                    confirmModal.setOnConfirm(() => {
                      handleDeleteFile(resource.resource_id);
                    });
                  }}
                  className="text-sm text-red-500"
                >
                  Delete
                </button>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="flex justify-center items-center"></div>
      )}
    </div>
  );
};
