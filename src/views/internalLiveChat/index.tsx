import React, { useEffect, useRef, useState } from "react";
import * as signalR from "@microsoft/signalr";
import { sendToAgent } from "apis/messaging.api";
import { getAgent, getSessions, rejectChat, updateAgent, updateAgentStatus, updateSessionStatus } from "apis/internalLiveChat.api";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { Separator } from "common/ui/separator";
import { Search } from "lucide-react";
import { Input } from "common/ui/input";
import SessionList from "./components/SessionList";
import ChatWindow from "./components/ChatWindow";
import { Button } from "common/ui/button";
import useInternalLiveChatStore from "store/internalLivechat/internalLivechat.store";
import { customStyles } from "common/components/MultiSelectInput";
import { SelectTimezone } from "reactjs-timezone-select";

export type Session = {
  session_id: number;
  queue_id: number;
  agent_id: number;
  start_time: Date;
  end_time: Date;
  status:
    | "pending"
    | "active"
    | "closed"
    | "closed due to agent inactivity"
    | "closed due to customer inactivity"
    | "closed due to agent rejection"
    | "closed due to inactivity";
  queue: {
    queue_id: number;
    conversation_id: string;
    customer_name: string;
    customer_email: string;
    customer_phone: string;
    channel: string;
  };
};

export const LiveChatsView: React.FC = () => {
  const bot = useBotStore((state) => state.bot);
  const user = useUserStore((state) => state.user);
  const [sessions, setSessions] = useState<Session[]>([]); 
  const [selectedSession, setSelectedSession] = useState<Session["session_id"] | null>(null);
  const [messagesPerSession, setMessagesPerSession] = useState<Record<number, any>>({});
  const [isLive, setIsLive] = useState(false);
  const {agent, set_agent} = useInternalLiveChatStore()
  const [unreadMessages, setUnreadMessages] = useState<Record<number, number>>({});

  const selectedSessionData = sessions.find(session => session.session_id === selectedSession);

  const [selectedTimeZone, setSelectedTimeZone] = useState<string | null>(null);
  const getAgentTimeZone = async () => {
    try {
      const temp = await getAgent(bot.bot_id, bot.user_id);
      setSelectedTimeZone(temp.time_zone);
    } catch (error) {
      console.error("Error fetching agent time zone:", error);
    }
  };

  useEffect(() => {
    getAgentTimeZone();
  }, []);

  const signalRNegotiate = async (bot_id, agent_id) => {
    const socketServer = "https://i2i-messaging.azurewebsites.net";
    const userId = `internal_live_chat_${bot_id}_${agent_id}`;
    const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot_id}&type=server`;
    const _connection = new signalR.HubConnectionBuilder()
      .withUrl(negotiationURL)
      .build();
    _connection
      .start()
      .then(() => {
        console.log("SHOULD BE WORKING", userId);
      })
      .catch((err) => {
        console.error("SignalR connection error:", err);
      });

    _connection.on("chatMessage", (message) => {
      console.log("chatMessage message", message);

      if (message.type === 'message') {
        setMessagesPerSession((prev) => ({
          ...prev,
          [message.session_id]: [
            ...(prev[message.session_id] || []),
            {
              message: message.text,
              sender: 'customer',
            }
          ],
        }));

        setUnreadMessages((prev) => ({
          ...prev,
          [message.session_id]: (prev[message.session_id] || 0) + 1, 
        }));
      } else if (message.type === 'new_session') {
        populateSessions(agent);
      } else if (message.type === 'session_dropped' || message.type === 'session_dropped_inactivity' ) {
          populateSessions(agent);
      }
    });
  };

  const populateSessions = (currAgent) => {
    if (!currAgent) return;
    getSessions(currAgent.agent_id, bot.bot_id).then((sessions) => {
      setSessions(sessions);
    }).catch((error) => {
      console.error("Error fetching sessions:", error);
      setSessions([]); 
    });
  };  

  // update session status in the component state
  const updateSessionStatusInState = (sessionId, newStatus) => {
    setSessions((prevSessions) =>
      prevSessions.map((session) =>
        session.session_id === sessionId ? { ...session, status: newStatus } : session
      )
    );
  };

  // reject a session
  const rejectSession = async (sessionId) => {
    if (!agent) return; 
  
    try {
      const response = await rejectChat({
        session_id: sessionId,
        agent_id: agent.agent_id,
        bot_id: bot.bot_id,
      });
  
      if (response) {
        const userId = `internal_live_chat_to_${bot.bot_id}_${selectedSession}`;
        sendToAgent({
          userId,
          message: {
            type: "session_rejected",
            conversation_id: selectedSessionData.queue.conversation_id,
          },
        }).catch((error) => {
          console.error("Error sending message:", error);
        });
        updateSessionStatus({ session_id: sessionId, status: "closed due to agent rejection", bot_id: bot.bot_id}).then(() => {
          updateSessionStatusInState(sessionId, "closed due to agent rejection");
        });
        setSelectedSession(null); 
      } else {
        console.error("Failed to reject session:", response?.error || response);
      }
    } catch (error) {
      console.error("Error rejecting session:", error);
    }
  };
  

  //send an accept msg
  // accept a session
  // const acceptSession = async (sessionId) => {
  //   try {
  //     await updateSessionStatus({ session_id: sessionId, status: "active" });
  //     updateSessionStatusInState(sessionId, "active");
  //   } catch (error) {
  //     console.error("Error accepting session:", error);
  //   }
  // };

const connectionRef = useRef(null);

  useEffect(() => {
    if (agent && !connectionRef.current) {
      connectionRef.current = true;
      signalRNegotiate(bot.bot_id, agent.agent_id);
    }
  }, [agent, bot.bot_id]);

  // fetch agent details on component mount
  useEffect(() => {

    setIsLive(agent.status === 'active');
      populateSessions(agent);
  }, [agent]);

  // add event listener for beforeunload to warn the agent
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (isLive) {
        event.preventDefault();
        event.returnValue = "Are you sure you want to leave? You will be marked as offline.";
        try {
          updateAgentStatus(agent.agent_id, bot.bot_id);
          
        } catch (error) {
          console.error("Error updating agent status to offline and closing sessions:", error);
        }
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isLive]);

  // filter sessions based on status
  const filterSessions = (status: "all" | "unread" | "closed") => {
    let filteredSessions = sessions || []

    switch (status) {
      case "unread":
        filteredSessions = sessions.filter(session => ["pending", "active"].includes(session.status));
        break;
      case "closed":
        filteredSessions = sessions.filter(session => session.status.includes("closed"));
        break;
      case "all":
      default:
        break;
    }

    return Array.isArray(filteredSessions) ? filteredSessions.sort((a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime()) : [];
  };

  // handle agent status toggle (online/offline)
  const handleStatusToggle = async () => {
    if (!agent) return;
    try {
      const updatedAgent = await updateAgentStatus(agent.agent_id, bot.bot_id);
      console.log(updatedAgent);
      set_agent(updatedAgent);
      setIsLive(updatedAgent.status === 'active');
    } catch (error) {
      console.error("Error updating agent status:", error);
    }
  };

  return (
    <div className="grid grid-cols-12 text-white min-h-screen-75">
      <div className="col-span-4 py-4 pr-4">
        
        <SelectTimezone
          name="Custom timezone"
          label="Select Timezone"
          value={selectedTimeZone}
          onChange={({ label, value }) =>{
              setSelectedTimeZone(value)
              updateAgent({ agent_id: agent.agent_id, time_zone: value })
              }
            }
          selectStyles={customStyles}
        />

        <Tabs defaultValue="unread" className="-ml-7 mt-4  flex flex-col">
          <TabsList className="flex justify-between">
            <TabsTrigger value="all" className="w-full text-center">All Chats</TabsTrigger>
            <TabsTrigger value="unread" className="w-full text-center">Ongoing</TabsTrigger>
            <TabsTrigger value="closed" className="w-full text-center">Closed Chats</TabsTrigger>
          </TabsList>
          <Separator />
          <div className="flex items-center mt-4">
            <span className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-500' : 'bg-gray-500'} mr-2`}></span>
            <button 
              onClick={handleStatusToggle} 
              className={`px-4 py-2 rounded-md text-white transition duration-300 ${isLive ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'}`}
            >
              {isLive ? 'Go Offline' : 'Go Live'}
            </button>
          </div>
          <div className="overflow-y-auto mt-4 flex-1">
            <TabsContent value="all">
              <SessionList
                sessions={filterSessions("all")}
                selectedSession={selectedSession}
                setSelectedSession={setSelectedSession}
                unreadMessages={unreadMessages}
                setUnreadMessages={setUnreadMessages}
              />
            </TabsContent>
            <TabsContent value="unread">
              <SessionList
                sessions={filterSessions("unread")}
                selectedSession={selectedSession}
                setSelectedSession={setSelectedSession}
                unreadMessages={unreadMessages}
                setUnreadMessages={setUnreadMessages}
              />
            </TabsContent>
            <TabsContent value="closed">
              <SessionList
                sessions={filterSessions("closed")}
                selectedSession={selectedSession}
                setSelectedSession={setSelectedSession}
                unreadMessages={unreadMessages}
                setUnreadMessages={setUnreadMessages}
              />
            </TabsContent>
          </div>
          {selectedSession && sessions.find(session => session.session_id === selectedSession)?.status === 'pending' && (
            <div className="m-2 p-2 flex gap-5">
              <Button 
                onClick={() => rejectSession(selectedSession)} 
                className="px-4 py-2 rounded-md text-white bg-red-500 hover:bg-red-600"
              >
                Reject Session
              </Button>

              {/* <Button 
                onClick={() => acceptSession(selectedSession)} 
                className="px-4 py-2 rounded-md text-white bg-green-500 hover:bg-green-600"
              >
                Accept Session
              </Button> */}
            </div>
          )}
        </Tabs>
      </div>
      {selectedSession && (
        <div className="w-full h-full col-span-8 pt-4">
          <ChatWindow
            session={sessions.find(session => session.session_id === selectedSession)}
            messages={messagesPerSession[selectedSession] || []}
            setMessages={(messages) => setMessagesPerSession((prev) => ({
              ...prev,
              [selectedSession]: messages,
            }))}
            updateSessionStatusInState={updateSessionStatusInState}
            setSelectedSession={setSelectedSession}
          />
        </div>
      )}
    </div>
  );
};
