import React, { useEffect, useState, useCallback, useRef } from "react";
import { closeChat, getTransactions, updateSessionStatus } from "apis/internalLiveChat.api";
import { sendToAgent } from "apis/messaging.api";
import useBotStore from "store/bot/bot.store";
import { Send, X } from "lucide-react";
import { Button } from "common/ui/button";
import { toast } from "react-hot-toast";

export default function ChatWindow({
  session,
  messages = [],
  setMessages,
  updateSessionStatusInState,
  setSelectedSession,
}) {
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    if (!session) return;

    setMessage("");
    const fetchTransactions = async () => {
      const res = await getTransactions(session.session_id);
      if (res) {
        setMessages(res);
      }
    };

    fetchTransactions();
  }, [session]);

  // scroll to the bottom whenever messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  const bot = useBotStore((state) => state.bot);

  const handleSendMessage = () => {
    if (message.trim() === "") return;
  
    const newMessage = {
      type: "message",
      message: message,
      sender: "agent",
    };
    setMessages([...messages, newMessage]);
    setMessage("");
  
    const userId = `internal_live_chat_to_${bot.bot_id}_${session.session_id}`;
    sendToAgent({
      userId,
      message: {
        type: "message",
        conversation_id: session.queue.conversation_id,
        text: newMessage.message,
      },
    })
      .then((res) => {
        if (session.status === "pending") {
          updateSessionStatus({
            session_id: session.session_id,
            status: "active",
            bot_id: bot.bot_id,
          })
            .then(() => {
              updateSessionStatusInState(session.session_id, "active");
              toast.success("Chat Connected");

            })
            .catch((err) => {
              console.error("Failed to update session status:", err);
            });
        }
      })
      .catch((error) => {
        console.error("Error sending message:", error);
        setMessages(messages);
      });
  };
  

  const handleCloseChat = async () => {
    setLoading(true)
    try {
        await  sendToAgent({
        userId: `internal_live_chat_to_${bot.bot_id}_${session.session_id}`,
        message: {
          type: "session_ended",
          conversation_id: session.queue.conversation_id,
          text: "Chat has been closed",
        },
      });

      const res = await closeChat({
        session_id: session.session_id,
        agent_id: session.agent_id,
        bot_id: bot.bot_id,
      });


      if (res) {
        try {
          await updateSessionStatus({
            session_id: session.session_id,
            status: "closed",
            bot_id: bot.bot_id,
          });
          updateSessionStatusInState(session.session_id, "closed");
          toast.success("chat ended successfully");

        } catch (error) {
          console.error("Failed to update session status:", error);
        }
  
        setMessage("");
        setSelectedSession(null);
      }
    } catch (error) {
      console.error("Error while closing chat:", error);
    } finally {
      setLoading(false)

    }
  };
  

  const handleCloseWindow = useCallback(() => {
    setMessage("");
    setSelectedSession(null);
  }, []);

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleChange = (e) => {
    setMessage(e.target.value);
  };

  return (
    <div className="relative flex h-full min-h-[50vh] max-h-[90vh] flex-col rounded-xl p-4 shadow-lg bg-accent/20 text-gray-200">
      <div className="flex justify-between items-center p-4 rounded-lg mb-4">
        <Button onClick={handleCloseWindow} variant="no_variant" >
          <X className="w-5 h-5 justify-end" />
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto p-4">
        {Array.isArray(messages) && messages.map((msg, index) => (
          <div
            key={index}
            className={`flex mb-2 ${msg.sender === "agent" ? "justify-end" : "justify-start"}`}
          >
            <div
              className={msg.sender === "agent"
                ? 'bg-primary text-white p-3 rounded-t-3xl rounded-bl-3xl rounded-br-sm max-w-[90%] shadow-md'
                : 'bg-gray-700 text-white p-3 rounded-t-3xl rounded-bl-sm rounded-br-3xl max-w-[90%] shadow-md'}
            >
              {msg.message}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      {session?.status.includes('active')  && (
        <div className="flex justify-end mb-4">
 
          <Button
           className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-full min-w-20" 
           title="send-message"
            onClick={handleCloseChat} 
            loading={loading} 
            disabled={session?.status.includes('closed') || loading} 
             > End Session
             </Button>

        </div>
      )}
      {session?.status === "closed due to customer" && (
        <div className="mb-4 text-center flex flex-col items-center">
          <div className="text-red-600 bg-red-100 p-4 rounded-lg shadow-md animate-pulse flex items-center space-x-2">
            <span>User ended this session</span>
          </div>
        </div>
      )}
      <div className="relative flex items-center p-4 border-t border-gray-700">
        <textarea
          id="message"
          placeholder="Type your message"
          className={`w-full h-12 p-3 bg-gray-800 border border-gray-600 rounded-md resize-none text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${session?.status.includes('closed') ? 'cursor-not-allowed' : ''}`}
          onChange={handleChange}
          value={message}
          onKeyDown={handleKeyDown}
          disabled={session?.status.includes('closed')}
        />
        <button
          title="send-message"
          onClick={handleSendMessage}
          className=" text-white p-3 rounded-full ml-2 flex items-center justify-center"
          disabled={session?.status.includes('closed')}
        >
          <Send className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}
