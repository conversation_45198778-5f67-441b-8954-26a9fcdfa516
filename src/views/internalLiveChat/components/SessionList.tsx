import React, { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "lib/utils";
import { Button } from "common/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from "@radix-ui/react-dropdown-menu";
import WhatsappSVG from "common/icons/WhatsappSVG";
import { Facebook, Filter, FilterX, Globe, Mail, Phone, User, Instagram } from "lucide-react";

type Session = {
  session_id: number;
  status: string;
  start_time: Date;
  queue: {
    queue_id: number;
    conversation_id: string;
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
    channel?: string;
  };
};

type Props = {
  sessions: Session[];
  setSelectedSession: React.Dispatch<React.SetStateAction<Session["session_id"] | null>>;
  selectedSession: Session["session_id"] | null;
  unreadMessages: Record<number, number>;
  setUnreadMessages: React.Dispatch<React.SetStateAction<Record<number, number>>>;
};

const channelIcons = {
  whatsapp: <WhatsappSVG color="#34d399" width={30} height={30} />,
  facebook: <Facebook className="h-6 w-6 text-blue-500" />,
  web: <Globe className="h-6 w-6 text-gray-500" />,
  instagram: <Instagram className="h-6 w-6 text-purple-500" />,
};

const statusColors = {
  active: "bg-green-500",
  pending: "bg-yellow-500",
  closed: "bg-red-500",
};

export default function SessionList({
  sessions,
  setSelectedSession,
  selectedSession,
  unreadMessages,
  setUnreadMessages,
}: Props) {
  const [searchInput, setSearchInput] = useState("");
  const [selectedChannel, setSelectedChannel] = useState("");

  const handleSessionSelect = (sessionId: number) => {
    setSelectedSession(sessionId);
    setUnreadMessages((prev) => ({
      ...prev,
      [sessionId]: 0,
    }));
  };

  const handleChannelFilter = (channel: string) => {
    setSelectedChannel(channel);
  };

  const removeFilters = () => {
    setSelectedChannel("");
    setSearchInput("");
  };

  const filteredSessions = sessions.filter((session) => {
    const lowerCaseSearch = searchInput.toLowerCase();
    return (
      (session?.queue?.channel?.toLowerCase() === selectedChannel.toLowerCase() || !selectedChannel) &&
      (
        session?.session_id?.toString().includes(lowerCaseSearch) ||
        session?.status?.toLowerCase().includes(lowerCaseSearch) ||
        session?.queue?.queue_id?.toString().includes(lowerCaseSearch) ||
        session?.queue?.conversation_id?.toLowerCase().includes(lowerCaseSearch) ||
        session?.queue?.customer_name?.toLowerCase().includes(lowerCaseSearch) ||
        session?.queue?.customer_email?.toLowerCase().includes(lowerCaseSearch) ||
        session?.queue?.customer_phone?.includes(lowerCaseSearch) ||
        session?.queue?.channel?.toLowerCase().includes(lowerCaseSearch)
      )
    );
  });

  return (
    <div>
      <div className="relative mt-4 flex items-center">
        <input
          placeholder="Search"
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="pl-3 pr-4 py-2 rounded-md w-full bg-gray-800 text-gray-300 focus:ring-2 focus:ring-purple-800"
        />
        <div className="ml-2 flex">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className="p-2 rounded-md"
                variant="no_variant"
              >
                <Filter />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="ml-2 mt-1 bg-black border-gray-600 text-white shadow-lg w-64 p-4"
            >
              <DropdownMenuSeparator className="bg-gray-600" />
              <DropdownMenuItem
                className="cursor-pointer hover:bg-gray-700 p-2 rounded"
                onClick={() => handleChannelFilter("web")}
              >
                <span>Web</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer hover:bg-gray-700 p-2 rounded"
                onClick={() => handleChannelFilter("facebook")}
              >
                <span>Facebook</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer hover:bg-gray-700 p-2 rounded"
                onClick={() => handleChannelFilter("instagram")}
              >
                <span>Instagram</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer hover:bg-gray-700 p-2 rounded"
                onClick={() => handleChannelFilter("whatsapp")}
              >
                <span>WhatsApp</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-600" />
            </DropdownMenuContent>
          </DropdownMenu>
          {selectedChannel && (
            <Button
              onClick={removeFilters}
              className="ml-2 p-2 rounded-md"
              variant="no_variant"
            >
              <FilterX />
            </Button>
          )}
        </div>
      </div>

      <div className="max-h-96 overflow-y-auto mt-4">
        {filteredSessions.map((session) => {
          const isSelected = session.session_id === selectedSession;
          const unreadMessageCount = unreadMessages[session.session_id] || 0;
          const statusColor = statusColors[session.status.toLowerCase()] || "bg-gray-500";

          return (
            <div
              key={session.session_id}
              className={cn("p-4 mb-2 bg-slate-700/25 rounded-md cursor-pointer hover:bg-slate-700/30", {
                "bg-slate-700": isSelected,
              })}
              onClick={() => handleSessionSelect(session.session_id)}
            >
              <div className="flex justify-between items-center">
                <div className="text-sm flex items-center font-medium">
                  <User className="mr-3" />
                  <span>{session?.queue?.customer_name || "Anonymous"}</span>
                </div>
                {unreadMessageCount > 0 && (
                  <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {unreadMessageCount}
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-400">
                {formatDistanceToNow(new Date(session.start_time), {
                  addSuffix: true,
                })}
              </div>
              <div className="mt-2 flex items-center justify-between text-sm">
                <div
                  className={cn(
                    "px-2 py-1 rounded-full text-xs font-semibold text-white capitalize",
                    statusColor
                  )}
                >
                  {session.status}
                </div>
                <div className="flex flex-col">
                  {session?.queue?.customer_email && (
                    <div className="flex items-center mb-1">
                      <Mail className="h-5 w-5 text-gray-500 mr-2" />
                      <span>{session.queue.customer_email}</span>
                    </div>
                  )}
                  {session?.queue?.customer_phone && (
                    <div className="flex items-center mb-1">
                      <Phone className="h-5 w-5 text-gray-500 mr-2" />
                      <span>{session.queue.customer_phone}</span>
                    </div>
                  )}
                  {session.queue.channel && channelIcons[session.queue.channel.toLowerCase()] && (
                    <div className="flex items-center">
                      {channelIcons[session.queue.channel.toLowerCase()]}
                      <span className="ml-2 capitalize">{session.queue.channel}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
