import { ViewCard } from "common/components/cards";
import { MainPageHeader } from "common/components/headers";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "common/ui/tabs";
import React, { useEffect, useState } from "react";
import useUserPrivilegeStore from "store/priviliges/UserPrivilege.store";
import { IEditorPrivilges } from "store/priviliges/UserPrivilege.types";
import useUserStore from "store/user/user.store";
import views from "views";
import PlansView from "./subviews/plans";
import { BotInfoView } from "./subviews/botInfo";
import { AppearanceView } from "./subviews/appearance";
import { PublishView } from "./subviews/publish";
import { useRouter } from "next/router";
import useBotStore from "store/bot/bot.store";

interface ConfigureViewProps {
  setView: (view: string) => void;
}

export const ConfigureView: React.FC<ConfigureViewProps> = ({ setView }) => {
  const user = useUserStore((state) => state.user);
  const { priviliges, isAdmin } = useUserPrivilegeStore();
  const bot = useBotStore((state) => state.bot);
  const hasEditorAccess = (privilege: keyof IEditorPrivilges | undefined) => {
    return Boolean(priviliges[privilege] || user.user_id === 17 || isAdmin);
  };
  const router = useRouter();

  const [activeTab, setActiveTab] = useState("publish");
  useEffect(() => {
    const { tab } = router.query;
    if (tab) {
      setActiveTab(tab as string);
    }
  }, [router]);

  return (
    <div className="space-y-5">
      <MainPageHeader
        title="Configure your bot"
        description="Configure your bot to your needs and share it to the world."
      />
      <Tabs key={activeTab} defaultValue={activeTab} className="w-full">
        <TabsList
          // onClick={() => {
          //   setActiveTab("botinfo");
          // }}
          className="flex justify-stretch w-full"
        >
          {hasEditorAccess("deployment_privilege") && (
            <TabsTrigger
              onClick={() => {
                setActiveTab("publish");
              }}
              className="grow"
              value="publish"
            >
              Publish
            </TabsTrigger>
          )}
          {hasEditorAccess(undefined) && (
            <TabsTrigger
              onClick={() => {
                setActiveTab("botinfo");
              }}
              className="grow"
              value="botinfo"
            >
              Bot Info
            </TabsTrigger>
          )}
          {hasEditorAccess("appearance_privilege") && (
            <TabsTrigger
              onClick={() => {
                setActiveTab("appearance");
              }}
              className="grow"
              value="appearance"
            >
              Appearance
            </TabsTrigger>
          )}
          {/* {user.user_id !== 269 && bot.bot_id !== 666 && (
            <TabsTrigger
              onClick={() => {
                setActiveTab("plans");
              }}
              className="grow"
              value="plans"
            >
              Plans
            </TabsTrigger>
          )} */}
        </TabsList>
        {/* {user.user_id !== 269 && bot.bot_id !== 666 && (
          <TabsContent value="plans">
            <PlansView />
          </TabsContent>
        )} */}
        {hasEditorAccess(undefined) && (
          <TabsContent value="botinfo">
            <BotInfoView />
          </TabsContent>
        )}
        {hasEditorAccess("appearance_privilege") && (
          <TabsContent value="appearance">
            <AppearanceView />
          </TabsContent>
        )}
        {hasEditorAccess("deployment_privilege") && (
          <TabsContent value="publish">
            <PublishView />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};
