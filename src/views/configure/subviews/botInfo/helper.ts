import constant from "constant";

const isUrlValid = (userInput) => {
  var res = userInput.match(constant.IS_URL_REGEX);
  if (res == null) return false;
  else return true;
};

const preUpdateCards = (cards, businessSettings) => {
  const jsonBody = {
    attachmentLayout: "carousel",
    businessSettings: businessSettings,
    attachments: [
      ...cards.map((a) => {
        return {
          $schema: "https://adaptivecards.io/schemas/adaptive-card.json",
          type: "AdaptiveCard",
          version: "1.1",
          card_type: "business_card",
          body: [...a.body],
          actions: [...a.actions],
        };
      }),
    ],
  };

  return jsonBody;
};

const generatePoliciesDialog = (info, bot) => {
  return {
    info: {
      bot_id: bot.bot_id,
    },
    entities: {},
    steps: [
      {
        name: "step00",
        block_type: "Send Message",
        handler: "promptTextAndContinue",
        handlerParams: [info],
        id: 2,
      },
    ],
  };
};

const generateWorkingHoursCard = (src) => {
  const json_data = {
    body: [
      {
        id: "image",
        type: "Image",
        url: src,
        horizontalAlignment: "Center",
      },
      {
        id: "heading",
        type: "TextBlock",
        horizontalAlignment: "Center",
        text: `Working Hours`,
        weight: "Bolder",
        wrap: true,
      },
    ],
    actions: [],
  };
  return json_data;
};

const generateAddressCard = (info, src, center) => {
  const json_data = {
    body: [
      {
        id: "image",
        type: "Image",
        url: src,
        horizontalAlignment: "Center",
      },
      {
        id: "qrcode",
        type: "Image",
        url: `https://qrcode.tec-it.com/API/QRCode?data=BEGIN%3aVCARD%0d%0aVERSION%3a2.1%0d%0aN%3a${encodeURI(
          info.company_name
        )}%0d%0aTEL%3bWORK%3bVOICE%3a${info.phone}%0d%0aORG%3a${
          info.company_name
        }%0d%0aURL%3a${info.website}%0d%0aEMAIL%3a${
          info.email
        }%0d%0aEND%3aVCARD&backcolor=%23ffffff`,
        horizontalAlignment: "Center",
      },
      {
        id: "heading",
        type: "TextBlock",
        horizontalAlignment: "Center",
        text: `${info.continent ? info.continent + "," : ""} ${
          info.country ? info.country + "," : ""
        } ${info.city ? info.city + "," : ""} ${
          info.suburb ? info.suburb + "," : ""
        } ${info.street ? "street: " + info.street + "," : ""} ${
          info.building_number ? "Building Number: " + info.building_number : ""
        }`,
        weight: "Bolder",
        wrap: true,
      },
      {
        id: "subheading",
        type: "TextBlock",
        horizontalAlignment: "Center",
        text: `${info.additional_details ? info.additional_details : ""}`,
        wrap: true,
      },
    ],
    actions: [],
  };

  json_data.actions.push({
    type: "Action.OpenUrl",
    title: "Open Google Maps",
    url: `https://www.google.com/maps/search/?api=1&query=${center.lat},${center.lng}`,
    iconUrl:
      "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/1200px-Google_Maps_icon_%282020%29.svg.png",
  });

  if (Boolean(info.facebook) && isUrlValid(info.facebook)) {
    json_data.actions.push({
      type: "Action.OpenUrl",
      title: "Facebook",
      url: info.facebook,
      iconUrl:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/Facebook_Logo_(2019).png",
    });
  }
  if (Boolean(info.instegram) && isUrlValid(info.instegram)) {
    json_data.actions.push({
      type: "Action.OpenUrl",
      title: "Instagram",
      url: info.instegram,
      iconUrl:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/Glossy-Instagram-logo-PNG.png",
    });
  }
  if (Boolean(info.twitter) && isUrlValid(info.twitter)) {
    json_data.actions.push({
      type: "Action.OpenUrl",
      title: "Twitter",
      url: info.twitter,
      iconUrl:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/logo-twitter-11549535419aik8i3pkro.png",
    });
  }
  if (Boolean(info.website) && isUrlValid(info.website)) {
    json_data.actions.push({
      type: "Action.OpenUrl",
      title: "Website",
      url: info.website,
      iconUrl:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/web-vector-icon-png_253122.jpeg",
    });
  }
  return json_data;
};

const initBusinessSettings = () => {
  return {
    additional_details: null,
    additional_phone: null,
    bot_id: 0,
    bot_info_id: 0,
    building_number: null,
    city: "",
    company_name: null,
    continent: "",
    country: "",
    country_code: "",
    createdAt: "",
    email: null,
    facebook: null,
    instegram: null,
    latitude: 0,
    longitude: 0,
    phone: null,
    privacy_policy: null,
    replacement_policy: null,
    street: null,
    suburb: null,
    terms_conditions: null,
    timezone: null,
    twitter: null,
    updatedAt: "",
    website: null,
  };
};

const extractTimeFromDate = (date) => {
  const timeString = date.toTimeString().split(" ")[0].split(":");
  return `${timeString[0]}:${timeString[1]}`;
};

function dataURItoBlob(dataURI) {
  // convert base64/URLEncoded data component to raw binary data held in a string
  var byteString;
  if (dataURI.split(",")[0].indexOf("base64") >= 0)
    byteString = atob(dataURI.split(",")[1]);
  else byteString = unescape(dataURI.split(",")[1]);

  // separate out the mime component
  var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];

  // write the bytes of the string to a typed array
  var ia = new Uint8Array(byteString.length);
  for (var i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }

  return new Blob([ia], { type: mimeString });
}

export default {
  generatePoliciesDialog,
  generateWorkingHoursCard,
  generateAddressCard,
  initBusinessSettings,
  preUpdateCards,
  isUrlValid,
  extractTimeFromDate,
  dataURItoBlob,
};
