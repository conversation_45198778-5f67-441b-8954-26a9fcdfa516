import { Card } from "common/ui/card";
import React, { useEffect, useState } from "react";
import {
  getOne as getWorkingHours,
  createOne as createWorkingHours,
} from "apis/workingHours.api";
import useBotStore from "store/bot/bot.store";
import constant from "constant";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";
import helper from "../helper";
import html2canvas from "html2canvas";
import { uploadBotImages, uploadFile } from "apis/file.api";
import { Button } from "common/ui/button";
import { Loader2, Save } from "lucide-react";

export const WorkingHours = () => {
  const bot = useBotStore((state) => state.bot);

  const [workingHoursToChange, setWorkingHoursToChange] = useState([]);

  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    getWorkingHours(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setWorkingHoursToChange([...data]);
      }
    });
  }, []);

  const onChangeWorkingHours = (day, entity, value) => {
    const working_hours_upd = [...workingHoursToChange];
    var valueToSet = new Date().setHours(value.split(":")[0]);
    valueToSet = new Date(valueToSet).setMinutes(value.split(":")[1]);
    working_hours_upd.map((a) => {
      if (a.day === day) {
        a[entity] = new Date(valueToSet).toISOString();
      }
    });
    setWorkingHoursToChange([...working_hours_upd]);
  };
  const onRemoveDay = (day) => {
    const working_hours_upd = [...workingHoursToChange];
    setWorkingHoursToChange([
      ...working_hours_upd.filter((a) => a.day !== day),
    ]);
  };
  const onAddDay = (day) => {
    const now = new Date();
    now.setHours(8, 0, 0, 0);
    const from = now.toISOString();
    now.setHours(17, 0, 0, 0);
    const to = now.toISOString();

    const working_hours_upd = [...workingHoursToChange];
    working_hours_upd.push({
      day: day,
      from: from,
      to: to,
    });
    setWorkingHoursToChange([...working_hours_upd]);
  };

  const onSave = async () => {
    setSaveLoading(true);
    const input = document.getElementById("business-hours-id");
    const canvas = await html2canvas(input);
    const imgData = canvas.toDataURL("image/png");
    var blob = helper.dataURItoBlob(imgData);
    var fd = new FormData(document.forms[0]);
    fd.append("file", blob);

    await uploadBotImages({
      path: `Bots/${bot.file_name}/cardsImages/workinghoursimage.png`,
      formData: fd,
    });
    await uploadFile({
      path: `Bots/${bot.file_name}/CardsConfig/workinghourscard.json`,
      file_body: JSON.stringify({
        ...helper.preUpdateCards(
          [
            {
              ...helper.generateWorkingHoursCard(
                "https://infotointell.fra1.digitaloceanspaces.com/" +
                  `Bots/${bot.file_name}/cardsImages/workinghoursimage.png`
              ),
            },
          ],
          {}
        ),
      }),
    });
    await createWorkingHours({
      bot_id: bot.bot_id,
      workinghours: [...workingHoursToChange],
    });
    setSaveLoading(false);
  };



  return (
    <Card title="Working Hours" hr>
      <div className="grid grid-cols-2 gap-2">
        {constant.DAYS.map((day, i) => {
          return (
            <div
              key={i}
              className="grid grid-cols-3 gap-1 content-center bg-secondary/50 p-3 rounded hover:bg-secondary/75"
            >
              <Checkbox
                name={day.name}
                label={day.name}
                checked={
                  workingHoursToChange.length &&
                  workingHoursToChange.find((a) => a.day === day.value)
                }
                onChange={(e) => {
                  if (e.target.checked) {
                    onAddDay(day.value);
                  } else {
                    onRemoveDay(day.value);
                  }
                }}
              />
              <Input
                type="time"
                name="from"
                title="From"
                onChange={(e) =>
                  onChangeWorkingHours(day.value, "from", e.target.value)
                }
                disabled={
                  !(
                    workingHoursToChange.length &&
                    workingHoursToChange.find((a) => a.day === day.value)
                  )
                }
                value={
                  workingHoursToChange.find((a) => a.day === day.value)
                    ? helper.extractTimeFromDate(
                        new Date(
                          workingHoursToChange.find(
                            (a) => a.day === day.value
                          ).from
                        )
                      )
                    : ""
                }
              />
              <Input
                type="time"
                name="to"
                title="To"
                onChange={(e) =>
                  onChangeWorkingHours(day.value, "to", e.target.value)
                }
                disabled={
                  !(
                    workingHoursToChange.length &&
                    workingHoursToChange.find((a) => a.day === day.value)
                  )
                }
                value={
                  workingHoursToChange.find((a) => a.day === day.value)
                    ? helper.extractTimeFromDate(
                        new Date(
                          workingHoursToChange.find(
                            (a) => a.day === day.value
                          ).to
                        )
                      )
                    : ""
                }
              />
            </div>
          );
        })}
      </div>
      <hr />
      <div className="flex justify-between">
        <div id="business-hours-id" className="p-5 w-1/3">
          <div className="text-lg font-semibold py-2 border-l-4 border-primary pl-1">
            Opening Hours
          </div>
          <div className="flex flex-col gap-2 mt-1">
            {constant.DAYS.map((day, i) => {
              return (
                <div key={i} className="grid grid-cols-2 gap-2 border-b pb-1">
                  <div className="text-sm font-semibold">{day.name}</div>

                  {workingHoursToChange.length &&
                  workingHoursToChange.find((a) => a.day === day.value) ? (
                    <div className="text-sm font-semibold">
                      {helper.extractTimeFromDate(
                        new Date(
                          workingHoursToChange.find(
                            (a) => a.day === day.value
                          ).from
                        )
                      )}{" "}
                      -{" "}
                      {helper.extractTimeFromDate(
                        new Date(
                          workingHoursToChange.find(
                            (a) => a.day === day.value
                          ).to
                        )
                      )}
                    </div>
                  ) : (
                    <div className="text-red-500 text-sm font-semibold">
                      Closed
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
        <Button onClick={onSave} loading={saveLoading} className="self-end">    
            <Save size={16} className="mr-2" />
          Save
        </Button>
      </div>
    </Card>
  );
};
