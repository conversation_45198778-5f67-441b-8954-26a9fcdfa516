import { restartOne } from "apis/bot.api";
import { But<PERSON> } from "common/ui/button";
import { Card } from "common/ui/card";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import constant from "constant";
import checkForErrors from "helpers/forms";
import { Loader2, ShieldClose } from "lucide-react";
import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import { z } from "zod";

export const BasicInfo = () => {
  const { bot, bots, update_one_bot, get_all_bots, get_one_bot } =
    useBotStore();
  const { user } = useUserStore();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>();

  const [updBot, setUpdBot] = useState({ ...bot });
  useEffect(() => {
    setUpdBot({ ...bot });
  }, [bot]);

  const UpdateBotSchema = z.object({
    // user_id: z.number(),
    bot_name: z
      .string()
      .min(1, "Name is required")
      .regex(constant.IS_ENG_REGEX, "Only English is Allowed, No Spaces")
      .refine(
        (value) =>
          !bots
            .filter((a) => a.bot_id !== bot.bot_id)
            .find((a) => a.bot_name === value),
        {
          message: "Bot name is reserved, Try another one",
        }
      )
      .refine((value) => Boolean(value.trim()), {
        message: "This Field Cannot Be Blank.",
      }),
    description: z.string(),
    language: z.string().min(1, "Language is required"),
  });

  const validateField =
    (field: keyof z.infer<typeof UpdateBotSchema>) =>
    (value: unknown): string => {
      const parsedResult = UpdateBotSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setUpdBot({
      ...updBot,
      [key]: value,
    });
  };

  const onSubmit = async () => {
    // check if there is any changes
    if (JSON.stringify(bot) === JSON.stringify(updBot)) {
      return;
    }

    const isErrors = checkForErrors(
      {
        zodSchema: UpdateBotSchema,
        data: updBot,
      },
      setErrors
    );

    if (isErrors) return;

    setLoading(true);
    await update_one_bot({ user_id: user.user_id, ...updBot });
    setTimeout(() => {
      restartOne({
        bot_id: bot.bot_id,
      });
      get_one_bot(bot.bot_id);
      get_all_bots(user.user_id);
      toast.success("Updated Successfully");
      setLoading(false);
    }, 2000);
  };

  return (
    <Card title="Basic Info" hr>
      <div className="flex flex-col gap-3">
        <Input
          title="Bot Name"
          subtitle="Only English is Allowed, No Spaces"
          type="text"
          name="bot_name"
          value={updBot.bot_name}
          onChange={(event) => {
            onChangeHandler("bot_name", event.target.value);
          }}
          error={errors?.bot_name}
        />
        <div className="space-y-1 relative">
          <label className="block" htmlFor="">
            Bot Description
          </label>
          <Textarea
            placeholder="Describe your bot!"
            value={updBot.description}
            onChange={(event) => {
              onChangeHandler("description", event.target.value);
            }}
          />
          {errors?.description && (
            <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
              {errors?.description ? (
                <>
                  <ShieldClose size={12} />
                  {errors?.description}
                </>
              ) : null}
            </span>
          )}
        </div>
        <div className="space-y-1">
          <label className="block" htmlFor="">
            Bot Language
          </label>

          <fieldset className="flex flex-wrap gap-3">
            <legend className="sr-only">Language</legend>

            <div>
              <input
                type="radio"
                name="Language"
                value="ar"
                id="ar"
                className="peer hidden"
                checked={updBot.language === "ar"}
                onChange={() => onChangeHandler("language", "ar")}
              />

              <label
                htmlFor="ar"
                className="flex cursor-pointer items-center justify-center rounded-md border border-gray-100 py-2 px-3 text-white hover:border-gray-200 peer-checked:border-black peer-checked:bg-white peer-checked:text-black"
              >
                <p className="text-sm font-medium">Arabic</p>
              </label>
            </div>

            <div>
              <input
                type="radio"
                name="Language"
                value="en"
                id="en"
                className="peer hidden"
                checked={updBot.language === "en"}
                onChange={() => onChangeHandler("language", "en")}
              />

              <label
                htmlFor="en"
                className="flex cursor-pointer items-center justify-center rounded-md border border-gray-100 py-2 px-3 text-white hover:border-gray-200 peer-checked:border-black peer-checked:bg-white peer-checked:text-black"
              >
                <p className="text-sm font-medium">English</p>
              </label>
            </div>
            <div className="relative group">
              <input
                type="radio"
                name="Language"
                value="both"
                id="both"
                className="peer hidden"
                checked={updBot.language === "both"}
                onChange={() => onChangeHandler("language", "both")}
              />
              <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-themeSecondary text-white text-xs rounded py-1 px-2 group-hover:block">
                Prompt user for preferred language.
              </div>
              <label
                htmlFor="both"
                className="flex cursor-pointer items-center justify-center rounded-md border border-gray-100 py-2 px-3 text-white hover:border-gray-200 peer-checked:border-black peer-checked:bg-white peer-checked:text-black"
              >
                <p className="text-sm font-medium">User Choice</p>
              </label>
            </div>

            <div className="relative group">
              <input
                type="radio"
                name="Language"
                value="webDefault"
                id="webDefault"
                className="peer hidden"
                checked={updBot.language === "webDefault"}
                onChange={() => onChangeHandler("language", "webDefault")}
              />
              <div className="absolute left-0 bottom-full mb-2 hidden w-max bg-themeSecondary text-white text-xs rounded py-1 px-2 group-hover:block">
                Start dialogs with website&apos;s default language
              </div>
              <label
                htmlFor="webDefault"
                className="flex cursor-pointer items-center justify-center rounded-md border border-gray-100 py-2 px-3 text-white hover:border-gray-200 peer-checked:border-black peer-checked:bg-white peer-checked:text-black"
              >
                <p className="text-sm font-medium"> Web Default </p>
              </label>
            </div>
          </fieldset>
        </div>
        <Button
          disabled={JSON.stringify(bot) === JSON.stringify(updBot)}
          onClick={onSubmit}
          className="self-end"
          loading={loading}
        >
          Save
        </Button>
      </div>
    </Card>
  );
};
