import {
  geocodingLocation,
  getOne,
  getUserLocation,
  getUserLocationImage,
  updateOne,
} from "apis/address.api";
import { Card } from "common/ui/card";
import { Input } from "common/ui/inputs/input";
import React, { useEffect, useRef, useState } from "react";
import useBotStore from "store/bot/bot.store";
import helper from "../helper";
import { GoogleMap, useJsApi<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@react-google-maps/api";
import constant from "constant";
import { Button } from "common/ui/button";
import { Loader2, LocateFixed, Save } from "lucide-react";
import { PhoneInput } from "common/ui/inputs/phoneInput";
import { cleanPhoneNumber } from "helpers/phone";
import { Textarea } from "common/ui/inputs/textarea";
import { findTZ } from "helpers/date";
import { uploadFile } from "apis/file.api";
import { z } from "zod";
import checkForErrors from "helpers/forms";

export const BusinessInfo = () => {
  const bot = useBotStore((state) => state.bot);
  const [businessSettings, setBusinessSettings] = useState(
    helper.initBusinessSettings()
  );
  useEffect(() => {
    getOne(bot.bot_id).then((data) => {
      if (data && !data.message) {
        getUserLocation().then((location_data) => {
          // setSaveLoading(false);
          if (location_data) {
            setBusinessSettings({
              ...data,
              continent: Boolean(data.continent)
                ? data.continent
                : location_data.continent_name,
              country: Boolean(data.country)
                ? data.country
                : location_data.country_name,
              city: Boolean(data.city) ? data.city : location_data.city,
              longitude: Boolean(data.longitude)
                ? data.longitude
                : location_data.longitude,
              latitude: Boolean(data.latitude)
                ? data.latitude
                : location_data.latitude,
              country_code: location_data.country_code,
            });
            setCenter({
              lng: Boolean(data.longitude)
                ? parseFloat(data.longitude)
                : parseFloat(location_data.longitude),
              lat: Boolean(data.latitude)
                ? parseFloat(data.latitude)
                : parseFloat(location_data.latitude),
            });
          }
        });
      }
    });
  }, []);

  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: constant.GOOGLE_MAP_KEY,
  });

  const mapRef = useRef(null);
  const [mapLoading, setMapLoading] = useState(false);
  const [map, setMap] = useState(null);
  const [center, setCenter] = useState({
    lat: 0,
    lng: 0,
  });
  const [zoom, setZoom] = useState(16);
  const [showMarker, setShowMarker] = useState(true);

  const [errors, setErrors] = useState<Record<string, string>>();

  const infoSchema = z.object({
    facebook: z
      .string()
      .regex(/^$|^https?:\/\/.+/, "Invalid URL")
      .nullable()
      .optional(),
    instegram: z
      .string()
      .regex(/^$|^https?:\/\/.+/, "Invalid URL")
      .nullable()
      .optional(),
    twitter: z
      .string()
      .regex(/^$|^https?:\/\/.+/, "Invalid URL")
      .nullable()
      .optional(),
    website: z
      .string()
      .regex(/^$|^https?:\/\/.+/, "Invalid URL")
      .nullable()
      .optional(),
  });

  const validateField =
    (field: keyof z.infer<typeof infoSchema>) =>
    (value: unknown): string => {
      const parsedResult = infoSchema.pick({ [field]: true } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onLoad = React.useCallback(function callback(map) {
    window.google.maps.event.addListener(map, "click", function (event) {
      setCenter({
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      });
      setShowMarker(false);
      setTimeout(() => setShowMarker(true), 200);
    });
  }, []);

  const onUnmount = React.useCallback(function callback(map) {
    setMap(null);
  }, []);

  const [saveLoading, setSaveLoading] = useState(false);

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setBusinessSettings({ ...businessSettings, [key]: value });
  };

  const onSaveHandler = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: infoSchema,
        data: businessSettings,
      },
      setErrors
    );

    if (isErrors) return;

    setSaveLoading(true);
    await updateOne({
      ...businessSettings,
      longitude: center.lng,
      latitude: center.lat,
      bot_id: bot.bot_id,
      timezone: findTZ(),
    });
    setSaveLoading(false);

    await uploadFile({
      path: `Bots/${bot.file_name}/Dialogs/privacy_policy_dialog.json`,
      file_body: JSON.stringify({
        ...helper.generatePoliciesDialog(businessSettings.privacy_policy, bot),
      }),
    });

    await uploadFile({
      path: `Bots/${bot.file_name}/Dialogs/terms_conditions_dialog.json`,
      file_body: JSON.stringify({
        ...helper.generatePoliciesDialog(
          businessSettings.terms_conditions,
          bot
        ),
      }),
    });

    await uploadFile({
      path: `Bots/${bot.file_name}/Dialogs/trading_policy_dialog.json`,
      file_body: JSON.stringify({
        ...helper.generatePoliciesDialog(
          businessSettings.replacement_policy,
          bot
        ),
      }),
    });

    const data = await getUserLocationImage({
      bot_id: bot.bot_id,
      longitude: center.lng,
      latitude: center.lat,
      country_code: businessSettings.country_code,
    });
    if (data) {
      await uploadFile({
        path: `Bots/${bot.file_name}/CardsConfig/address_card.json`,
        file_body: JSON.stringify({
          ...helper.preUpdateCards(
            [
              {
                ...helper.generateAddressCard(
                  businessSettings,
                  data.src,
                  center
                ),
              },
            ],
            businessSettings
          ),
        }),
      });
    }
  };

  return (
    <Card title="Business Info" hr>
      <div className="flex flex-col gap-3">
        <div className="space-y-3  grid grid-cols-2 gap-5">
          <div className="grid grid-cols-2 gap-3">
            <Input
              title="Company Name"
              name="company_name"
              value={businessSettings?.company_name}
              onChange={(e) => onChangeHandler("company_name", e.target.value)}
            />
            <Input
              title="Continent"
              name="continent"
              value={businessSettings?.continent}
              onChange={(e) => onChangeHandler("continent", e.target.value)}
            />
            <Input
              title="Country"
              name="country"
              value={businessSettings?.country}
              onChange={(e) => onChangeHandler("country", e.target.value)}
            />
            <Input
              title="City"
              name="city"
              value={businessSettings?.city}
              onChange={(e) => onChangeHandler("city", e.target.value)}
            />
            <Input
              title="Suburb"
              name="suburb"
              value={businessSettings?.suburb}
              onChange={(e) => onChangeHandler("suburb", e.target.value)}
            />
            <Input
              title="Street"
              name="street"
              value={businessSettings?.street}
              onChange={(e) => onChangeHandler("street", e.target.value)}
            />
            <Input
              title="Building Number"
              name="building_number"
              value={businessSettings?.building_number}
              onChange={(e) =>
                onChangeHandler("building_number", e.target.value)
              }
            />
            <Input
              title="Additional Details"
              name="additional_details"
              value={businessSettings?.additional_details}
              onChange={(e) =>
                onChangeHandler("additional_details", e.target.value)
              }
            />
            <Button
              onClick={() => {
                setMapLoading(true);
                geocodingLocation(
                  `${businessSettings.country} ${businessSettings.city} ${businessSettings.suburb}  ${businessSettings.company_name}`
                ).then((data) => {
                  setMapLoading(false);
                  if (data && data?.results?.length) {
                    setCenter({
                      lng: data?.results[0]?.geometry?.location?.lng,
                      lat: data?.results[0]?.geometry?.location?.lat,
                    });
                  }
                });
              }}
            >
              {mapLoading ? (
                <>
                  <Loader2 size={16} className="mr-2" />
                  Locating
                </>
              ) : (
                <>
                  {" "}
                  <LocateFixed size={16} className="mr-2" /> Locate Me
                </>
              )}
            </Button>
          </div>
          {isLoaded && center ? (
            <GoogleMap
              center={center}
              zoom={zoom}
              onLoad={onLoad}
              onUnmount={onUnmount}
              ref={mapRef}
              id="google-map"
            >
              {showMarker ? <Marker position={center} /> : null}
            </GoogleMap>
          ) : null}
        </div>
        <hr />
        <div className="grid grid-cols-3 gap-5">
          <div className="space-y-3">
            <Input
              title="Facebook"
              name="facebook"
              value={
                businessSettings?.facebook ? businessSettings?.facebook : ""
              }
              onChange={(e) => onChangeHandler("facebook", e.target.value)}
              error={errors?.facebook}
            />
            <Input
              title="Instagram"
              name="instagram"
              value={
                businessSettings?.instegram ? businessSettings?.instegram : ""
              }
              onChange={(e) => onChangeHandler("instegram", e.target.value)}
              error={errors?.instegram}
            />
            <Input
              title="Twitter"
              name="twitter"
              value={businessSettings?.twitter ? businessSettings?.twitter : ""}
              onChange={(e) => onChangeHandler("twitter", e.target.value)}
              error={errors?.twitter}
            />
            <Input
              title="Website"
              name="website"
              value={businessSettings?.website ? businessSettings?.website : ""}
              onChange={(e) => onChangeHandler("website", e.target.value)}
              error={errors?.website}
            />
          </div>
          <div className="space-y-3">
            {" "}
            <div className="space-y-1 relative">
              <label className="block" htmlFor="">
                Privacy Policy
              </label>
              <Textarea
                placeholder="Privacy Policy"
                value={businessSettings?.privacy_policy}
                onChange={(e) =>
                  onChangeHandler("privacy_policy", e.target.value)
                }
              />
            </div>
            <div className="space-y-1 relative">
              <label className="block" htmlFor="">
                Terms & Conditions
              </label>
              <Textarea
                placeholder="Terms & Conditions"
                value={businessSettings?.terms_conditions}
                onChange={(e) =>
                  onChangeHandler("terms_conditions", e.target.value)
                }
              />
            </div>
            <div className="space-y-1 relative">
              <label className="block" htmlFor="">
                Trading Policy
              </label>
              <Textarea
                placeholder="Trading Policy"
                value={businessSettings?.replacement_policy}
                onChange={(e) =>
                  onChangeHandler("replacement_policy", e.target.value)
                }
              />
            </div>
          </div>
          <div className="space-y-3">
            <PhoneInput
              value={
                businessSettings?.phone
                  ? cleanPhoneNumber(businessSettings?.phone)
                  : ""
              }
              onChange={(value) => onChangeHandler("phone", value)}
            />
            <PhoneInput
              value={
                businessSettings?.additional_phone
                  ? cleanPhoneNumber(businessSettings?.additional_phone)
                  : ""
              }
              onChange={(value) => onChangeHandler("additional_phone", value)}
            />
            <Input
              title="Email"
              name="email"
              value={businessSettings?.email}
              onChange={(e) => onChangeHandler("email", e.target.value)}
            />
          </div>
        </div>
        <hr />
        <Button
          onClick={onSaveHandler}
          loading={saveLoading}
          className="self-end"
        >
          <Save size={16} className="mr-2" />
          Save
        </Button>
      </div>
    </Card>
  );
};
