import { getAll } from "apis/plans.api";
import { getAll as getAllPlanFunctions } from "apis/planFunctions.api";
import { CheckCircle2, XCircle } from "lucide-react";
import { FC, memo, useEffect, useState } from "react";
import PlanFunctionItem from "./planFunctionItem";
import { PlanCard } from ".";
import CustomPlanCard from "./customPlanCard";

interface PlansProps {
  current_plan_id?: number;
  selectedPlan?: number;
  setSelectedPlan?: (plan: number) => void;
}

const Plans: FC<PlansProps> = ({
  current_plan_id,
  selectedPlan,
  setSelectedPlan,
}) => {
  const [plans, setPlans] = useState([]);
  const [promotion, setPromotion] = useState(undefined);

  useEffect(() => {
    const promotion_data = localStorage.getItem("promotion");
    if (promotion_data) {
      setPromotion(JSON.parse(promotion_data));
    }
    getAll().then((data) => {
      getAllPlanFunctions().then((planFunctionsData) => {
        if (planFunctionsData && data) {
          const results = [];
          data?.forEach((a) => {
            results.push({
              ...a,
              ...planFunctionsData.find((b) => b.plan_id === a.plan_id),
            });
          });
          setPlans(results);
        }
      });
    });
  }, []);
  return current_plan_id ? (
    <>
      {plans
        ?.filter((p) => p.plan_id !== 4)
        .map((plan, i) => (
          <PlanCard key={i} plan={plan} currentPlanId={current_plan_id} />
        ))}

      {plans?.find((p) => p.plan_id === 4) && (
        <CustomPlanCard plan={plans?.find((p) => p.plan_id === 4)} />
      )}
    </>
  ) : (
    <div className="grid grid-cols-4 gap-5">
      {plans
        ?.filter((p) => p.plan_id !== 4)
        .map((plan, i) => (
          <div className="relative" key={i}>
            <input
              type="radio"
              id={plan.plan_name}
              name="plan"
              value={plan.plan_id}
             checked={+selectedPlan === +plan.plan_id}
                onChange={() => setSelectedPlan(plan.plan_id)}
              className="peer hidden "
            />

<CheckCircle2 className="absolute top-2 right-2 text-primary hidden peer-checked:block" />
            <label
              htmlFor={plan.plan_name}
              className="flex w-full h-24 cursor-pointer items-center  gap-2 rounded-md border-2 border-white/25 py-1 px-3 text-white hover:border-primary hover:backdrop-brightness-200 peer-checked:border-primary  peer-checked:text-white "
            >
              <div className="w-full">
                <h4 className="text-3xl font-extrabold"> {plan.plan_name} </h4>
                <p className="text-sm font-bold tracking-wider uppercase">
                  {plan.plan_description}
                </p>
                <h6>
                  {" "}
                  {plan.plan_price}
                  {plan.currency} / month{" "}
                </h6>
              </div>
            </label>
          </div>
        ))}
      <hr className="col-span-full" />
      {plans?.find((p) => p.plan_id === 4) && (
        <div className="col-span-full scale-90">
          <CustomPlanCard plan={plans?.find((p) => p.plan_id === 4)} />
        </div>
      )}
    </div>
  );
};

export default memo(Plans);
