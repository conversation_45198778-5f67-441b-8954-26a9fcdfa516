import { FC, memo } from "react";
import { IPlan } from "types/plans.types";
import PlanFunctionItem from "./planFunctionItem";
import { Button } from "common/ui/button";
import useContactModal from "views/contact/hooks/useContactModal";

interface CustomPlanCardProps {
  plan: IPlan;
}

const CustomPlanCard: FC<CustomPlanCardProps> = ({ plan }) => {
  const contactModal = useContactModal();

  return (
    <div className="flex flex-col col-span-full justify-between p-5 bg-secondary text-white border rounded shadow-sm">
      <div className="mb-6">
        <div className="flex w-full items-center text-center justify-between pb-6 mb-6 border-b">
          <div className="w-full">
            <h4 className="text-3xl font-extrabold"> {plan.plan_name} </h4>
            <p className="text-sm font-bold tracking-wider uppercase">
              {plan.plan_description}
            </p>
            <h6>Call us</h6>
          </div>
        </div>
        <div>
          If you want to build a specialized chatbot for your business with many
          special features, we can help you to get an advanced and distinctive
          bot to serve your customers and employees at the same time.
          <br />
          <br />
          Contact us to find out more about the special features that we can
          offer you.
        </div>
      </div>
      <Button
        onClick={() => contactModal.onOpen()}
        variant="outline"
        className="inline-flex items-center justify-center w-full h-12 px-6 mb-4 text-md font-medium"
      >
        Contact us
      </Button>
    </div>
  );
};

export default memo(CustomPlanCard);
