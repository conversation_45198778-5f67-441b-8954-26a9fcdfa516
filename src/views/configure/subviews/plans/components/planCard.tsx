import { FC, memo, useState } from "react";
import PlanFunctionItem from "./planFunctionItem";
import { IPlan } from "types/plans.types";
import { Button } from "common/ui/button";
import useBotStore from "store/bot/bot.store";
import { updateOne } from "apis/bot.api";
import useUserStore from "store/user/user.store";
import { Loader2 } from "lucide-react";
import { toast } from "react-hot-toast";

interface PlanCardProps {
  plan: IPlan;
  currentPlanId?: number;
}

const PlanCard: FC<PlanCardProps> = ({ plan, currentPlanId }) => {
  const { get_one_bot, bot } = useBotStore();
  const user = useUserStore((state) => state.user);

  const [loading, setLoading] = useState(false);

  return (
    <div
      className={`flex flex-col justify-between p-5 bg-secondary text-white border rounded shadow-xl shadow-white/10 ${
        plan.plan_id === currentPlanId ? "border-primary" : ""
      } `}
    >
      <div className="mb-6">
        <div className="flex w-full items-center text-center justify-between pb-6 mb-6 border-b">
          <div className="w-full">
            <h4 className="text-3xl font-extrabold"> {plan.plan_name} </h4>
            <p className="text-sm font-bold tracking-wider uppercase">
              {plan.plan_description}
            </p>
            <h6>
              {" "}
              {plan.plan_price}
              {plan.currency} / month{" "}
            </h6>
          </div>
        </div>
        <div>
          <ul className="space-y-2">
            <PlanFunctionItem
              available={true}
              label={`${plan.max_faqs ? plan.max_faqs : "Unlimited"} FAQ's`}
            />
            <PlanFunctionItem
              available={true}
              label={`${plan.max_items ? plan.max_items : "Unlimited"} Items`}
            />
            <PlanFunctionItem
              available={plan.facebook_channel}
              label="Facebook channel"
            />
            <PlanFunctionItem available={plan.web_channel} label="Web Widget" />
            <PlanFunctionItem
              available={plan.transaction_dashboard}
              label="Dashboard"
            />
            <PlanFunctionItem
              available={plan.whatsapp_channel}
              label="Whatsapp"
            />
            <PlanFunctionItem
              available={plan.instegram_channel}
              label="Instagram"
            />
            <PlanFunctionItem
              available={plan.web_designer}
              label="Bot Design (UI)"
            />
          </ul>
        </div>
      </div>
      {!currentPlanId
        ? null
        : (plan.plan_order > currentPlanId + 1 || currentPlanId === 5) &&
          plan.plan_id !== currentPlanId && (
            <Button
              variant="outline"
              className="inline-flex items-center justify-center w-full h-12 px-6 mb-4 text-md font-medium"
              onClick={() => {
                setLoading(true);
                updateOne({
                  user_id: user.user_id,
                  ...bot,
                  plan_id: plan.plan_id,
                }).then((res) => {
                  get_one_bot(bot.bot_id);
                  setLoading(false);
                  if (!res.message) {
                    toast.success("Plan Changed Successfully");
                  }
                });
              }}
              disabled={loading}
            >
              {loading && <Loader2 className="mr-2" size={20} />}
              Upgrade Plan
            </Button>
          )}
      {plan.plan_order < currentPlanId + 1 &&
        plan.plan_id !== currentPlanId &&
        currentPlanId !== 5 && (
          <Button
            variant="outline"
            className="inline-flex items-center justify-center w-full h-12 px-6 mb-4 text-md font-medium"
            onClick={() => {
              setLoading(true);
              updateOne({
                user_id: user.user_id,
                ...bot,
                plan_id: plan.plan_id,
              }).then((res) => {
                get_one_bot(bot.bot_id);
                setLoading(false);
                if (!res.message) {
                  toast.success("Plan Changed Successfully");
                }
              });
            }}
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2" size={20} />}
            Downgrade Plan
          </Button>
        )}
      {plan.plan_id === currentPlanId && (
        <div className="inline-flex items-center justify-center w-full h-12 px-6 mb-4 font-medium tracking-wide text-white transition duration-200 bg-primary rounded shadow-md">
          Current Plan
        </div>
      )}
    </div>
  );
};

export default memo(PlanCard);
