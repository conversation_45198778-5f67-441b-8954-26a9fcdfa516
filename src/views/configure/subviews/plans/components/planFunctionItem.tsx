import { CheckCircle2, XCircle } from "lucide-react";
import { FC, memo } from "react";

interface planFunctionItemProps {
  available: boolean;
  label: string;
}

const planFunctionItem: FC<planFunctionItemProps> = ({ available, label }) => {
  return (
    <li
      className={`flex items-center ${
        available ? "text-white" : "text-white/50"
      } `}
    >
      {available ? (
        <CheckCircle2 size={18} className="mr-2" />
      ) : (
        <XCircle size={18} className="mr-2" />
      )}
      <p className="font-medium "> {label}</p>
    </li>
  );
};

export default memo(planFunctionItem);
