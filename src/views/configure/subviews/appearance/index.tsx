import { SubPageHeader } from "common/components/headers";
import { Button } from "common/ui/button";
import React, { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import {
  CardApperance,
  ChatbotIcons,
  GeneralSettings,
  SaveBanner,
} from "./components";
import { uploadBotImages } from "apis/file.api";
import useUserStore from "store/user/user.store";
import { toast } from "react-hot-toast";

export const AppearanceView = () => {
  const user_id = useUserStore((state) => state.user.user_id);
  const { design, get_one_bot, update_one_bot, bot } = useBotStore();

  const [loading, setLoading] = useState(false);

  const [showSave, setShowSave] = useState(false);
  const [headerIcon, setHeaderIcon] = useState(null);

  const [headerColorKey, setHeaderColorKey] = useState(Math.random());
  const [iconColorsKey, setIconColorsKey] = useState(Math.random());
  const [cardThemeColorKey, setCardThemeColorKey] = useState(Math.random());
  const [cardButtonFontColorKey, setCardButtonFontColorKey] = useState(
    Math.random()
  );
  const [cardFontColorKey, setCardFontColorKey] = useState(Math.random());
  const [suggestedActionsThemeKey, setSuggestedActionsThemeKey] = useState(
    Math.random()
  );
  const [botFontColorKey, setBotFontColorKey] = useState(Math.random());
  const [botBubbleBackgroundKey, setBotBubbleBackgroundKey] = useState(
    Math.random()
  );
  const [userFontColorKey, setUserFontColorKey] = useState(Math.random());
  const [userBubbleBackgroundKey, setUserBubbleBackgroundKey] = useState(
    Math.random()
  );

  const [error, setError] = useState(null);
  const [imagesToUpload, setImagesToUpload] = useState({
    headerLogo: {
      path: "",
      formData: null,
    },
    botIcon: {
      path: "",
      formData: null,
    },
  });

  const [designData, setDesignData] = useState({
    design: {
      ...design,
    },
  });

  // useEffect(() => {
  //   get_one_bot(bot.bot_id);
  // }, []);

  const changeAssets = (assetsToChange) => {
    const updatedAssets = { ...designData };
    assetsToChange.map((asset) => {
      updatedAssets[`design`][`${asset.assetName}`] = asset.newAsset;
    });
    setDesignData({
      ...updatedAssets,
    });
    const iframe = document.getElementById(
      "searchat-chatbot-iframe-appearance"
    ) as HTMLIFrameElement;

    iframe.contentWindow.postMessage(
      JSON.stringify({
        type: "update_appearance",
        design: {
          ...designData.design,
        },
      }),
      "*"
    );
  };

  useEffect(() => {
    const iframe = document.getElementById(
      "searchat-chatbot-iframe-appearance"
    ) as HTMLIFrameElement;

    const reader = new FileReader();
    reader.onload = function (e) {
      const imageSource = e.target.result;
      // iframe.onload = function () {
      iframe.contentWindow.postMessage(
        JSON.stringify({
          type: "update_appearance",
          design: {
            ...designData.design,
            headerLogo: imageSource,
          },
        }),
        "*"
      );
      // };
    };
    if (headerIcon) {
      reader.readAsDataURL(headerIcon);
    }
  }, [headerIcon]);

  useEffect(() => {
    if (JSON.stringify(design) !== JSON.stringify(designData.design)) {
      setShowSave(true);
    } else {
      setShowSave(false);
    }
  }, [designData]);

  const onSave = async () => {
    if (bot.language == "en" || bot.language == "ar") {
      if (designData.design.tooltip && !designData.design.tooltipText.trim()) {
        setError("Please enter tooltip text");
        return;
      }
    } else {
      if (
        designData.design.tooltip &&
        (!designData.design.tooltipText.trim() ||
          !designData.design.tooltipTextAr.trim())
      ) {
        setError("Please enter both tooltip texts");
        return;
      }
    }
    setLoading(true);
    setError(null);
    console.log(imagesToUpload.headerLogo.formData);

    if (imagesToUpload.headerLogo.formData) {
      console.log("uploading");

      await uploadBotImages({
        path: imagesToUpload.headerLogo.path,
        formData: imagesToUpload.headerLogo.formData,
      });
    }

    if (imagesToUpload.botIcon.formData) {
      await uploadBotImages({
        path: imagesToUpload.botIcon.path,
        formData: imagesToUpload.botIcon.formData,
      });
    }

    await update_one_bot({ user_id, ...bot, design: { ...designData.design } });

    await get_one_bot(bot.bot_id);
    // toast.success("Theme updated successfully");
    setShowSave(false);
    setLoading(false);
  };

  return (
    <div className="space-y-3">
      {showSave && (
        <SaveBanner
          setDesignData={setDesignData}
          design={design}
          onSave={() => {
            toast.promise(onSave(), {
              loading: "Saving...",
              success: <b>Theme saved!</b>,
              error: <b>Could not save.</b>,
            });
          }}
          setHeaderIcon={setHeaderIcon}
          setHeaderColorKey={setHeaderColorKey}
          setIconColorsKey={setIconColorsKey}
          setCardThemeColorKey={setCardThemeColorKey}
          setCardButtonFontColorKey={setCardButtonFontColorKey}
          setCardFontColorKey={setCardFontColorKey}
          setSuggestedActionsThemeKey={setSuggestedActionsThemeKey}
          setBotFontColorKey={setBotFontColorKey}
          setBotBubbleBackgroundKey={setBotBubbleBackgroundKey}
          setUserFontColorKey={setUserFontColorKey}
          setUserBubbleBackgroundKey={setUserBubbleBackgroundKey}
          loading={loading}
        />
      )}
      <SubPageHeader title="Appearance" description="Customize your chatbot!" />
      <div
        className={`grid grid-cols-1 xl:grid-cols-2 gap-3 gap-x-10 items-start place-items-center ${
          showSave ? "pb-14" : "pb-2"
        } `}
      >
        <div className="space-y-3 pl-9">
          <ChatbotIcons
            designData={designData}
            changeAssets={changeAssets}
            imagesToUpload={imagesToUpload}
            setImagesToUpload={setImagesToUpload}
            setHeaderIcon={setHeaderIcon}
            error={error}
          />
          <GeneralSettings
            designData={designData}
            changeAssets={changeAssets}
            headerColorKey={headerColorKey}
            iconColorsKey={iconColorsKey}
          />
          <CardApperance
            designData={designData}
            changeAssets={changeAssets}
            cardThemeColorKey={cardThemeColorKey}
            cardButtonFontColorKey={cardButtonFontColorKey}
            cardFontColorKey={cardFontColorKey}
            suggestedActionsThemeKey={suggestedActionsThemeKey}
            botFontColorKey={botFontColorKey}
            botBubbleBackgroundKey={botBubbleBackgroundKey}
            userFontColorKey={userFontColorKey}
            userBubbleBackgroundKey={userBubbleBackgroundKey}
          />
        </div>
        {/* TODO change this to production incubator */}
        <div className="sticky top-0 right-0  gap-10 hidden lg:flex lg:flex-col">
          <iframe
            className="sticky top-5 right-0 h-[500px] rounded-lg z-50"
            src={`https://chatbot-incubator-refactor-g668y.ondigitalocean.app/?bot_id=${bot.bot_id}&is_appearance=true`}
            allow="clipboard-read; clipboard-write;"
            id="searchat-chatbot-iframe-appearance"
          ></iframe>

          <div className="h-32  sticky top-32 right-0 z-0" id="bot-circle">
            <div
              className="tooltip-bot"
              style={{ display: designData.design.tooltip ? "" : "none" }}
            >
              <p>{designData.design.tooltipText}</p>
            </div>

            <img
              src={designData.design.botIcon}
              id="Chat-bot-icon"
              className={` ${
                designData.design.BotIconAnimation
                  ? designData.design.BotIconAnimationType
                  : ""
              } h-16 w-16 float-right rounded-full object-cover absolute bottom-0 right-0`}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
