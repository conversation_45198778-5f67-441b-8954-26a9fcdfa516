import { Button } from "common/ui/button";
import React from "react";

export const SaveBanner = ({
  setDesignData,
  design,
  onSave,
  setHeaderIcon,
  setHeaderColorKey,
  setIconColorsKey,
  setCardFontColorKey,
  setCardButtonFontColorKey,
  setCardThemeColorKey,
  setSuggestedActionsThemeKey,
  setBotFontColorKey,
  setBotBubbleBackgroundKey,
  setUserFontColorKey,
  setUserBubbleBackgroundKey,
  loading
}) => {
  return (
    <div className="absolute bottom-0 right-0 left-[299px] bg-[#1A1A1D] z-30 h-[85px]  shadow-[0_-5px_5px_-5px_#141416] flex items-center justify-center gap-5">
      <Button
        variant="outline"
        onClick={() => {
          setDesignData({ design: { ...design } });
          setHeaderIcon(null);
          const iframe = document.getElementById(
            "searchat-chatbot-iframe-appearance"
          ) as HTMLIFrameElement;

          iframe.contentWindow.postMessage(
            JSON.stringify({
              type: "update_appearance",
              design: {
                ...design,
              },
            }),
            "*"
          );
          setHeaderColorKey(Math.random());
          setIconColorsKey(Math.random());
          setCardFontColorKey(Math.random());
          setCardButtonFontColorKey(Math.random());
          setCardThemeColorKey(Math.random());
          setSuggestedActionsThemeKey(Math.random());
          setBotFontColorKey(Math.random());
          setBotBubbleBackgroundKey(Math.random());
          setUserFontColorKey(Math.random());
          setUserBubbleBackgroundKey(Math.random());
        }}
      >
        Cancel
      </Button>
      <Button loading={loading} onClick={onSave}>Save Changes</Button>
    </div>
  );
};
