import { Card } from "common/ui/card";
import ColorPicker from "common/ui/inputs/colorPicker";
import React from "react";

export const CardApperance = ({
  designData,
  changeAssets,
  cardThemeColorKey,
  cardFontColorKey,
  cardButtonFontColorKey,
  suggestedActionsThemeKey,
  botFontColor<PERSON>ey,
  botB<PERSON>bleBackground<PERSON>ey,
  userFontColor<PERSON>ey,
  userB<PERSON>bleBackgroundKey,
}) => {
  return (
    <Card title="Content" hr>
      <div className="flex gap-5 py-3">
        <div className="relative pr-5">
          <ColorPicker
            key={cardThemeColorKey}
            colorField="Button Background"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "cardThemeColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.cardThemeColor}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
        <div className="relative pr-5">
          <ColorPicker
            key={cardButtonFontColorKey}
            colorField="Button Font Color"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "cardButtonFontColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.cardButtonFontColor}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
        <div className="relative pr-5">
          <ColorPicker
            key={cardFontColorKey}
            colorField="Card Font Color"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "cardFontColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.cardFontColor}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
      </div>
      <hr className="text-white/25" />
      <div className="relative pr-5">
        <ColorPicker
          key={suggestedActionsThemeKey}
          colorField="Suggested Actions Theme"
          onChange={(color) => {
            changeAssets([
              {
                assetName: "suggestedActionsBackground",
                newAsset: color,
              },
            ]);
          }}
          value={designData.design.suggestedActionsBackground}
        />
        <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
      </div>
      <hr className="text-white/25" />
      <div className="flex gap-5 py-3">
        <div className="relative pr-5">
          <ColorPicker
            key={botFontColorKey}
            colorField="Bot Message Font Color"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "botFontColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.botFontColor}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
        <div className="relative pr-5">
          <ColorPicker
            key={botBubbleBackgroundKey}
            colorField="Bot Message Background"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "botBubbleBackground",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.botBubbleBackground}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
      </div>
      <div className="flex gap-5 py-3">
        <div className="relative pr-5">
          <ColorPicker
            key={userFontColorKey}
            colorField="User Message Font Color"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "userFontColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.userFontColor}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
        <div className="relative pr-5">
          <ColorPicker
            key={userBubbleBackgroundKey}
            colorField="User Message Background"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "userBubbleBackground",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.userBubbleBackground}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
      </div>
    </Card>
  );
};
