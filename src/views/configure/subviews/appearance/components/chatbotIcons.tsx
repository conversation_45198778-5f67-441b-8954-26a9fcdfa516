import { BotIcon } from "common/components/botIcon";
import { But<PERSON> } from "common/ui/button";
import { Card } from "common/ui/card";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";
import { Trash2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import React, { useState } from "react";
import generateStorageId from "helpers/generateStorageId";
import useBotStore from "store/bot/bot.store";
import { handleUploadImg } from "helpers/media";
import constant from "constant";

export const ChatbotIcons = ({
  designData,
  changeAssets,
  imagesToUpload,
  setImagesToUpload,
  error,
  setHeaderIcon,
}) => {
  const bot = useBotStore((state) => state.bot);

  const uploadImageHandler = (event) => {
    const file = event.target.files[0];
    const name = event.target.name;

    if (!file) return;

    const formData = new FormData();
    formData.append("file", file);

    const path = `Bots/${bot.file_name}/assets/${generateStorageId()}.${
      file?.type?.split("/")[1]
    }`;

    setImagesToUpload({
      ...imagesToUpload,
      [name]: {
        path,
        formData,
      },
    });

    setTimeout(() => {
      changeAssets([
        {
          assetName: name,
          newAsset: "https://infotointell.fra1.digitaloceanspaces.com/" + path,
        },
      ]);
    }, 100);
    setTimeout(() => {
      handleUploadImg(`img-${name}`, file);
      if (name === "botIcon") {
        handleUploadImg("Chat-bot-icon", file);
      }
      if (name === "headerLogo") {
        setHeaderIcon(file);
      }
    }, 200);
  };

  return (
    <Card title="Chatbot Icons" hr>
      <div className="space-y-2 py-3">
        <div className="font-bold flex gap-3 items-end pb-2">
          Bot Logo
          <span className="font-light text-sm">
            This will show on the chatbot header
          </span>
        </div>
        <div className="flex gap-5">
          <BotIcon
            key="headerLogo"
            icon={designData.design.headerLogo}
            isUpload
            name="headerLogo"
            uploadImg={uploadImageHandler}
          />
          {designData.design.headerLogo && (
            <Button
              variant="outline"
              onClick={() => {
                changeAssets([
                  {
                    assetName: "headerLogo",
                    newAsset: "",
                  },
                ]);
                setImagesToUpload({
                  ...imagesToUpload,
                  headerLogo: {
                    path: "",
                    formData: null,
                  },
                });
                setHeaderIcon(null);
              }}
            >
              <Trash2 className="mr-2 h-4 w-4" /> Remove Logo
            </Button>
          )}
        </div>
      </div>
      <hr className="text-white/25" />
      <div className="space-y-2 py-3">
        <div className="font-bold flex gap-3 items-end pb-2">
          Bot Icon
          <span className="font-light text-sm">
            This will show as the chatbot bubble
          </span>
        </div>
        <div className="flex gap-5">
          <BotIcon
            key="botIcon"
            icon={designData.design.botIcon}
            isUpload
            name="botIcon"
            uploadImg={uploadImageHandler}
          />
          {designData.design.botIcon &&
            designData.design.botIcon !==
              constant.BOT_DESIGNER_ASSETS_URL + "bubble.png" && (
              <Button
                variant="outline"
                onClick={() => {
                  changeAssets([
                    {
                      assetName: "botIcon",
                      newAsset: constant.BOT_DESIGNER_ASSETS_URL + "bubble.png",
                    },
                  ]);
                  setImagesToUpload({
                    ...imagesToUpload,
                    botIcon: {
                      path: "",
                      formData: null,
                    },
                  });
                }}
              >
                <Trash2 className="mr-2 h-4 w-4" /> Remove Logo
              </Button>
            )}
        </div>
      </div>
      <hr className="text-white/25" />
      <div className="flex justify-between items-center w-full">
        <div className="w-full">
          <Checkbox
            name="enable_animation"
            label="Enable Bot Icon Animation"
            checked={designData.design.BotIconAnimation}
            onChange={(event) =>
              changeAssets([
                {
                  assetName: "BotIconAnimation",
                  newAsset: event.target.checked,
                },
              ])
            }
          />
        </div>
        <div
          className={`${
            designData.design.BotIconAnimation ? "visible" : "invisible"
          } w-full `}
        >
          <Select
            value={designData.design.BotIconAnimationType}
            onValueChange={(value) => {
              changeAssets([
                {
                  assetName: "BotIconAnimationType",
                  newAsset: value,
                },
              ]);
            }}
          >
            <SelectTrigger className="w-1/2 ">
              <SelectValue placeholder="Animation Type" />
            </SelectTrigger>
            <SelectContent className="capitalize">
              <SelectItem value="shake">shake</SelectItem>
              <SelectItem value="floating">float</SelectItem>
              <SelectItem value="flip">flip</SelectItem>
              <SelectItem value="swing">swing</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <hr className="text-white/25" />
      <div className="flex justify-between items-center w-full">
        <div className="w-full">
          <Checkbox
            name="enable_tooltip"
            label="Add Bot Icon Tooltip"
            checked={designData.design.tooltip}
            onChange={(event) =>
              changeAssets([
                {
                  assetName: "tooltip",
                  newAsset: event.target.checked,
                },
              ])
            }
          />
        </div>
        <div
          className={`${
            designData.design.tooltip ? "visible" : "invisible"
          } w-full space-y-4`}
        >
          <Input
            name="tooltip_text"
            placeholder={
              bot.language == "webDefault" || bot.language == "both"
                ? "Tooltip Text En"
                : "Tooltip Text"
            }
            value={designData.design.tooltipText}
            onChange={(event) => {
              if (bot.language == "en" || bot.language == "ar") {
                changeAssets([
                  {
                    assetName: "tooltipText",
                    newAsset: event.target.value,
                  },
                  {
                    assetName: "tooltipTextAr",
                    newAsset: "", // clear arabic tooltip if user changes language
                  },
                ]);
              } else {
                changeAssets([
                  {
                    assetName: "tooltipText",
                    newAsset: event.target.value,
                  },
                ]);
              }
            }}
            error={error}
          />
          {(bot.language == "webDefault" || bot.language == "both") && (
            <Input
              name="tooltip_text_ar"
              placeholder="Tooltip Text Ar"
              value={designData.design.tooltipTextAr}
              onChange={(event) => {
                changeAssets([
                  {
                    assetName: "tooltipTextAr",
                    newAsset: event.target.value,
                  },
                ]);
              }}
              error={error}
            />
          )}
        </div>
      </div>
    </Card>
  );
};
