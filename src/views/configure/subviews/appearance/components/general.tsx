import ColorPicker from "common/ui/inputs/colorPicker";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Input } from "common/ui/inputs/input";
import React, { useState } from "react";
import { Card } from "common/ui/card";

export const GeneralSettings = ({
  designData,
  changeAssets,
  iconColorsKey,
  headerColorKey,
}) => {
  return (
    <Card title="General" hr>
      <div className="flex gap-5 py-3">
        <div className="relative pr-5">
          <ColorPicker
            key={headerColorKey}
            colorField="Header Color"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "headerColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.headerColor}
          />
          <div className="absolute right-0 top-2 hidden h-full min-h-[1em] w-px self-stretch border-t-0 bg-gradient-to-tr from-transparent via-neutral-500 to-transparent opacity-25 dark:opacity-100 lg:block"></div>
        </div>
        <div>
          <ColorPicker
            key={iconColorsKey}
            colorField="Header Text Color"
            onChange={(color) => {
              changeAssets([
                {
                  assetName: "iconsColor",
                  newAsset: color,
                },
              ]);
            }}
            value={designData.design.iconsColor}
          />
        </div>
      </div>
      <hr className="text-white/25" />
      <div className="flex gap-10">
        <Checkbox
          name="show_voice"
          label="Show Voice Icon"
          checked={designData.design.voiceIcon}
          onChange={(event) =>
            changeAssets([
              {
                assetName: "voiceIcon",
                newAsset: event.target.checked,
              },
            ])
          }
        />
        <Checkbox
          name="show_branding"
          label="Show Branding"
          checked={designData.design.branding}
          onChange={(event) =>
            changeAssets([
              {
                assetName: "branding",
                newAsset: event.target.checked,
              },
            ])
          }
        />
      </div>
      <hr className="text-white/25" />
      <Input
        bold
        name="placeholder"
        title="Message Placeholder"
        value={designData.design.placeholder}
        onChange={(e) => {
          changeAssets([
            {
              assetName: "placeholder",
              newAsset: e.target.value,
            },
          ]);
        }}
      />
    </Card>
  );
};
