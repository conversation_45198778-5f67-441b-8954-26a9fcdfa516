import { restartOne } from "apis/bot.api";
import {
  createOne,
  getPageByBotId,
  getPagesFromToken,
  updateOne,
  getAll,
} from "apis/facebook.api";
import { generateWebchat } from "apis/file.api";
import { getPagesFromToken as getInstagramPagesFromToken } from "apis/instagram.api";
import { SubPageHeader } from "common/components/headers";
import { Button } from "common/ui/button";
import { Card } from "common/ui/card";
import { Input } from "common/ui/inputs/input";
import { Textarea } from "common/ui/inputs/textarea";
import {
  Copy,
  XCircle,
  FacebookIcon,
  Globe,
  InstagramIcon,
  Loader2,
  Save,
  Slack,
  Trash2,
} from "lucide-react";
import { getOne as getBotWhatsapp } from "apis/whatsapp.api";

import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useUserStore from "store/user/user.store";
import helper from "./helper";
import { Checkbox } from "common/ui/inputs/checkbox";
import { toast } from "react-hot-toast";
import planFunctionChecker from "helpers/planFunctionChecker";
import constant from "constant";
import { ConnectButton as Whataspp360ConnectButton } from "360dialog-connect-button";
import { createOne as createOneWhatsapp } from "../../../../apis/whatsapp.api";
import Image from "next/image";
import {
  getSlackBotToken,
  getAllConnections,
  SlackIntegrationObj,
  deleteOneConnection,
} from "apis/slack.api";

import {
  getTelegramIntegration,
  createTelegramIntegration,
  TelegramIntegrationObj,
  deleteTelegramIntegration,
} from "apis/telegram.api";

import { copyToClipboard } from "helpers/helper";
import TelegramSVG from "common/icons/TelegramSVG";
import WhatsappSVG from "common/icons/WhatsappSVG";
import FacebookPages from "./components/FacebookPages";
import { TConnectedPage } from "types/facebook.types";

function validURL(str) {
  var pattern = new RegExp(
    "^(https?:\\/\\/)?" + // protocol
      "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
      "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
      "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
      "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
      "(\\#[-a-z\\d_]*)?$",
    "i"
  ); // fragment locator
  return !!pattern.test(str);
}

export const PublishView = () => {
  const { bot, update_one_bot, get_one_bot, planfunction } = useBotStore();
  const user = useUserStore((state) => state.user);
  const [domain, setDomain] = useState(bot.domain || "");
  const [domainError, setDomainError] = useState("");
  const [domainSuccess, setDomainSuccess] = useState("");

  const [needsRestart, setNeedsRestart] = useState(false);

  const [connectionString, setConnectionString] = useState(undefined);

  const [loading, setLoading] = useState(false);
  const [loadingTelegram, setLoadingTelegram] = useState(false);

  const [userFacebookPages, setUserFacebookPages] = useState([]);
  const [userInstagramPages, setUserInstagramPages] = useState([]);
  const [selectedFacebookPage, setselectedFacebookPage] =
    useState<TConnectedPage | null>(null);
  const [selectedInstagramPage, setselectedInstagramPage] =
    useState<TConnectedPage | null>(null);
  const [userConnectedPages, setUserConnectedPages] = useState([]);

  const getAllData =  () => {
    getAll(bot.bot_id).then((data) => {
      setUserConnectedPages(data || []);
      getPages(data || []);
    });

  }

  useEffect(() => {
    getAllData();
  }, []);

  const handleUpdateselectedFacebookPage = (page: TConnectedPage) => {
    if (page?.channel === "facebook") {
      setselectedFacebookPage({
        ...page,
      });
    } else {
      setselectedInstagramPage({
        ...page,
      });
    }
  };

  const [slackBotToken, setSlackBotToken] = useState("");
  const [telegramIntegeration, setTelegramIntegeration] =
    useState<TelegramIntegrationObj>({
      telegram_integration_id: null,
      bot_id: null,
      token: "",
    });
  const [slackChannels, setSlackChannels] = useState<SlackIntegrationObj[]>([]);

  const redirect_uri = window.location.href?.includes("localhost")
    ? "https://app.searchat.com"
    : window.location.protocol + "//" + window.location.host;

  const handleFacebookLogin = () => {
    const url = encodeURI(
      "https://www.facebook.com/v11.0/dialog/oauth?display=popup&response_type=token&client_id=2944020909203376&redirect_uri=" +
        redirect_uri +
        "&auth_type=rerequest&scope=pages_show_list,pages_messaging,instagram_basic,instagram_manage_messages,pages_manage_metadata,public_profile,business_management"
    );
    // localStorage.removeItem("getInstagramPages");
    localStorage.setItem("getFacebookPages", "true");
    setTimeout(() => (window.location.href = url), 200);
  };

  const handleInstagramLogin = () => {
    const url = encodeURI(
      "https://www.facebook.com/v11.0/dialog/oauth?display=popup&response_type=token&client_id=2944020909203376&redirect_uri=" +
        redirect_uri +
        "&auth_type=rerequest&scope=pages_show_list,pages_messaging,instagram_basic,instagram_manage_messages,pages_manage_metadata,public_profile,business_management"
    );
    // localStorage.removeItem("getFacebookPages");
    localStorage.setItem("getInstagramPages", "true");
    setTimeout(() => (window.location.href = url), 200);
  };

  const getPages = (connectedPages) => {
    var url = new URL(window.location.href);
    var facebook_access_token = undefined;
    if (Boolean(url.hash) && url.hash.split("=")[0] === "#access_token") {
      facebook_access_token = url.hash.split("=")[1];
    }

    if (Boolean(facebook_access_token)) {
      if (localStorage.getItem("getFacebookPages")) {
        getPagesFromToken({
          access_token: facebook_access_token,
        }).then((data) => {
          console.log(data, "data");
          if (data && data.pages) {
            const newData =
              data?.pages?.length > 0
                ? [
                    ...data?.pages?.map((page) => {
                      const connectedPageRecord = connectedPages?.find(
                        (connectedPage) =>
                          connectedPage.pageId === page.id &&
                          connectedPage.channel === "facebook"
                      );

                      const connectedInstagramPageRecord =
                        page.instagram_business_account
                          ? connectedPages?.find(
                              (connectedPage) =>
                                connectedPage.pageId ===
                                  page.instagram_business_account?.id &&
                                connectedPage.channel === "instagram"
                            )
                          : null;

                      return {
                        ...page,
                        connected: Boolean(connectedPageRecord),
                        facebook_channel_id:
                          connectedPageRecord?.facebook_channel_id,
                        instagram_business_account:
                          page.instagram_business_account
                            ? {
                                ...page.instagram_business_account,
                                connected: Boolean(
                                  connectedInstagramPageRecord
                                ),
                                facebook_channel_id:
                                  connectedInstagramPageRecord?.facebook_channel_id,
                              }
                            : null,
                      };
                    }),
                  ]
                : [];
console.log(newData, "newData")
            setUserFacebookPages(newData);
          }
        });
        // localStorage.removeItem("getFacebookPages");
      }
      if (localStorage.getItem("getInstagramPages")) {
        getInstagramPagesFromToken({
          access_token: facebook_access_token,
        }).then((data) => {
          if (data && data.pages) {
            setUserInstagramPages([
              ...data.pages.map((page) => {
                return {
                  ...page,
                  connected: connectedPages?.some(
                    (connectedPage) =>
                      connectedPage.pageId === page.id &&
                      connectedPage.channel === "instagram"
                  ),
                };
              }),
            ]);
          }
        });
        // localStorage.removeItem("getInstagramPages");
      }
    }
  };

  useEffect(() => {
    if (needsRestart) {
      restartOne({
        bot_id: bot.bot_id,
      });
      get_one_bot(bot.bot_id);
      setNeedsRestart(false);
    }
    setLoading(false);
  }, [bot]);

  //   TODO : preview added pages , testing

  useEffect(() => {
    getSlackBotToken(bot.bot_id).then((token) => {
      setSlackBotToken(token);
    });
    getTelegramIntegration(bot.bot_id).then((data) => {
      setTelegramIntegeration(data);
    });

    getAllConnections(bot.bot_id).then((data) => {
      setSlackChannels(data);
    });
  }, []);

  const handleDeleteOneConnection = async (bot_id: number, channel: string) => {
    const isDeleted = await deleteOneConnection(bot_id, channel);
    console.log(isDeleted);

    if (isDeleted) {
      getAllConnections(bot_id).then((data) => {
        setSlackChannels(data);
      });
    }
  };

  const saveTelegramIntegerationHandler = async () => {
    if (loadingTelegram) return;

    try {
      setLoadingTelegram(true);
      const res = await createTelegramIntegration(
        telegramIntegeration.token,
        bot.bot_id
      );
      setTelegramIntegeration(res);
      toast.success("Your bot connected to the Telegram successfully");
    } catch (error) {
      console.log(error);
      toast.error("Error connecting to the Telegram");
    } finally {
      setLoadingTelegram(false);
    }
  };

  const handleDeleteTelegramIntegration = () => {
    deleteTelegramIntegration(bot.bot_id);
  };

  const [botWhatsapp, setBotWhatsapp] = React.useState<any>(null);

  const getBotWhatsappData = async () => {
    const botWhatsappData = await getBotWhatsapp(bot.bot_id);
    console.log(botWhatsappData);
    setBotWhatsapp(botWhatsappData);
  };

  React.useEffect(() => {
    getBotWhatsappData();
  }, [bot.bot_id]);
  return (
    <div className="space-y-3">
      <SubPageHeader
        title="Publish your bot"
        description="Publish your bot to the world by connecting it to different channels!"
      />
      <div className="grid grid-cols-2 gap-4">
        <Card title="Connect to Website" hr headerIcon={Globe}>
          {planFunctionChecker(planfunction, "web_channel") ? (
            <>
              <div className="flex justify-between items-end gap-5">
                <Input
                  name="hostDomain"
                  title="Host Domain"
                  value={domain}
                  onChange={(event) => {
                    if (validURL(event.target.value)) {
                      setDomain(event.target.value);
                      setDomainSuccess("Domain is valid 🤞🏻");
                      setDomainError("");
                    } else {
                      setDomain(event.target.value);
                      setDomainError("Domain is not valid 😔");
                      setDomainSuccess("");
                    }
                  }}
                  error={domainError}
                  message={domainSuccess}
                  disabled={bot.bot_id === 666}
                />
                <Button
                  className="mb-[0.15rem]"
                  disabled={
                    domainError !== "" ||
                    domain.trim() === "" ||
                    bot.bot_id === 666
                  }
                  onClick={() => {
                    generateWebchat({ file_name: bot.file_name }).then(
                      (data) => {
                        setConnectionString(data.script);
                      }
                    );
                  }}
                >
                  Generate
                </Button>
                <Button
                  className="mb-[0.15rem]"
                  disabled={domain === bot.domain || domain === ""}
                  onClick={() => {
                    setLoading(true);
                    setNeedsRestart(true);
                    update_one_bot({
                      user_id: user.user_id,
                      ...bot,
                      domain: !domainError
                        ? helper.extractHostname(domain)
                        : undefined,
                    });
                  }}
                >
                  <Save size={20} />
                </Button>
              </div>
              {connectionString && (
                <div>
                  <label htmlFor="">Connection String</label>
                  <Textarea value={connectionString} disabled />
                </div>
              )}
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card>

        {/* <Card title="Connect to Messenger" hr headerIcon={FacebookIcon}>
          {planFunctionChecker(planfunction, "facebook_channel") ? (
            <>
              {userFacebookPages?.length > 0 ? (
                <>
                  <div>Select a page to connect to your bot</div>
                  <FacebookPages
                    selectedFacebookPage={selectedFacebookPage}
                    handleUpdateselectedFacebookPage={
                      handleUpdateselectedFacebookPage
                    }
                    loading={loading}
                    userFacebookPages={userFacebookPages}
                    channel="facebook"
                  />
                </>
              ) : (
                <>
                  <div>Connect your bot To Facebook</div>
                  <Button
                    variant="outline"
                    className="p-2 bg-blue-600 hover:bg-blue-500"
                    onClick={handleFacebookLogin}
                  >
                    <FacebookIcon size={20} className="mr-2" /> Open Facebook
                  </Button>
                </>
              )}
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card>
        <Card title="Connect to Instagram" hr headerIcon={InstagramIcon}>
          {planFunctionChecker(planfunction, "instegram_channel") ? (
            <>
              {userInstagramPages?.length > 0 ? (
                <>
                  <div>Select a page to connect to your bot</div>
                  <FacebookPages
                    selectedFacebookPage={selectedInstagramPage}
                    handleUpdateselectedFacebookPage={
                      handleUpdateselectedFacebookPage
                    }
                    loading={loading}
                    userFacebookPages={userInstagramPages}
                    channel="instagram"
                  />
                </>
              ) : (
                <>
                  <div>Connect your bot To Instagram</div>
                  <Button
                    variant="outline"
                    className="p-2 bg-[linear-gradient(45deg,#405de6,#5851db,#833ab4,#c13584,#e1306c,#fd1d1d)] hover:backdrop-brightness-200"
                    onClick={handleInstagramLogin}
                  >
                    <InstagramIcon size={20} className="mr-2" /> Open Instagram
                  </Button>
                </>
              )}
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card> */}
        <Card title="Connect to Whatsapp" hr svg={WhatsappSVG}>
          {planFunctionChecker(planfunction, "whatsapp_channel") ? (
            <>
              {botWhatsapp ? <p>Select a page to connect to your bot</p> : null}
              <div className="flex items-end ">
                <Whataspp360ConnectButton
                  partnerId={constant.PARTNER_ID_360_DIALOG}
                  callback={(callbackObject) => {
                    createOneWhatsapp({
                      bot_id: bot.bot_id,
                      channel_id: callbackObject.channels,
                    });
                    if (callbackObject.revokedChannels) {
                      console.log(
                        "Revoked Channel IDs: " + callbackObject.revokedChannels
                      );
                    }
                  }}
                  className="bg-green-600 p-2 rounded-md" // <-- Insert your own styles via className definition or through inline styling
                  label="Connect To Whatsapp"
                  queryParameters={{
                    redirect_url: "http://localhost:3000/",
                    next: "login",
                  }}
                />
              </div>
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card>
        <Card
          title="Connect to Messenger / Instagram"
          hr
          headerIcon={FacebookIcon}
        >
          {planFunctionChecker(planfunction, "facebook_channel") ? (
            <>
              {userFacebookPages?.length > 0 ? (
                <>
                  <div>Select a page to connect to your bot</div>
                  <FacebookPages
                    selectedFacebookPage={selectedFacebookPage}
                    handleUpdateselectedFacebookPage={setselectedFacebookPage}
                    loading={loading}
                    userFacebookPages={userFacebookPages}
                    channel="facebook"
                  />
                </>
              ) : (
                <>
                  {userConnectedPages?.length > 0 ? (
                    <div className="border border-white/25 rounded-md p-3">
                      Your bot is connected to the following pages
                      <ul className="space-y-3 text-gray-500  list-disc  list-inside pl-3 marker:text-purple-600">
                        {userConnectedPages.map((page, i) => {
                          return (
                            <li key={i} className="flex items-center gap-1 ">
                              {page.name ? page.name : page.pageId}
                              {page.channel === "facebook" ? (
                                <FacebookIcon size={15} />
                              ) : (
                                <InstagramIcon size={15} />
                              )}
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  ) : (
                    <div>Connect your bot To Facebook</div>
                  )}

                  <Button
                    variant="outline"
                    className="p-2 bg-blue-600 hover:bg-blue-500"
                    onClick={handleFacebookLogin}
                  >
                    <FacebookIcon size={20} className="mr-2" /> Open Facebook
                  </Button>
                </>
              )}
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card>
        <Card id="slack" title="Connect to Slack" hr headerIcon={Slack}>
          {true ? (
            <>
              <div className="flex flex-col gap-3">
                <div className="flex items-center gap-3">
                  <Input disabled name="hostDomain" value={slackBotToken} />
                  <Button className="mt-1">
                    <Copy
                      onClick={(e) => {
                        copyToClipboard(e, slackBotToken);
                      }}
                    />
                  </Button>
                </div>
                {slackChannels.length > 0 && (
                  <ul className="space-y-2">
                    <li>Connected Channels</li>
                    {slackChannels.map((obj, i) => {
                      return (
                        <li
                          className="flex items-center justify-between "
                          key={i}
                        >
                          {obj.channel}
                          <XCircle
                            className="cursor-pointer hover:text-red-500"
                            onClick={() => {
                              handleDeleteOneConnection(
                                obj.bot_id,
                                obj.channel
                              );
                            }}
                          />
                        </li>
                      );
                    })}
                  </ul>
                )}
                <a href="https://slack.com/oauth/v2/authorize?client_id=5876182247284.5876188892036&scope=app_mentions:read,commands,incoming-webhook,chat:write&user_scope=">
                  <Image
                    alt="Add to Slack"
                    height="40"
                    width="139"
                    src="https://platform.slack-edge.com/img/add_to_slack.png"
                  />
                </a>
              </div>
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card>
        <Card id="telegram" title="Connect to Telegram" hr svg={TelegramSVG}>
          {true ? (
            <>
              <div className="flex flex-col gap-3">
                <div className="flex items-center gap-3">
                  <Input
                    name="hostDomain"
                    placeholder="please enter your telegram token"
                    onChange={(event) => {
                      setTelegramIntegeration({
                        ...telegramIntegeration,
                        token: event.target.value,
                      });
                    }}
                    value={telegramIntegeration.token}
                  />

                  {telegramIntegeration?.telegram_integration_id ? (
                    <Button
                      loading={loadingTelegram}
                      className=" bg-red-600 hover:bg-red-900"
                      onClick={handleDeleteTelegramIntegration}
                    >
                      <Trash2 className=" h-5 w-5" />
                    </Button>
                  ) : (
                    <>
                      <Button
                        loading={loadingTelegram}
                        className=""
                        disabled={telegramIntegeration?.token?.trim() === ""}
                        onClick={saveTelegramIntegerationHandler}
                      >
                        <Save size={20} />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div>Please upgrade your plan to use this feature</div>
          )}
        </Card>
      </div>
    </div>
  );
};
