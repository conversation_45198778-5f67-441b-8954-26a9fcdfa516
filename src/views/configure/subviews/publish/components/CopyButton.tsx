import React, { useRef, useState } from "react";
import { Button } from "common/ui/button";
import { Input } from "common/ui/input";
import { CopyIcon, CheckCircleIcon } from "lucide-react"; // Assuming you have a check circle icon
import toast from "react-hot-toast";

type CopyButtonProps = {
  pageId: string;
};

const CopyButton: React.FC<CopyButtonProps> = ({ pageId }) => {
  const pageIdRef = useRef<HTMLInputElement>(null);
  const [copied, setCopied] = useState(false); // State to manage copied state

  const copyToClipboard = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.select();
      navigator.clipboard.writeText(ref.current.value);
      toast.success(ref.current.name + " Copied to clipboard");
      setCopied(true); // Set copied state to true
      // Reset copied state after a delay (if needed)
      setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
    }
  };

  return (
    <div className="flex items-center ">
      <div className="hidden">
        <label htmlFor="pageId" className="sr-only">
          Page Id
        </label>
        <Input
          name="pageId"
          id="pageId"
          value={pageId}
          disabled={true}
          ref={pageIdRef}
          className="h-9"
        />
      </div>
      {copied ? ( // Render check icon if copied is true
        <Button
          type="button"
          size="sm"
          className="px-3"
          //   onClick={() => copyToClipboard(pageIdRef)}
        >
          <span className="sr-only"></span>
          <CheckCircleIcon className="h-4 w-4 text-green-500" />
        </Button>
      ) : (
        // Render copy icon otherwise
        <Button
          type="button"
          size="sm"
          className="px-3"
          onClick={() => copyToClipboard(pageIdRef)}
        >
          <span className="sr-only">Copy</span>
          <CopyIcon className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

export default CopyButton;
