import { disconnectPage, createOne, updateOne } from "apis/facebook.api";
import { FullModal } from "common/components/modals";
import { Button } from "common/ui/button";
import { Checkbox } from "common/ui/inputs/checkbox";
import { Separator } from "common/ui/separator";
import { FacebookIcon, InstagramIcon, Loader2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useBotStore from "store/bot/bot.store";
import { TConnectedPage, TUserFacebookPage } from "types/facebook.types";

type TFacebookPagesProps = {
  userFacebookPages: TUserFacebookPage[];
  selectedFacebookPage: TConnectedPage;
  loading: boolean;
  handleUpdateselectedFacebookPage: (page: TConnectedPage) => void;
  channel: "facebook" | "instagram";
};
const FacebookPages: React.FC<TFacebookPagesProps> = ({
  userFacebookPages,
  selectedFacebookPage,
  loading,
  handleUpdateselectedFacebookPage,
  channel,
}) => {
  const { bot } = useBotStore();

  const [pageLoader, setPageLoader] = useState(false);
  const [pagesData, setPagesData] = useState<TUserFacebookPage[]>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDisconnectModalOpen, setIsDisconnectModalOpen] = useState(false);

  useEffect(() => {
    if (userFacebookPages?.length > 0) {
      setPagesData([...userFacebookPages]);
    }
  }, [userFacebookPages]);

  const handleDisconnectFacebookPage = async (page: TConnectedPage) => {
    console.log("page in disconnect", page);
    try {
      setPageLoader(true);
      const res = await disconnectPage({
        facebook_channel_id: page.facebook_channel_id,
        bot_id: bot.bot_id,
      });
      console.log("res", res);
      if (res.error) {
        toast.error(
          `Error Disconnecting  (${selectedFacebookPage?.name})  Page`
        );
        return;
      }

      handleUpdateselectedFacebookPage({
        ...page,
        connected: false,
        facebook_channel_id: null,
      });
      const newPagesData = [...pagesData]?.map((prevPage) => {
        if (prevPage.id === page.pageId) {
          prevPage.connected = false;
        }
        return { ...prevPage };
      });
      console.log("newPagesData", newPagesData);
      setPagesData([...newPagesData]);
      toast.success(
        `Your Bot Disconnected From (${selectedFacebookPage?.name})  Page Successfully`
      );
    } catch (error) {
      toast.error(`Error Disconnecting  (${selectedFacebookPage?.name})  Page`);
    } finally {
      setPageLoader(false);
      setIsModalOpen(false);
    }
  };

  const handleDisconnectFacebookPageAndInstagram = async (
    page: TConnectedPage,
    instagramOnly: boolean
  ) => {
      try {
        setPageLoader(true);
        const res = await disconnectPage({
          facebook_channel_id: page.instagram_business_account.facebook_channel_id,
          bot_id: bot.bot_id,
        });
        if (res.error) {
          toast.error(
            `Error Disconnecting  (${selectedFacebookPage?.name})  Page`
          );
          return;
        }
        if(!instagramOnly){
          const res = await disconnectPage({
            facebook_channel_id: page.facebook_channel_id,
            bot_id: bot.bot_id,
          });
          if (res.error) {
            toast.error(
              `Error Disconnecting  (${selectedFacebookPage?.name})  Page`
            );
            return;
          }
          handleUpdateselectedFacebookPage({
            ...page,
            connected: false,
            facebook_channel_id: null,
            instagram_business_account: {
              ...page.instagram_business_account,
              connected: false,
            },
          });
          const newPagesData = [...pagesData]?.map((prevPage) => {
            if (prevPage.id === page.pageId) {
              prevPage.connected = false;
              prevPage.instagram_business_account.connected = false;
            }
            return { ...prevPage };
          });
          setPagesData([...newPagesData]);
          toast.success(
            `Your Bot Disconnected From (${selectedFacebookPage?.name})  Page Successfully`
          );
          return 
        }
  
        handleUpdateselectedFacebookPage({
          ...page,
          connected: true,
          instagram_business_account: {
            ...page.instagram_business_account,
            connected: false,
          },
        });
        const newPagesData = [...pagesData]?.map((prevPage) => {
          if (prevPage.id === page.pageId) {
            prevPage.instagram_business_account.connected = false;
          }
          return { ...prevPage };
        });
        console.log("newPagesData", newPagesData);
        setPagesData([...newPagesData]);
        toast.success(
          `Your Bot Disconnected From (${selectedFacebookPage?.name})  Page Successfully`
        );
      } catch (error) {
        toast.error(`Error Disconnecting  (${selectedFacebookPage?.name})  Page`);
      } finally {
        setPageLoader(false)  
        setIsModalOpen(false);
      }
  }

  const handleConnectFacebookPages = async (
    selectedFacebookPage: TConnectedPage,
    isInstagram: boolean = false
  ) => {
    if(isInstagram){
      createOne({
        bot_id: bot.bot_id,
        pageId: selectedFacebookPage.instagram_business_account.id,
        channel: "instagram",
        page_token:
          selectedFacebookPage.instagram_business_account.access_token,
        name: selectedFacebookPage.instagram_business_account.name,
      }).then((data2) => {
        console.log("data2", data2);
        if(data2.message === "facebook is already exists") {
          toast.error(`Page ${selectedFacebookPage.instagram_business_account.name} is already connected to another bot`);
          setPageLoader(false);
          return;
        } else if(data2.message) {
          toast.error("Error connecting to the page");
          setPageLoader(false);
          return;
        }
        setPageLoader(false);
      
        setPagesData((prev) => {
          const updatedPages = prev.map((prevPage) => {
            if (prevPage.id === selectedFacebookPage.pageId) {
              return {
                ...prevPage,
                instagram_business_account: {
                  ...prevPage.instagram_business_account,
                  connected: true,
                  facebook_channel_id: data2.facebook_channel_id,
                },
              };
            }
            return prevPage;
          });
          return updatedPages;
        });
        handleUpdateselectedFacebookPage({ 
          ...selectedFacebookPage,
          instagram_business_account: {
            ...selectedFacebookPage.instagram_business_account,
            connected: true,
            facebook_channel_id: data2.facebook_channel_id,
          },
         });

        toast.success("Your bot connected to the selected page successfully");
        setIsModalOpen(false);
      });
    } else {
      const connectedPage = pagesData?.filter((page) => page.connected === true);
      if (connectedPage?.length > 0) {
        toast.error(`Your Bot Connected To ${connectedPage[0].name} Page`);
        return;
      }
  
      if (selectedFacebookPage?.facebook_channel_id) {
        setPageLoader(true);
        updateOne({
          ...selectedFacebookPage,
        }).then((data) => {
          console.log("data", data);
          toast.success(
            `Your Bot Connected To (${selectedFacebookPage?.name})  Page Successfully`
          );
          setPageLoader(false);
          handleUpdateselectedFacebookPage({ ...data, connected: true });
          console.log("here");
          const newPagesData = [...pagesData]?.map((prevPage) => {
            if (prevPage.id === data.pageId) {
              prevPage.connected = true;
              prevPage.facebook_channel_id = data.facebook_channel_id;
            }
            return { ...prevPage };
          });
  
          setPagesData([...newPagesData]);
        });
      } else if (selectedFacebookPage) {
        setPageLoader(true);
        createOne({
          bot_id: bot.bot_id,
          pageId: selectedFacebookPage.pageId,
          channel: channel,
          page_token: selectedFacebookPage.page_token,
          name: selectedFacebookPage?.name || null,
        }).then((data) => {
          if(data?.message) {
            toast.error(`Page ${selectedFacebookPage.name} is already connected to another bot`);
            setPageLoader(false);
            return;
          }
          toast.success("Your bot connected to the selected page successfully");
          handleUpdateselectedFacebookPage({ ...data, connected: true, facebook_channel_id: data.facebook_channel_id });
          setPageLoader(false);
          setPagesData((prev) => {
            const updatedPages = [...prev]?.map((prevPage) => {
              if (prevPage.id === data.pageId) {
                prevPage.connected = true;
                prevPage.facebook_channel_id = data.facebook_channel_id;
              }
              return { ...prevPage };
            });
            return [...updatedPages];
          });
          setIsModalOpen(false);
        });
      }
      
    }
    localStorage.removeItem("getFacebookPages");
    window.location.hash = "";
  };

  const handleConnectFBAndInstagram = async (
    selectedFacebookPage: TConnectedPage
  ) => {
    setPageLoader(true);
    createOne({
      bot_id: bot.bot_id,
      pageId: selectedFacebookPage.pageId,
      channel: channel,
      page_token: selectedFacebookPage.page_token,
      name: selectedFacebookPage?.name || null,
    }).then((data) => {
      if(data?.message) {
        toast.error(`Page ${selectedFacebookPage.name} is already connected to another bot`);
        setPageLoader(false);
        return;
      }
      createOne({
        bot_id: bot.bot_id,
        pageId: selectedFacebookPage.instagram_business_account.id,
        channel: "instagram",
        page_token:
          selectedFacebookPage.instagram_business_account.access_token,
        name: selectedFacebookPage.instagram_business_account.name,
      }).then((data2) => {
        if(data2.message) {
          toast.error(`Page ${selectedFacebookPage.instagram_business_account.name} is already connected to another bot`);
          setPageLoader(false);
          return;
        }
        handleUpdateselectedFacebookPage({
          ...selectedFacebookPage,
          connected: true,
          facebook_channel_id: data.facebook_channel_id,
          instagram_business_account: {
            ...selectedFacebookPage.instagram_business_account,
            connected: true,
            facebook_channel_id: data2.facebook_channel_id,
          },
        });
        setPageLoader(false);
        setPagesData((prev) => {
          const updatedPages = [...prev]?.map((prevPage) => {
            if (prevPage.id === data.pageId) {
              prevPage.instagram_business_account = {
                ...prevPage.instagram_business_account,
                connected: true,
                facebook_channel_id: data2.facebook_channel_id,
              };
            }
            return { ...prevPage };
          });
          return [...updatedPages];
        });
      });

      toast.success("Your bot connected to the selected page successfully");
      handleUpdateselectedFacebookPage({ ...data, connected: true, 
          facebook_channel_id: data.facebook_channel_id,
       });
      setPageLoader(false);
      setPagesData((prev) => {
        const updatedPages = [...prev]?.map((prevPage) => {
          if (prevPage.id === data.pageId) {
            prevPage.connected = true;
            prevPage.facebook_channel_id = data.facebook_channel_id;
          }
          return { ...prevPage };
        });
        return [...updatedPages];
      });
      setIsModalOpen(false);
    });
    localStorage.removeItem("getFacebookPages");
  };

  return (
    <div>
      {selectedFacebookPage && (
        <ConnectModal
          selectedFacebookPage={selectedFacebookPage}
          handleCloseModal={() => setIsModalOpen(false)}
          isModalOpen={isModalOpen}
          handleConnectFacebookPage={handleConnectFacebookPages}
          handleConnectFBAndInstagram={handleConnectFBAndInstagram}
          handleDisconnectFacebookPage={handleDisconnectFacebookPage}
          handleDisconnectFacebookPageAndInstagram={handleDisconnectFacebookPageAndInstagram}
          loading={pageLoader || loading}
        />
      )}
      {pagesData?.length > 0 && (
        <div className="flex flex-col gap-3">
          {pagesData?.map((page) => {
            return (
              <div key={page.id}>
                <div className="flex items-center justify-start gap-1">
                  <Checkbox
                    name={page.name}
                    key={page.id}
                    label={page.name}
                    checked={
                      selectedFacebookPage
                        ? selectedFacebookPage?.pageId === page.id
                        : false
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        const newPage = { ...page };
                        const selectedPage = {
                          facebook_channel_id: newPage?.facebook_channel_id,
                          bot_id: bot.bot_id,
                          pageId: newPage.id,
                          page_token: newPage.access_token,
                          channel: channel,
                          name: newPage?.name,
                          connected: newPage?.connected,
                          instagram_business_account:
                            newPage.instagram_business_account,
                        };
                        handleUpdateselectedFacebookPage({ ...selectedPage });
                      } else {
                        handleUpdateselectedFacebookPage(null);
                      }
                    }}
                  />
                  <FacebookIcon className="w-3" />
                  <p className="text-green-500 text-xs">
                    {page.connected ? `Connected` : ""}
                  </p>
                </div>
                {page.instagram_business_account ? (
                  <div className="text-xs flex gap-2 items-center ml-5">
                    {`- ${page.instagram_business_account.name}`}
                    <InstagramIcon className="w-3" />
                    <p className="text-green-500 text-xs">
                      {page.instagram_business_account.connected
                        ? `Connected`
                        : ""}
                    </p>
                  </div>
                ) : (
                  ""
                )}
              </div>
            );
          })}
          {selectedFacebookPage?.connected ? (
            <Button
              className="bg-red-500 hover:bg-red-700"
              disabled={pageLoader || loading}
              onClick={() => setIsModalOpen(true)}
            >
              
                Disconnect {selectedFacebookPage?.name}
            
            </Button>
          ) : (
            <Button
              onClick={() => setIsModalOpen(true)}
              disabled={pageLoader || loading || !selectedFacebookPage}
            >
           Connect {
                  selectedFacebookPage ? selectedFacebookPage?.name : ""
                }
              
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

const ConnectModal = ({
  selectedFacebookPage,
  isModalOpen,
  handleCloseModal,
  handleConnectFacebookPage,
  handleConnectFBAndInstagram,
  handleDisconnectFacebookPage,
  handleDisconnectFacebookPageAndInstagram,
  loading
}: {
  selectedFacebookPage: TConnectedPage;
  isModalOpen: boolean;
  handleCloseModal: () => void;
  handleConnectFacebookPage: (page: TConnectedPage, isInstagram?: boolean) => void;
  handleConnectFBAndInstagram: (page: TConnectedPage) => void;
  handleDisconnectFacebookPage: (page: TConnectedPage) => void;
  handleDisconnectFacebookPageAndInstagram: (page: TConnectedPage, instagramOnly: boolean) => void;
  loading: boolean;
}) => {
  return (
    <FullModal
      title="Connect Facebook Page"
      onClose={handleCloseModal}
      isOpen={isModalOpen}
    >
      <div>
        Are you sure you want to connect {selectedFacebookPage?.name} page?
      </div>
      <Separator className="mt-5 mb-2" />
      <div className="flex gap-3 justify-end">
        <Button onClick={handleCloseModal} variant="outline" className="">
          Cancel
        </Button>
        {selectedFacebookPage?.connected && 
        selectedFacebookPage?.instagram_business_account && 
        !selectedFacebookPage?.instagram_business_account?.connected ? (
          <Button
            onClick={() => handleConnectFacebookPage(selectedFacebookPage, true)}
            loading={loading}
          >
            Connect Instagram
          </Button>
        ) : ""}
        {selectedFacebookPage?.connected && 
        (!selectedFacebookPage?.instagram_business_account || (selectedFacebookPage?.instagram_business_account && !selectedFacebookPage?.instagram_business_account?.connected))
        ? (
          <Button
            onClick={() => handleDisconnectFacebookPage(selectedFacebookPage)}
            className="bg-red-500 hover:bg-red-700"
            loading={loading}
          >
            Disconnect Facebook Page
          </Button>
        ) : (
          ""
        )}

        {selectedFacebookPage?.instagram_business_account &&
        selectedFacebookPage?.instagram_business_account?.connected ? (
          <>
          <Button
            onClick={() => handleDisconnectFacebookPageAndInstagram(selectedFacebookPage, true)}
            className="bg-red-500 hover:bg-red-700"
            loading={loading}
          >
            Disconnect Instagram Only
          </Button>
          <Button
            onClick={() => handleDisconnectFacebookPageAndInstagram(selectedFacebookPage, false)}
            className="bg-red-500 hover:bg-red-700"
            loading={loading}
          >
            Disconnect Facebook And Instagram
          </Button>
        </>
        )
          : ""
        }
        {!selectedFacebookPage?.connected ? (
        <Button
          onClick={() => handleConnectFacebookPage(selectedFacebookPage)}
          className="bg-blue-500 hover:bg-blue-700"
          loading={loading}
        >
          Connect Facebook Page
        </Button>
        ) : ""}
        {!selectedFacebookPage?.connected  && selectedFacebookPage?.instagram_business_account ? (
          <Button
            onClick={() => handleConnectFBAndInstagram(selectedFacebookPage)}
            className="bg-violet-500 hover:bg-violet-700"
            loading={loading}
          >
            Connect Facebook And Instagram 
          </Button>
        ) : (
          ""
        )}
      </div>
    </FullModal>
  );
};

export default FacebookPages;
