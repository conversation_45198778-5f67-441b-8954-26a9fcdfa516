import { MainTable } from "common/components/tables/main.table";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useLogStore from "store/logs/logs.store";
import { ILog } from "store/logs/logs.types";
import { formatDistanceToNow } from 'date-fns';

const LogsTable = () => {
  const { get_all_logs, LOGS, loading } = useLogStore();
  const { bot } = useBotStore();
const [updatedLogs,setUpdatedLogs] = useState<ILog[]>([])
  useEffect(() => {
    if (get_all_logs && bot.bot_id) {
      get_all_logs(bot.bot_id);
    }
  }, [get_all_logs]);

  useEffect(()=>{
 if(LOGS.length>0){
  const updatedLogs = LOGS.map((log)=>{
  const timestamp = log.createdAt;
  let timeAgo = '';

  if (timestamp) {
    const date = new Date(timestamp);
    timeAgo = formatDistanceToNow(date, { addSuffix: true });
  }
    return {
      ...log,
      log: `${log?.bot_designer_user?.email || ''} ${log.action_type.toLowerCase()}d ${log.category} ${log.log}`,
      createdAt:timeAgo
    }
  })
  setUpdatedLogs(updatedLogs)
 }
  },[LOGS])

  return (
    <MainTable
      key={"Logs"}
      loading={false}
      data={updatedLogs}
      columns={[

        {
          name: "Log",
          key: "log",
        },
        {
          name: "Category",
          key: "category",
        },
        {
          name: "Time",
          key: "createdAt",
        },
      ]}
      itemsPerPage={10}
    />
  );
};

export default LogsTable;
