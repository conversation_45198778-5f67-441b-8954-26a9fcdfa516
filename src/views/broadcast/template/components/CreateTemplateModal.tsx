import { FullModal } from "common/components/modals";
import useBotStore from "store/bot/bot.store";
import React, { Dispatch, useEffect, useState } from "react";
import {
  TCteadedTemplateData,
  TerrorsDefault,
  TtemplateDataDefault,
  errorsDefault,
  templateDataDefault,
} from "views/broadcast/broadcastTypes/createTemplate.types";
import CreateTemplateForm from "./CreateTemplateForm";
import templateJsonHelper from "views/broadcast/templateJson.helper";
import { Timage } from "..";
import { waMedia } from "apis/whatsapp.api";
import { uploadBotImages } from "apis/file.api";

interface CreateTemplateModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  onCreateTemplate: (template: TCteadedTemplateData) => void;
  loading: boolean;
  images: Timage;
  setImages: React.Dispatch<React.SetStateAction<Timage>>;
}

export const CreateTemplateModal: React.FC<CreateTemplateModalProps> = ({
  showModal,
  setShowModal,
  onCreateTemplate,
  loading,
  images,
  setImages,
}) => {
  const bot = useBotStore((state) => state.bot);
  const [templateData, setTemplateData] =
    useState<TtemplateDataDefault>(templateDataDefault);
  const [errors, setErrors] = useState<TerrorsDefault>(errorsDefault);
  const [createDisabled, setCreateDisabled] = useState(true);
  const [cLoading, setCLoading] = useState(false);

  const handleClose = () => {
    setShowModal(false);
  };

  const onSaveHandler = async () => {
    if (createDisabled) return;
    try {
      setCLoading(true);
      let imageHandler;
      if (images && templateData.image) {
        uploadBotImages(images);
        imageHandler = await waMedia(images, bot.bot_id);
      }
      const template2: TCteadedTemplateData =
        templateJsonHelper.onGenerateJson2(templateData, imageHandler);
      onCreateTemplate(template2);
    } catch (error) {
    } finally {
      setCLoading(false);
    }
  };


  return (
    <FullModal
      title={"Create Template"}
      isOpen={showModal}
      onClose={handleClose}
      //   disabled={!isChanged}
      onSave={onSaveHandler}
      loading={loading || cLoading}
      footer
    >
      <>
        <CreateTemplateForm
          images={images}
          setImages={setImages}
          setCreateDisabled={setCreateDisabled}
          setErrors={setErrors}
          errors={errors}
          templateData={templateData}
          setTemplateData={setTemplateData}
        />
      </>
    </FullModal>
  );
};
