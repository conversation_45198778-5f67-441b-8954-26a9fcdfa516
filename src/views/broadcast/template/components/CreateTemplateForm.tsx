import { Input } from "common/ui/inputs/input";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { Textarea } from "common/ui/inputs/textarea";
import { Checkbox } from "common/ui/inputs/checkbox";
import {
  TerrorsDefault,
  TtemplateDataDefault,
} from "views/broadcast/broadcastTypes/createTemplate.types";
import { X } from "lucide-react";
import { Button } from "common/ui/button";
import helper from "views/broadcast/helper";

import useBotStore from "store/bot/bot.store";
import useDialogStore from "store/dialog/dialog.store";
import generateStorageId from "helpers/generateStorageId";
import ImageUplaodOrUrl from "views/broadcast/boradcasts/components/createBroadcastComponents/ImageUplaodOrUrl";
import toast from "react-hot-toast";
import { WaMediaInput } from "./waMediaInput";
import { Timage } from "..";
import { Switch } from "common/ui/inputs/switch";
type Tevent = React.ChangeEvent<HTMLInputElement>;
type TeventTextAria = React.ChangeEvent<HTMLTextAreaElement>;
type CreateTemplateFormProps = {
  setIsCSV?: React.Dispatch<React.SetStateAction<boolean>>;
  isCSV?: boolean;
  templateData: TtemplateDataDefault;
  setTemplateData: Dispatch<SetStateAction<TtemplateDataDefault>>;
  errors: TerrorsDefault;
  setErrors: Dispatch<SetStateAction<TerrorsDefault>>;
  setCreateDisabled: Dispatch<SetStateAction<boolean>>;
  readMode?: boolean;
  images: Timage;
  setImages: React.Dispatch<React.SetStateAction<Timage>>;
  setImageUrlData?: React.Dispatch<React.SetStateAction<any>>;
  ImageUrlData?: { show_url: boolean; image_url: string };
};

const CreateTemplateForm: React.FC<CreateTemplateFormProps> = ({
  templateData,
  setTemplateData,
  errors,
  setErrors,
  setCreateDisabled,
  readMode,
  images,
  setImages,
  setImageUrlData,
  ImageUrlData,
  setIsCSV,
  isCSV,
}) => {
  const { bot } = useBotStore();
  const { getAll, dialogs } = useDialogStore();
  useEffect(() => {
    if (bot.bot_id) {
      getAll(bot.bot_id);
    }
  }, [bot]);

  const validateInputs = () => {
    let preErrors = { ...errors };
    if (templateData.templateName.trim().length === 0) {
      preErrors = {
        ...preErrors,
        templateName: "This field is required",
      };
    } else {
      preErrors = {
        ...preErrors,
        templateName: "",
      };
    }

    if (!templateData.body) {
      preErrors = {
        ...preErrors,
        body: "this field is required",
      };
    } else {
      preErrors = {
        ...preErrors,
        body: "",
      };
    }

    if (templateData.bodyVariables.length > 0) {
      const bodyVariables = templateData.bodyVariables.map(
        (variable) => Object.values(variable)[0]
      );
      const bodyVariablesFilled = bodyVariables.filter((variable) => variable);
      if (bodyVariablesFilled.length !== bodyVariables.length) {
        preErrors = {
          ...preErrors,
          bodyVariables: "You must fill all variables examples",
        };
      } else {
        preErrors = {
          ...preErrors,
          bodyVariables: "",
        };
      }
    } else {
      preErrors = {
        ...preErrors,
        bodyVariables: "",
      };
    }

    if (templateData.headerVariables.length > 0) {
      const headerVariables = templateData.headerVariables.map(
        (variable) => Object.values(variable)[0]
      );
      const headerVariablesFilled = headerVariables.filter(
        (variable) => variable
      );
      if (headerVariablesFilled.length !== headerVariables.length) {
        preErrors = {
          ...preErrors,
          headerVariables: "You must fill all variables examples",
        };
      } else {
        preErrors = {
          ...preErrors,
          headerVariables: "",
        };
      }
    } else if (templateData.headerVariables.length === 0) {
      preErrors = {
        ...preErrors,
        headerVariables: "",
      };
    }

    if (templateData.header) {
      if (templateData.headerType === "text") {
        preErrors = {
          ...preErrors,
          image: "",
          video: "",
          document: "",
          location: "",
        };
        if (!templateData.headerText) {
          preErrors = {
            ...preErrors,
            headerText: "This field is required",
          };
        } else {
          preErrors = {
            ...preErrors,
            headerText: "",
          };
        }
      } else if (templateData.headerType.toLowerCase() === "media") {
        preErrors = {
          ...errors,
          headerText: "",
          image: "",
          video: "",
          document: "",
          location: "",
        };
        if (!templateData[templateData.mediaType.toLowerCase()]) {
          preErrors = {
            ...preErrors,
            [templateData.mediaType.toLowerCase()]: "This field is required",
          };
        } else {
          preErrors = {
            ...preErrors,
            [templateData.mediaType.toLowerCase()]: "",
          };
        }
      } else if (templateData.headerType.toLowerCase() === "location") {
        preErrors = {
          ...errors,
          headerText: "",
          image: "",
          video: "",
          document: "",
        };

        if (
          !templateData.location.address ||
          !templateData.location.latitude ||
          !templateData.location.longitude ||
          !templateData.location.name
        ) {
          preErrors = {
            ...errors,
            location: "you must specify location",
            headerText: "",
            image: "",
            video: "",
            document: "",
          };
        } else {
          preErrors = {
            ...errors,
            headerText: "",
            image: "",
            video: "",
            document: "",
            location: "",
          };
        }
      }
    }

    setErrors({ ...preErrors });
  };

  const acceptMediaTypes =
    templateData.mediaType === "IMAGE"
      ? "image/jpeg, image/jpg, image/png"
      : templateData.mediaType === "VIDEO"
      ? "video/mp4"
      : "application/pdf";

  const isArabic = (text: string) => {
    const arabic = /[\u0600-\u06FF]/;
    return arabic.test(text);
  };

  const checkForLangMatch = (name: string, value: string) => {
    const textFields = ["body", "headerText", "footerText"];
    if (textFields.includes(name)) {
      const textLang = isArabic(value) ? "ar" : "en";
      if (textLang !== templateData.language) {
        setErrors({
          ...errors,
          [name]: `The ${name} language doesn't match the template language`,
        });
      }
    }
  };

  function processTextForVariables(
    text: string,
    name: string,
    limit = Infinity
  ) {
    const pattern = /\{\{(\d+)\}\}/g;
    let replacements = [];
    let numbersSeen = new Set();
    let error = "";
    let match;

    let existingVariables =
      templateData[name.replace("Text", "") + "Variables"];
    const existingVariablesNumbers = existingVariables.map((variable) =>
      parseInt(Object.keys(variable)[0].replace("Example for ", ""))
    );

    while ((match = pattern.exec(text)) !== null) {
      const number = parseInt(match[1]);

      if (number > limit) {
        error = `You can only use ${limit} variables`;
        continue;
      }

      if (number <= numbersSeen.size + 1) {
        if (!numbersSeen.has(number)) {
          numbersSeen.add(number);
          if (!existingVariablesNumbers.includes(number)) {
            replacements.push({ [`Example for ${number}`]: "" });
          } else {
            const existingVariable = existingVariables.find(
              (variable) =>
                parseInt(
                  Object.keys(variable)[0].replace("Example for ", "")
                ) === number
            );
            replacements.push(existingVariable);
          }
        } else {
          error = `Duplicate variable: ${number}`;
          break;
        }
      } else {
        error = `Missing variable or wrong order: ${numbersSeen.size + 1}`;
        break;
      }
    }

    if (error) {
      setErrors({
        ...errors,
        [name]: error,
      });
    } else {
      setErrors({
        ...errors,
        [name]: "",
      });
      error = "";
    }

    return replacements;
  }

  const handleBodyChange = (event: Tevent | TeventTextAria) => {
    const { value } = event.target;
    const variables = processTextForVariables(value, "body");
    if (value.length > 1024) return;
    checkForLangMatch("body", value);
    if (!value) {
      setErrors({
        ...errors,
        body: "This field is required",
      });
    }
    setTemplateData({
      ...templateData,
      body: value,
      bodyVariables: variables,
    });
  };

  const handleInputChange = (event: Tevent) => {
    const { name, value } = event.target;

    checkForLangMatch(name, value);
    if (name === "mediaType") {
      setTemplateData({
        ...templateData,
        [name]: value,
        image: "",
        video: "",
        document: "",
      });
    } else {
      setTemplateData({
        ...templateData,
        [name]: value,
      });
    }
  };

  const handleVariablesChange = (event: Tevent, variable, index: number) => {
    const { name, value } = event.target;
    const variables = JSON.parse(JSON.stringify([...templateData[name]]));
    variables[index][Object.keys(variable)[0]] = value;
    setTemplateData({
      ...templateData,
      [name]: variables,
    });
  };

  const handleHeaderTextChange = (event: Tevent | TeventTextAria) => {
    const { value } = event.target;
    const variables = processTextForVariables(value, "headerText", 1);
    checkForLangMatch("headerText", value);
    setTemplateData({
      ...templateData,
      headerText: value,
      headerVariables: variables,
    });
  };

  const handleFooterTextChange = (event: Tevent | TeventTextAria) => {
    const { value } = event.target;
    checkForLangMatch("footerText", value);
    if (!value) {
      setErrors({
        ...errors,
        footerText: "This field is required",
      });
    } else {
      setErrors({
        ...errors,
        footerText: "",
      });
    }
    setTemplateData({
      ...templateData,
      footerText: value,
    });
  };

  const handleIsFooterChange = (event: Tevent) => {
    setTemplateData({
      ...templateData,
      footer: event.target.checked,
    });
    if (event.target.checked) {
      if (!templateData.footerText) {
        setErrors({
          ...errors,
          footerText: "This field is required",
        });
      }
    } else {
      setErrors({
        ...errors,
        footerText: "",
      });
    }
  };

  function validateName(name: string) {
    return /^[a-z0-9_]+$/.test(name);
  }

  const handleButtonVariablesChange = (
    event: Tevent | Partial<Tevent>,
    variable,
    key: number,
    index: number
  ) => {
    let preErrors = { ...errors };

    const { name, value } = event.target;
    const urlPattern = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
    urlPattern.test(value);

    if (!value) {
      preErrors = {
        ...preErrors,
        buttonVariables: "you must fill all button variables",
      };
    } else {
      preErrors = {
        ...preErrors,
        buttonVariables: "",
      };
    }
    if (Object.keys(variable)[key]?.toLowerCase() === "url") {
      if (!helper.checkUrl(value)) {
        preErrors = {
          ...preErrors,
          buttonVariables: "Not Valid Url",
        };
      } else if (variable.text === "") {
        preErrors = {
          ...preErrors,
          buttonVariables: "button text is empty",
        };
      } else {
        preErrors = {
          ...preErrors,
          buttonVariables: "",
        };
      }
    }

    if (Object.keys(variable)[key]?.toLowerCase() === "phone_number") {
      if (!helper.checkPhone(value) || value.length < 7) {
        preErrors = {
          ...preErrors,
          button: "Not Valid Phone Number",
        };
      } else if (variable.text === "") {
        preErrors = {
          ...preErrors,
          button: "button text is empty",
        };
      } else {
        preErrors = {
          ...preErrors,
          button: "",
        };
      }
    }

    const variables = JSON.parse(JSON.stringify([...templateData[name]]));
    variables[index][Object.keys(variable)[key]] = value;

    setTemplateData({
      ...templateData,
      [name]: variables,
    });

    setErrors({ ...preErrors });
  };

  const handleIsButtonChange = (event: Tevent) => {
    let preErrors = { ...errors };
    setTemplateData({
      ...templateData,
      button: event.target.checked,
    });
    if (event.target.checked) {
      templateData.buttonVariables.map((button) => {
        if (button.type === "PHONE_NUMBER" && !button.phone_number) {
          preErrors = {
            ...preErrors,
            buttonVariables: "you must fill all button variables",
          };
        } else if (button.type === "URL" && !button.url) {
          preErrors = {
            ...preErrors,
            buttonVariables: "you must fill all button variables",
          };
        }
      });
    } else {
      preErrors = {
        ...preErrors,
        buttonVariables: "",
      };
    }

    setErrors({ ...errors });
  };

  useEffect(() => {
    if (readMode) {
      let preErrors = { ...errors };

      if (templateData.bodyVariables.length > 0) {
        const bodyVariables = templateData.bodyVariables.map(
          (variable) => Object.values(variable)[0]
        );
        const bodyVariablesFilled = bodyVariables.filter(
          (variable) => variable
        );
        if (bodyVariablesFilled.length !== bodyVariables.length) {
          preErrors = {
            ...preErrors,
            bodyVariables: "You must fill all variables examples",
          };
        } else {
          preErrors = {
            ...preErrors,
            bodyVariables: "",
          };
        }
      } else {
        preErrors = {
          ...preErrors,
          bodyVariables: "",
        };
      }

      if (templateData.button && templateData.buttonVariables.length > 0) {
        const buttonVariables = templateData.buttonVariables.map(
          (variable) => Object.values(variable)[0]
        );
        const buttonVariablesText = templateData.buttonVariables.map(
          (variable) => Object.values(variable)[2]
        );
        const buttonVariablesFilled = buttonVariables.filter(
          (variable) => variable
        );
        const buttonVariablesFilledText = buttonVariablesText.filter(
          (variable) => variable
        );

        if (
          buttonVariablesFilled.length !== buttonVariables.length ||
          buttonVariablesFilledText.length !== buttonVariablesText.length
        ) {
          preErrors = {
            ...preErrors,
            buttonVariables: `You must fill all button Variables ${
              readMode ? "value" : "example"
            } `,
          };
        } else {
          // preErrors = {
          //   ...preErrors,
          //   buttonVariables: "",
          // };
        }
      }

      if (templateData.header && templateData.headerVariables.length > 0) {
        const headerVariables = templateData.headerVariables.map(
          (variable) => Object.values(variable)[0]
        );
        const headerVariablesFilled = headerVariables.filter(
          (variable) => variable
        );

        if (headerVariablesFilled.length !== headerVariables.length) {
          preErrors = {
            ...preErrors,
            headerVariables: "You must fill all variables examples",
          };
        } else {
          preErrors = {
            ...preErrors,
            headerVariables: "",
          };
        }
      } else {
        preErrors = {
          ...preErrors,
          headerVariables: "",
          headerText: "",
          image: "",
          video: "",
          document: "",
        };
      }

      if (templateData.header) {
        if (templateData.headerType === "text") {
          if (!templateData.headerText) {
            preErrors = {
              ...preErrors,
              headerText: "Header Text is required",
            };
          } else {
            preErrors = {
              ...preErrors,
              headerText: "",
              image: "",
              video: "",
              document: "",
            };
          }
        } else if (templateData.headerType.toLowerCase() === "media") {
          if (!templateData[templateData.mediaType.toLowerCase()]) {
            preErrors = {
              ...preErrors,
              [templateData.mediaType.toLowerCase()]: "Media Field is required",
            };
          } else {
            if (
              templateData.mediaType.toLocaleLowerCase() === "image" &&
              !templateData.image
            ) {
              preErrors = {
                ...preErrors,
                [templateData.mediaType.toLowerCase()]:
                  "The image URL is Not valid.",
              };
            } else {
              preErrors = {
                ...preErrors,
                [templateData.mediaType.toLowerCase()]: "",
              };
            }
          }
        }
      } else {
        setErrors({
          ...preErrors,
          headerText: "",
          image: "",
          video: "",
          document: "",
        });
      }

      setErrors({ ...preErrors });
    }
  }, [readMode, templateData]);

  const isThereErrors = () => {
    const errorsValues = Object.values(errors);
    const errorsFilled = errorsValues.filter((error) => error);
    if (errorsFilled.length > 0) {
      return true;
    }
    return false;
  };

  useEffect(() => {
    const isErrors = isThereErrors();
    if (isErrors || !templateData.body || !templateData.templateName) {
      setCreateDisabled(true);
    } else {
      setCreateDisabled(false);
    }
  }, [errors]);

  const handleNewButton = () => {
    if (templateData.buttonVariables.length < 10) {
      let prevButtonVariables = [...templateData.buttonVariables];

      const newButton = {
        type: "QUICK_REPLY",
        text: "text",
      };
      setTemplateData({
        ...templateData,
        buttonVariables: [...prevButtonVariables, newButton],
      });
      setErrors({
        ...errors,
        buttonVariables: "",
      });
    }
  };
  const handleRemoveButton = (index: number) => {
    const prevButtonVariables = [...templateData.buttonVariables];
    prevButtonVariables.splice(index, 1);
    prevButtonVariables.map((button) => {
      if (button?.phone_number && button?.phone_number === "") {
        setErrors({
          ...errors,
          buttonVariables: "phone number is required",
        });
      } else if (button?.url && button?.url === "") {
        setErrors({
          ...errors,
          buttonVariables: "Url is required",
        });
      } else {
        setErrors({
          ...errors,
          buttonVariables: "",
        });
      }
    });
    setTemplateData({
      ...templateData,
      buttonVariables: [...prevButtonVariables],
    });
  };

  useEffect(() => {
    if (!readMode) {
      validateInputs();
    }
  }, [templateData]);

  const handleFileURLChange = (name: string, value: string) => {
    let preErrors = { ...errors };

    // const { name, value } = event.target;

    if (!value) {
      preErrors = {
        ...preErrors,
        [name]: "This field is required",
      };
    } else {
      preErrors = {
        ...preErrors,
        [name]: "",
      };
    }
    setTemplateData({
      ...templateData,
      [name]: value,
      headerVariables: [`example for ${value}`],
    });

    if (name.toLowerCase() === "image") {
      preErrors = {
        ...preErrors,
        video: "",
        document: "",
      };
    }

    if (name.toLowerCase() === "video") {
      preErrors = {
        ...preErrors,
        image: "",
        document: "",
      };
    }
    if (name.toLowerCase() === "document") {
      preErrors = {
        ...preErrors,
        image: "",
        video: "",
      };
    }
    setErrors({ ...preErrors });
  };
  const uploadImg = async (image: File) => {
    const key = templateData.mediaType.toLowerCase();

    if (!image?.type.toLowerCase().includes(key) && key !== "document") {
      toast.error(`This field requires  ${key} not ${image?.type}`);
      return false;
    } else if (key === "document" && !image?.type.includes("pdf")) {
      toast.error(`This field requires  pdf not ${image?.type}`);
      return false;
    }

    const formData = new FormData();
    formData.append("file", image);
    const path = `Bots/${bot.file_name}/waMediaVonage/${generateStorageId()}.${
      image?.type.split("/")[1]
    }`;

    const imageData = {
      url: "https://infotointell.fra1.digitaloceanspaces.com/" + path,
      path,
      formData,
      image,
      file_type: image?.type,
      name: image?.name,
      isVideo: image?.type.includes("video"),
      isDocument: image?.type.includes("pdf"),
      isImage: image?.type.includes("image"),
    };
    setImages(imageData);
    setTemplateData((prev) => ({ ...prev, [key]: imageData.url }));
  };

  const handleSwitchChange = (v) => {
    setIsCSV(!isCSV);
    if (!isCSV) {
      setErrors({
        ...errors,
        [`bodyVariables`]: "",
      });
    }
  };
  return (
    <div className="">
      {readMode ? null : (
        <div className="my-1 mb-5">
          <Input
            disabled={readMode}
            title="Template Name"
            type="text"
            name="templateName"
            placeholder="Enter Template Name"
            value={templateData.templateName}
            onChange={(e: Tevent) => {
              // if (!validateName(e.target.value)) return;
              handleInputChange(e);
            }}
          />
          {errors.templateName && (
            <p className="text-red-500">{errors.templateName}</p>
          )}
        </div>
      )}

      <div className="my-1 mb-5">
        <label className={`block`} htmlFor={"language"}>
          {readMode ? "Language" : "Select a Language"}{" "}
        </label>
        <Select
          disabled={readMode}
          key={"language"}
          name="language"
          onValueChange={(value) => {
            setTemplateData({ ...templateData, language: value });
          }}
          value={templateData.language}
        >
          <SelectTrigger className="w-full">
            <SelectValue
              placeholder={readMode ? "language" : "Select a language"}
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem key={"en"} value={"en"}>
              {"English"}
            </SelectItem>
            <SelectItem key={"ar"} value={"ar"}>
              {"Arabic"}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="my-1 mb-5">
        <label className={`block`} htmlFor={"category"}>
          {readMode ? "Category" : "Select a Category"}{" "}
        </label>
        <Select
          disabled={readMode}
          key={"category"}
          onValueChange={(value) => {
            setTemplateData({ ...templateData, category: value });
          }}
          value={templateData.category}
        >
          <SelectTrigger className="w-full">
            <SelectValue
              placeholder={readMode ? "category" : "Select a category"}
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem key={"UTILITY"} value={"UTILITY"}>
              {"UTILITY"}
            </SelectItem>
            <SelectItem key={"MARKETING"} value={"MARKETING"}>
              {"MARKETING"}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="my-1 mb-5">
        <label className={`block`} htmlFor={"body_broadcast"}>
          {"Body text"}{" "}
        </label>
        <Textarea
          id="body_broadcast"
          name="body"
          placeholder="welcome Mr.{{1}} to our {{2}} ..."
          value={templateData.body}
          onChange={(e) => {
            handleBodyChange(e);
          }}
          rows={5}
          maxLength={1024}
          disabled={readMode}
        />
        {errors.body && <p className="text-red-500">{errors.body}</p>}

        {templateData.bodyVariables.length > 0 && (
          <div>
            <h4>
              {readMode ? "" : "Variables Examples"}
              <div>{readMode ? null : "Give an Example for each variable"}</div>
            </h4>

            <div>
              {readMode ? (
                <>
                  <p className="text-sm font-bold mt-5">
                    {" "}
                    <span className="text-yellow-300">Note:</span> you can
                    import body variables from CSV in the next page
                  </p>
                  <Switch
                    label={
                      isCSV ? `use default body variables` : `Import From CSV`
                    }
                    name={`enable custom response`}
                    checked={isCSV}
                    onChange={handleSwitchChange}
                  />
                </>
              ) : null}
              {isCSV ? null : (
                <>
                  {templateData.bodyVariables.map((variable, index) => (
                    <div key={index}>
                      {readMode ? null : (
                        <label>{Object.keys(variable)[0]}</label>
                      )}

                      <Input
                        title={
                          readMode
                            ? ` value for variable ${index + 1}`
                            : `variable ${index + 1}`
                        }
                        aria-label="bodyVariables"
                        type="text"
                        name="bodyVariables"
                        placeholder={`variable ${index + 1}`}
                        onChange={(e: Tevent) => {
                          handleVariablesChange(e, variable, index);
                        }}
                        value={Object.values(variable)[0] as any}
                      />
                    </div>
                  ))}
                  {errors.bodyVariables && (
                    <p className="text-red-500">{errors.bodyVariables}</p>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>
      <div className="my-1 mb-5">
        <div>
          {!readMode ? (
            <>
              <Checkbox
                name="Add Header"
                label="Add Header"
                key={"Add_Header"}
                onChange={(e) => {
                  setTemplateData({
                    ...templateData,
                    header: e.target.checked,
                  });
                }}
              />
            </>
          ) : null}

          {templateData.header && (
            <div className="my-1 mb-2">
              <label htmlFor="header_type">| Header Type</label>

              <Select
                disabled={readMode}
                onValueChange={(value) => {
                  setTemplateData({ ...templateData, headerType: value });
                }}
                value={templateData.headerType}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={
                      readMode ? "headerType" : "Select a headerType"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem key={"text"} value={"text"}>
                    {"Text"}
                  </SelectItem>
                  <SelectItem key={"media"} value={"media"}>
                    {"Media"}
                  </SelectItem>
                  <SelectItem key={"location"} value={"location"}>
                    {"Location"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          {templateData.header && templateData.headerType === "media" && (
            <div>
              <label className="whitespace-nowrap" htmlFor="mediaType">
                | Media Type
              </label>
              <Select
                disabled={readMode}
                onValueChange={(value) => {
                  setTemplateData({ ...templateData, mediaType: value });
                }}
                value={templateData.mediaType}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={readMode ? "mediaType" : "Select a mediaType"}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem key={"IMAGE"} value={"IMAGE"}>
                    {"Image"}
                  </SelectItem>
                  <SelectItem key={"VIDEO"} value={"VIDEO"}>
                    {"Video"}
                  </SelectItem>
                  <SelectItem key={"DOCUMENT"} value={"DOCUMENT"}>
                    {"Document"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        {templateData.header && templateData.headerType === "text" && (
          <div className="flex flex-col gap-1">
            <div>
              <label htmlFor="headerText">
                Header Text
                {!readMode ? (
                  <div className="text-sm">
                    Up to 60 characters is allowed <br />
                  </div>
                ) : null}
              </label>
              <Textarea
                disabled={readMode}
                id="headerText"
                name="headerText"
                placeholder="You can only add one variable {{1}}"
                value={templateData.headerText}
                onChange={(e) => {
                  handleHeaderTextChange(e);
                }}
                rows={2}
                maxLength={60}
              />
            </div>
            {errors.headerText && (
              <p className="text-red-500">{errors.headerText}</p>
            )}
            {templateData.headerVariables.length > 0 && (
              <div>
                <h4>
                  Variables {readMode ? "Values" : "Examples"}
                  <div>
                    Give {readMode ? "a Value" : "an Example"} for each variable
                  </div>
                </h4>
                {errors.headerVariables && (
                  <p className="text-red-500">{errors.headerVariables}</p>
                )}
                <div className="flex flex-wrap gap-1">
                  {templateData.headerVariables.map((variable, index) => (
                    <div className="flex gap-1 flex-col" key={index}>
                      {readMode ? (
                        <label>value</label>
                      ) : (
                        <label>{Object.keys(variable)[0]}</label>
                      )}
                      <Input
                        type="text"
                        name="headerVariables"
                        onChange={(e: Tevent) => {
                          handleVariablesChange(e, variable, index);
                        }}
                        value={Object.values(variable)[0] as any}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        {templateData.header && templateData.headerType === "media" && (
          <div className="flex flex-col gap-1">
            <label htmlFor={templateData.mediaType}>
              {templateData.mediaType} {readMode ? "Value" : "Example"}
              <div className="text-sm ">
                Give {readMode ? "" : "an Example"} url of the{" "}
                {templateData.mediaType} you want to send <br />
                Make sure the url has the type:{" "}
                <span className="text-green-500">{acceptMediaTypes}</span>
              </div>
            </label>

            {readMode ? (
              <ImageUplaodOrUrl
                bot={bot}
                setImages={setImages}
                images={images}
                setImageUrlData={setImageUrlData}
                ImageUrlData={ImageUrlData}
                onChange={handleFileURLChange}
              />
            ) : (
              <WaMediaInput
                images={images}
                video={images?.isVideo}
                isPdf={images?.isDocument}
                accept={templateData?.mediaType.toLowerCase()}
                videoSrc={
                  images?.image ? URL?.createObjectURL(images.image) : ""
                }
                imgSrc={images?.image ? URL?.createObjectURL(images.image) : ""}
                uploadImg={uploadImg}
              />
            )}

            {errors[templateData.mediaType.toLowerCase()] && (
              <p className="text-red-500">
                {errors[templateData.mediaType.toLowerCase()]}
              </p>
            )}
          </div>
        )}

        {templateData.header && templateData.headerType === "location" ? (
          <div className="flex gap-1">
            <Input
              value={templateData?.location?.name}
              onChange={(e) =>
                setTemplateData((prev) => ({
                  ...prev,
                  location: { ...prev.location, name: e.target.value },
                }))
              }
              placeholder="NAME"
              name="NAME"
            />

            <Input
              value={templateData?.location?.address}
              onChange={(e) =>
                setTemplateData((prev) => ({
                  ...prev,
                  location: { ...prev.location, address: e.target.value },
                }))
              }
              placeholder="ADDRESS"
              name="ADDRESS"
            />
            <Input
              value={templateData?.location?.latitude}
              onChange={(e) =>
                setTemplateData((prev) => ({
                  ...prev,
                  location: { ...prev.location, latitude: e.target.value },
                }))
              }
              placeholder="LATITUDE"
              name="LATITUDE"
            />
            <Input
              value={templateData?.location?.longitude}
              onChange={(e) =>
                setTemplateData((prev) => ({
                  ...prev,
                  location: { ...prev.location, longitude: e.target.value },
                }))
              }
              placeholder="LONGITUDE"
              name="LONGITUDE"
            />
          </div>
        ) : null}
        {errors.location && <p className="text-red-500">{errors.location}</p>}
      </div>

      <div className="my-1 mb-5">
        <div>
          {!readMode ? (
            <>
              <Checkbox
                name="Add Footer"
                label="Add Footer"
                key={"Add_Footer"}
                onChange={handleIsFooterChange}
              />
            </>
          ) : null}

          {templateData.footer && (
            <div>
              <label htmlFor="footerText">
                Footer Text
                <div className="text-sm">
                  Up to 60 characters is allowed <br />
                  You can only use text
                </div>
              </label>
              <Textarea
                className="form-control"
                id="footerText"
                name="footerText"
                placeholder="Enter Template Footer"
                value={templateData.footerText}
                onChange={(e) => {
                  handleFooterTextChange(e);
                }}
                rows={2}
                maxLength={60}
              />
              {errors.footerText && (
                <p className="text-red-500">{errors.footerText}</p>
              )}
            </div>
          )}
        </div>
      </div>
      <div className="my-1 mb-5">
        <div>
          {!readMode ? (
            <>
              <Checkbox
                name="Add Buttons"
                label="Buttons"
                key={"Add_Buttons"}
                onChange={handleIsButtonChange}
              />
            </>
          ) : null}

          {templateData.button && (
            <div className="my-1 mb-2">
              {errors.buttonVariables && (
                <p className="text-red-500">{errors.buttonVariables}</p>
              )}
              {errors.button && <p className="text-red-500">{errors.button}</p>}
              <div className="">
                {templateData.buttonVariables.map((variable, index) => (
                  <div
                    className="flex justify-center items-center gap-3 p-4 bg-slate-900 rounded-xl shadow-transparent relative mb-5"
                    key={index}
                  >
                    {index === 0 ? null : (
                      <X
                        onClick={() => handleRemoveButton(index)}
                        className="absolute right-0 top-0 text-red-500 hover:scale-105 cursor-pointer"
                      />
                    )}

                    <div className="w-1/3">
                      <Input
                        title="Button Text"
                        type="text"
                        name="buttonVariables"
                        disabled={readMode}
                        onChange={(e: Tevent) => {
                          handleButtonVariablesChange(e, variable, 2, index);
                        }}
                        value={variable.text as any}
                      />
                    </div>
                    <div className="w-1/3">
                      <label htmlFor="button_type">| Button Type</label>
                      <div className="mt-1">
                        <Select
                          disabled={readMode}
                          onValueChange={(value) => {
                            let updatedButtonVariables = [
                              ...templateData.buttonVariables,
                            ];
                            if (value === "URL") {
                              updatedButtonVariables[index] = {
                                url: "https://example.com/special-offer-opt-in",
                                type: "URL",
                                text: "Yes",
                              };
                            } else if (value === "QUICK_REPLY") {
                              updatedButtonVariables[index] = {
                                quick_reply: "fall back",
                                type: "QUICK_REPLY",
                                text: "Yes",
                              };
                            } else {
                              updatedButtonVariables[index] = {
                                phone_number: "8001111111",
                                type: "PHONE_NUMBER",
                                text: "Yes",
                              };
                            }
                            setTemplateData({
                              ...templateData,
                              buttonVariables: updatedButtonVariables,
                            });
                            let preErrors = { ...errors };
                            preErrors = {
                              ...preErrors,
                              button: "",
                            };
                            setErrors({ ...preErrors });
                          }}
                          value={variable.type}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={
                                readMode ? "ButtonType" : "Select a ButtonType"
                              }
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem key={"URL"} value={"URL"}>
                              {"URL"}
                            </SelectItem>
                            <SelectItem
                              key={"PHONE_NUMBER"}
                              value={"PHONE_NUMBER"}
                            >
                              {"PHONE NUMBER"}
                            </SelectItem>
                            <SelectItem
                              key={"QUICK_REPLY"}
                              value={"QUICK_REPLY"}
                            >
                              {"QUICK REPLY"}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="w-1/3">
                      {variable.type === "QUICK_REPLY" ? (
                        <>
                          {readMode ? (
                            <>
                              <label htmlFor="trigger_id">
                                {" "}
                                Dialog Triggers
                              </label>
                              <div id="trigger_id" className="mt-1">
                                <Select
                                  // disabled={readMode}
                                  onValueChange={(value) => {
                                    const e: Partial<
                                      React.ChangeEvent<HTMLInputElement>
                                    > = {
                                      target: {
                                        name: "buttonVariables",
                                        value: value,
                                      } as EventTarget & HTMLInputElement,
                                    };
                                    handleButtonVariablesChange(
                                      e,
                                      variable,
                                      0,
                                      index
                                    );
                                  }}
                                  // value={variable.type}
                                >
                                  <SelectTrigger className="w-full">
                                    <SelectValue
                                      placeholder={"Select a Trigger"}
                                    />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {dialogs?.map((dialog) => {
                                      return (
                                        <SelectItem
                                          key={dialog.dialog_id}
                                          value={dialog.dialog_id.toString()}
                                        >
                                          {dialog.dialog_name}
                                        </SelectItem>
                                      );
                                    })}
                                  </SelectContent>
                                </Select>
                              </div>
                            </>
                          ) : null}
                        </>
                      ) : (
                        <Input
                          title={
                            variable.type === "URL" ? "URL:" : "Phone Number:"
                          }
                          type="text"
                          name="buttonVariables"
                          onChange={(e: Tevent) => {
                            handleButtonVariablesChange(e, variable, 0, index);
                          }}
                          value={
                            variable.type === "URL"
                              ? variable.url
                              : variable.phone_number
                          }
                        />
                      )}
                    </div>
                  </div>
                ))}
                <div className="w-full flex justify-end items-center">
                  {templateData.buttonVariables?.length < 10 && !readMode ? (
                    <Button onClick={handleNewButton}>ADD Button</Button>
                  ) : null}{" "}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateTemplateForm;
