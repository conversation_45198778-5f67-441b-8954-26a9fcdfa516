import { FullModal } from "common/components/modals";
import { useEffect, useState } from "react";
import { Itemplate } from "../../number.types";

import CreateTemplateForm from "views/broadcast/template/components/CreateTemplateForm";
import {
  Tbuttons,
  TerrorsDefault,
  TtemplateDataDefault,
  errorsDefault,
  templateDataDefault,
} from "views/broadcast/broadcastTypes/createTemplate.types";
import { Input } from "common/ui/inputs/input";

interface ShowTemplateModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  message?: Itemplate;
  setMessageEdit?: (message: Itemplate) => void;
  canAddItem?: boolean;
  templateToShow?: Itemplate;
}

export const ShowTemplateModal: React.FC<ShowTemplateModalProps> = ({
  showModal,
  setShowModal,
  message,
  templateToShow,
}) => {
  function processTextForVariables(
    variableKey: string,
    text: string,
    name: string,
    limit = Infinity
  ) {
    const pattern = /\{\{(\d+)\}\}/g;
    let replacements = [];
    let numbersSeen = new Set();
    let error = "";
    let match;

    let existingVariables = [];
    const existingVariablesNumbers = existingVariables?.map((variable) =>
      parseInt(Object.keys(variable)[0].replace("Example for ", ""))
    );

    while ((match = pattern.exec(text)) !== null) {
      const number = parseInt(match[1]);

      if (number > limit) {
        error = `You can only use ${limit} variables`;
        continue;
      }

      if (number <= numbersSeen.size + 1) {
        if (!numbersSeen.has(number)) {
          numbersSeen.add(number);
          if (!existingVariablesNumbers.includes(number)) {
            replacements.push({ [`Example for ${number}`]: "" });
          } else {
            const existingVariable = existingVariables.find(
              (variable) =>
                parseInt(
                  Object.keys(variable)[0].replace("Example for ", "")
                ) === number
            );
            replacements.push(existingVariable);
          }
        } else {
          error = `Duplicate variable: ${number}`;
          break;
        }
      } else {
        error = `Missing variable or wrong order: ${numbersSeen.size + 1}`;
        break;
      }
    }

    return replacements;
  }

  const [templateData, setTemplateData] =
    useState<TtemplateDataDefault>(templateDataDefault);

  const [errors, setErrors] = useState<TerrorsDefault>(errorsDefault);

  const handleClose = () => {
    setShowModal(false);
  };

  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  useEffect(() => {
    if (templateToShow) {
      setSelectedTemplate(templateToShow);
    }
  }, [templateToShow]);

  type TresBody = {
    type: string;
    format: string;
    text: string;
    media?: boolean;
  };
  type TresHeader = {
    type: string;
    format: string;
    text?: string;
    media?: boolean;
    example?: any;
  };
  useEffect(() => {
    let bodyText: string;
    let header: TresHeader;
    let footer: TresBody;
    let buttons = [];
    let bodyWaVariables = [];
    let headerWaVariables = [];

    selectedTemplate?.components?.map((component) => {
      if (component.type === "BODY") {
        bodyText = component.text;
        bodyWaVariables = component?.example?.body_text;
      }
      if (component.type === "HEADER" && component.format === "TEXT") {
        header = component;
        headerWaVariables = component?.example?.header_text;
      }
      // if (component.type === "HEADER" && component.format !== "TEXT") {
      //   header = component;
      //   header.media = true;
      // }
      if (component.type === "HEADER" && component.format === "IMAGE") {
        header = component;
        header.media = true;
      }
      if (component.type === "FOOTER") {
        footer = component;
      }
      if (component.type?.toLowerCase() === "button") {
        buttons.push(component);
      }
    });

    const filledTemplate = {
      templateName: selectedTemplate?.name,
      category: selectedTemplate?.category,
      language: selectedTemplate?.language,
      header: header?.type ? true : false,
      headerType:
        header?.format === "TEXT"
          ? header.format.toLocaleLowerCase()
          : header?.format !== "TEXT"
          ? "media"
          : "",
      headerText: header?.text ? header.text : "",
      // headerVariables: header?.text
      //   ? processTextForVariables("headerVariables", header?.text, "headerText")
      //   : [],
      headerVariables: header?.text ? headerWaVariables : [],
      mediaType: header?.media ? header.format : "IMAGE",
      image:
        header?.example?.header_handle?.length > 0
          ? header?.example?.header_handle[0]
          : "",
      video: "",
      document: "",
      body: bodyText,
      // bodyVariables: processTextForVariables("bodyVariables", bodyText, "body"),
      bodyVariables: bodyWaVariables,
      footer: footer?.type ? true : false,
      footerText: footer?.text ? footer.text : "",
      button: buttons?.length > 0,
      buttonVariables: buttons || [],
    };
    if (selectedTemplate?.name) {
      setTemplateData(filledTemplate as any);
    }
  }, [selectedTemplate]);

  return (
    <FullModal
      key={"showTemplate"}
      title={`${templateData?.templateName}`}
      isOpen={showModal}
      onClose={handleClose}
    >
      <div className="max-h-80 overflow-y-auto">
        <p className="my-1">
          Category:{" "}
          <span className="text-primary">{templateData?.category}</span>{" "}
        </p>
        <p className="my-1">
          Language:{" "}
          <span className="text-primary">
            {templateData?.language?.toUpperCase()}
          </span>{" "}
        </p>

        <div className="my-2">
          <p>
            Body: <span className="text-primary">{templateData?.body}</span>{" "}
          </p>

          <div className="flex flex-wrap gap-2 my-2">
            <p>Body Variables Examples: </p>
            {templateData.bodyVariables.map((variable, i) => {
              return (
                <div key={i} className="flex flex-wrap gap-2">
                  {variable?.map((variable, i) => {
                    return (
                      <span key={variable + i} className="text-primary">
                        {`{{${i + 1}}}`} {variable} {"   "}
                      </span>
                    );
                  })}
                </div>
              );
            })}
          </div>
        </div>
        {templateData.header ? (
          <>
            <div className="my-2 mt-5">
              <p>
                HeaderType:{" "}
                <span className="text-primary">{templateData?.headerType}</span>
              </p>
              <p>
                HeaderText:{" "}
                <span className="text-primary">{templateData?.headerText}</span>{" "}
              </p>

              <div className="flex flex-wrap gap-2">
                {templateData.headerVariables.map((variable, i) => {
                  return (
                    <div key={variable + i} className="w-40">
                      {/* <Input
                        disabled
                        placeholder={Object.keys(variable)[0]}
                        name={Object.keys(variable)[0]}
                      /> */}
                      <Input disabled placeholder={variable} name={variable} />
                    </div>
                  );
                })}
              </div>
            </div>
            {templateData?.headerType?.toLowerCase() === "media" ? (
              <p>Media Type: {templateData?.mediaType}</p>
            ) : null}
            {templateData?.mediaType?.toLowerCase() === "image" &&
            templateData?.headerType?.toLowerCase() === "media" ? (
              <img alt="image" src={templateData?.image} />
            ) : null}
          </>
        ) : null}
        <div className="my-2 mt-5">
          {templateData.footer ? (
            <>
              <p>
                Footer Text:{" "}
                <span className="text-primary">{templateData?.footerText}</span>
              </p>
              <div className="w-40">
                <Input
                  disabled
                  placeholder={templateData?.footerText}
                  name={templateData?.footerText}
                />
              </div>
            </>
          ) : null}
        </div>
        {templateData.button ? (
          <p>
            Buttons:<span className="text-primary">{templateData?.button}</span>
          </p>
        ) : null}
        {/* <CreateTemplateForm
            setCreateDisabled={setCreateDisabled}
            setErrors={setErrors}
            errors={errors}
            templateData={templateData}
            setTemplateData={setTemplateData}
            readMode={true}
          /> */}
      </div>
    </FullModal>
  );
};
