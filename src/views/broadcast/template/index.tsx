import searchedArray from "helpers/search";
import { PlusCircleIcon, View } from "lucide-react";
import React, { useEffect, useState } from "react";
import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import { ShowTemplateModal } from "./components/ShowTemplateModal";
import { CreateTemplateModal } from "./components/CreateTemplateModal";
import {  createWaTemplate, getWaTemplates } from "apis/whatsapp.api";
import useBotStore from "store/bot/bot.store";
import { TCteadedTemplateData } from "../broadcastTypes/createTemplate.types";
import toast from "react-hot-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
export   type Timage = {
  url: string;
  path: string;
  formData: FormData;
  image: File;
  file_type:string,
  name:string,
  file?:File
  isVideo: boolean,
  isDocument: boolean,
  isImage: boolean,

};

export const Templates = () => {
  const bot = useBotStore((state) => state.bot);
console.log(bot)
  const [keySearch, setKeySearch] = useState("");
  const [renderedTemplates, setRenderedTemplates] = useState<any[]>(
    []
  );
  const [filteredTemplates, setFilteredTemplates] = useState<any[]>(
    []
  );
  const [tableLoading, setTableLoading] = useState(false);

  const fetchTemplates = async (bot_id:number) => {
    setTableLoading(true)
      try {
        const apiTemplates = await getWaTemplates(bot_id);
        setRenderedTemplates(apiTemplates?.templates || []);
        setFilteredTemplates(apiTemplates?.templates || []);
      } catch (error) {
        console.error("Error fetching templates:", error);
      }finally{
        setTableLoading(false)

      }

  };
  useEffect(() => {
    if (bot?.bot_id) {
    fetchTemplates(bot.bot_id);
  }
  }, [bot?.bot_id]);

  const [filterBy, setFilterBy] = useState("");
   
  useEffect(()=>{
    const newFilteredTemplates = renderedTemplates.filter((temp)=>{
      return temp.status.toLowerCase() === filterBy.toLowerCase()
    })
    setFilteredTemplates( filterBy  ? newFilteredTemplates : renderedTemplates  );

  },[filterBy])

  useEffect(() => {
    const fetchTemplates = async () => {
      const newFilteredTemplates_ = 
      filterBy  ?  
      renderedTemplates.filter((temp)=>{
        return temp.status.toLowerCase() === filterBy.toLowerCase()
      }) : renderedTemplates
  
      if (newFilteredTemplates_?.length >0) {
             const uniqueTemplates = newFilteredTemplates_?.reduce((acc, current) => {
            const x = acc.find((item) => item.name === current.name);
            if (!x) {
              return acc.concat([current]);
            } else {
              return acc;
            }
          }, []);
  
          const generatedFilteredTemplates = searchedArray(keySearch, uniqueTemplates, [
            "name",
            "name",
          ]);
          setFilteredTemplates(generatedFilteredTemplates);

      }
    };
  
    fetchTemplates();
  }, [keySearch]);

  const [templateToShow, setTemplateToShow] = useState(null);
  const [loading, setLoading] = useState(false);

  const numbersTableActions = [
    {
      label: "Show",
      onClick: (item) => {
        setTemplateToShow(item);
        setShowModal(true);
      },
      icon: View,
    },

  ];

  const [showModal, setShowModal] = useState(false);
  const [showModalCreate, setShowModalCreate] = useState(false);

  const [images, setImages] = useState<Timage>();

  const onClickCreateTemplate = () => {
    setShowModalCreate(true);
  };
  const onCreateTemplate = async(template:TCteadedTemplateData) => {
const templateDataToCreate = {
  template
}
try {
  setLoading(true);





 const templateStatus = await createWaTemplate(templateDataToCreate,bot.bot_id)

 if(templateStatus.status === "REJECTED" || templateStatus?.detail) {
  toast.error(templateStatus?.detail ||"Template rejected")
 }else{
   toast.success("Template accepted")
 }
  fetchTemplates(bot.bot_id)
  
} catch (error) {
  
} finally{
  setLoading(false);

}


  };

  return (
    <div className="space-y-3">
      <ShowTemplateModal
        showModal={showModal}
        setShowModal={setShowModal}
        templateToShow={templateToShow}
      />
      <CreateTemplateModal
        showModal={showModalCreate}
        setShowModal={setShowModalCreate}
        onCreateTemplate={onCreateTemplate}
        loading={loading}
        images={images}
        setImages={setImages}
      />
      

      <div className="flex w-full justify-between  align-middle">
        <div className="w-1/3">
          <Button  onClick={onClickCreateTemplate} disabled={false}>
            <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Template
          </Button>
        </div>

        <div className="w-1/3">
          <CustomSearch
            placeholder="Find a template..."
            onChange={(value) => setKeySearch(value)}
          />
        </div>
        <div className="w-1/3 flex justify-center">
        <div className="my-1 mb-1 flex items-center justify-center w-52">
        <label className={`block w-28`} htmlFor={"FILTER"}>
          { "Sort by:" }{" "}
        </label>
        <Select
          disabled={false}
          key={"FILTER"}
          name="FILTER"
          onValueChange={(value) => {
            setFilterBy(value);
          }}
          value={filterBy}
        >
          <SelectTrigger className="w-full">
            <SelectValue
              placeholder= "FILTER" 
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem key={"ALL"} value={""}>
              {"ALL"}
            </SelectItem>
            <SelectItem key={"APPROVED"} value={"APPROVED"}>
              {"APPROVED"}
            </SelectItem>
            <SelectItem key={"REJECTED"} value={"REJECTED"}>
              {"REJECTED"}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
        </div>
      </div>

      <div className="pt-5">
        <div className=" w-full">
          <MainTable
            loading={tableLoading}
            data={filteredTemplates}
            columns={[
              {
                name: "Template name",
                key: "name",
              },
              {
                name: "Language",
                key: "language",
              },
              {
                name: "Category",
                key: "category",
              },
              // {
              //   name: "Previous category",
              //   key: "previous_category",
              // },
              {
                name: "Status",
                key: "status",
              },
            ]}
            itemsPerPage={10}
            actions={numbersTableActions}
            idKey="number_id"
          />
        </div>
      </div>
    </div>
  );
};
