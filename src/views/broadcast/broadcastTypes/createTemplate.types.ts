export type Tbuttons = [
  | {
      url: string;
      type: "URL";
      text: string;
    }
  | {
      phone_number: "";
      type: "PHONE_NUMBER";
      text: string;
    }
  | {
      quick_reply: "";
      type: "QUICK_REPLY";
      text: string;
    }
];
export const templateDataDefault = {
  templateName: "",
  category: "UTILITY",
  language: "en",
  header: false,
  headerType: "text",
  headerText: "",
  headerVariables: [],
  mediaType: "IMAGE",
  location: { name: "", address: "", longitude: "", latitude: "" },
  image: "",
  video: "",
  document: "",
  body: "",
  bodyVariables: [],
  footer: false,
  footerText: "",
  button: false,
  buttonVariables: [],
  //   buttonVariables: [
  //     {
  //       url: "",
  //       type: "URL",
  //       text: "text",
  //    }
  //    ||  {
  //      phone_number: "",
  //     type: "PHONE_NUMBER",
  //     text: "text",
  //  }],
};

export type TCteadedTemplateData = {
  name: string;
  language: string;
  category: string;
  allow_category_change: boolean;
  components: unknown[];
  buttons?: Tbuttons;
};
export const errorsDefault = {
  templateName: "",
  category: "",
  language: "",
  header: "",
  headerType: "",
  headerText: "",
  headerVariables: "",
  mediaType: "",
  image: "",
  video: "",
  document: "",
  body: "",
  bodyVariables: "",
  footer: "",
  footerText: "",
  button: "",
  buttonVariables: "",
  location: "",
};

export type TtemplateDataDefault = typeof templateDataDefault;
export type TerrorsDefault = typeof errorsDefault;
