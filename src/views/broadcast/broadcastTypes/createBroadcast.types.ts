import { z } from "zod";
export type TBroadCastData ={
    broadcast_log_id: number;
    broadcast_id: number;
    phone_number: string | string[];
    status: number;
}
export const CreateItemSchema = z.object({
  selectedPhoneNumbers: z.array(z.string()).refine((arr) => arr.length > 0, {
    message: "phone_number must have at least one element",
  }), 
   broadcast_name: z.string().min(1, { message: "broadcast_name is required" }),
  broadcast_message: z
      .string()
      .min(1, { message: "broadcast_messageis required" }),
    show_url: z.boolean(),
    item_url: z.string().url().nullable().optional(),
  });

  export const RefinedCreateItemSchema = CreateItemSchema.refine(
    (data) => {
      if (data.show_url) {
        return data.item_url !== "" && data.item_url !== null;
      }
      return true;
    },
    {
      path: ["item_url"],
      message: "Item url is required",
    }
  );

export  type CreateItemType = z.infer<typeof CreateItemSchema>;
