import { MainPageHeader } from "common/components/headers";
import { Alert } from "common/ui/alert";
import { InfoIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";

import { Templates } from "./template";
import { Broadcasts } from "./boradcasts";

export const BroadcastView = () => {

  const [showTip, setShowTip] = useState(false);

  return (
    <div className="space-y-5">
      <MainPageHeader
        title="Broadcast"
        description="Start Broadcasting with your members."
      />
      {/* {showTip ? (
        <Alert
          title={"Broadcast tip title!"}
          content={ "Broadcast tip content."}
          setShowAlert={setShowTip}
        />
      ) : (
        <InfoIcon
          size={15}
          className="text-amber-300 float-right cursor-pointer"
          onClick={() => setShowTip(true)}
        />
      )} */}
      {/* <Tabs defaultValue="broadcasts" className="w-full mt-3">
        <TabsList className="grid w-full sm:grid-cols-2 lg:grid-cols-2">
          <TabsTrigger value="broadcasts">Broadcasts</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>
        <TabsContent value="broadcasts">
          <Broadcasts />
        </TabsContent>
        <TabsContent value="templates">
          <Templates />
          </TabsContent>

      </Tabs> */}
          <Broadcasts />
    </div>
  );
};
