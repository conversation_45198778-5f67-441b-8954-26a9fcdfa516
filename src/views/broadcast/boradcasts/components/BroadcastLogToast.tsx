import { useEffect, useState } from "react";
import { statType, statusType } from "..";

interface BroadcastLogToastProps {
  incomingActivity: statType[];
}

export const BroadcastLogToast: React.FC<BroadcastLogToastProps> = ({
  incomingActivity,
}) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (incomingActivity && incomingActivity?.length > 0 && incomingActivity?.[0]?.phoneNumber) {
      setLoading(false);
    }
  }, [incomingActivity]);

  const backgroundStatus = (status: statusType) => {
    switch (status) {
      case "submitted":
        return "bg-yellow-300";
      case "delivered":
        return "bg-blue-300";
      case "read":
        return "bg-green-300";
      case "rejected":
        return "bg-red-300";
      default:
        return "bg-slate-300";
    }
  }

  return (
    <div className="h-96 overflow-y-auto">
      {loading ? (
        <p>Sending...</p>
      ) : (
        <table className="min-w-full border border-gray-300">
          <thead className="bg-gray-100">
            <tr>
              <th className="py-2 px-4 border-b text-black">Phone Number</th>
              <th className="py-2 px-4 border-b text-black">Status</th>
            </tr>
          </thead>
          <tbody>
            {incomingActivity?.map((activity, i) => (
              <tr key={i} className="border-b">
                <td className="py-2 px-4">{activity.phoneNumber}</td>
                <td className={`py-2 px-4 ${backgroundStatus(activity.status)} text-black capitalize`}>{activity.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};
