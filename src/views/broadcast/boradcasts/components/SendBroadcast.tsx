import { FullModal } from "common/components/modals";
import { useEffect, useState } from "react";
import { Tbroadcast } from "..";
import useBroadcastStore from "store/broadcast/wa.boradcast.store";
import PhoneNumbers from "./createBroadcastComponents/PhoneNumbers";
import useBotStore from "store/bot/bot.store";
import { BroadcastLogToast } from "./BroadcastLogToast";
import toast from "react-hot-toast";
import { sendBroadcast, signalRNegotiate } from "apis/broadcast.api";
import ResendBroadCastForm from "./resendBroadCastComponents/ResendBroadCastForm";

interface SendBroadcastProps {
  showModal: boolean;
  setSendModal: (showModal: boolean) => void;
  broadcastToSend: Tbroadcast;
  incomingActivity?: any;
  setIncomingActivity?: any;
  setStatsToShow?: any;
}

export const SendBroadcast: React.FC<SendBroadcastProps> = ({
  showModal,
  setSendModal,
  broadcastToSend,
  incomingActivity,
  setIncomingActivity,
  setStatsToShow,
}) => {
  const { get_braodcasts_logs, broadcastLogs, loading } = useBroadcastStore();

  const { bot } = useBotStore();

  const [allPhoneNumbers, setAllPhoneNumbers] = useState<{
    TR: string[];
    CSV: string[];
  }>({ TR: [], CSV: [] });
  const [importedFileData, setImpotedFileData] = useState([]);

  const handleClose = () => {
    setSendModal(false);
    setShowLogs(false);
    setIncomingActivity([]);
    setImpotedFileData([]);
    setStatsToShow(null);
  };

  const handleGetNumbers = (data: string[]) => {
    setAllPhoneNumbers({ ...allPhoneNumbers, TR: data });
  };
  const handleGetNumbersCSV = (data: string[]) => {
    setAllPhoneNumbers({ ...allPhoneNumbers, CSV: data });
  };
  const [showLogs, setShowLogs] = useState(false);

  const onSaveHandler = async () => {
    setIncomingActivity([]);
    if (allPhoneNumbers.CSV?.length === 0 && allPhoneNumbers.TR?.length === 0) {
      toast.error("number is required");
      return;
    }

    const phoneNumbersArr = [
      ...allPhoneNumbers.TR,
      ...allPhoneNumbers.CSV,
    ].filter((num) => num);
    const distinctPhoneNumbers = [...new Set(phoneNumbersArr)];

    setShowLogs(true);
    await sendBroadcast({
      bot_id: bot.bot_id,
      broadcast_id: broadcastToSend.broadcast_id,
      phone_numbers: distinctPhoneNumbers,
    });
    get_braodcasts_logs(broadcastToSend.broadcast_id);
  };

  useEffect(() => {
    if (broadcastToSend?.broadcast_id) {
      signalRNegotiate(
        bot.bot_id,
        broadcastToSend?.broadcast_id,
        setIncomingActivity
      );
    }
  }, [broadcastToSend]);

  return (
    <FullModal
      title={"Re send Broadcast"}
      isOpen={showModal}
      onClose={handleClose}
      onSave={onSaveHandler}
      footer={!showLogs}
    >
      <div className="flex flex-col justify-center items-center   w-full gap-2">
        <div className="w-[80%] mb-4">
          <div className=" overflow-y-auto">
            {
              showLogs ? (
                <BroadcastLogToast incomingActivity={incomingActivity} />
              ) : (
                <ResendBroadCastForm
                  broadcast={broadcastToSend}
                  handleGetNumbers={handleGetNumbers}
                />
              )
              // <PhoneNumbers
              //   importedFileData={importedFileData}
              //   setImpotedFileData={setImpotedFileData}
              //   onChange={handleGetNumbers}
              //   onChangeCSV={handleGetNumbersCSV}
              // />
            }
          </div>
        </div>
      </div>
    </FullModal>
  );
};
