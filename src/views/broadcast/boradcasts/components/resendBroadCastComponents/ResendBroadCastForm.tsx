import React, { use, useEffect, useState } from "react";
import { Tbroadcast } from "../..";
import { getFile } from "apis/file.api";
import UsersNumbersTableVariables from "../createBroadcastComponents/UsersNumbersTableVariables";
import { MainTable } from "common/components/tables/main.table";
import { Input } from "common/ui/inputs/input";
import {
  templateDataDefault,
  TtemplateDataDefault,
} from "views/broadcast/broadcastTypes/createTemplate.types";
import { processTextForVariables, TresBody } from "../CreateBroadcast";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useBroadcastStore from "store/broadcast/wa.boradcast.store";
import UsersNumbersTable from "../createBroadcastComponents/UsersNumbersTable";
type ResendBroadCastFormProps = {
  broadcast: Tbroadcast;
  handleGetNumbers: (data: string[]) => void;
};
const ResendBroadCastForm: React.FC<ResendBroadCastFormProps> = ({
  broadcast,
  handleGetNumbers,
}) => {
  const [tableData, setTableData] = useState([]);
  const [templateVariables, setTemplateVariables] = useState({});
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  useEffect(() => {
    if (broadcast?.path) {
      const template = JSON.parse(broadcast?.template);
      setSelectedTemplate(template);
      const templateData = template.components;
      const bodyData = templateData.find(
        (component) => component.type === "body"
      );

      const bodyDataParameter = bodyData?.parameters?.map(
        (component, index) => {
          return { [`variable_${index + 1}`]: component.text };
        }
      );
      const result = bodyDataParameter.reduce((acc, obj) => {
        return { ...acc, ...obj };
      }, {});
      setTemplateVariables(result);
    }
  }, [broadcast]);
  useEffect(() => {
    const handleGetFile = async (path: string) => {
      const resposne = path ? await getFile(path) : "";
      let templates = [];
      const stemp = resposne?.file_data
        ? JSON.parse(resposne?.file_data)?.users || []
        : "";
      templates = resposne?.file_data ? stemp : "";

      const generatedTableData = templates?.map((template) => {
        const templateData = JSON.parse(template.template).components;
        const bodyData = templateData.find(
          (component) => component.type === "body"
        );

        const bodyDataParameter = bodyData?.parameters?.map(
          (component, index) => {
            return { [`variable_${index + 1}`]: component.text };
          }
        );
        const result = bodyDataParameter.reduce(
          (acc, obj) => {
            return { ...acc, ...obj };
          },
          { phone: template.phone }
        );

        return result;
      });

      handleGetNumbers(
        generatedTableData.map((generated) => {
          return generated.phone;
        })
      );
      setTableData(generatedTableData);
    };
    if (broadcast?.path) {
      handleGetFile(broadcast.path);
    }
  }, [broadcast]);

  const bot = useBotStore((state) => state.bot) as IBot;

  const { get_wa_phone_numbers, phoneNumbers } = useBroadcastStore();

  useEffect(() => {
    if (bot?.bot_id) {
      get_wa_phone_numbers(bot.bot_id);
    }
  }, [bot]);

  return (
    <div>
      <h3>Click save to resend the broadcast</h3>
      <div>
        {phoneNumbers && !broadcast?.path ? (
          <UsersNumbersTable
            onChange={handleGetNumbers}
            tableData={phoneNumbers}
            keyName={"tr_session"}
          />
        ) : null}
      </div>
      {broadcast?.path ? (
        <MainTable
          key={"phone numbers"}
          loading={false}
          data={tableData}
          columns={
            tableData.length > 0
              ? Object?.keys(tableData[0])?.map((key) => {
                  return {
                    name: key,
                    key: key,
                  };
                })
              : [
                  {
                    name: "phone",
                    key: "phone",
                  },
                ]
          }
          itemsPerPage={4}
          idKey={"phone"}
        />
      ) : null}
    </div>
  );
};

export default ResendBroadCastForm;
