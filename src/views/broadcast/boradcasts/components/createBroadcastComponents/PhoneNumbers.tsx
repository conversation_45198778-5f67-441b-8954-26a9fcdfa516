import { But<PERSON> } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { DownloadIcon } from "lucide-react";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useCSVReader } from "react-papaparse";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import UsersNumbersTable from "./UsersNumbersTable";
import useBroadcastStore from "store/broadcast/wa.boradcast.store";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { TtemplateDataDefault } from "views/broadcast/broadcastTypes/createTemplate.types";
import UsersNumbersTableVariables from "./UsersNumbersTableVariables";
import { Card } from "common/ui/card";
import CheckPointPhoneNumbers from "./CheckPointPhoneNumbers";

interface PhoneNumbersProps {
  // onChange?: (key: any, value: string | number) => void;
  importedFileData: any;
  isCSV: boolean;
  setImpotedFileData: any;
  templateData: TtemplateDataDefault;
  setRenamedArray: React.Dispatch<React.SetStateAction<unknown[]>>;
  renamedArray: unknown[];
  onChange: (numbers: string[]) => void;
  onChangeCSV: (numbers: string[]) => void;
  onChangeCP: (numbers: string[]) => void;
}
export type TselectedKeyArray = {
  column: string;
  variable: string;
  value?: string;
};
const PhoneNumbers: React.FC<PhoneNumbersProps> = ({
  onChange,
  onChangeCSV,
  onChangeCP,
  importedFileData,
  setImpotedFileData,
  templateData,
  setRenamedArray,
  renamedArray,
  isCSV,
}) => {
  const [showImportModal, setShowImportModal] = useState(false);
  const [allKeys, setAllKeys] = useState<string[]>();
  const [selectedKey, setSelectedKey] = useState<string>("");
  const [selectedKeyV, setSelectedKeyV] = useState<string>("");
  const [selectedKeyArray, setSelectedKeyArray] = useState<TselectedKeyArray[]>(
    [
      {
        column: "phone",
        variable: "phone",
      },
    ]
  );
  const [selectedKeyArrayV2, setSelectedKeyArrayV2] = useState<
    TselectedKeyArray[]
  >([]);

  useEffect(() => {
    const bodyVariables = templateData.bodyVariables.map((variable, index) => {
      const key = `variable_${index + 1}`;
      return { variable: key, column: "" };
    });
    setSelectedKeyArrayV2((prev) => {
      return [
        {
          variable: "phone",
          column: "",
        },
        ...bodyVariables,
      ];
    });
  }, [templateData.bodyVariables.length]);

  const bot = useBotStore((state) => state.bot) as IBot;

  const { get_wa_phone_numbers, phoneNumbers } = useBroadcastStore();

  useEffect(() => {
    if (bot?.bot_id) {
      get_wa_phone_numbers(bot.bot_id);
    }
  }, [bot]);
  useEffect(() => {
    if (onChangeCSV && importedFileData?.length > 0) {
      const allKeys = Object.keys(importedFileData[0]);
      const predectedKey = allKeys?.filter((key) => {
        return key.includes("num");
      });
      setAllKeys(allKeys);
      setSelectedKey(predectedKey[0]);
    }
  }, [importedFileData]);

  const { CSVReader } = useCSVReader();
  const convertCSVToJSON = (file) => {
    const data_json = [];

    const headerRow = file[0];

    file.slice(1).forEach((entity) => {
      const obj = {};
      headerRow.forEach((columnName, index) => {
        obj[columnName] = entity[index];
      });
      data_json.push(obj);
    });

    return data_json;
  };

  useEffect(() => {
    if (selectedKeyArrayV2.length > 0 && importedFileData.length > 0) {
      const map = {};
      selectedKeyArrayV2.forEach((mapping) => {
        map[mapping.column] = mapping.variable;
      });
      const phoneKey = selectedKeyArrayV2.filter((key) => {
        return key.variable === "phone";
      });

      // Rename fields in array1 based on the mappings in array2
      const renamedArray = importedFileData.map((obj) => {
        const newObj = {};
        for (const key in obj) {
          if (map[key]) {
            newObj[map[key]] = obj[key];
          } else {
            newObj[key] = obj[key];
          }
        }
        return newObj;
      });

      let TableData = templateData.bodyVariables.reduce(
        (acc, curr, index) => {
          acc[`variable_${index + 1}`] = curr[`Example for ${index + 1}`];
          return acc;
        },
        { phone: "" }
      );

      const result = importedFileData.map((data) => {
        return {
          ...TableData,
          phone: data[phoneKey[0].column],
        };
      });

      // Iterate over each key in the result objects
      Object.keys(result[0]).forEach((key) => {
        const defaultValue = result[0][key];

        // Skip phone key since it's used for comparison and not for replacement
        if (key !== "phone") {
          result.forEach((obj1) => {
            const correspondingObj = renamedArray.find(
              (obj2) => obj2.phone === obj1.phone
            );
            if (correspondingObj) {
              // Replace the value of the current key with the value from the corresponding object
              obj1[key] = correspondingObj[key] || defaultValue; // Default value from result array
            }
          });
        }
      });

      const upd =
        result?.filter((obj) => {
          return obj["phone"] !== "" && obj["phone"] !== undefined;
        }) || [];

      setRenamedArray(upd);
    }
  }, [importedFileData, selectedKeyArrayV2]);

  return (


      <>
        {isCSV ? (
          <>
          <p>Select Phone Numbers from csv</p>
            <div className="flex flex-col  justify-center items-center gap-2 ">
              <div className="mt-6">
                <CSVReader
                  onUploadAccepted={(results: any) => {
                    if (results.data.length > 0) {
                      const data_json = convertCSVToJSON(results.data);
                      setImpotedFileData([...data_json]);
                      setShowImportModal(true);
                    } else {
                      toast.error("No data found in the file");
                    }
                  }}
                >
                  {({
                    getRootProps,
                    acceptedFile,
                    ProgressBar,
                    getRemoveFileProps,
                  }: any) => (
                    <div {...getRootProps()}>
                      <Button variant="outline">
                        <DownloadIcon className="mr-2 h-4 w-4" /> Import CSV
                      </Button>
                    </div>
                  )}
                </CSVReader>
              </div>
              {showImportModal && importedFileData?.length > 0 ? (
                <Card className="w-full">
                  <p>
                    You have {templateData?.bodyVariables?.length} variables in
                    body so please select the correct column for each variable
                  </p>
                  <div className="w-full">
                    {" "}
                    <div className="flex items-end justify-center gap-2 w-full h-20">
                      <div className="flex flex-col text-start justify-center items-center w-1/2">
                        <label className={`block`} htmlFor={"csvselect"}>
                          {"Select a column name for "}
                          <span className="text-blue-500">
                            {selectedKeyArrayV2[0].variable}{" "}
                          </span>
                        </label>
                        <Select
                          key={"csvselect"}
                          onValueChange={(value) => {
                            setSelectedKey(value);
                            setSelectedKeyArrayV2((prev) => {
                              const data = [...prev];
                              const selected = (data[0].column = value);
                              return data;
                            });
                          }}
                          value={selectedKeyArrayV2[0].column}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a column " />
                          </SelectTrigger>
                          <SelectContent>
                            {allKeys?.map((key) => {
                              return (
                                <SelectItem key={key} value={key}>
                                  {key}
                                </SelectItem>
                              );
                            })}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    {templateData?.bodyVariables?.map((variable, i) => {
                      return (
                        <div key={i} className="flex items-end justify-center gap-2 w-full h-20">
                          {/* <div className="flex flex-col text-start justify-center items-center w-1/3">
                      <p>{`Variable ${i + 1}`}</p>
                    </div> */}
                          <div className="flex flex-col text-start justify-center items-center w-1/2">
                            <label className={`block`} htmlFor={"csvselect"}>
                              {"Select a column name for "}
                              <span className="text-blue-500">
                                {selectedKeyArrayV2[i + 1].variable}{" "}
                              </span>
                            </label>
                            <Select
                              key={"csvselect"}
                              onValueChange={(value) => {
                                setSelectedKey(value);
                                setSelectedKeyArrayV2((prev) => {
                                  const data = [...prev];
                                  const selected = (data[i + 1].column = value);
                                  return data;
                                });
                              }}
                              value={selectedKeyArray[`Variable ${i + 1}`]}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select a column " />
                              </SelectTrigger>
                              <SelectContent>
                                {allKeys?.map((key) => {
                                  return (
                                    <SelectItem key={key} value={key}>
                                      {key}
                                    </SelectItem>
                                  );
                                })}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </Card>
              ) : null}
            </div>

            {showImportModal && importedFileData?.length > 0 ? (
              <div className="my-2">
                <UsersNumbersTableVariables
                  isCSV={isCSV}
                  userselectedKeyArray={selectedKeyArrayV2}
                  onChange={onChangeCSV}
                  templateData={templateData}
                  tableData={renamedArray || []}
                  setTableData={setRenamedArray}
                  keyName={"phone"}
                  selectedKeyArray={[
                    {
                      column: "phone",
                      variable: "phone",
                    },
                    ...(templateData?.bodyVariables?.map((value, i) => ({
                      column: `variable_${i + 1}`,
                      variable: `variable_${i + 1}`,
                      value:
                        templateData?.bodyVariables[i][`Example for ${i + 1}`],
                    })) || []),
                  ]}
                />
              </div>
            ) : null}
          </>
        ) : (
          <>

            <div className="mt-2">
            <p>Select Active Phone Numbers</p>

              {phoneNumbers ? (
                <UsersNumbersTable
                  onChange={onChange}
                  tableData={phoneNumbers}
                  keyName={"tr_session"}
                />
              ) : null}
            </div>
    
            <>
            <CheckPointPhoneNumbers onChange={onChangeCP} />
            </>
          </>
        )}
      </>
 
  );
};

export default PhoneNumbers;