import React from "react";
import { Input } from "common/ui/inputs/input";
import { ShieldClose } from "lucide-react";
import { Textarea } from "common/ui/inputs/textarea";

interface BroadcastsInputsFormProps {
  onChange: (key: any, value: any) => void;
  broadcastFormData: any;
  errors: any;
}
const BroadcastsInputsForm: React.FC<BroadcastsInputsFormProps> = ({
  onChange,
  broadcastFormData,
  errors,
}) => {
  return (
    <>
      <div className="flex flex-col gap-5 space-y-5 my-5">
        <Input
          title="broadcast name"
          name="broadcast_name"
          error={errors?.broadcast_name}
          onChange={(e) => onChange("broadcast_name", e.target.value)}
          value={broadcastFormData?.broadcast_name}
        />
      </div>

      <div className=" relative">
        <label htmlFor="broadcast_message">broadcast message</label>
        <Textarea
          id="broadcast_message"
          name="broadcast_message"
          value={broadcastFormData?.broadcast_message}
          onChange={(e) => onChange("broadcast_message", e.target.value)}
        />
        <span className="text-red-500 text-xs flex gap-1 items-center absolute -bottom-4 left-0">
          {errors?.broadcast_message ? (
            <>
              <ShieldClose size={12} />
              {errors?.broadcast_message}
            </>
          ) : null}
        </span>
      </div>
    </>
  );
};

export default BroadcastsInputsForm;
