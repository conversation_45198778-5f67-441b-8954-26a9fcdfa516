import { getAll } from "apis/checkpoints.api";
import { MainTable } from "common/components/tables/main.table";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
export type TCheckpoint = {
  dialog_checkpoint_id: number;
  bot_id: number;
  phone?: string;
  channel: "web" | "whatsapp";
  conversation_id: string;
  createdAt: string;
  dialog_id: number;
  tag: string;
  updatedAt: string;
};
type CheckPointPhoneNumbersProps = {
  onChange: (numbers: string[]) => void;
};
const CheckPointPhoneNumbers: React.FC<CheckPointPhoneNumbersProps> = ({
  onChange,
}) => {
  const [checkpoints, setCheckpoints] = useState<TCheckpoint[]>([]);
  const { bot } = useBotStore();
  useEffect(() => {
    const getCheckPointPhoneNumbers = async (bot_id: number) => {
      const nums = (await getAll(bot_id)) as TCheckpoint[];
      const waNums = nums
        ?.filter((num) => {
          return num.channel === "whatsapp";
        })
        ?.map((num) => {
          const number = num.conversation_id.split("_")[0];

          return {
            ...num,
            phone: number,
          };
        });
      setCheckpoints(waNums);
    };
    if (bot.bot_id) {
      getCheckPointPhoneNumbers(bot.bot_id);
    }
  }, []);

  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const onSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(checkpoints?.map((data) => data.phone.toString()));
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(selected.filter((id) => id !== name.split("-")[1]));
        setSelectAll(false);
      }
    }
  };

  useEffect(() => {
    if (selected.length === checkpoints?.length) {
      onChange(selected);
      setSelectAll(true);
    } else {
      setSelectAll(false);
      onChange(selected);
    }
  }, [selected, checkpoints]);
  return (
   <>
      {checkpoints?.length > 0 ? (
        <div className=" w-full mb-5">
      <p>Select Phone Numbers from CheckPoints </p>

        <MainTable
          key={"phone numbers"}
          loading={false}
          data={checkpoints}
          columns={[
            {
              name: "CheckPoint Numbers",
              key: "phone",
            },

            {
              name: "channel",
              key: "channel",
            },
            {
              name: "tag",
              key: "tag",
            },
          ]}
          itemsPerPage={5}
          checkBoxes
          onSelect={onSelect}
          isSelectAll={selectAll}
          isSelectOne={(id) => selected.includes(id)}
          idKey={"phone"}
        />
        </div>
      ) : null}

</>
  );
};

export default CheckPointPhoneNumbers;