import { MainTable } from "common/components/tables/main.table";
import React, { useEffect, useState } from "react";
interface broadcastTableProps {
  tableData: any;
  onChange: (numbers: string[]) => void;
  keyName?: string;
}
const UsersNumbersTable: React.FC<broadcastTableProps> = ({
  tableData,
  onChange,
  keyName,
}) => {
  const [selected, setSelected] = useState([]);
  // const [selectedNum, setSelectedNum] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    if (selected.length === tableData?.length) {
      onChange(selected);
      setSelectAll(true);
    } else {
      setSelectAll(false);
      onChange(selected);
    }
  }, [selected, tableData]);

  const onSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(tableData?.map((data) => data[keyName].toString()));
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(selected.filter((id) => id !== name.split("-")[1]));
        setSelectAll(false);
      }
    }
  };

  return (
    <div className=" w-full mb-5">
      {keyName && tableData ? (
        <MainTable
          key={"phone numbers"}
          loading={false}
          data={tableData}
          columns={[
            {
              name: "Active Numbers",
              key: keyName ? keyName : "num",
            },
          ]}
          itemsPerPage={5}
          checkBoxes
          onSelect={onSelect}
          isSelectAll={selectAll}
          isSelectOne={(id) => selected.includes(id)}
          idKey={keyName ? keyName : "num"}
        />
      ) : null}
    </div>
  );
};

export default UsersNumbersTable;
