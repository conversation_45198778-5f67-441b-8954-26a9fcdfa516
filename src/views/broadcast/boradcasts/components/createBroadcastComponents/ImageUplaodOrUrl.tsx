import React, { useState } from "react";
import { Input } from "common/ui/inputs/input";
import { MediaInput } from "common/ui/inputs/mediaInput";
import generateStorageId from "helpers/generateStorageId";
import { DownloadIcon, Link, ShieldClose, Upload, X } from "lucide-react";
import { toast } from "react-hot-toast";
import { Tooltip } from "common/ui/tooltip";
import { Button } from "common/ui/button";
import { uploadBotImages } from "apis/file.api";
import { Timage } from "views/broadcast/template";

interface ImageUplaodOrUrlProps {
  bot: any;
  setImages: React.Dispatch<React.SetStateAction<Timage>>;
  images: Timage;
  setImageUrlData: React.Dispatch<React.SetStateAction<any>>;
  ImageUrlData: { show_url: boolean; image_url: string };
  onChange: (key:any,value:any)=>void;
}

const ImageUplaodOrUrl: React.FC<ImageUplaodOrUrlProps> = ({
  bot,
  setImages,
  images,
  setImageUrlData,
  ImageUrlData,
  onChange
}) => {
  const [byLink, setByLink] = useState(false);

  // const [images, setImages] = useState<Timage>();
  const [imageUrl, setImageUrl] = useState("");
  const onDeleteImage = () => {
    setImageUrl("");
    setImages(null);

    setImageUrlData({
      show_url: false,
      image_url: "",
    });
    onChange("image","")

  };

  const uploadImg = (image: File) => {
    if(!image) return
    setImageUrl("");
    const formData = new FormData();
    formData.append("file", image);
    const path = `Bots/${bot.file_name}/itemsAssets/${generateStorageId()}.${
      image?.type.split("/")[1]
    }`;

    const imageData = {
      url: "https://infotointell.fra1.digitaloceanspaces.com/" + path,
      path,
      formData,
      image,
      file:image,
      file_type:image.type,
      name:image.name,
      isVideo: image?.type.includes("video"),
      isDocument: image?.type.includes("pdf"),
      isImage: image?.type.includes("image"),
    };
    onChange("image",imageData.url)
    // uploadBotImages(imageData)
    
    setImages(imageData);
  };

  function isImageUrl(url: string) {
    const img = new Image();
    img.src = url;
    return new Promise((resolve) => {
      img.onload = () => resolve(true);
      img.onerror = () => resolve(false);
    });
  }

  return (
    <div className="w-full px-5 border-white ">
      {/* <div className="w-full flex justify-end px-5">
        {byLink ? (
          <Tooltip text="By Upload">
            <Upload
              size={18}
              className="cursor-pointer"
              onClick={() => setByLink(false)}
            />
          </Tooltip>
        ) : (
          <Tooltip text="Add by url">
            <Link
              size={18}
              className="cursor-pointer"
              onClick={() => setByLink(true)}
            />
          </Tooltip>
        )}
      </div> */}
      {false ? (
        <div className="flex flex-col gap-2 mt-4 w-full">
          <Input
            title="image url"
            name="add_img"
            value={imageUrl ? imageUrl : ""}
            onChange={(e) => {
              setImageUrl(e.target.value);
            }}
          />
          {ImageUrlData?.show_url ? (
            <div className="w-20">
         
            <Button
              onClick={async (e) => {
                setImageUrlData({
                  show_url: false,
                  image_url: null,
                });
                setImageUrl("");
              }}
            >
              delete
            </Button>
            </div>
          ) : (
            <div className="w-20">
            <Button
              onClick={async (e) => {
                if (await isImageUrl(imageUrl)) {
                  e.preventDefault();
                  onChange("image_url",imageUrl)
                  setImageUrlData({
                    show_url: true,
                    image_url: imageUrl,
                  });
                } else {
                  toast.error("Invalid image url");
                }
              }}
            >
              add
            </Button>
            </div>
          )}
        </div>
      ) : (
        <>
          {ImageUrlData?.image_url || images?.image ? (
            <X
              onClick={onDeleteImage}
              color="red"
              className="hover:cursor-pointer"
            />
          ) : null}
          <MediaInput
            imgSrc={
              ImageUrlData?.show_url
                ? ImageUrlData?.image_url
                : images?.image
                ? URL?.createObjectURL(images?.image)
                : ""
            }
            uploadImg={uploadImg}
            accept="image/png, image/jpeg, image/jpg"
          />
        </>
      )}
    </div>
  );
};

export default ImageUplaodOrUrl;
