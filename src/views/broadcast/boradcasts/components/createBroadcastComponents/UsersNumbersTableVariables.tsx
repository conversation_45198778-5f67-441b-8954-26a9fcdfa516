import { MainTable } from "common/components/tables/main.table";
import React, { useEffect, useState } from "react";
import { TselectedKeyArray } from "./PhoneNumbers";
import { Input } from "common/ui/inputs/input";
import { TtemplateData } from "views/broadcast/templateJson.helper";
import { TtemplateDataDefault } from "views/broadcast/broadcastTypes/createTemplate.types";
import { Card } from "common/ui/card";
interface broadcastTableProps {
  tableData: any;
  isCSV: boolean;
  onChange: (numbers: string[]) => void;
  keyName?: string;
  userselectedKeyArray: TselectedKeyArray[];
  selectedKeyArray: TselectedKeyArray[];
  setTableData: React.Dispatch<React.SetStateAction<any>>;
  templateData: TtemplateDataDefault;
}
const UsersNumbersTableVariables: React.FC<broadcastTableProps> = ({
  tableData,
  onChange,
  keyName,
  selectedKeyArray,
  userselectedKeyArray,
  setTableData,
  templateData,
  isCSV,
}) => {
  const [selected, setSelected] = useState([]);
  // const [selectedNum, setSelectedNum] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    if (selected.length === tableData?.length) {
      onChange(selected);
      setSelectAll(true);
    } else {
      setSelectAll(false);
      onChange(selected);
    }
  }, [selected, tableData]);

  const onSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    if (name === "select-all") {
      setSelectAll(checked);
      if (checked) {
        setSelected(tableData?.map((data) => data[keyName].toString()));
      } else {
        setSelected([]);
      }
    } else {
      if (checked) {
        setSelected([...selected, name.split("-")[1]]);
      } else {
        setSelected(selected.filter((id) => id !== name.split("-")[1]));
        setSelectAll(false);
      }
    }
  };
  const [remainingVariables, setRemianingVariables] = useState([]);
  useEffect(() => {
    function getMissingElements() {
      const variables = userselectedKeyArray?.map((key) => {
        return key.variable;
      });
      const updSelectedKeyArray = selectedKeyArray?.filter((item) => {
        return item?.variable && !variables.includes(item.variable);
      });

      setRemianingVariables((prev) => {
        const prevData = [...prev];
        const updatedSelectedKeyArray = updSelectedKeyArray.map((item) => {
          // Check your condition here, for example:
          const existingItem = prevData.find(
            (prevItem) => prevItem.variable === item.variable
          );
          if (existingItem && existingItem.value !== undefined) {
            item.value = existingItem.value; // Update the value from prev if it exists
          }
          return item;
        });
        return updatedSelectedKeyArray;
      });
    }

    getMissingElements();
  }, [selectedKeyArray, userselectedKeyArray]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index) => {
    const { name, value } = e.target;
    const remainingVariablesN = [...remainingVariables];
    remainingVariablesN[index].value = value;

    setRemianingVariables(remainingVariablesN);
    const newTableData = tableData.map((data) => {
      return {
        ...data,
        [name]: value,
      };
    });
    setTableData(newTableData);
  };
  const handleEx = () => {
    const filledTemplate = templateData.body.replace(
      /\{\{(\d+)\}\}/g,
      (match, key) => {
        return tableData[0][`variable_${key}`] || ""; // Access object properties dynamically
      }
    );

    return filledTemplate;
  };
  return (
    <div className=" w-full mb-5">
      {isCSV ? (
        <div className="w-full mb-5 flex gap-5">
          {remainingVariables.map((key, i) => {
            return (
              <div key={i} className="w-40">
                <Input
                  key={i}
                  value={remainingVariables[i]?.value || ""}
                  name={key.column}
                  title={key.column}
                  onChange={(e) => handleChange(e, i)}
                />
              </div>
            );
          })}
        </div>
      ) : null}
      {tableData.length > 0 ? (
        <Card className="my-2">
          message for first contact is:{" "}
          <span className="text-blue-500">{handleEx()}</span>
        </Card>
      ) : null}

      {keyName && tableData ? (
        <>
          <p className="my-1 mt-5">
            Please select the Numbers To send the template
          </p>
          <MainTable
            key={"phone numbers"}
            loading={false}
            data={tableData}
            columns={selectedKeyArray.map((key) => {
              return {
                name: key.column,
                key: key.column,
              };
            })}
            itemsPerPage={10}
            checkBoxes
            onSelect={onSelect}
            isSelectAll={selectAll}
            isSelectOne={(id) => selected.includes(id)}
            idKey={keyName ? keyName : "num"}
          />
        </>
      ) : null}
    </div>
  );
};

export default UsersNumbersTableVariables;
