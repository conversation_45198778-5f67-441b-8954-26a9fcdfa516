import { FullModal } from "common/components/modals";
import { useEffect, useState } from "react";
import { Tbroadcast } from "..";
import useBroadcastStore from "store/broadcast/wa.boradcast.store";
import { MainTable } from "common/components/tables/main.table";
import helper from "views/broadcast/helper";
import { signalRNegotiate } from "apis/broadcast.api";
import useBotStore from "store/bot/bot.store";

interface ShowBroadcastProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  broadcastToShow: Tbroadcast;
  incomingActivity: any;
  setIncomingActivity: any;
}

export const ShowBroadcast: React.FC<ShowBroadcastProps> = ({
  showModal,
  setShowModal,
  broadcastToShow,
  incomingActivity,
  setIncomingActivity,
}) => {
  const { get_braodcasts_logs, broadcastLogs, loading } = useBroadcastStore();
  const { bot } = useBotStore();

  const [updatedLogs, setUpdatedLogs] = useState([]);
  const [sentTo, setSentTo] = useState(0);
  const [statusReport, setStatusReport] = useState<
    Record<
      string,
      {
        count: number;
        percentage: string;
      }
    >
  >({});

  useEffect(() => {
    if (broadcastToShow?.broadcast_id) {
      signalRNegotiate(
        bot.bot_id,
        broadcastToShow?.broadcast_id,
        setIncomingActivity
      );
    }
  }, [broadcastToShow?.broadcast_id]);

  useEffect(() => {
    if (broadcastToShow && broadcastToShow?.broadcast_id) {
      get_braodcasts_logs(broadcastToShow.broadcast_id);
    }
  }, [broadcastToShow?.broadcast_id, incomingActivity]);
  useEffect(() => {
    if (broadcastLogs?.length > 0) {
      const UpdatedBroadcast = broadcastLogs.map((acc) => {
        const formattedDateTime = helper.changeDateFormat(acc.createdAt);
        return { ...acc, createdAt: formattedDateTime };
      });
      let sentCount = broadcastLogs?.filter(
        (log) => log.status !== "rejected"
      ).length;

      setSentTo(sentCount);
      setUpdatedLogs(UpdatedBroadcast);

      const statusRep = {};

      const dynamicallydefinedStatus = broadcastLogs.reduce((acc, log) => {
        acc[log.status] = (acc[log.status] || 0) + 1;
        return acc;
      }, {});

      const dynamicallydefinedStatusAndPercentage = broadcastLogs.reduce(
        (acc, log) => {
          acc[log.status] = acc[log.status] || {};
          acc[log.status].count = (acc[log.status].count || 0) + 1;
          acc[log.status].percentage = `${
            ((acc[log.status].count / broadcastLogs.length) * 100).toFixed(2) ||
            0
          }%`;
          return acc;
        },
        {}
      );

      // make the order total read delivered submitted rejected then any other status
      const order = ["total", "read", "delivered", "submitted", "rejected"];
      const orderedStatus = Object.keys(dynamicallydefinedStatusAndPercentage)
        .sort((a, b) => order.indexOf(a) - order.indexOf(b))
        .reduce((obj, key) => {
          obj[key] = dynamicallydefinedStatusAndPercentage[key];
          return obj;
        }, {});

      setStatusReport({
        total: {
          count: broadcastLogs.length,
          percentage: "100%",
        },
        ...orderedStatus,
      });
    } else {
      setUpdatedLogs([]);
      setSentTo(0);
      setStatusReport({});
    }
  }, [broadcastLogs]);

  const handleClose = () => {
    setShowModal(false);
    setUpdatedLogs([]);
    setSentTo(0);
  };

  const statusClassName = (status) => {
    switch (status) {
      case "total":
        return "text-blue-500";
      case "delivered":
      case "read":
        return "text-green-500";
      case "rejected":
      case "undeliverable":
      case "failed":
        return "text-red-500";
      default:
        return "text-yellow-500";
    }
  };

  const exportToCSV = () => {
    const editedData = updatedLogs.map((log) => {
      return {
        "Broadcast Name": broadcastToShow.broadcast_name,
        "phone number": log.phone_number.toString(),
        status: log.status,
        Date: log.createdAt,
      };
    });
    // add broadcast name
    const csv = editedData.map((row) =>
      Object.values(row)
        .map((value) => `"${value}"`)
        .join(",")
    );
    csv.unshift(
      Object.keys(editedData[0])
        .map((key) => `"${key}"`)
        .join(",")
    );

    // leave an empty line
    csv.unshift("");

    csv.unshift(
      Object.values(statusReport)
        .map((value) => `"${value!.percentage}"`)
        .join(",")
    );

    csv.unshift(
      Object.values(statusReport)
        .map((value) => `"${value!.count}"`)
        .join(",")
    );
    // status report
    csv.unshift(
      Object.keys(statusReport)
        .map((key) => `"${key}"`)
        .join(",")
    );

    const csvArray = csv.join("\r\n");
    const a = document.createElement("a");
    a.href = "data:attachment/csv," + encodeURIComponent(csvArray);
    a.target = "_blank";
    a.download = `${broadcastToShow.broadcast_name} Report.csv`;
    document.body.appendChild(a);
    a.click();
  };

  return (
    <FullModal
      title={" Broadcast logs"}
      isOpen={showModal}
      onClose={handleClose}
      //   disabled={!isChanged}
      // onSave={onSaveHandler}
      loading={loading}
      className="!w-[90%] !h-[90%] "
    >
      <div className="flex flex-col justify-center items-center   !w-full gap-2">
        <div className="w-[80%] !h-full">
          {updatedLogs?.length ? (
            <div className="flex items-center justify-between mb-1 capitalize">
              <div className="flex gap-2 items-center">
                {Object.keys(statusReport).map((key, i) => {
                  return (
                    <div
                      key={i}
                      className={`flex gap-2 items-center ${statusClassName(
                        key
                      )}`}
                    >
                      <div className="text-xs font-bold">{key}:</div>
                      <div className="text-xs">{statusReport[key].count}</div>
                      <div className="text-xs">
                        ({statusReport[key].percentage})
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="flex justify-end items-center gap-2">
                <button
                  onClick={exportToCSV}
                  className="bg-accent text-white px-3 py-1 rounded-md"
                >
                  Export to CSV
                </button>
              </div>
            </div>
          ) : null}
          <MainTable
            key={"Broadcast"}
            loading={loading}
            data={updatedLogs}
            columns={[
              {
                name: "phone number",
                key: "phone_number",
              },
              {
                name: "status",
                key: "status",
              },
              {
                name: "Date",
                key: "createdAt",
              },
            ]}
            itemsPerPage={8}
            idKey="phone_number"
          />
        </div>
      </div>
    </FullModal>
  );
};
