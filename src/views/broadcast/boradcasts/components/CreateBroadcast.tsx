import { FullModal } from "common/components/modals";
import { useEffect, useState } from "react";
import { Itemplate } from "../../number.types";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import CreateTemplateForm from "views/broadcast/template/components/CreateTemplateForm";
import {
  TCteadedTemplateData,
  TerrorsDefault,
  TtemplateDataDefault,
  errorsDefault,
  templateDataDefault,
} from "views/broadcast/broadcastTypes/createTemplate.types";
import { Button } from "common/ui/button";
import PhoneNumbers from "./createBroadcastComponents/PhoneNumbers";
import toast from "react-hot-toast";
import templateJsonHelper from "views/broadcast/templateJson.helper";
import useBroadcastStore from "store/broadcast/wa.boradcast.store";
import useBotStore from "store/bot/bot.store";
import { Input } from "common/ui/inputs/input";
import { Loader2 } from "lucide-react";
// import * as api from "apis/broadcast.api";
import { BroadcastLogToast } from "./BroadcastLogToast";
import { getWaTemplates, waMedia } from "apis/whatsapp.api";
import { Timage } from "views/broadcast/template";
import {
  createOne as createBroadcast,
  sendBroadcast,
  signalRNegotiate,
} from "apis/broadcast.api";
import { getFile, uploadBotImages } from "apis/file.api";
import generateStorageId from "helpers/generateStorageId";
import { createOne, updateOne } from "apis/dialog.api";
import { uploadFile } from "apis/file.api";

interface CreateBroadcastModalProps {
  setIsCSV: React.Dispatch<React.SetStateAction<boolean>>;
  isCSV: boolean;
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  message?: Itemplate;
  setMessageEdit?: (message: Itemplate) => void;
  templateToShow?: Itemplate;
  incomingActivity?: any;
  setIncomingActivity?: any;
  setStatsToShow?: any;
}
export type TresBody = {
  type: string;
  format: string;
  text: string;
  media?: boolean;
};

export function processTextForVariables(
  variableKey: string,
  text: string,
  name: string,
  limit = Infinity
) {
  const pattern = /\{\{(\d+)\}\}/g;
  let replacements = [];
  let numbersSeen = new Set();
  let error = "";
  let match;

  let existingVariables = [];
  const existingVariablesNumbers = existingVariables?.map((variable) =>
    parseInt(Object.keys(variable)[0].replace("Example for ", ""))
  );

  while ((match = pattern.exec(text)) !== null) {
    const number = parseInt(match[1]);

    if (number > limit) {
      error = `You can only use ${limit} variables`;
      continue;
    }

    if (number <= numbersSeen.size + 1) {
      if (!numbersSeen.has(number)) {
        numbersSeen.add(number);
        if (!existingVariablesNumbers.includes(number)) {
          replacements.push({ [`Example for ${number}`]: "" });
        } else {
          const existingVariable = existingVariables.find(
            (variable) =>
              parseInt(Object.keys(variable)[0].replace("Example for ", "")) ===
              number
          );
          replacements.push(existingVariable);
        }
      } else {
        error = `Duplicate variable: ${number}`;
        break;
      }
    } else {
      error = `Missing variable or wrong order: ${numbersSeen.size + 1}`;
      break;
    }
  }

  return replacements;
}
export const CreateBroadcastModal: React.FC<CreateBroadcastModalProps> = ({
  showModal,
  setShowModal,
  message,
  templateToShow,
  incomingActivity,
  setIncomingActivity,
  setStatsToShow,
  setIsCSV,
  isCSV,
}) => {
  const [renamedArray, setRenamedArray] = useState<any[]>([]);
  const [selectedRenamedArray, setSelectedRenamedArray] = useState<any[]>([]);
  const [templatesForNumbers, setTemplatesForNumbers] = useState<any[]>([]);

  useEffect(() => {
    if (selectedRenamedArray.length > 0) {
      const templatesForNumbers = [];
      selectedRenamedArray?.map((item, j) => {
        const uTemplateData = { ...templateData };

        const updBodyVariables = uTemplateData.bodyVariables.map((v, i) => {
          return {
            ...v,
            [`Example for ${i + 1}`]: item[`variable_${i + 1}`],
          };
        });
        uTemplateData.bodyVariables = updBodyVariables;
        const templates = JSON.stringify(
          templateJsonHelper.onGenerateJson2Send(uTemplateData)
        );
        templatesForNumbers.push({
          template: templates,
          phone: item?.phone,
        });
      });
      setTemplatesForNumbers(templatesForNumbers);
    }
  }, [renamedArray, selectedRenamedArray]);

  const { loading, create_one_broadcast, get_braodcasts, broadcast } =
    useBroadcastStore();
  const { bot } = useBotStore();
  const [importedFileData, setImpotedFileData] = useState([]);

  const [broadcastID, setBroadcastID] = useState(null);

  useEffect(() => {
    if (broadcastID) {
      signalRNegotiate(bot.bot_id, broadcastID, setIncomingActivity);
    }
  }, [broadcastID, bot.bot_id]);

  const [createDisabled, setCreateDisabled] = useState(false);
  const [selectedPhoneNumbers, setSelectedPhoneNumbers] = useState<string[]>(
    []
  );

  const [templateData, setTemplateData] =
    useState<TtemplateDataDefault>(templateDataDefault);

  const [errors, setErrors] = useState<TerrorsDefault>(errorsDefault);

  const [boradcastName, setBoradcastName] = useState<string>("");
  const [allPhoneNumbers, setAllPhoneNumbers] = useState<{
    TR: string[];
    CSV: string[];
    CP: string[];
  }>({ TR: [], CSV: [], CP: [] });
  const [templates, setTemplates] = useState<TCteadedTemplateData[]>([]);
  const [images, setImages] = useState<Timage>(null);

  const handleClose = () => {
    setShowModal(false);
    setIncomingActivity([]);
    setTemplateData(templateDataDefault);
    setErrors(errorsDefault);
    setBoradcastName("");
    setAllPhoneNumbers({ TR: [], CSV: [], CP: [] });
    setImages(null);
    setSelectedTemplate(null);
    setImpotedFileData([]);
    setUploadLoading(false);
    setPageNum(0);
    setStatsToShow([]);
  };

  const fetchTemplates = async (bot_id: number) => {
    // setTableLoading(true)
    try {
      const apiTemplates = await getWaTemplates(bot_id);
      const approvedTemplates = apiTemplates?.templates?.filter((item) => {
        return item.status === "APPROVED";
      });
      setTemplates(approvedTemplates || []);
    } catch (error) {
      console.error("Error fetching templates:", error);
    } finally {
      // setTableLoading(false)
    }
  };
  useEffect(() => {
    if (bot?.bot_id) {
      fetchTemplates(bot.bot_id);
    }
  }, [bot?.bot_id]);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  const generateButtons = (buttonsV) => {
    const generatedButtons = buttonsV[0]?.buttons?.map((button) => {
      if (button.type.toLowerCase() === "url") {
        return {
          url: "",
          type: "URL",
          text: "Yes",
        };
      } else if (button.type.toLowerCase() === "quick_reply") {
        return {
          quick_reply: "",
          type: "QUICK_REPLY",
          text: "Yes",
        };
      } else {
        return {
          phone_number: "",
          type: "PHONE_NUMBER",
          text: "Yes",
        };
      }
    });
    return generatedButtons || [];
  };

  useEffect(() => {
    if (templateToShow) {
      setSelectedTemplate(templateToShow);
    }
  }, [templateToShow]);

  useEffect(() => {
    let bodyText: string;
    let header: TresBody;
    let footer: TresBody;
    let button = false;
    let buttonsV = [];
    selectedTemplate?.components?.map((component) => {
      if (component.type === "BODY") {
        bodyText = component.text;
      }
      if (component.type === "HEADER" && component.format === "TEXT") {
        header = component;
      }
      if (component.type === "HEADER" && component.format !== "TEXT") {
        header = component;
        header.media = true;
      }
      if (component.type === "FOOTER") {
        footer = component;
      }
      if (component?.type?.toLowerCase() === "buttons") {
        button = true;
        buttonsV.push(component);
      }
    });
    const filledTemplate = {
      templateName: selectedTemplate?.name,
      category: selectedTemplate?.category,
      language: selectedTemplate?.language,
      header: header?.type ? true : false,
      headerType:
        header?.format === "TEXT"
          ? header.format.toLocaleLowerCase()
          : header?.format !== "TEXT"
          ? "media"
          : "",
      headerText: header?.text ? header.text : "",
      headerVariables: header?.text
        ? processTextForVariables("headerVariables", header?.text, "headerText")
        : [],
      mediaType: header?.media ? header.format : "IMAGE",
      image: "",
      video: "",
      document: "",
      location: { name: "", address: "", longitude: "", latitude: "" },
      body: bodyText,
      bodyVariables: processTextForVariables("bodyVariables", bodyText, "body"),
      footer: footer?.type ? true : false,
      footerText: footer?.text ? footer.text : "",
      button: button,
      buttonVariables: generateButtons(buttonsV),
    };
    if (selectedTemplate?.name) {
      setTemplateData(filledTemplate);
    }
  }, [selectedTemplate]);

  function areAllKeysEmptyString(obj) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key) && obj[key] !== "") {
        toast.error(obj[key]);
        return false;
      }
    }
    return true;
  }
  const [pageNum, setPageNum] = useState(0);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [onSending, setOnSending] = useState(false);
  const [ImageUrlData, setImageUrlData] = useState({
    show_url: false,
    image_url: "",
  });
  const handleNextPage = async () => {
    if (pageNum == 0 && areAllKeysEmptyString(errors)) {
      if (!boradcastName) {
        toast.error("name is required");
        return;
      }
      try {
        setUploadLoading(true);
        if (images && templateData.image) {
          await uploadBotImages(images);
        }

        setTemplateData((prev) => ({
          ...prev,
          [prev.mediaType.toLowerCase()]: images?.url || "",
        }));
        setUploadLoading(false);
      } catch (error) {
        setUploadLoading(false);

        toast.error("image is not valid");
        console.log(error);
        return;
      }

      setPageNum((prev) => prev + 1);
    } else if (pageNum == 1) {
      setPageNum((prev) => prev + 1);
    } else {
    }
  };

  const handleGetNumbers = (data: string[]) => {
    setSelectedPhoneNumbers(data);

    setAllPhoneNumbers({ ...allPhoneNumbers, TR: data });
  };
  const handleGetNumbersCSV = (data: string[]) => {
    const selectedUsers = renamedArray?.filter((value) => {
      return data.includes(value.phone);
    });
    setSelectedRenamedArray(selectedUsers);
    setAllPhoneNumbers({ ...allPhoneNumbers, CSV: data });
  };
  const handleGetNumbersCheckPoints = (data: string[]) => {
    setAllPhoneNumbers({ ...allPhoneNumbers, CP: data });
  };

  const onSaveHandler = async () => {
    if (!boradcastName) {
      toast.error("Name is required");
      return;
    }
    if (
      allPhoneNumbers.CSV?.length === 0 &&
      allPhoneNumbers.TR?.length === 0 &&
      allPhoneNumbers.CP?.length === 0
    ) {
      toast.error("Numbers are required");
      return;
    }

    setIncomingActivity([]);
    let path = ``;
    // console.log(templatesForNumbers);
    // return;
    if (templatesForNumbers.length > 0) {
      path = `wa/${bot.file_name}/Templates/${generateStorageId(12)}.json`;
      const upload = await uploadFile({
        path,
        file_body: JSON.stringify({
          users: templatesForNumbers,
        }),
      });
      // const file = await getFile(path);
    }
    const DataToCreate = {
      broadcast_name: boradcastName,
      message: "",
      template: JSON.stringify(
        templateJsonHelper.onGenerateJson2Send(templateData)
      ),
      sent_count: 0,
      image_id: "",
      image_url: templateData.image || "",
      bot_id: bot.bot_id,
      path,
    };
    setPageNum((prev) => prev + 1);

    const phoneNumbersArr = [
      ...allPhoneNumbers.TR,
      ...allPhoneNumbers.CSV,
      ...allPhoneNumbers.CP,
    ].filter((num) => num);
    const distinctPhoneNumbers = [...new Set(phoneNumbersArr)];

    createBroadcast(DataToCreate)
      .then(async (createdBroadcast) => {
        if (createdBroadcast) {
          const DataToSend = {
            broadcast_id: createdBroadcast.broadcast_id,
            phone_numbers: distinctPhoneNumbers,
            // path: path,
            // phone_numbers_templates: templatesForNumbers,
            bot_id: bot.bot_id,
          };

          sendBroadcast(DataToSend);
          setBroadcastID(createdBroadcast.broadcast_id);
        }
      })
      .then(() => {
        get_braodcasts(bot.bot_id);
      });
  };
  /**
   * Converts a WhatsApp-approved template object into a Vonage request body.
   * It replaces all dynamic fields (e.g. placeholders in header and body text)
   * with placeholder strings of the form ##key## and returns a mapping object for later substitution.
   *
   * For buttons, all buttons of type "QUICK_REPLY" are processed and assigned a component
   * with the appropriate index.
   *
   * @param {Object} waTemplate - The WhatsApp template object.
   * @returns {Object} An object containing:
   *                   - vonageRequest: The Vonage request body.
   *                   - mapping: An object mapping each placeholder key to an empty string.
   */
  function convertTemplate(waTemplate) {
    // This object will collect keys for later value substitution.
    const mapping = {};

    // Helper to extract all placeholders from a given text.
    function extractPlaceholders(text) {
      const regex = /{{\s*([^}]+)\s*}}/g;
      const placeholders = [];
      let match;
      while ((match = regex.exec(text)) !== null) {
        placeholders.push(match[1]);
      }
      return placeholders;
    }

    // Array to hold the Vonage template components.
    const vonageComponents = [];

    // --- Process HEADER Component ---
    const headerComponent = waTemplate.components.find(
      (comp) => comp.type.toUpperCase() === "HEADER"
    );
    if (headerComponent) {
      const placeholders = extractPlaceholders(headerComponent.text);
      const parameters = placeholders.map((ph) => {
        const key = `header_${ph}`;
        mapping[key] = "";
        return {
          type: "text",
          text: `##${key}##`,
        };
      });
      vonageComponents.push({
        type: "header",
        parameters: parameters,
      });
    }

    // --- Process BODY Component ---
    const bodyComponent = waTemplate.components.find(
      (comp) => comp.type.toUpperCase() === "BODY"
    );
    if (bodyComponent) {
      const placeholders = extractPlaceholders(bodyComponent.text);
      const parameters = placeholders.map((ph) => {
        const key = `BODY_${ph}`;
        mapping[key] = "";
        return {
          type: "text",
          text: `##${key}##`,
        };
      });
      vonageComponents.push({
        type: "body",
        parameters: parameters,
      });
    }

    // --- Process BUTTONS Component for QUICK_REPLY buttons ---
    const buttonsComponent = waTemplate.components.find(
      (comp) => comp.type.toUpperCase() === "BUTTONS"
    );
    if (buttonsComponent && Array.isArray(buttonsComponent.buttons)) {
      // Filter out all buttons of type QUICK_REPLY.
      const quickReplyButtons = buttonsComponent.buttons;

      // For each quick reply button, add a separate Vonage button component.
      quickReplyButtons.forEach((btn, index) => {
        if (btn.type.toUpperCase() === "QUICK_REPLY") {
          const key = `button_payload_${index}`;
          mapping[key] = "";
          vonageComponents.push({
            type: "button",
            sub_type: "quick_reply",
            index: `${index}`,
            parameters: [
              {
                type: "payload",
                payload: `##${key}##`,
              },
            ],
          });
        }
      });
    }

    // --- Build the final Vonage request body ---
    const vonageRequest = {
      message_type: "custom",
      custom: {
        type: "template",
        template: {
          name: waTemplate.name,
          language: { code: waTemplate.language },
          components: vonageComponents,
        },
      },
      channel: "whatsapp",
    };

    return { vonageRequest, mapping };
  }

  return (
    <FullModal
      key={"broadcasts"}
      title={"Create Broadcast"}
      isOpen={showModal}
      onClose={handleClose}
      className="xl:w-1/2"
    >
      {pageNum === 0 ? (
        <div className=" overflow-y-auto max-h-96 px-14">
          <div className="mb-5">
            <Input
              title="Broadcast name"
              name="broadcast name"
              value={boradcastName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setBoradcastName(e.target.value)
              }
            />
            {!boradcastName.trim() && (
              <p className="text-red-500">{"Name is required"}</p>
            )}
          </div>
          <div className="mb-5">
            <label className={`block`} htmlFor={"selectTemplate"}>
              {"Select a Template"}{" "}
            </label>
            <Select
              key={"selectTemplate"}
              onValueChange={(value) => {
                const Temp = templates?.filter((template) => {
                  return template.name === value;
                });
                setErrors((prev) => {
                  const newErrors = { ...prev };
                  Object.keys(prev).forEach((key) => {
                    newErrors[key] = "";
                  });
                  return newErrors;
                });
                setSelectedTemplate(Temp[0]);
              }}
              value={selectedTemplate?.name}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a template" />
              </SelectTrigger>
              <SelectContent>
                {templates?.map((template) => {
                  return (
                    <SelectItem key={template.name} value={template.name}>
                      {template.name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          {selectedTemplate
            ? JSON.stringify(
                convertTemplate({
                  name: "test_template_h_text",
                  parameter_format: "POSITIONAL",
                  components: [
                    {
                      type: "HEADER",
                      format: "TEXT",
                      text: "hi {{1}}",
                      example: {
                        header_text: ["azzam"],
                      },
                    },
                    {
                      type: "BODY",
                      text: "hi {{1}}, your order is {{2}} , order number {{3}}",
                      example: {
                        body_text: [["azzam", "on the way", "123"]],
                      },
                    },
                    {
                      type: "FOOTER",
                      text: "asad",
                    },
                    {
                      type: "BUTTONS",
                      buttons: [
                        {
                          type: "PHONE_NUMBER",
                          text: "call us",
                          phone_number: "+962778086316",
                        },
                        {
                          type: "QUICK_REPLY",
                          text: "view",
                        },
                        {
                          type: "URL",
                          text: "video",
                          url: "https://www.youtube.com/watch?v=QkzI-r1JNCo",
                        },
                        {
                          type: "QUICK_REPLY",
                          text: "view",
                        },
                      ],
                    },
                  ],
                  language: "en",
                  status: "APPROVED",
                  category: "UTILITY",
                  id: "978207077085821",
                })
              )
            : // <CreateTemplateForm
              //   setCreateDisabled={setCreateDisabled}
              //   setErrors={setErrors}
              //   errors={errors}
              //   templateData={templateData}
              //   setTemplateData={setTemplateData}
              //   readMode={true}
              //   setImages={setImages}
              //   images={images}
              //   ImageUrlData={ImageUrlData}
              //   setImageUrlData={setImageUrlData}
              //   setIsCSV={setIsCSV}
              //   isCSV={isCSV}
              // />
              null}
        </div>
      ) : pageNum === 1 ? (
        <div className="h-96 overflow-y-auto">
          <PhoneNumbers
            isCSV={isCSV}
            templateData={templateData}
            importedFileData={importedFileData}
            setImpotedFileData={setImpotedFileData}
            onChange={handleGetNumbers}
            onChangeCSV={handleGetNumbersCSV}
            onChangeCP={handleGetNumbersCheckPoints}
            setRenamedArray={setRenamedArray}
            renamedArray={renamedArray}
          />
        </div>
      ) : (
        <BroadcastLogToast incomingActivity={incomingActivity} />
      )}
      <div className="flex justify-around items-center mt-2  h-10 border-t-2 border-gray-400 pt-5">
        <>
          <Button
            onClick={() => {
              setPageNum((prev) => prev - 1);
            }}
            disabled={pageNum == 0}
          >
            {"<< Prev"}
          </Button>
          {pageNum === 2 ? (
            <p>{""}</p>
          ) : (
            <>
              {pageNum !== 1 ? (
                <>
                  <Button
                    loading={uploadLoading}
                    onClick={() => {
                      handleNextPage();
                    }}
                    disabled={selectedTemplate === null}
                  >
                    {"Next >>"}
                  </Button>
                </>
              ) : (
                <>
                  <Button loading={loading} onClick={onSaveHandler}>
                    {"Create and Send"}
                  </Button>
                </>
              )}
            </>
          )}
        </>
      </div>
    </FullModal>
  );
};
