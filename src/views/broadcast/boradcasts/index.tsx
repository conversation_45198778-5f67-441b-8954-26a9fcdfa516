import useConfirmModal from "common/hooks/useConfirmModal";
import searchedArray from "helpers/search";
import { PlusCircleIcon, SendIcon, View } from "lucide-react";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";

import useBroadcastStore from "store/broadcast/wa.boradcast.store";
import { ShowBroadcast } from "./components/ShowBroadcast";
import { CreateBroadcastModal } from "./components/CreateBroadcast";

import helper from "../helper";
import { SendBroadcast } from "./components/SendBroadcast";
import { signalRNegotiate } from "apis/broadcast.api";

export type Tbroadcast = {
  broadcast_id?: number;
  broadcast_name: string;
  message: string;
  image_url: string;
  image_id: string;
  sent_count: number;
  template: string;
  path?: string | null;
};

export type statusType = "submitted" | "delivered" | "read" | "rejected";

export type statType = {
  phoneNumber: string;
  status: statusType;
};

export const Broadcasts = () => {
  const [incomingActivity, setIncomingActivity] = useState(null);

  const bot = useBotStore((state) => state.bot) as IBot;
  const [keySearch, setKeySearch] = useState("");
  const [renderedBroadcasts, setRenderedBroadcasts] = useState<Tbroadcast[]>(
    []
  );
  const [showModal, setShowModal] = useState(false);

  const [showModalBroadcast, setShowModalBroadcast] = useState(false);
  const [sendModalBroadcast, setSendModalBroadcast] = useState(false);

  const [broadcastToSend, setBroadcastsToSend] = useState<Tbroadcast>(null);

  const [broadcastToShow, setBroadcastsToShow] = useState<Tbroadcast>(null);

  const [statsToShow, setStatsToShow] = useState<statType[]>(null);

  const {
    get_braodcasts,
    broadcast,
    get_braodcasts_logs,
    broadcastLogs,
    loading,
    get_wa_phone_numbers,
    phoneNumbers,
  } = useBroadcastStore();
  useEffect(() => {
    if (bot?.bot_id) {
      get_braodcasts(bot.bot_id);
      get_wa_phone_numbers(bot.bot_id);
    }
  }, [bot.bot_id]);

  useEffect(() => {
    if (broadcast && broadcast?.length > 0) {
      const UpdatedBroadcast = broadcast
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .map((acc) => {
          const formattedDateTime = helper.changeDateFormat(acc.createdAt);
          return { ...acc, createdAt: formattedDateTime };
        });

      let broadcasts_to_view = UpdatedBroadcast?.reduce(
        (acc: Tbroadcast[], current: Tbroadcast) => {
          const x = acc.find(
            (item) => item.broadcast_name === current.broadcast_name
          );
          if (!x) {
            return acc.concat([current]);
          } else {
            return acc;
          }
        },
        []
      );

      setRenderedBroadcasts(
        searchedArray(keySearch, broadcasts_to_view, [
          "broadcast_id",
          "broadcast_name",
        ])
      );
    } else {
      setRenderedBroadcasts(
        searchedArray(keySearch, [], ["broadcast_id", "broadcast_name"])
      );
    }
  }, [keySearch, broadcast]);

  const broadcastsTableActions = [
    {
      label: "Show",
      onClick: (boradcast: Tbroadcast) => {
        setBroadcastsToShow(boradcast);
        setShowModalBroadcast(true);
      },
      icon: View,
    },
    // {
    //   label: "Send",
    //   onClick: (boradcast: Tbroadcast) => {
    //     setBroadcastsToSend(boradcast);
    //     setSendModalBroadcast(true);
    //   },
    //   icon: SendIcon,
    // },
  ];

  const onClickCreateTemplate = () => {
    setShowModal(true);
  };

  useEffect(() => {
    if (!incomingActivity) {
      setStatsToShow([]);
    } else {
      let newStats = statsToShow ? [...statsToShow] : [];
      const newStat = {
        phoneNumber: incomingActivity?.phoneNumber,
        status: incomingActivity?.status,
      };
      const index = newStats.findIndex(
        (stat) => stat.phoneNumber === newStat.phoneNumber
      );
      if (index === -1) {
        newStats.push(newStat);
      } else {
        newStats[index] = newStat;
      }
      newStats = newStats.filter((stat) => stat.phoneNumber && stat.status);
      setStatsToShow(newStats);
    }
  }, [incomingActivity]);
  const [isCSV, setIsCSV] = useState(false);

  return (
    <div className="space-y-3">
      <CreateBroadcastModal
        setIsCSV={setIsCSV}
        isCSV={isCSV}
        showModal={showModal}
        setShowModal={setShowModal}
        incomingActivity={statsToShow}
        setIncomingActivity={setIncomingActivity}
        setStatsToShow={setStatsToShow}
      />

      <ShowBroadcast
        showModal={showModalBroadcast}
        setShowModal={setShowModalBroadcast}
        broadcastToShow={broadcastToShow}
        incomingActivity={incomingActivity}
        setIncomingActivity={setIncomingActivity}
      />
      <SendBroadcast
        showModal={sendModalBroadcast}
        setSendModal={setSendModalBroadcast}
        broadcastToSend={broadcastToSend}
        incomingActivity={statsToShow}
        setIncomingActivity={setIncomingActivity}
        setStatsToShow={setStatsToShow}
      />

      <div className="flex w-full justify-between  align-middle">
        {/* <div className="w-1/3">
          <Button onClick={onClickCreateTemplate} disabled={true}>
            <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Broadcast
          </Button>
        </div> */}

        <div className="w-1/3">
          <CustomSearch
            placeholder="Find a Broadcast..."
            onChange={(value) => setKeySearch(value)}
          />
        </div>
        <div className="w-1/3"></div>
      </div>

      <div className="pt-5">
        <div className=" w-full">
          <MainTable
            key={"Broadcast"}
            loading={loading}
            data={renderedBroadcasts}
            columns={[
              {
                name: "Broadcast Name",
                key: "broadcast_name",
              },
              {
                name: "Sent count",
                key: "sent_count",
              },
              {
                name: "Created At",
                key: "createdAt",
              },
            ]}
            itemsPerPage={10}
            actions={broadcastsTableActions}
            idKey="broadcast_id"
          />
        </div>
      </div>
    </div>
  );
};