import {
  Tbuttons,
  TtemplateDataDefault,
} from "./broadcastTypes/createTemplate.types";

const generateName = (templateData) => {
  const name = `
    "name": "${templateData.templateName}",
    "language": "${templateData.language}",
    "category": "${templateData.category}",
    "allow_category_change": true,
    `;
  return name;
};

const generateBodyText = (templateData) => {
  const body_textData = templateData.bodyVariables.map((variable, index) => {
    const key = `Example for ${index + 1}`;
    return `'${variable[key]}'`;
  });
  const bodyText = `{
    "type": "BODY",
    "format": "TEXT",
    "text":"${templateData.body}.",
    "example": {
      "body_text": "[[${body_textData}]]"
   },},`;
  return bodyText;
};

const generateFooterText = (templateData) => {
  const headerText = `{
    "type": "FOOTER",
    "format": "TEXT",
    "text":"${templateData.footerText}",
 },`;
  return headerText;
};

const generateHeaderImage = (templateData) => {
  const body_textData = templateData.bodyVariables.map((variable, index) => {
    const key = `Example for ${index + 1}`;
    return `'${variable[key]}'`;
  });
  const headerText = `{
   "type": "HEADER",
   "format": "IMAGE",
   "text":"${templateData.body}",
   "example": {
    "header_handle": "h:3:MjAyMi0wOC0wNCUyMDExXzE0XzQ0Ljc0Mjk0NV9XaGF0c0FwcCUyMEltYWdlJTIwMjAyMi0wOC0wNCUyMGF0JTIwMTEuMDcuMzclMjBBTS5qcGVn:aW1hZ2UvanBlZw==:ARZBgKmPpdCDKM9p8VulwOuNGrHg5RZYIrvwYWpn5YgJezdnoufigT9OTwtRYfAhP4EbpvJik7GZv5QGT7cU7k1Fqu3fnDxEe5XkdIypjSgngQ:e:1662475718:ARauPjU_zJe83Ukp5pY",
    "body_text": "[[${body_textData}]]"
 },},`;
  return headerText;
};
const generateHeaderText = (templateData) => {
  const header_textData = templateData.headerVariables.map(
    (variable, index) => {
      const key = `Example for ${index + 1}`;
      return `'${variable[key]}'`;
    }
  );
  const headerText = `{
   "type": "HEADER",
   "format": "TEXT",
   "text":"${templateData.headerText}",
   "example": {
    "header_text": "${header_textData}"
 },
},`;
  return headerText;
};
const generateButtons = (templateData: TtemplateDataDefault) => {
  const buttons = templateData.buttonVariables;
  const generatedButtons = buttons.map((button) => {
    if (button.type === "URL") {
      return `{
       "type": "URL",
      "text": ${JSON.stringify(button.text)},
      "url": ${JSON.stringify(button.url)}, 
    }`;
    } else {
      return `{
      "type": "URL",
     "text": ${JSON.stringify(button.text)},
     "phone_number": ${JSON.stringify(button.phone_number)}, 
   },`;
    }
  });
  const JSONbutton = `
"buttons":[${generatedButtons}]
`;

  //   const headerText = `{
  //     "type": "URL",
  //     "text": "Yes",
  //     "url": "https://example.com/special-offer-opt-in",
  //     "phone_number": "8001111111"
  // },`;
  return JSONbutton;
};

const onGenerateJson = (templateData) => {
  const broadCastJson = `
    {${generateName(templateData)}
    "components": [
     
      ${
        templateData?.header && templateData?.headerType === "text"
          ? generateHeaderText(templateData)
          : ""
      }
      ${
        templateData?.header && templateData?.headerType === "media"
          ? generateHeaderImage(templateData)
          : generateBodyText(templateData)
      } 
      ${templateData?.footer ? generateFooterText(templateData) : ""}
      
      ${templateData?.button ? generateButtons(templateData) : ""}
     
  ]
   }
    
    `;

  return broadCastJson;
};

const onGenerateJson2 = (
  templateData: TtemplateDataDefault,
  mediaHandler: { h: string }
) => {
  const data = {
    name: templateData.templateName,
    language: templateData.language,
    category: templateData.category,
    allow_category_change: true,
    components: [],
  };

  const body_textData = templateData.bodyVariables.map((variable, index) => {
    const key = `Example for ${index + 1}`;
    return `${variable[key]}`;
  });

  const finalStringBody =
    "[[" + body_textData.map((item) => `'${item}'`).join(",") + "]]";
  const bodyText = {
    type: "BODY",
    // format: "TEXT",
    text: templateData.body,
    example: {
      body_text: finalStringBody,
    },
  };

  let DataToSend = {
    ...data,
    components: [...data.components, { ...bodyText }],
  } as any;

  if (templateData.button) {
    const buttons = templateData.buttonVariables;

    const generatedButtons = buttons?.map((button) => {
      if (button.type === "URL") {
        return {
          type: "URL",
          text: button.text,
          url: button.url,
        };
      } else if (button.type === "QUICK_REPLY") {
        return {
          type: "QUICK_REPLY",
          text: button.text,
        };
      } else {
        return {
          type: "PHONE_NUMBER",
          text: button.text,
          phone_number: button.phone_number,
        };
      }
    });
    DataToSend = {
      ...DataToSend,
      components: [
        ...DataToSend.components,
        { type: "BUTTONS", buttons: generatedButtons },
      ],
    };
  }

  if (
    templateData.header &&
    templateData.headerType?.toLocaleLowerCase() === "text"
  ) {
    const header_textData = templateData?.headerVariables.map(
      (variable, index) => {
        const key = `Example for ${index + 1}`;
        return variable[key];
      }
    );

    const headerText = {
      type: "HEADER",
      format: "TEXT",
      text: templateData?.headerText,
      example: {
        header_text: header_textData[0],
      },
    };

    DataToSend = {
      ...DataToSend,
      components: [...DataToSend.components, { ...headerText }],
    };
  } else if (
    templateData.header &&
    templateData.headerType?.toLocaleLowerCase() === "media"
  ) {
    if (templateData.mediaType.toLowerCase() === "image") {
      const headerImage = {
        type: "HEADER",
        format: templateData.mediaType.toUpperCase(),
        example: {
          header_handle: mediaHandler.h,
        },
      };
      DataToSend = {
        ...DataToSend,
        components: [...DataToSend.components, { ...headerImage }],
      };
    } else if (templateData.mediaType.toLowerCase() === "video") {
      const headerVideo = {
        type: "HEADER",
        format: templateData.mediaType.toUpperCase(),
        example: {
          header_handle: mediaHandler.h,
        },
      };
      DataToSend = {
        ...DataToSend,
        components: [...DataToSend.components, { ...headerVideo }],
      };
    } else if (templateData.mediaType.toLowerCase() === "document") {
      const headerDocument = {
        type: "HEADER",
        format: templateData.mediaType.toUpperCase(),
        example: {
          header_handle: mediaHandler.h,
        },
      };
      DataToSend = {
        ...DataToSend,
        components: [...DataToSend.components, { ...headerDocument }],
      };
    }
  } else if (
    templateData.header &&
    templateData.headerType?.toLocaleLowerCase() === "location"
  ) {
    const headerLocation = {
      type: "HEADER",
      format: "LOCATION",
      example: {
        latitude: templateData.location.latitude,
        longitude: templateData.location.latitude,
        name: templateData.location.latitude,
        address: templateData.location.latitude,
      },
    };

    DataToSend = {
      ...DataToSend,
      components: [...DataToSend.components, { ...headerLocation }],
    };
  }

  if (templateData.footer) {
    const headerText = {
      type: "FOOTER",
      // format: "TEXT",
      text: templateData.footerText,
    };

    DataToSend = {
      ...DataToSend,
      components: [...DataToSend.components, { ...headerText }],
    };
  }

  console.log("object😇😇😇😇😇😇");
  console.log(DataToSend);
  console.log(templateData);
  console.log("object😇😇😇😇😇😇");

  return DataToSend;
};

export type TtemplateData = {
  name: string;
  language: string;
  category: string;
  id: string;
  status: string;
  components: any[];
};
const onGenerateJson2Send = (templateData: TtemplateDataDefault) => {
  const TtemplateDataToSend = {
    name: templateData.templateName,
    language: { code: templateData.language },
    // category: templateData.category,
    // id:SelctedTemplateData.id,
    // status: SelctedTemplateData.status,
    components: [],
  };
  const bodyParameters = templateData.bodyVariables.map((v, i) => {
    return {
      type: "text",
      text: v[`Example for ${i + 1}`],
    };
  });
  const body = {
    type: "body",
    parameters: bodyParameters,
  };

  TtemplateDataToSend.components.push(body);

  if (templateData.button) {
    const buttonParameters = templateData.buttonVariables.map((v, i) => {
      if (v.type.toLowerCase() === "phone_number") {
        return {
          type: "button",
          sub_type: v.type,
          index: i,
          parameters: [
            {
              type: "phone_number",
              phone_number: v.phone_number,
            },
          ],
          //     parameters: [{
          //       type: "text",
          //       text: v.text
          //   },{
          //     type: "phone_number",
          //     phone_number: v.phone_number
          //   }
          // ]
        };
      } else if (v.type.toLowerCase() === "quick_reply") {
        return {
          type: "button",
          sub_type: v.type,
          index: i,
          parameters: [
            {
              type: "payload",
              payload: +v?.quick_reply,
            },
          ],
        };
      } else {
        return {
          type: "button",
          sub_type: v.type,
          index: i,
          parameters: [
            {
              type: "url",
              url: v.url,
            },
          ],
          //     parameters: [{
          //       type: "text",
          //       text: v.text
          //   },{
          //     type: "url",
          //     text: v.url
          //   }
          // ]
        };
      }
    });
    const button = {
      type: "buttons",
      parameters: buttonParameters,
    };

    TtemplateDataToSend.components.push(buttonParameters);
  }

  if (templateData.header) {
    const headerParameters = templateData.headerVariables.map((component) => {
      if (templateData.headerType.toLowerCase() === "text") {
        return {
          // type: "header",
          // parameters: [
          // {
          type: "text",
          text: templateData.headerText,
          // }
          // ]
        };
      } else if (templateData.headerType.toLowerCase() === "media") {
        return {
          // type: "header",
          // parameters: [
          // {
          type: templateData.mediaType.toLowerCase().toString(),
          [templateData.mediaType.toLowerCase()]: {
            link: templateData[templateData.mediaType.toLowerCase()],
          },
          // }
          // ]
        };
      } else if (templateData.headerType.toLowerCase() === "location") {
        return {
          // type: "header",
          // parameters: [
          // {
          type: "location",
          location: {
            latitude: templateData.location.latitude,
            longitude: templateData.location.longitude,
            name: templateData.location.name,
            address: templateData.location.address,
          },
          // }
          // ]
        };
      }
    });

    const header = {
      type: "header",
      parameters: headerParameters,
    };

    TtemplateDataToSend.components.push(header);
  }
  return TtemplateDataToSend;
};

export default {
  onGenerateJson,
  onGenerateJson2,
  onGenerateJson2Send,
};
