
const initCreatenumberData = () => {
  return {
    number_title: "",
    number_description: "",
    show_url: false,
    image_url: null,
  };
};
const initCreateBroadcastData = () => {
  return {
    broadcast_name: "",
    message: "",
    show_url: false,
    image_url: null,
  };
};
const initCreateBroadcasts= () => {
  return {
    broadcast_name: "",
    broadcast_message: "",
    message: "",
    template:"testTemp",
    sent_count:0,
    image_id:"0",
    selectedPhoneNumbers:[],
    show_url: false,
    image_url: "",
    bot_id: 0,
  };
};
const changeDateFormat = (date:string)=>{
  const originalDate = new Date(date);
  const day = String(originalDate.getDate()).padStart(2, '0');
  const month = String(originalDate.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const year = originalDate.getFullYear();
  const hours = String(originalDate.getHours()).padStart(2, '0');
  const minutes = String(originalDate.getMinutes()).padStart(2, '0');
  const seconds = String(originalDate.getSeconds()).padStart(2, '0');
  
  const formattedDateTime = `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  return formattedDateTime
}
const checkUrl = (url:string)=>{
  const urlPattern = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
 return urlPattern.test(url);
}
const checkPhone = (phone:string) => {
  const phonePattern = /^[\d\s\-()+]+$/;
  return phonePattern.test(phone);
};
const checkUrlImage = (url:string)=>{
  var urlPattern = /^(https?|ftp):\/\/[^\s/$.?#]+\.(jpg|jpeg|png|gif|bmp|svg)$/i;
  return urlPattern.test(url);
}



export default {
  initCreatenumberData,initCreateBroadcastData,initCreateBroadcasts,changeDateFormat,checkUrl,checkUrlImage,checkPhone
};
