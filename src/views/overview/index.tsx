import { BotIcon } from "common/components/botIcon";
import { But<PERSON> } from "common/ui/button";
import { Card } from "common/ui/card";
import { format } from "date-fns";
import planFunctionChecker from "helpers/planFunctionChecker";
import { LayoutDashboard, Lock, Plus, Settings } from "lucide-react";
import React, { FC, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useBotStore from "store/bot/bot.store";
import { IBot } from "store/bot/bot.types";
import useFAQStore from "store/faq/faq.store";
import useTriggerStore from "store/trigger/trigger.store";
import { botConfigType } from "types/botconfig.types";
import views from "views";

interface IOverviewViewProps {
  setView: React.Dispatch<React.SetStateAction<string>>;
  botConfig: botConfigType;
}

export const OverviewView: FC<IOverviewViewProps> = ({
  setView,
  botConfig,
}) => {
  const { bot, planfunction } = useBotStore();
  const faqs = useFAQStore((state) => state.FAQs);
  const triggers = useTriggerStore((state) => state.triggers);
  const [stats, setStats] = useState({
    faqs: 0,
    dialogs: 0,
    suggestions: 0,
    cards: 0,
  });

  useEffect(() => {
    var faq_to_view = faqs?.reduce((acc, current) => {
      const x = acc.find((item) => item.answer === current.answer);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);

    var tiggers_to_view = triggers?.reduce((acc, current) => {
      const x = acc.find((item) => item.url === current.url);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    tiggers_to_view = tiggers_to_view?.filter(
      (a) =>
        !((botConfig && botConfig.suggested_actions) || []).includes(a.trigger)
    );

    setStats({
      faqs: faq_to_view.length,
      dialogs: tiggers_to_view?.filter(
        (trigger) => trigger.trigger_type === "dialog"
      ).length,
      suggestions: tiggers_to_view?.filter(
        (trigger) => trigger.trigger_type === "suggestion"
      ).length,
      cards: tiggers_to_view?.filter(
        (trigger) => trigger.trigger_type === "card"
      ).length,
    });
  }, [faqs, triggers]);

  return (
    <div className="space-y-8">
      <div className="flex justify-between">
        <div className="flex items-center gap-3">
          <BotIcon
            icon={bot.icon}
            className="w-20 h-20 cursor-pointer"
            onIconClick={() => setView(views.botViews.CONFIGURE_VIEW)}
          />
          <p className=" text-xl text-center">{bot.bot_name}</p>
        </div>
      </div>
      <hr className="text-white/25" />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
        <Card title="General" hr>
          <div className="text-sm space-y-2">
            <div className="flex justify-between gap-5 bg-secondary/50 p-3 rounded">
              <span className="font-bold"> Description:</span>
              <div className="">{bot.description}</div>
            </div>
          </div>
        </Card>

        <Card title="Statistics" hr>
          <div className="flex flex-col gap-3 text-sm">
            {Object.keys(stats)?.map((stat, i) => (
              <div
                className="space-y-2 bg-secondary/50 p-3 rounded flex justify-between"
                key={i}
              >
                <span className="capitalize font-bold">{stat}: </span>{" "}
                {stats[stat]}
              </div>
            ))}
            <hr className="text-white/25 mt-2" />
            <div className="self-end">
              <Button
                variant="outline"
                onClick={() => {
                  if (
                    planFunctionChecker(planfunction, "transaction_dashboard")
                  ) {
                    setView(views.botViews.DASHBOARD_VIEW);
                  } else {
                    // change view to plans
                    toast(
                      "You need to upgrade your plan to access this feature",
                      {
                        position: "bottom-center",
                        icon: "🔒",
                      }
                    );
                  }
                }}
              >
                {planFunctionChecker(planfunction, "transaction_dashboard") ? (
                  <LayoutDashboard className="mr-2 h-4 w-4" />
                ) : (
                  <Lock className="mr-2 h-4 w-4" />
                )}{" "}
                View Dashboard
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
