import { SubPageHeader } from "common/components/headers";
import React from "react";
import { GPTPlugin, LiveChatPlugin, TrelloPlugin,GenesysPlugin,SmtpPlugin } from "./components";
import useBotStore from "store/bot/bot.store";
import { CalendlyPlugin } from "./components/calendly/calendly.plugin";
import SendgridPlugin from "./components/sendgrid/sendgrid.plugin";
import { LLMPlugin } from "./components/LLM/llm.plugin";
export const PluginsView = () => {
  const planfunction = useBotStore((state) => state.planfunction);
  if (planfunction.plan_id !== 3)
    return (
      <div className="flex items-center justify-center h-full">
        <h1 className="text-2xl font-bold text-center">
          You need to upgrade your plan to access this feature.
        </h1>
      </div>
    );
  return (
    <div className="space-y-3">
      <SubPageHeader
        title="Plugins"
        description="Integrate your bot with these services!"
      />
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3  gap-4">
        {/* <GPTPlugin /> */}
        <LLMPlugin />
        {/* <TrelloPlugin /> */}
        <LiveChatPlugin />
        {/* <CalendlyPlugin /> */}
        <SendgridPlugin />
        <GenesysPlugin />
        <SmtpPlugin />
      </div>
    </div>
  );
};
