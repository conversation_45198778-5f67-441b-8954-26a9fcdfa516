import React, { useEffect, useState } from "react";
import { Plugin } from "../plugin";
import { GPTSettings } from "./gpt.settings";
import useGPTStore from "store/gpt/gpt.store";
import useBotStore from "store/bot/bot.store";
import GPTSVG from "common/icons/GPTSVG";

export const GPTPlugin = () => {
  const [showModal, setShowModal] = useState(false);
  const bot = useBotStore((state) => state.bot);
  const { gpt, get_one_gpt } = useGPTStore();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (gpt?.openai_key) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [gpt]);

  useEffect(() => {
    get_one_gpt(bot.bot_id);
  }, []);

  return (
    <>
      <GPTSettings
        isUpdate={isConnected}
        showModal={showModal}
        setShowModal={setShowModal}
      />
      <Plugin
        title="GPT"
        subtitle="Generate text using GPT-3"
        icon={GPTSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};
