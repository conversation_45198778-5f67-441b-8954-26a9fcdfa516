import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Switch } from "common/ui/inputs/switch";
import { Loader2, Trash2 } from "lucide-react";
import React, { useEffect, useState } from "react";

import useBotStore from "store/bot/bot.store";
import useGPTStore from "store/gpt/gpt.store";
import useSearchSettingsStore from "store/search/search.store";
import useSentimentAddonStore from "store/sentimentAddon/sentimentAddon.store";

interface GPTSettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

export const GPTSettings: React.FC<GPTSettingsProps> = ({
  showModal,
  setShowModal,
  isUpdate,
}) => {
  const confirmModal = useConfirmModal();
  const bot = useBotStore((state) => state.bot);
  const { sentimentAddon, updateSentimentAddon, getSentimentAddon } =
    useSentimentAddonStore();
  const {
    create_one_searchSettings,
    get_one_searchSettings,
  } = useSearchSettingsStore();
  const {
    create_one_gpt,
    gpt,
    update_one_gpt,
    get_one_gpt,
    delete_one_gpt,
  } = useGPTStore();
  const [key, setKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    if (isUpdate && gpt.openai_key) {
      setKey(gpt?.openai_key);
    } else {
      setKey("");
    }
  }, [isUpdate, gpt]);

  const onSaveHandler = async () => {
    if (key.trim() === "") return;
    setSaveLoading(true);
    if (isUpdate) {
      if (key === gpt.openai_key) return;
      await update_one_gpt({
        bot_id: bot.bot_id,
        openai_key: key,
        status_active: true,
      });
    } else {
      await create_one_gpt({
        bot_id: bot.bot_id,
        openai_key: key,
        status_active: true,
      });
      await create_one_searchSettings({
        bot_id: bot.bot_id,
        engine: "gpt",
        kb_type: "structured",
      });

      get_one_searchSettings(bot.bot_id);
    }
    setSaveLoading(false);
  };

  const onSwitchChange = async () => {
    setLoading(true);
    await update_one_gpt({
      bot_id: bot.bot_id,
      status_active: !gpt.status_active,
    });
    await create_one_searchSettings({
      bot_id: bot.bot_id,
      engine: !gpt.status_active ? "gpt" : "searchat",
      kb_type: "structured",
    });
    get_one_searchSettings(bot.bot_id);
    
    if (sentimentAddon?.sentiment_addon_id) {
      await updateSentimentAddon({
        bot_id: bot.bot_id,
        ...sentimentAddon,
        generated_by: "user",
      });
      getSentimentAddon(bot.bot_id);
    }
    get_one_gpt(bot.bot_id);
    setLoading(false);
  };

  const onDelete = async () => {
    await delete_one_gpt(bot.bot_id);
    if (sentimentAddon?.sentiment_addon_id) {
      await updateSentimentAddon({
        bot_id: bot.bot_id,
        ...sentimentAddon,
        generated_by: "user",
      });
      getSentimentAddon(bot.bot_id);
    }
    await create_one_searchSettings({
      bot_id: bot.bot_id,
      engine: "searchat",
      kb_type: "structured",
    });
    setLoading(false);
    get_one_searchSettings(bot.bot_id);
    get_one_gpt(bot.bot_id);

    handleClose();
  };

  const handleClose = () => {
    if (gpt.openai_key !== key) setKey(gpt.openai_key);
    setShowModal(false);
  };
  return (
    <FullModal
      title="Connect Your OpenAi Account"
      isOpen={showModal}
      onClose={handleClose}
      loading={saveLoading}
      onSave={onSaveHandler}
      disabled={
        key.trim() === "" ||
        (gpt.openai_key && key === gpt.openai_key) ||
        loading
      }
      footer
    >
      <div className="flex flex-col gap-10">
        <div className="space-y-1">
          <Input
            name="openai_key"
            title="Your OpenAi Key"
            value={key}
            onChange={(e) => setKey(e.target.value)}
            disabled={gpt?.gpt_integration_id && !gpt?.status_active}
          />
          <div className="text-sm text-white/50">
            By connecting your OpenAI account, we will use the GPT engine to
            answer your customers questions, you can customize search settings
            in Knowledge Base page. <br />
            <br />
            You can enable or disable GPT connection at any time. And you can
            delete your data at any time.
          </div>
        </div>
        {isUpdate && (
          <div className="flex flex-col gap-3 border-t pt-4 border-white/25">
            <div className="self-start scale-90">
              {loading ? (
                <Loader2 size={25} className="animate-spin" />
              ) : (
                <Switch
                  label={
                    gpt.status_active
                      ? " Disable GPT connection"
                      : "Activate GPT connection"
                  }
                  name={`enable_gpt`}
                  checked={gpt?.status_active}
                  onChange={onSwitchChange}
                />
              )}
            </div>
            <div className="self-start scale-90">
              <Button
                onClick={() => {
                  confirmModal.onOpen();
                  confirmModal.setOnConfirm(async () => await onDelete());
                }}
                variant="destructive"
              >
                <Trash2 className="w-5 h-5 mr-2" /> Delete data
              </Button>
            </div>
          </div>
        )}
      </div>
    </FullModal>
  );
};
