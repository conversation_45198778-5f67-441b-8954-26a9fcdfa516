import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import Select from "react-select";
import { Switch } from "common/ui/inputs/switch";
import { ChevronDown, ChevronRight, Info, Loader2, Trash2 } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { customStyles } from "common/components/MultiSelectInput";
import { Separator } from "common/ui/separator";
import toast from "react-hot-toast";
import { Slider } from "common/ui/slider";

import useBotStore from "store/bot/bot.store";
import useLLMStore from "store/llmIntegration/llm.store";
import { testLLM, testStore } from "apis/llmIntegration.api";
import { Textarea } from "common/ui/inputs/textarea";
import { HoverCard, HoverCardContent } from "common/ui/hoverCard";
import { HoverCardTrigger } from "@radix-ui/react-hover-card";
import constant from "constant";

interface LLMSettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

const defLlmConfig = {
  llm_type: "openai",
  llm_model: "gpt-4o-mini",
  llm_key: "",
  llm_temperature: 0.5,
  chunk_size: 2500,
  chunk_overlap: 250,
  chat_history: true,
  store_url: constant.RAG_STORE_URL,
  status_active: true,
  store_type: "chroma",
  collection_name: "",
  personal_vector_db: false,
  chunk_methodology: "recursive",
  top_k: 20,
  persona: "",
  llm_embedding_model: "",
  fetch_k: 50,
  lambda_mult: 0.5,
};

export const LLMSettings: React.FC<LLMSettingsProps> = ({
  showModal,
  setShowModal,
  isUpdate,
}) => {
  const confirmModal = useConfirmModal();
  const bot = useBotStore((state) => state.bot);
  const { create_one_LLM, llm, update_one_LLM, get_one_LLM, delete_one_LLM } =
    useLLMStore();
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [advancedSettings, setAdvancedSettings] = useState(false);
  const [checkApiKey, setCheckApiKey] = useState(null);
  const [checkStore, setCheckStore] = useState(true);
  const [llmSettingsToEdit, setLlmSettingsToEdit] = useState(defLlmConfig);
  const [timestampedMessage, setTimestampedMessage] = useState("");
  useEffect(() => {
    get_one_LLM(bot.bot_id);
  }, [showModal]);

  useEffect(() => {
    console.log("+++++++++LLM+++++++++", llm);
    initValues();
  }, [llm]);

  const initValues = () => {
    if (llm && llm?.LLM_integration_id) {
      setLlmSettingsToEdit({ ...llm });
    } else {
      setLlmSettingsToEdit({
        ...defLlmConfig,
        collection_name: collectionNameDefault,
      });
    }
  };

  const onSaveHandler = async () => {
    if (llmSettingsToEdit.llm_key?.trim() === "") {
      toast("Api Key is a must", {
        icon: "⚠️",
      });
      return;
    }
    setSaveLoading(true);
    if ("LLM_integration_id" in llm && llm?.LLM_integration_id !== 0) {
      update_one_LLM({
        ...llmSettingsToEdit,
        LLM_integration_id: llm?.LLM_integration_id,
        bot_id: bot.bot_id,
      } as any);
    } else {
      await create_one_LLM({
        ...llmSettingsToEdit,
        bot_id: bot.bot_id,
      });
    }
    setSaveLoading(false);
  };

  const onDelete = async () => {
    await delete_one_LLM(bot.bot_id, llm.LLM_integration_id);
    setLoading(false);
    get_one_LLM(bot.bot_id);

    handleClose();
  };

  //default values
  const reset = () => {
    if (llm.LLM_integration_id != 0) {
      setLlmSettingsToEdit({ ...llm });
      return;
    }
    setLlmSettingsToEdit({ ...defLlmConfig });
  };

  const handleClose = () => {
    reset();
    setShowModal(false);
  };

  const onSwitchChangeActiveHandler = async () => {
    setLoading(true);
    if ("LLM_integration_id" in llmSettingsToEdit) {
      await update_one_LLM({
        bot_id: bot.bot_id,
        LLM_integration_id: +llmSettingsToEdit?.LLM_integration_id || 0,
        status_active: !llmSettingsToEdit.status_active,
      });
    }

    get_one_LLM(bot.bot_id);
    setLoading(false);
  };

  const onBlurLLM = async () => {
    try {
      const x = await testLLM({
        llm_config: {
          llm_key: llmSettingsToEdit.llm_key,
          llm_type: llmSettingsToEdit.llm_type,
        },
      });
      if (x.isGood && llmSettingsToEdit.llm_key) {
        setCheckApiKey(true);
      } else {
        setCheckApiKey(false);
      }
      const message = x.isGood ? "Looks good!" : "Invalid Api Key";
      setTimestampedMessage(message);

      setTimeout(() => {
        setTimestampedMessage("");
      }, 10000);
    } catch (error) {}
  };

  const testingStore = async () => {
    try {
      const x = await testStore({
        store_config: {
          store_url: llmSettingsToEdit.store_url,
          store_type: llmSettingsToEdit.store_type,
        },
      });
      if (x.isGood) {
        setCheckStore(true);
      } else {
        setCheckStore(false);
      }
      // console.log("Response from testStore:", x);
    } catch (error) {
      // console.error("Error during testStore call:", error);
    }
  };

  useEffect(() => {
    testingStore();
  }, [llm]);

  const updateSettings = (data: Record<string, any>) => {
    setLlmSettingsToEdit({ ...llmSettingsToEdit, ...data });
  };

  const collectionNameDefault = useMemo(() => {
    return `searchatCollection${bot.bot_id}${Math.floor(
      Math.random() * 10000
    )}`;
  }, [llm]);

  useEffect(() => {
    if (llmSettingsToEdit.llm_type === defLlmConfig.llm_type) {
      get_one_LLM(bot.bot_id);
    } else {
      setLlmSettingsToEdit({
        ...llmSettingsToEdit,
        llm_model: "",
        llm_key: "",
      });
    }
  }, [llmSettingsToEdit.llm_type]);

  return (
    <FullModal
      title="Connect Your LLM Account"
      isOpen={showModal}
      onClose={handleClose}
      loading={saveLoading}
      onSave={onSaveHandler}
      footer
    >
      <div className="flex flex-col gap-10">
        <div className="space-y-4 py-5">
          <div className="space-y-4">
            <label>AI Engine</label>
            <Select
              id="ai_engine"
              value={
                llmSettingsToEdit.llm_type
                  ? [
                      {
                        value: llmSettingsToEdit.llm_type,
                        label: llmSettingsToEdit.llm_type,
                      },
                    ]
                  : null
              }
              options={[
                { value: "openai", label: "openai" },
                { value: "cohere", label: "cohere" },
                { value: "1", label: "DeepSeek" },
                { value: "2", label: "Qwen AI" },
                { value: "3", label: "claude" },
                { value: "4", label: "Gemini" },
              ]}
              onChange={(s) => {
                updateSettings({ llm_type: s.value });
              }}
              placeholder={"Select llm type..."}
              styles={customStyles}
            />
          </div>
          <div>
            <label>LLM Model</label>
            <Select
              id="model"
              value={
                llmSettingsToEdit.llm_model
                  ? [
                      {
                        value: llmSettingsToEdit.llm_model,
                        label: llmSettingsToEdit.llm_model,
                      },
                    ]
                  : null
              }
              // options={[{ value: "gpt-4o-mini", label: "gpt-4o-mini" }]}
              options={
                llmSettingsToEdit.llm_type === "openai"
                  ? [{ value: "gpt-4o-mini", label: "gpt-4o-mini" }]
                  : [
                      { value: "command-r-plus", label: "command-r-plus" },
                      { value: "command-r", label: "command-r" },
                    ]
              }
              onChange={(s) => {
                updateSettings({ llm_model: s.value });
              }}
              placeholder={"Select llm model..."}
              styles={customStyles}
            />
          </div>
          <Input
            name="API_key"
            title="Your LLM API Key"
            value={llmSettingsToEdit.llm_key}
            placeholder="Enter...."
            onChange={(e) => updateSettings({ llm_key: e.target.value })}
            onBlur={onBlurLLM}
            error={checkApiKey === false && timestampedMessage}
            message={checkApiKey === true && timestampedMessage}
          />
          <Separator className="my-4" />

          {llm?.llm_key && (
            <>
              <div onClick={() => setAdvancedSettings(!advancedSettings)}>
                {advancedSettings ? (
                  <div className="flex items-center justify-between p-3">
                    <div className="flex items-center space-x-2">
                      <ChevronDown />
                      <p>Advanced Settings:</p>
                    </div>
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        reset();
                      }}
                      disabled={false}
                      className="text-sm"
                    >
                      Reset To Defaults
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 p-3 ">
                    <ChevronRight />
                    <p>Advanced Settings: </p>
                  </div>
                )}
              </div>
              {advancedSettings && (
                <div>
                  <div>
                    <div className="flex space-x-4 w-full">
                      <label>
                        Temperature {llmSettingsToEdit.llm_temperature}
                      </label>
                    </div>
                    <Slider
                      defaultValue={[0.5]}
                      value={[llmSettingsToEdit.llm_temperature ?? 0.5]}
                      max={1}
                      min={0}
                      step={0.1}
                      onValueChange={(value) => {
                        updateSettings({ llm_temperature: value[0] });
                      }}
                    />
                  </div>
                  <span className="p-5"></span>
                  <div>
                    <label>Embedding Settings:</label>
                    <div>
                      <div className="flex space-x-4 w-full">
                        <div className="space-y-1 relative w-full">
                          <div className="flex space-x-4 w-full">
                            <label className="text-m ">Chunk Size</label>
                          </div>
                          <input
                            type="text"
                            min={0}
                            id="chunk_size"
                            placeholder="0"
                            className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                            value={llmSettingsToEdit.chunk_size}
                            onChange={(e) =>
                              updateSettings({
                                chunk_size: parseInt(e.target.value, 10) || 0,
                              })
                            }
                          />
                        </div>
                        <div className="space-y-1 relative w-full">
                          <div className="flex space-x-4 w-full">
                            <label className="text-m ">Chunk OverLap</label>
                          </div>
                          <input
                            type="text"
                            min={0}
                            id="chunk_overlap"
                            placeholder="0"
                            className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                            value={llmSettingsToEdit.chunk_overlap}
                            onChange={(e) =>
                              updateSettings({
                                chunk_overlap:
                                  parseInt(e.target.value, 10) || 0,
                              })
                            }
                          />
                        </div>
                      </div>
                      <div className="flex space-x-4 w-full">
                        <div className="space-y-1 relative w-full">
                          <div className="flex space-x-4 w-full">
                            <label className="text-m ">Chunk Methodology</label>
                          </div>
                          <Select
                            id="sdf"
                            value={
                              llmSettingsToEdit.chunk_methodology
                                ? [
                                    {
                                      value:
                                        llmSettingsToEdit.chunk_methodology,
                                      label:
                                        llmSettingsToEdit.chunk_methodology,
                                    },
                                  ]
                                : null
                            }
                            options={[
                              { value: "semantic", label: "semantic" },
                              {
                                value: "CharacterTextSplitter",
                                label: "CharacterTextSplitter",
                              },
                              {
                                value: "RecursiveCharacterTextSplitter",
                                label: "RecursiveCharacterTextSplitter",
                              },
                              {
                                value: "SentenceTransformersTokenTextSplitter",
                                label: "SentenceTransformersTokenTextSplitter",
                              },
                              {
                                value: "TokenTextSplitter",
                                label: "TokenTextSplitter",
                              },
                            ]}
                            onChange={(s) => {
                              updateSettings({ chunk_methodology: s.value });
                            }}
                            placeholder={"Select Chuck methodology..."}
                            styles={customStyles}
                          />
                        </div>
                        <div className="space-y-1 relative w-full">
                          <div className="flex space-x-4 w-full">
                            <label className="text-m ">Embedding Model</label>
                          </div>
                          <input
                            title="llm embedding module"
                            type="text"
                            id="llm_embedding_module"
                            placeholder=""
                            className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                            value={llmSettingsToEdit.llm_embedding_model || ""}
                            onChange={(e) =>
                              updateSettings({
                                llm_embedding_model: e.target.value,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <span className="p-5"></span>

                  <div>
                    <label>Retrieval Settings:</label>
                    <div className="flex space-x-4 w-full">
                      <div className="space-y-1 relative w-full">
                        <div className="flex space-x-4 w-full">
                          <label className="text-m ">Fetch K</label>
                        </div>
                        <input
                          type="number"
                          min={0}
                          id="fetch_k"
                          placeholder="0"
                          className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                          value={llmSettingsToEdit.fetch_k}
                          onChange={(e) =>
                            updateSettings({
                              fetch_k: parseInt(e.target.value, 10) || 0,
                            })
                          }
                        />
                      </div>
                      <div className="space-y-1 relative w-full">
                        <div className="flex space-x-4 w-full">
                          <label className="text-m ">Top K</label>
                        </div>
                        <input
                          type="number"
                          min={0}
                          id="top_k"
                          placeholder="0"
                          className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                          value={llmSettingsToEdit.top_k}
                          onChange={(e) =>
                            updateSettings({
                              top_k: parseInt(e.target.value, 10) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                    <div className="space-y-1 relative w-full">
                      <div className="flex space-x-4 w-full">
                        <label className="text-m ">Lambda Mult</label>
                      </div>
                      <input
                        type="number"
                        id="lambda_mult"
                        placeholder="0"
                        className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                        value={llmSettingsToEdit.lambda_mult}
                        onChange={(e) =>
                          updateSettings({
                            lambda_mult: parseFloat(e.target.value),
                          })
                        }
                      />
                    </div>
                    <div>
                      <div className="flex space-x-4 w-full">
                        <label>Add Bot Persona Information:</label>
                      </div>
                      <Textarea
                        name="persona"
                        id="persona"
                        value={llmSettingsToEdit.persona}
                        onChange={(e) => {
                          updateSettings({ persona: e.target.value });
                        }}
                      ></Textarea>
                    </div>
                  </div>
                  <div className="flex justify-center items-center p-4 space-x-8 ">
                    <div className="flex items-center mb-4 ">
                      <Switch
                        label={"Chat History"}
                        name={`chat_history`}
                        checked={llmSettingsToEdit.chat_history}
                        onChange={() =>
                          updateSettings({
                            chat_history: !llmSettingsToEdit.chat_history,
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center mb-4">
                      <Switch
                        label={"Personal Vector DB"}
                        name={`vectorDb`}
                        checked={llmSettingsToEdit.personal_vector_db}
                        onChange={() => {
                          if (llmSettingsToEdit.personal_vector_db) {
                            updateSettings({
                              personal_vector_db: false,
                              store_url: constant.RAG_STORE_URL,
                              store_type: "chroma",
                              collection_name: collectionNameDefault,
                            });
                          } else {
                            updateSettings({
                              personal_vector_db: true,
                              store_url: "",
                              store_type: "",
                              collection_name: collectionNameDefault,
                            });
                          }
                        }}
                      />
                    </div>
                  </div>

                  {llmSettingsToEdit.personal_vector_db && (
                    <div>
                      <div className="space-y-1 relative w-full">
                        <div className="flex space-x-4 w-full">
                          <label className="text-m ">Connection String</label>
                        </div>
                        <Input
                          name="connection_string"
                          type="text"
                          className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                          value={llmSettingsToEdit.store_url}
                          onChange={(e) =>
                            updateSettings({ store_url: e.target.value })
                          }
                          // error={
                          //   checkStore === false &&
                          //   "Invalid store connection String or Type"
                          // }
                          // message={checkStore === true && "Looks good!"}
                        />
                      </div>
                      <div className="space-y-1 relative w-full py-4">
                        <div className="flex space-x-4 w-full">
                          <label>Type</label>
                        </div>
                        <Select
                          id="regweff"
                          value={
                            llmSettingsToEdit.store_type
                              ? [
                                  {
                                    value: llmSettingsToEdit.store_type,
                                    label: llmSettingsToEdit.store_type,
                                  },
                                ]
                              : null
                          }
                          options={[
                            { value: "chroma", label: "chroma" },
                            { value: "milvus", label: "milvus" },
                          ]}
                          onChange={(s) => {
                            updateSettings({ store_type: s.value });
                          }}
                          placeholder={"Select store type..."}
                          styles={customStyles}
                        />
                      </div>
                      <div className="space-y-1 relative w-full">
                        <div className="flex space-x-4 w-full">
                          <label className="text-m ">Collection Name</label>
                        </div>
                        <input
                          title="Collection Name"
                          type="text"
                          id="con_string"
                          className="px-3 py-2 w-full bg-transparent border border-white/25 rounded-md focus:border-white/70 focus:ring-0 disabled:text-white/25 "
                          value={llmSettingsToEdit.collection_name}
                          onChange={(e) => {
                            updateSettings({ collection_name: e.target.value });
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
              <Separator />
            </>
          )}
          <div className="text-sm text-white/50">
            By connecting your OpenAI account, we will use the GPT engine to
            answer your customers questions, you can customize search settings
            in Knowledge Base page. <br />
            <br />
            You can enable or disable GPT connection at any time. And you can
            delete your data at any time.
          </div>
        </div>
        {isUpdate && (
          <div className="flex flex-col gap-3 border-t pt-4 border-white/25">
            <div className="self-start scale-90">
              {loading ? (
                <Loader2 size={25} className="animate-spin" />
              ) : (
                <Switch
                  label={
                    !llmSettingsToEdit?.status_active ? "Disabled" : "Active"
                  }
                  name={`enable_llm`}
                  checked={llmSettingsToEdit?.status_active}
                  onChange={onSwitchChangeActiveHandler}
                />
              )}
            </div>
            <div className="self-start scale-90">
              <Button
                onClick={() => {
                  confirmModal.onOpen();
                  confirmModal.setOnConfirm(async () => await onDelete());
                }}
                variant="destructive"
              >
                <Trash2 className="w-5 h-5 mr-2" /> Delete data
              </Button>
            </div>
          </div>
        )}
      </div>
    </FullModal>
  );
};
