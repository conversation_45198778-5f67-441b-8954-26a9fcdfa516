import React, { useEffect, useState } from "react";
import { Plugin } from "../plugin";
import { LLMSettings } from "./llm.settings";
import useBotStore from "store/bot/bot.store";
import useLLMStore from "store/llmIntegration/llm.store";
import LLMIntegrationSVG from "common/icons/LLMSVG";

export const LLMPlugin = () => {
  const [showModal, setShowModal] = useState(false);
  const bot = useBotStore((state) => state.bot);
  const { llm, get_one_LLM } = useLLMStore();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (llm?.llm_key) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [llm]);

  useEffect(() => {
    get_one_LLM(bot.bot_id);
  }, []);

  return (
    <>
      <LLMSettings
        isUpdate={isConnected}
        showModal={showModal}
        setShowModal={setShowModal}
      />
      <Plugin
        title="LLM"
        subtitle="Generate text using custom AI"//fix the subtitle
        icon={LLMIntegrationSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};
