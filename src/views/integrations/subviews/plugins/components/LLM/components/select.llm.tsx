import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface SelectLLM {
  value: string;
  onChange: (value: string) => void;
  data: { value: string; label: string }[];
  label?: string;
}
export const SelectLLM = ({ value, onChange, data, label }: SelectLLM) => {
  return (
    <Select
      value={value}
      onValueChange={(value) => {
        console.log("value", value);
        onChange(value);
      }}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select..." />
      </SelectTrigger>
      <SelectContent>
        {data.map((item, i) => {
          return (
            <SelectItem key={i} value={item.value}>
              {item.label}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};
