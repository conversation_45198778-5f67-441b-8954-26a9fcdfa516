import { checkExist } from "apis/trello.api";
import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Switch } from "common/ui/inputs/switch";
import toast from "react-hot-toast";
import { Trash2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useTrelloStore from "store/trello/trello.store";
import { Loader2 } from "lucide-react";
interface TrelloSettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

export const TrelloSettings: React.FC<TrelloSettingsProps> = ({
  showModal,
  setShowModal,
  isUpdate,
}) => {
  const confirmModal = useConfirmModal();
  const bot = useBotStore((state) => state.bot);
  const {
    get_one_trello,
    trello,
    update_one_trello,
    create_one_trello,
    delete_one_trello,
    loading
  } = useTrelloStore();
  const [trelloData, setTrelloData] = useState({
    key: "",
    token: "",
  });

  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    if (isUpdate && trello.key) {
      setTrelloData({
        token: trello.token,
        key: trello.key,
      });
    } else {
      setTrelloData({
        token: "",
        key: "",
      });
    }
  }, [isUpdate, trello]);

  const onSaveHandler = async () => {
    if (trelloData.key.trim() === "" || trelloData.token.trim() === "") return;
    setSaveLoading(true);
    var validateTrello = await checkExist(trelloData.token, trelloData.key);

    if (validateTrello.status == 200) {
      if (isUpdate) {
        if (trelloData.key === trello.key && trelloData.token === trello.token)
          return;
        update_one_trello({
          bot_id: bot.bot_id,
          key: trelloData.key,
          token: trelloData.token,
          status_active: true,
        })
      } else {
        create_one_trello({
          bot_id: bot.bot_id,
          key: trelloData.key,
          token: trelloData.token,
          status_active: true,
        })
      }
    } else {
      toast.error("Invalid Trello Key or Token");
    }
    setSaveLoading(false);
  };

  const onSwitchChange = async () => {
    await update_one_trello({
      bot_id: bot.bot_id,
      status_active: !trello.status_active,
    })
    get_one_trello(bot.bot_id);
  };

  const onDelete = async () => {
    await delete_one_trello(bot.bot_id);
    get_one_trello(bot.bot_id);
    toast.success("Trello data deleted successfully");
    handleClose();
  };

  const handleClose = () => {
    setShowModal(false);
    if (isUpdate) {
      setTrelloData({
        key: trello.key,
        token: trello.token,
      });
    } else {
      setTrelloData({
        key: "",
        token: "",
      });
    }
  };
  return (
    <FullModal
      title="Connect Trello"
      isOpen={showModal}
      onClose={handleClose}
      loading={saveLoading}
      onSave={onSaveHandler}
      disabled={
        trelloData.key.trim() === "" ||
        trelloData.token.trim() === "" ||
        (trello.key &&
          trelloData.key === trello.key &&
          trelloData.token === trello.token) 
      }
      footer
    >
      <div className="flex flex-col gap-6">
        <Input
          name="trello_key"
          title="Your Trello Key"
          value={trelloData.key}
          onChange={(e) =>
            setTrelloData({ ...trelloData, key: e.target.value })
          }
          disabled={trello.trello_plugin_id && !trello.status_active}
        />
        <Input
          name="trello_token"
          title="Your Trello Token"
          value={trelloData.token}
          onChange={(e) =>
            setTrelloData({ ...trelloData, token: e.target.value })
          }
          disabled={trello.trello_plugin_id && !trello.status_active}
        />
        {isUpdate && (
          <>
            <div className="self-start">
            {loading ? (
              <Loader2 size={25} className=" animate-spin" />
            ) : (
              <Switch
                label={
                  trello.status_active
                    ? " Disable Trello connection"
                    : "Activate Trello connection"
                }
                name={`enable_trello`}
                checked={trello.status_active}
                onChange={onSwitchChange}
              />
            )}
            </div>
            <div className="self-start">
              <Button
                onClick={() => {
                  confirmModal.onOpen();
                  confirmModal.setOnConfirm(async()=> await onDelete());
                }}
                variant="destructive"
              >
                <Trash2 className="w-5 h-5 mr-2" /> Delete data
              </Button>
            </div>
          </>
        )}
      </div>
    </FullModal>
  );
};
