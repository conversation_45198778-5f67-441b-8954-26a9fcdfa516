import React, { useEffect, useState } from "react";
import { Plugin } from "../plugin";
import { TrelloSettings } from "./trello.settings";
import useTrelloStore from "store/trello/trello.store";
import useBotStore from "store/bot/bot.store";
import TrelloSVG from "common/icons/TrelloSVG";

export const TrelloPlugin = () => {
  const [showModal, setShowModal] = useState(false);
  const bot = useBotStore((state) => state.bot);
  const [isConnected, setIsConnected] = useState(false);
  const { get_one_trello, trello, update_one_trello, create_one_trello } =
    useTrelloStore();
  useEffect(() => {
    if (trello.key) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [trello]);

  useEffect(() => {
    get_one_trello(bot.bot_id);
  }, []);
  return (
    <>
      <TrelloSettings
        showModal={showModal}
        setShowModal={setShowModal}
        isUpdate={isConnected}
      />
      <Plugin
        title="Trello"
        subtitle="Manage your tasks with Trello"
        icon={TrelloSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};
