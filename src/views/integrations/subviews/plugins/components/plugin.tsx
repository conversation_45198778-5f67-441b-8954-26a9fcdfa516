import { Card } from "common/ui/card";
import React from "react";
import { Button } from "common/ui/button";
import { Edit, Link, Settings, Trash2 } from "lucide-react";

interface PluginProps {
  title?: string;
  subtitle?: string;
  icon?: React.FC;
  onClick?: () => void;
  isConnected?: boolean;
  oauth?: boolean;
  connectedOnClick?: () => void;
}

export const Plugin: React.FC<PluginProps> = ({
  title,
  subtitle,
  icon: Icon,
  onClick,
  isConnected,
  oauth = false,
  connectedOnClick,
}) => {
  return (
    <div className="relative">
      <Card title={title} subtitle={subtitle} hr svg={Icon}>
        <div className="flex justify-center items-center gap-2">
          {isConnected && !oauth ? (
            <Button onClick={onClick}>
              <Edit className="w-5 h-5 mr-2" /> Configure {title}
            </Button>
          ) : isConnected && oauth ? (
            <Button onClick={connectedOnClick}>
              <Edit className="w-5 h-5 mr-2" /> Configure {title}
            </Button>
          ) : (
            <Button onClick={onClick} variant="outline">
              <Link className="w-5 h-5 mr-2" /> Connect {title}
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
};
