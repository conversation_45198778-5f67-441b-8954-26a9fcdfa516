import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import checkForErrors from "helpers/forms";
import { <PERSON><PERSON><PERSON>riangle, Trash2 } from "lucide-react";
import { FC, memo, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useSmtpStore from "store/smtp/smtp.store";
import { z } from "zod";

interface SmtpSettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

const SmtpSettings: FC<SmtpSettingsProps> = ({
  showModal,
  setShowModal,
  isUpdate = false,
}) => {
  const {
    get_one_Smtp,
    create_one_Smtp,
    Smtp,
    update_one_Smtp,
    delete_one_Smtp,
  } = useSmtpStore();
  const confirmModal = useConfirmModal();
  const bot = useBotStore((state) => state.bot);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>();

  const [smtpData, setsmtpData] = useState({
    host: "",
    username: "",
    password: "",
    email: "",
    port: "587",
    secure: false,
  });

  const handleClose = () => {
    setShowModal(false);
    getData();
  };

  const SmtpSchema = z.object({
    host: z.string().refine((val) => val.trim() !== "", {
      message: "Host is required",
    }),
    username: z.string().refine((val) => val.trim() !== "", {
      message: "username is required",
    }),
    password: z.string().refine((val) => val.trim() !== "", {
      message: "password is required",
    }),
    email: z.string().email("Invalid email"),
    port: z.string().refine((val) => val.trim() !== "", {
      message: "port is required",
    }),
    secure: z.boolean(),
  });

  const validateField =
    (field: keyof z.infer<typeof SmtpSchema>) =>
    (value: unknown): string => {
      const parsedResult = SmtpSchema.pick({ [field]: true } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setsmtpData({ ...smtpData, [key]: value });
  };

  const onSaveHandler = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: SmtpSchema,
        data: smtpData,
      },
      setErrors
    );

    if (isErrors) return;

    if (isUpdate) {
      if (JSON.stringify(Smtp) === JSON.stringify(smtpData)) return;
      update_one_Smtp({
        bot_id: bot.bot_id,
        ...smtpData,
      }).then(() => {
        handleClose();
        setLoading(false);
      });
    } else {
      create_one_Smtp({
        bot_id: bot.bot_id,
        ...smtpData,
      }).then(() => {
        handleClose();
        setLoading(false);
      });
    }
  };

  const getData = () => {
    if (isUpdate && Smtp.host) {
      setsmtpData({
        host: Smtp.host,
        username: Smtp.username,
        password: Smtp.password,
        email: Smtp.email,
        port: Smtp.port,
        secure: Smtp.secure,
      });
    } else {
      setsmtpData({
        host: "",
        username: "",
        password: "",
        email: "",
        port: "587",
        secure: false,
      });
    }
  };

  useEffect(() => {
    getData();
  }, [isUpdate, Smtp]);

  const onDelete = () => {
    delete_one_Smtp(bot.bot_id).then(() => {
      get_one_Smtp(bot.bot_id);
      handleClose();
    });
  };

  return (
    <FullModal
      title="Connect Your Smtp Account"
      isOpen={showModal}
      onClose={handleClose}
      loading={loading}
      onSave={onSaveHandler}
      disabled={
        loading ||
        JSON.stringify({
          host: Smtp.host,
          username: Smtp.username,
          password: Smtp.password,
        }) === JSON.stringify(smtpData)
      }
      footer
    >
      <div className="flex flex-col gap-4">
        <div className="text-sm text-white/50">
          By connecting your Smtp account, you will be able to use the Smtp
          block in the dialogs section. <br /> <br />
          <span className="p-2 bg-yellow-300/40 border border-yellow-300/60 rounded text-white flex gap-2 items-center mb-2">
            <AlertTriangle size={15} /> Please be sure to provide correct
            information, otherwise the block will not work.
          </span>
        </div>
        <Input
          name="host"
          title="Host"
          placeholder="Enter your Host Name"
          value={smtpData.host}
          onChange={(e) => onChangeHandler("host", e.target.value)}
          error={errors?.host}
        />
        <Input
          name="username"
          title="username"
          placeholder="Enter your User Name"
          value={smtpData.username}
          onChange={(e) => onChangeHandler("username", e.target.value)}
          error={errors?.username}
        />
        <Input
          name="password"
          title="password"
          placeholder="Enter your Password"
          value={smtpData.password}
          onChange={(e) => onChangeHandler("password", e.target.value)}
          error={errors?.password}
        />
        <Input
          name="email"
          title="Email"
          placeholder="Enter your Email"
          value={smtpData.email}
          onChange={(e) => onChangeHandler("email", e.target.value)}
          error={errors?.email}
        />

        <Input
          name="port"
          title="Port"
          placeholder="Enter your Port"
          value={smtpData.port}
          onChange={(e) => onChangeHandler("port", e.target.value)}
          error={errors?.port}
        />
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={smtpData.secure}
            onChange={(e) => onChangeHandler("secure", e.target.checked)}
          />
          <span className="text-white">Secure</span>
          <small>
            defines if the connection should use SSL (if true) or not (if false)
          </small>
        </div>
        <div className="self-start scale-90">
          <Button
            onClick={() => {
              confirmModal.onOpen();
              confirmModal.setOnConfirm(onDelete);
            }}
            variant="destructive"
          >
            <Trash2 className="w-5 h-5 mr-2" /> Delete data
          </Button>
        </div>
      </div>
    </FullModal>
  );
};

export default memo(SmtpSettings);
