import { FC, memo, useEffect, useState } from "react";
import { Plugin } from "../plugin";
import SmtpSettings from "./smtp.settings";
import useSmtpStore from "store/smtp/smtp.store";
import useBotStore from "store/bot/bot.store";
import SmtpSVG from "common/icons/SmtpSVG";

export const SmtpPlugin  = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const { get_one_Smtp, Smtp } = useSmtpStore();
  const bot = useBotStore((state) => state.bot);

  useEffect(() => {
    if (Smtp?.host) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [Smtp]);

  useEffect(() => {
    get_one_Smtp(bot.bot_id);
  }, []);
  return (
    <>
      <SmtpSettings
        showModal={showModal}
        setShowModal={setShowModal}
        isUpdate={isConnected}
      />
      <Plugin
        title="SMTP"
        subtitle="Send Emails to your users"
        icon={SmtpSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};
