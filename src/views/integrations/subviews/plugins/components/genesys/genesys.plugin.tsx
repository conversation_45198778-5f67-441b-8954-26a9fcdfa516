import React, { useEffect, useState } from "react";
import { Plugin } from "../plugin";
import useBotStore from "store/bot/bot.store";
import { GenesysSettingsModal } from "./genesys.setting";
import useGenesysStore from "store/genesys/genesys.store";
import GenesysSVG from "common/icons/GenesysSVG";
export const GenesysPlugin = () => {
  const [showModal, setShowModal] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const bot = useBotStore((state) => state.bot);
  const { genesys, get_one_genesys } = useGenesysStore();
  useEffect(() => {
    if (genesys?.genesys_integration_id) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [genesys]);

  useEffect(() => {
    get_one_genesys(bot.bot_id);
  }, []);

  return (
    <>
      <GenesysSettingsModal
        showModal={showModal}
        setShowModal={setShowModal}
        isUpdate={isConnected}
      />
      <Plugin
        title="Genesys"
        subtitle="Integrate your bot with Genesys"
        icon={GenesysSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};
