import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import checkForErrors from "helpers/forms";
import { Alert<PERSON>riangle, Trash2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useGenesysStore from "store/genesys/genesys.store";
import { z } from "zod";

interface GenesysSettingsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

export const GenesysSettingsModal: React.FC<GenesysSettingsModalProps> = ({
  showModal,
  setShowModal,
  isUpdate,
}) => {
  const {
    genesys,
    get_one_genesys,
    create_one_genesys,
    update_one_genesys,
    delete_one_genesys,
    message,
    loading,
  } = useGenesysStore();

  const confirmModal = useConfirmModal();

  const bot = useBotStore((state) => state.bot);

  const [formData, setFormData] = useState({
    organization_id: "",
    deployment_id: "",
    target_Address: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isUpdate && genesys?.genesys_integration_id) {
      setFormData({
        organization_id: genesys?.organization_id,
        deployment_id: genesys?.deployment_id,
        target_Address: genesys?.target_Address,
      });
    } else {
      setFormData({
        organization_id: "",
        deployment_id: "",
        target_Address: "",
      });
    }
  }, [isUpdate, genesys]);

  const formDataSchema = z.object({
    organization_id: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Client ID cannot be empty or whitespace",
      }),
    deployment_id: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Client Secret cannot be empty or whitespace",
      }),
    target_Address: z
      .string()
      .min(3)
      .refine((value) => value.trim() !== "", {
        message: "Client Secret cannot be empty or whitespace",
      }),
  });

  type GenesysSettingsInfo = z.infer<typeof formDataSchema>;

  const isEmpty = () => {
    var err = false;
    Object.keys(formData).map((a) => {
      if (!Boolean(formData[a])) {
        err = true;
      }
    });
    return err;
  };

  const validateField =
    (field: keyof GenesysSettingsInfo) =>
    (value: unknown): string => {
      const parsedResult = formDataSchema
        .pick({
          [field]: true,
        } as any)
        .safeParse({
          [field]: value,
        });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  const isDataChanged = () => {
    if (!isUpdate) {
      return true;
    }

    const keysToCheck = ["organization_id", "deployment_id", "target_Address"];
    return keysToCheck.some((key) => formData[key] !== genesys?.[key]);
  };

  const onSaveHandler = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: formDataSchema,
        data: formData,
      },
      setErrors
    );
    if (isErrors) return;

    if (isUpdate) {
      await update_one_genesys({ ...formData, bot_id: bot.bot_id });

      setTimeout(() => {
        if (message === "") {
          get_one_genesys(bot.bot_id);
          handleClose();
        }
      }, 1000);
    } else {
      await create_one_genesys({ ...formData, bot_id: bot.bot_id });
      setTimeout(() => {
        if (!message) {
          get_one_genesys(bot.bot_id);
          handleClose();
        }
      }, 1000);
    }
  };

  const onDelete = async () => {
    await delete_one_genesys(bot.bot_id);
    get_one_genesys(bot.bot_id);
    handleClose();
  };

  const handleClose = () => {
    setShowModal(false);
    setErrors({});
    if (isUpdate) {
      setFormData({ ...genesys });
    } else {
      setFormData({
        organization_id: "",
        deployment_id: "",
        target_Address: "",
      });
    }
  };

  return (
    <FullModal
      title="Connect Your Genesys Account"
      isOpen={showModal}
      onClose={handleClose}
      loading={loading}
      onSave={onSaveHandler}
      disabled={isEmpty() || !isDataChanged() || loading}
      footer
    >
      <div className="flex flex-col gap-10">
        <div className="space-y-1">
          <div className="space-y-5">
            <Input
              name="organization_id"
              title="Your organization ID"
              value={formData.organization_id}
              onChange={(e) =>
                onChangeHandler("organization_id", e.target.value)
              }
              error={errors?.organization_id}
            />
            <Input
              name="deployment_id"
              title="Your deployment ID"
              value={formData.deployment_id}
              onChange={(e) => onChangeHandler("deployment_id", e.target.value)}
              error={errors?.deployment_id}
            />
            <Input
              name="target_Address"
              title="Your Queue Name"
              value={formData.target_Address}
              onChange={(e) =>
                onChangeHandler("target_Address", e.target.value)
              }
              error={errors?.target_Address}
            />
          </div>
        </div>
        {isUpdate && (
          <div className="flex flex-col gap-3 border-t pt-4 border-white/25">
            <div className="self-start scale-90">
              <Button
                onClick={() => {
                  confirmModal.onOpen();
                  confirmModal.setOnConfirm(async () => await onDelete());
                }}
                variant="destructive"
              >
                <Trash2 className="w-5 h-5 mr-2" /> Delete data
              </Button>
            </div>
          </div>
        )}
      </div>
    </FullModal>
  );
};
