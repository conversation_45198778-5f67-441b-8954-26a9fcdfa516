import { FC, memo, useEffect, useState } from "react";
import { Plugin } from "../plugin";
import SendgridSettings from "./sendgrid.settings";
import useSendgridStore from "store/sendgrid/sendgrid.store";
import useBotStore from "store/bot/bot.store";
import SendgridSVG from "common/icons/SendGridSVG";

interface SendgridpluginProps {}

const Sendgridplugin: FC<SendgridpluginProps> = ({}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const { get_one_sendgrid, sendgrid } = useSendgridStore();
  const bot = useBotStore((state) => state.bot);

  useEffect(() => {
    if (sendgrid?.email) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [sendgrid]);

  useEffect(() => {
    get_one_sendgrid(bot.bot_id);
  }, []);
  return (
    <>
      <SendgridSettings
        showModal={showModal}
        setShowModal={setShowModal}
        isUpdate={isConnected}
      />
      <Plugin
        title="SendGrid"
        subtitle="Send Emails to your users"
        icon={SendgridSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};

export default memo(Sendgridplugin);
