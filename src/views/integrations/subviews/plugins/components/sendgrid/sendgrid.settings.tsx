import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import checkForErrors from "helpers/forms";
import { <PERSON><PERSON><PERSON>riangle, Trash2 } from "lucide-react";
import { FC, memo, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useSendgridStore from "store/sendgrid/sendgrid.store";
import { z } from "zod";

interface SendgridSettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

const SendgridSettings: FC<SendgridSettingsProps> = ({
  showModal,
  setShowModal,
  isUpdate = false,
}) => {
  const {
    get_one_sendgrid,
    create_one_sendgrid,
    sendgrid,
    update_one_sendgrid,
    delete_one_sendgrid,
  } = useSendgridStore();
  const confirmModal = useConfirmModal();
  const bot = useBotStore((state) => state.bot);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>();

  const [sendgridData, setSendgridData] = useState({
    email: "",
    apiKey: "",
  });

  const handleClose = () => {
    setShowModal(false);
    getData();
  };

  const sendgridSchema = z.object({
    email: z
      .string()
      .email()
      .refine((val) => val.trim() !== "", {
        message: "Email is required",
      }),
    apiKey: z.string().refine((val) => val.trim() !== "", {
      message: "Api Key is required",
    }),
  });

  const validateField =
    (field: keyof z.infer<typeof sendgridSchema>) =>
    (value: unknown): string => {
      const parsedResult = sendgridSchema
        .pick({ [field]: true } as any)
        .safeParse({
          [field]: value,
        });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setSendgridData({ ...sendgridData, [key]: value });
  };

  const onSaveHandler = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: sendgridSchema,
        data: sendgridData,
      },
      setErrors
    );

    if (isErrors) return;

    if (isUpdate) {
      if (JSON.stringify(sendgrid) === JSON.stringify(sendgridData)) return;
      update_one_sendgrid({
        bot_id: bot.bot_id,
        ...sendgridData,
      }).then(() => {
        handleClose();
        setLoading(false);
      });
    } else {
      create_one_sendgrid({
        bot_id: bot.bot_id,
        ...sendgridData,
      }).then(() => {
        handleClose();
        setLoading(false);
      });
    }
  };

  const getData = () => {
    if (isUpdate && sendgrid.email) {
      setSendgridData({
        email: sendgrid.email,
        apiKey: sendgrid.apiKey,
      });
    } else {
      setSendgridData({
        email: "",
        apiKey: "",
      });
    }
  };

  useEffect(() => {
    getData();
  }, [isUpdate, sendgrid]);

  const onDelete = () => {
    delete_one_sendgrid(bot.bot_id).then(() => {
      get_one_sendgrid(bot.bot_id);
      handleClose();
    });
  };

  return (
    <FullModal
      title="Connect Your SendGrid Account"
      isOpen={showModal}
      onClose={handleClose}
      loading={loading}
      onSave={onSaveHandler}
      disabled={
        loading ||
        JSON.stringify({
          email: sendgrid.email,
          apiKey: sendgrid.apiKey,
        }) === JSON.stringify(sendgridData)
      }
      footer
    >
      <div className="flex flex-col gap-4">
        <div className="text-sm text-white/50">
          By connecting your Sendgrid account, you will be able to use the
          Sendgrid block in the dialogs section. <br /> <br />
          <span className="p-2 bg-yellow-300/40 border border-yellow-300/60 rounded text-white flex gap-2 items-center mb-2">
            <AlertTriangle size={15} /> Please be sure to provide correct
            information, otherwise the block will not work.
          </span>
        </div>
        <Input
          name="email"
          title="Email"
          placeholder="Enter your email"
          value={sendgridData.email}
          onChange={(e) => onChangeHandler("email", e.target.value)}
          error={errors?.email}
        />
        <Input
          name="apiKey"
          title="Api Key"
          placeholder="Enter your api key"
          value={sendgridData.apiKey}
          onChange={(e) => onChangeHandler("apiKey", e.target.value)}
          error={errors?.apiKey}
        />
        <div className="self-start scale-90">
          <Button
            onClick={() => {
              confirmModal.onOpen();
              confirmModal.setOnConfirm(onDelete);
            }}
            variant="destructive"
          >
            <Trash2 className="w-5 h-5 mr-2" /> Delete data
          </Button>
        </div>
      </div>
    </FullModal>
  );
};

export default memo(SendgridSettings);
