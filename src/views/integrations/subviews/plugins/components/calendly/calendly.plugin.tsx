import React, { useEffect, useState } from "react";
import { Plugin } from "../plugin";

import useBotStore from "store/bot/bot.store";
import {
  connectCalendly,
  getAccessTokenFromRefreshToken,
  getUserData,
} from "apis/calendly.api";
import constant from "constant";
import useCalendlyStore from "store/calendly/calendly.store";
import CalendlySettings from "./calendly.settings";
import CalendlySVG from "common/icons/CalendlySVG";

export const CalendlyPlugin = () => {
  const [showModal, setShowModal] = useState(false);
  const bot = useBotStore((state) => state.bot);
  const {
    get_one_calendly,
    calendly,
    create_one_calendly,
    update_one_calendly,
  } = useCalendlyStore();
  const [isConnected, setIsConnected] = useState(false);
  const [calendlyUserData, setCalendlyUserData] = useState(null);

  const getAccessToken = async () => {
    const res = await getAccessTokenFromRefreshToken(calendly.refresh_token);
    update_one_calendly({
      bot_id: bot.bot_id,
      refresh_token: res.refresh_token,
    });
    // console.log(res);
    return res.access_token;
  };

  useEffect(() => {
    if (calendly.refresh_token) {
      getAccessToken().then((access_token) => {
        getUserData(access_token).then((data) => {
          setCalendlyUserData(data.resource);
        });
      });
    }
  }, []);
  const url = `${constant.CALENDLY_AUTH_URL}/authorize?client_id=${constant.CALENDLY_CLIENT_ID}&response_type=code&redirect_uri=${constant.CALENDLY_REDIRECT_URI}`;
  useEffect(() => {
    if (calendly?.status_active) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [calendly]);

  useEffect(() => {
    get_one_calendly(bot.bot_id);
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get("code");
    if (localStorage.getItem("getCalendlyCode") === "true" && code) {
      connectCalendly(code).then((res) => {
        create_one_calendly({
          bot_id: bot.bot_id,
          status_active: true,
          refresh_token: res.refresh_token,
          owner: res.owner,
          organization: res.organization,
        }).then(() => {
          localStorage.removeItem("getCalendlyCode");
          urlParams.delete("code");

          getUserData(res.access_token).then((data) => {
            setCalendlyUserData(data.resource);
          });
        });
      });
    }
  }, []);

  return (
    <>
      <CalendlySettings
        showModal={showModal}
        setShowModal={setShowModal}
        calendlyUserData={calendlyUserData}
      />
      <Plugin
        title="Calendly"
        subtitle="Schedule meetings with your customers"
        icon={CalendlySVG}
        onClick={() => {
          localStorage.setItem("getCalendlyCode", "true");
          setTimeout(() => (window.location.href = url), 200);
        }}
        isConnected={isConnected}
        oauth
        connectedOnClick={() => setShowModal(true)}
      ></Plugin>
    </>
  );
};
