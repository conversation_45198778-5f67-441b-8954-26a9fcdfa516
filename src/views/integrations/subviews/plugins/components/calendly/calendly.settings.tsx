import { getAccessTokenFromRefreshToken, getUserData } from "apis/calendly.api";
import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import { Trash2 } from "lucide-react";
import { FC, memo, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useCalendlyStore from "store/calendly/calendly.store";
import { ICalendlyIntegration } from "store/calendly/calendly.types";

interface CalendlySettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  calendlyUserData: any;
}

const CalendlySettings: FC<CalendlySettingsProps> = ({
  showModal,
  setShowModal,
  calendlyUserData,
}) => {
  const handleClose = () => {
    setShowModal(false);
  };
  const { delete_one_calendly, get_one_calendly } = useCalendlyStore();
  const bot = useBotStore((state) => state.bot);
  const confirmModal = useConfirmModal();

  const onDelete = async () => {
    await delete_one_calendly(bot.bot_id)
      get_one_calendly(bot.bot_id);
      handleClose();
  };

  return (
    <FullModal
      title="Your Calendly Settings"
      isOpen={showModal}
      onClose={handleClose}
    >
      <div className="flex flex-col gap-5">
        <div className="text-sm text-white/50">
          By connecting your Calendly account, you will be able to use the
          Calendly block in the dialogs section.
        </div>
        <Input
          disabled
          title="Calendly Scheduling Link"
          name="calendly_link"
          value={calendlyUserData?.scheduling_url}
        />
        <div className="self-start scale-90">
          <Button
            onClick={() => {
              confirmModal.onOpen();
              confirmModal.setOnConfirm(async()=>await onDelete());
            }}
            variant="destructive"
          >
            <Trash2 className="w-5 h-5 mr-2" /> Delete data
          </Button>
        </div>
      </div>
    </FullModal>
  );
};

export default memo(CalendlySettings);
