import { FullModal } from "common/components/modals";
import useConfirmModal from "common/hooks/useConfirmModal";
import { Button } from "common/ui/button";
import { Input } from "common/ui/inputs/input";
import checkForErrors from "helpers/forms";
import { <PERSON><PERSON><PERSON>riangle, Trash2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import useLiveChatStore from "store/livechat/livechat.store";
import { z } from "zod";

interface LivechatSettingsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  isUpdate?: boolean;
}

export const LivechatSettingsModal: React.FC<LivechatSettingsModalProps> = ({
  showModal,
  setShowModal,
  isUpdate,
}) => {
  const {
    livechat,
    get_one_livechat,
    create_one_livechat,
    update_one_livechat,
    delete_one_livechat,
    message,
    loading,
  } = useLiveChatStore();

  const confirmModal = useConfirmModal();

  const bot = useBotStore((state) => state.bot);

  const [formData, setFormData] = useState({
    client_id: "",
    client_secret: "",
    personal_access_token: "",
    encoded_personal_access_token: "",
    refresh_token: "",
    organization_id: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isUpdate && livechat?.livechat_integration_id) {
      setFormData({
        client_id: livechat?.client_id,
        client_secret: livechat?.client_secret,
        personal_access_token: livechat?.personal_access_token,
        encoded_personal_access_token: livechat?.encoded_personal_access_token,
        refresh_token: livechat?.refresh_token,
        organization_id: livechat?.organization_id,
      });
    } else {
      setFormData({
        client_id: "",
        client_secret: "",
        personal_access_token: "",
        encoded_personal_access_token: "",
        refresh_token: "",
        organization_id: "",
      });
    }
  }, [isUpdate, livechat]);

  const formDataSchema = z.object({
    client_id: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Client ID cannot be empty or whitespace",
      }),
    client_secret: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Client Secret cannot be empty or whitespace",
      }),
    personal_access_token: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Personal Access Token cannot be empty or whitespace",
      }),
    encoded_personal_access_token: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Encoded Personal Access Token cannot be empty or whitespace",
      }),
    refresh_token: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Refresh Token cannot be empty or whitespace",
      }),
    organization_id: z
      .string()
      .min(10)
      .refine((value) => value.trim() !== "", {
        message: "Organization ID cannot be empty or whitespace",
      }),
  });

  type LivechatSettingsInfo = z.infer<typeof formDataSchema>;

  const isEmpty = () => {
    var err = false;
    Object.keys(formData).map((a) => {
      if (!Boolean(formData[a])) {
        err = true;
      }
    });
    return err;
  };

  const validateField =
    (field: keyof LivechatSettingsInfo) =>
    (value: unknown): string => {
      const parsedResult = formDataSchema
        .pick({
          [field]: true,
        } as any)
        .safeParse({
          [field]: value,
        });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    setFormData({
      ...formData,
      [key]: value,
    });
  };

  const isDataChanged = () => {
    if (!isUpdate) {
      return true;
    }

    const keysToCheck = [
      "client_id",
      "client_secret",
      "personal_access_token",
      "encoded_personal_access_token",
      "refresh_token",
      "organization_id",
    ];

    return keysToCheck.some((key) => formData[key] !== livechat?.[key]);
  };

  const onSaveHandler = async () => {
    const isErrors = checkForErrors(
      {
        zodSchema: formDataSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;

    if (isUpdate) {
      await update_one_livechat({ ...formData, bot_id: bot.bot_id });
      console.log(message, "message");

      setTimeout(() => {
        if (message === "") {
          get_one_livechat(bot.bot_id);
          // handleClose();
        }
      }, 1000);
    } else {
      await create_one_livechat({ ...formData, bot_id: bot.bot_id });
      setTimeout(() => {
        if (!message) {
          get_one_livechat(bot.bot_id);
          // handleClose();
        }
      }, 1000);
    }
  };

  const onDelete = async () => {
    await delete_one_livechat(bot.bot_id);
    get_one_livechat(bot.bot_id);
    handleClose();
  };

  const handleClose = () => {
    setShowModal(false);
    setErrors({});
    if (isUpdate) {
      setFormData({ ...livechat });
    } else {
      setFormData({
        client_id: "",
        client_secret: "",
        personal_access_token: "",
        encoded_personal_access_token: "",
        refresh_token: "",
        organization_id: "",
      });
    }
  };
  return (
    <FullModal
      title="Connect Your LiveChat Account"
      isOpen={showModal}
      onClose={handleClose}
      loading={loading}
      onSave={onSaveHandler}
      disabled={isEmpty() || !isDataChanged() || loading}
      footer
    >
      <div className="flex flex-col gap-10">
        <div className="space-y-1">
          <div className="text-sm text-white/50">
            By connecting your LiveChat account, you will be able to use the
            LiveChat block in the dialogs section. <br />
            <br />
            You need to create a LiveChat account first, you can follow the
            steps{" "}
            <a
              href="https://developers.livechat.com/docs/authorization/agent-authorization#authorization-code-grant"
              target="_blank"
              className="text-[rgb(255,81,0)]"
            >
              Here
            </a>{" "}
            to get the required information.
            <br /> <br />
            <span className="p-2 bg-yellow-300/40 border border-yellow-300/60 rounded text-white flex gap-2 items-center mb-2">
              <AlertTriangle size={15} /> Please be sure to provide correct
              information, otherwise the block will not work.
            </span>
            <span className="p-2 bg-yellow-300/40 border border-yellow-300/60 rounded text-white flex gap-2 items-center mb-2">
              <AlertTriangle size={15} />
              Make sure to add appropriate scopes for your personal access token
              and for your client id.
            </span>
            <span className="p-2 bg-yellow-300/40 border border-yellow-300/60 rounded text-white flex gap-2 items-center mb-2">
              <AlertTriangle size={15} />
              Don&apos;t reuse your account for other bots.
            </span>
          </div>
          <div className="space-y-5">
            <Input
              name="client_id"
              title="Your Client ID"
              value={formData.client_id}
              onChange={(e) => onChangeHandler("client_id", e.target.value)}
              error={errors?.client_id}
            />
            <Input
              name="client_secret"
              title="Your Client Secret"
              value={formData.client_secret}
              onChange={(e) => onChangeHandler("client_secret", e.target.value)}
              error={errors?.client_secret}
            />
            <Input
              name="personal_access_token"
              title="Your Personal Access Token"
              value={formData.personal_access_token}
              onChange={(e) =>
                onChangeHandler("personal_access_token", e.target.value)
              }
              error={errors?.personal_access_token}
            />
            <Input
              name="encoded_personal_access_token"
              title="Your Encoded Personal Access Token"
              value={formData.encoded_personal_access_token}
              onChange={(e) =>
                onChangeHandler("encoded_personal_access_token", e.target.value)
              }
              error={errors?.encoded_personal_access_token}
            />
            <Input
              name="refresh_token"
              title="Your Refresh Token"
              value={formData.refresh_token}
              onChange={(e) => onChangeHandler("refresh_token", e.target.value)}
              error={errors?.refresh_token}
            />
            <Input
              name="organization_id"
              title="Your Organization ID"
              value={formData.organization_id}
              onChange={(e) =>
                onChangeHandler("organization_id", e.target.value)
              }
              error={errors?.organization_id}
            />
          </div>
        </div>
        {isUpdate && (
          <div className="flex flex-col gap-3 border-t pt-4 border-white/25">
            <div className="self-start scale-90">
              <Button
                onClick={() => {
                  confirmModal.onOpen();
                  confirmModal.setOnConfirm(async () => await onDelete());
                }}
                variant="destructive"
              >
                <Trash2 className="w-5 h-5 mr-2" /> Delete data
              </Button>
            </div>
          </div>
        )}
      </div>
    </FullModal>
  );
};
