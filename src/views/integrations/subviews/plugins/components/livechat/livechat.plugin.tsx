import React, { useEffect, useState } from "react";
import { Plugin } from "../plugin";
import useBotStore from "store/bot/bot.store";
import { LivechatSettingsModal } from "./livechat.settings";
import useLiveChatStore from "store/livechat/livechat.store";
import LiveChatSVG from "common/icons/LiveChatSVG";

export const LiveChatPlugin = () => {
  const [showModal, setShowModal] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const bot = useBotStore((state) => state.bot);
  const { livechat, get_one_livechat } = useLiveChatStore();
  useEffect(() => {
    if (livechat?.livechat_integration_id) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [livechat]);

  useEffect(() => {
    get_one_livechat(bot.bot_id);
  }, []);

  return (
    <>
      <LivechatSettingsModal
        showModal={showModal}
        setShowModal={setShowModal}
        isUpdate={isConnected}
      />
      <Plugin
        title="LiveChat"
        subtitle="Integrate your bot with LiveChat"
        icon={LiveChatSVG}
        onClick={() => {
          setShowModal(true);
        }}
        isConnected={isConnected}
      ></Plugin>
    </>
  );
};
