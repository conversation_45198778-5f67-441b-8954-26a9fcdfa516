const generateWeatherDialog = (bot_id) => {
  return {
    info: {
      bot_id: bot_id,
      name: "check$$heroCard",
    },
    entities: {
      __location: "string",
    },
    steps: [
      {
        type: "text",
        name: "step00",
        handler: "promptText",
        handlerParams: ["Where you do you want to know the weather?"],
      },
      {
        type: "suggested_actions",
        recieving: "__location",
        name: "step01",
        handler: "continueStepDialog",
        handlerParams: [],
      },
      {
        type: "text",
        name: "step02",
        handler: "promptTextAndContinue",
        handlerParams: ["Processing ${__location}"],
      },
      {
        type: "text",
        name: "step03",
        handler: "promptWeather",
        handlerParams: ["__location"],
      },
      {
        type: "text",
        name: "step05",
        handler: "endStepDialog",
        handlerParams: [],
      },
    ],
  };
};

export { generateWeatherDialog };
