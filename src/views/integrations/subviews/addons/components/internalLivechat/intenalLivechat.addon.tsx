import { MessageSquare, Mic } from "lucide-react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { Addon } from "../addon";
import { LCSettingsModal } from "./intenalLivechat.settings";
import useInternalLiveChatStore from "store/internalLivechat/internalLivechat.store";

export const InternalLivechatAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const [showModal, setShowModal] = useState(false);
  const {integration, create_internal_livechat, update_internal_livechat} = useInternalLiveChatStore();


  const onIntegrationSwitchHandler = () => {
    if (!integration?.livechat_integration_id) {
      create_internal_livechat({
        bot_id: bot.bot_id,
        max_sessions_per_agent: 5
      })
    } else {
      update_internal_livechat({
        livechat_integration_id: integration.livechat_integration_id,
        status: integration.status === "active" ? "inactive" : "active"
      })
    }
  };

  return (
    <>
      <LCSettingsModal
        showModal={showModal}
        setShowModal={setShowModal}
        bot={bot}
      />
      <Addon
        function_name="internal-livechat"
        title="Internal Livechat"
        subtitle="Chat with your customers in real-time"
        headerIcon={MessageSquare}
        enabled={integration ? integration.status === "active" : false}
        onSwitchChange={onIntegrationSwitchHandler}
        onButtonClick={() => setShowModal(true)}
        // fetchLoading={fetchLoading}
        isAvailableForce={true}
      />
    </>
  );
};
