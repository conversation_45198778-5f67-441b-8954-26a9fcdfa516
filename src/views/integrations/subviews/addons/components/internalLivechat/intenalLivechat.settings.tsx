import { But<PERSON> } from "common/ui/button";
import { useEffect, useState } from "react";
import { FullModal } from "common/components/modals";
import { IBot } from "store/bot/bot.types";
import { Input } from "common/ui/inputs/input";
import useInternalLiveChatStore from "store/internalLivechat/internalLivechat.store";
import { Separator } from "common/ui/separator";
import {
  bulkCreateAgents,
  getEditorsWithAgentDetails,
} from "apis/internalLiveChat.api";
import MultiSelectInput, {
  customStyles,
} from "common/components/MultiSelectInput";
import { SelectTimezone } from "reactjs-timezone-select";
import { color } from "@amcharts/amcharts5";

interface LCSettingsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;

  bot: IBot;
}

export const LCSettingsModal: React.FC<LCSettingsModalProps> = ({
  showModal,
  setShowModal,

  bot,
}) => {
  const [loading, setLoading] = useState(false);
  const { integration, create_internal_livechat, update_internal_livechat } =
    useInternalLiveChatStore();

  const [formData, setFormData] = useState({
    max_sessions_per_agent: integration?.max_sessions_per_agent,
    timezone: integration?.time_zone,
  });
  const [maxSessionsPerAgent, setMaxSessionsPerAgent] = useState(
    integration?.max_sessions_per_agent
  );

  const [editors, setEditors] = useState([]);

  const [selectedEditors, setSelectedEditors] = useState([]);

  const getEditors = async () => {
    const data = await getEditorsWithAgentDetails(bot.bot_id);
    if (data && !data.message && data.length > 0) {
      setEditors([...data]);
    }
  };

  useEffect(() => {
    getEditors();
  }, []);

  useEffect(() => {
    if (integration) {
      setMaxSessionsPerAgent(integration.max_sessions_per_agent);
    }
  }, [integration, showModal]);

  const onSaveHandler = async () => {
    setLoading(true);

    if (integration && formData?.max_sessions_per_agent && formData?.timezone) {
      await update_internal_livechat({
        livechat_integration_id: integration.livechat_integration_id,
        max_sessions_per_agent: formData.max_sessions_per_agent,
        time_zone: formData.timezone,
      });
    }

    setLoading(false);
  };

  const onChangeHandler = (value) => {
    if (value > 0) {
      setFormData({
        ...formData,
        max_sessions_per_agent: value,
      });
    }
  };

  return (
    <FullModal
      title="Livechat Settings"
      isOpen={showModal}
      onClose={() => {
        setShowModal(false);
      }}
      loading={loading}
      // disabled={!isChanged}
      onSave={onSaveHandler}
      footer
    >
      <div>
        <div>
          <label>Create Agent</label>

          <MultiSelectInput
            data={editors
              .filter((e) => !e.agent?.agent_id)
              .map((d) => {
                return {
                  label: d.email,
                  value: d.editor_id,
                };
              })}
            setData={(data) => {
              setSelectedEditors(data || []);
            }}
            title="Select an editor"
            // defualtData={selectedEditors}
          />
          <Button
            onClick={() => {
              bulkCreateAgents({
                bot_id: bot.bot_id,
                editors: selectedEditors.map((e) => {
                  return {
                    editor_id: e.value,
                    email: e.label,
                  };
                }),
              }).then(() => {
                setSelectedEditors([]);
                getEditors();
              });
            }}
            disabled={selectedEditors.length === 0}
          >
            Create Agents with selected editors
          </Button>
        </div>
        <Separator />
        <div className="py-4">
          Agents:
          <div className="max-h-[50%] overflow-y-auto">
            {editors
              .filter((e) => e.agent?.agent_id)
              .map((e) => {
                return <div key={e.agent.agent_id}>{e.email}</div>;
              })}
          </div>
        </div>
        <Separator />

        <Input
          type="number"
          title="Max Sessions Per Agent"
          name="max_sessions_per_agent"
          value={maxSessionsPerAgent}
          onChange={(e) => onChangeHandler(e.target.value)}
        />

        <SelectTimezone
          name="Custom timezone"
          label="Select Timezone"
          value={formData.timezone}
          onChange={({ label, value }) =>
            setFormData({ ...formData, timezone: value })
          }
          selectStyles={customStyles}
        />
      </div>
    </FullModal>
  );
};
