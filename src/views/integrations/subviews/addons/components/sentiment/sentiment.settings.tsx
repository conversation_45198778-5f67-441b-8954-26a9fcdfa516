import { FullModal } from "common/components/modals";
import { FC, memo, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import useSentimentAddonStore from "store/sentimentAddon/sentimentAddon.store";
import useGPTStore from "store/gpt/gpt.store";
import { toast } from "react-hot-toast";

interface SentimentSettingsProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
}

const SentimentSettings: FC<SentimentSettingsProps> = ({
  showModal,
  setShowModal,
}) => {
  const {
    sentimentAddon,
    updateSentimentAddon,
    loading
  } = useSentimentAddonStore();
  const { gpt } = useGPTStore();
  const handleClose = () => {
    setShowModal(false);
    setFormData({
      generated_by: sentimentAddon?.generated_by,
    });
  };
  const [formData, setFormData] = useState<any>({
    generated_by: sentimentAddon?.generated_by,
  });

  useEffect(() => {
    if (!sentimentAddon) return;
    setFormData({
      generated_by: sentimentAddon?.generated_by,
    });
  }, [sentimentAddon]);

  const onSaveHandler = async () => {
   await updateSentimentAddon({
      ...sentimentAddon,
      generated_by: formData.generated_by,
    })
   toast.success("Sentiment Analysis Settings Updated Successfully");
  };
  return (
    <FullModal
      title="Sentiment Analysis Settings"
      isOpen={showModal}
      onClose={handleClose}
      loading={loading}
      disabled={
       formData?.generated_by === sentimentAddon?.generated_by
      }
      onSave={onSaveHandler}
      footer
    >
      <p>
        Do you want to manually add sentimental answers or use GPT to generate
        them?
      </p>
      <span className="text-sm text-white/50 block mb-2">
        You need to enable GPT plugin to be able to use GPT.
      </span>
      <Select
        value={formData?.generated_by}
        onValueChange={(value) => {
          setFormData({
            generated_by: value,
          });
        }}
      >
        <SelectTrigger className="w-1/2 ">
          <SelectValue placeholder="Generated By" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="user">Manually</SelectItem>
          <SelectItem disabled={gpt?.status_active ? false : true} value="gpt">
            GPT
          </SelectItem>
        </SelectContent>
      </Select>
    </FullModal>
  );
};

export default memo(SentimentSettings);
