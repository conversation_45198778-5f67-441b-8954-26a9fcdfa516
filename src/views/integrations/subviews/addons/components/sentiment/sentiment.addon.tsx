import { FC, memo, useEffect, useState } from "react";
import { Addon } from "../addon";
import { Smile } from "lucide-react";
import useSentimentAddonStore from "store/sentimentAddon/sentimentAddon.store";
import useBotStore from "store/bot/bot.store";
import SentimentSettings from "./sentiment.settings";

interface SentimentAddonProps {}

const SentimentAddon: FC<SentimentAddonProps> = ({}) => {
  const {
    sentimentAddon,
    createSentimentAddon,
    updateSentimentAddon,
    getSentimentAddon,
    loading,
    fetchLoading
  } = useSentimentAddonStore();
  const bot = useBotStore((state) => state.bot);
  const [showModal, setShowModal] = useState<boolean>(false);

  useEffect(() => {
    getSentimentAddon(bot.bot_id);
  }, []);

  const onSentimentSwitchHandler = () => {
    if (sentimentAddon.sentiment_addon_id) {
      if (sentimentAddon.active) {
        updateSentimentAddon({
          bot_id: bot.bot_id,
          active: false,
        })
      } else {
        updateSentimentAddon({
          bot_id: bot.bot_id,
          active: true,
        })
      }
    } else {
      createSentimentAddon({
        bot_id: bot.bot_id,
        active: true,
      })
    }
  };
  return (
    <>
      <SentimentSettings showModal={showModal} setShowModal={setShowModal} />
      <Addon
        function_name={null}
        title="Sentiment Analysis"
        subtitle="Reply to your customers with the right tone."
        headerIcon={Smile}
        enabled={sentimentAddon ? sentimentAddon?.active : false}
        onSwitchChange={onSentimentSwitchHandler}
        onButtonClick={() => {
          setShowModal(true);
        }}
        loading={loading}
        fetchLoading={fetchLoading}
      />
    </>
  );
};

export default memo(SentimentAddon);
