import { CloudSun } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Addon } from "../addon";
import { createOne, getOne, updateOne } from "apis/weather.api";
import useBotStore from "store/bot/bot.store";
import { uploadFile } from "apis/file.api";
import { generateWeatherDialog } from "../../helper";
import { WeatherType } from "types/addons.types";

export const WeatherAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const [weather, setWeather] = useState<WeatherType>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    getOne(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setWeather({
          ...data,
        });
        console.log(data, "data");
      }
    });
  }, []);

  const onWeatherSwitchHandler = () => {
    setLoading(true);
    const path = `Bots/${bot.file_name}/Dialogs/weather_dialog.json`;
    if (!weather) {
      uploadFile({
        path: path,
        file_body: JSON.stringify({
          ...generateWeatherDialog(bot.bot_id),
        }),
      });
      createOne({
        bot_id: bot.bot_id,
        weather_active: true,
        trigger_name: `__Weather__trigger__${bot.bot_id}`,
      }).then((data) => {
        setWeather({ ...data });
        setLoading(false);
        console.log(data, "data");
      });
    } else if (weather) {
      if (weather.weather_active) {
        updateOne({
          bot_id: bot.bot_id,
          ...weather,
          weather_active: false,
          trigger_name: `__Weather__trigger__${bot.bot_id}`,
        }).then((data) => {
          setWeather({ ...data });
          setLoading(false);
        });
      } else {
        updateOne({
          bot_id: bot.bot_id,
          ...weather,
          weather_active: true,
          trigger_name: `__Weather__trigger__${bot.bot_id}`,
        }).then((data) => {
          setWeather({ ...data });
          setLoading(false);
        });
      }
    }
  };

  return (
    <>
      <Addon
        function_name="weather"
        title="Weather"
        subtitle="Update your users with the latest weather."
        headerIcon={CloudSun}
        enabled={weather ? weather?.weather_active : false}
        onSwitchChange={onWeatherSwitchHandler}
        settings={false}
        loading={loading}
      />
    </>
  );
};
