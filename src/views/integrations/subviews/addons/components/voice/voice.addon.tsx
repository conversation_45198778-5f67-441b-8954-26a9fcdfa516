import { Mic } from "lucide-react";
import { useEffect, useState } from "react";
import { createOne, getOne, updateOne } from "apis/voice.api";
import useBotStore from "store/bot/bot.store";
import { restartOne } from "apis/bot.api";
import { Addon } from "../addon";
import { VoiceType } from "types/addons.types";
import { VoiceSettingsModal } from "./voice.settings";
import useVoiceStore from "store/voice/voice.store";

export const VoiceAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const [showModal, setShowModal] = useState(false);
  const [voice, setVoice] = useState<VoiceType>();
  const [fetchLoading, setFetchLoading] = useState<boolean>(false);
  const {get_bot_voice,update_bot_voice} = useVoiceStore()

  useEffect(() => {
    if(bot?.bot_id && get_bot_voice){
    setFetchLoading(true);
    get_bot_voice(bot.bot_id)
    getOne(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setVoice({
          ...data,
        });
      }
      setFetchLoading(false);
    });
  }
  }, [bot,get_bot_voice]);

  const onVoiceSwitchHandler = () => {
    if (!voice) {
      createOne({
        bot_id: bot.bot_id,
        voice_active: true,
      }).then((data) => {
        setVoice({ ...data });
        restartOne({
          bot_id: bot.bot_id,
        });
      });
    } else if (voice) {
      if (voice.voice_active) {
        updateOne({
          bot_id: bot.bot_id,
          ...voice,
          voice_active: false,
        }).then((data) => {
          update_bot_voice(data)
          setVoice({ ...data });
          restartOne({
            bot_id: bot.bot_id,
          });
        });
      } else {
        updateOne({
          bot_id: bot.bot_id,
          ...voice,
          voice_active: true,
        }).then((data) => {
          update_bot_voice(data)
          console.log(data, "data");
          setVoice({ ...data });
          restartOne({
            bot_id: bot.bot_id,
          });
        });
      }
    }
  };

  return (
    <>
      <VoiceSettingsModal
        showModal={showModal}
        setShowModal={setShowModal}
        voice={voice}
        setVoice={setVoice}
        bot={bot}
      />
      <Addon
        function_name="voice"
        title="Voice"
        subtitle="Add the ability to speak to your chatbot"
        headerIcon={Mic}
        enabled={voice ? voice.voice_active : false}
        onSwitchChange={onVoiceSwitchHandler}
        onButtonClick={() => setShowModal(true)}
        fetchLoading={fetchLoading}
      />
    </>
  );
};
