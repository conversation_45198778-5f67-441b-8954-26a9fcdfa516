"use client"
import { Play } from "lucide-react";
import { Pause } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

interface IProps {
  voiceUrl: string;
}

const VoicePlayer: React.FC<IProps> = ({ voiceUrl }) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const playAudio = () => {
    if (audioRef.current) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  useEffect(() => {
    const audio = audioRef.current;

    const handleAudioEnd = () => {
      setIsPlaying(false);
    };

    if (audio) {
      audio.addEventListener("ended", handleAudioEnd);
    }

    return () => {
      if (audio) {
        audio.removeEventListener("ended", handleAudioEnd);
        audio.pause();
      }
    };
  }, []);

  return (
    <div className="h-full">
      <audio ref={audioRef} className="hidden">
        <source src={voiceUrl} type="audio/mp3" />
      </audio>
      {isPlaying ? (
        <Pause
          name="Pause"
          className="hover:scale-105 cursor-pointer text-primary p-0 m-0"
          onClick={pauseAudio}
        />
      ) : (
        <Play
          name="Play"
          className="hover:scale-105 cursor-pointer text-primary p-0 m-0"
          onClick={playAudio}
        />
      )}
    </div>
  );
};

export default VoicePlayer;
