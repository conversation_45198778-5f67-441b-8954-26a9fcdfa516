import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
} from "common/ui/inputs/select";
import React, { useEffect, useMemo } from "react";
import { VoiceType } from "types/addons.types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "common/ui/table";
import { concatenateURLs } from "store/voice/voice.helper";
import useVoiceStore from "store/voice/voice.store";
import VoicePlayer from "./voice.player";

type TVoiceConfig = {
  botVoice: VoiceType;
  handleIsChange: (value: boolean) => void;
  handleFormData: (value: Partial<VoiceType>) => void;
};

export type TVoice = {
  value: string;
  category: string;
  gender: "Female" | "Male";
  country: string;
  language: "English" | "ARABIC";
  sample: string;
};

const VoiceConfig: React.FC<TVoiceConfig> = ({
  botVoice,
  handleIsChange,
  handleFormData,
}) => {
  const { selectedVoices, setSelectedVoices } = useVoiceStore();

  useEffect(() => {
    if (!botVoice?.female_voice || !botVoice?.male_voice && handleIsChange) {
      handleIsChange(true);
    }
  }, [botVoice,handleIsChange]);

  useEffect(() => {
    const maleVoiceAR = selectedVoices.find(
      (voice) => voice.gender === "male" && voice.language === "ar"
    );
    const maleVoiceEN = selectedVoices.find(
      (voice) => voice.gender === "male" && voice.language === "en"
    );
    const femaleVoiceAR = selectedVoices.find(
      (voice) => voice.gender === "female" && voice.language === "ar"
    );
    const femaleVoiceEN = selectedVoices.find(
      (voice) => voice.gender === "female" && voice.language === "en"
    );

    const male_voice = concatenateURLs(maleVoiceAR?.value, maleVoiceEN?.value);
    const female_voice = concatenateURLs(
      femaleVoiceAR?.value,
      femaleVoiceEN?.value
    );

    handleFormData({ male_voice, female_voice });

  }, [selectedVoices, handleFormData]);


const botVoices = useMemo(()=>{
return selectedVoices?.filter((v)=>v.gender?.toLowerCase() === botVoice?.tts_gender?.toLowerCase())
},[selectedVoices,botVoice])


  return (
    <div className="w-full p-2">
      <p className="text-sm text-primary">Choose the {`((${botVoice?.tts_gender?.toUpperCase()}))`} bot voices, and click save when your found the desired voices </p>
      <div className="flex items-end justify-start gap-4 mb-5">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-center">Voice</TableHead>
              <TableHead className="text-center w-20">Gender</TableHead>
              <TableHead className="text-center w-20">Language</TableHead>
              <TableHead className=" text-center">Test Voice</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {botVoices?.map((voice, index) => (
              <TableRow key={voice.value + index}>
                <TableCell className="font-medium ">
                  <Select
                    value={voice?.value}
                    onValueChange={(value) => {
                      setSelectedVoices(value, index);
                      handleIsChange(true);
                    }}
                  >
                    <SelectTrigger className="">
                      <SelectValue placeholder="Select a voice" />
                    </SelectTrigger>
                    <SelectContent>
                      {voice?.voices?.map((voice: TVoice, index: number) => {
                        return (
                          <SelectItem
                            key={index + voice.value}
                            value={voice.value}
                          >
                            {voice.value}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell className="text-center w-20">
                  {voice.gender?.toUpperCase()}
                </TableCell>
                <TableCell className="text-center w-20">
                  {voice.language?.toUpperCase()}
                </TableCell>
                <TableCell className="h-full p-5 flex items-center justify-center">
                  <VoicePlayer  voiceUrl={voice.sample}  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default VoiceConfig;
