import { <PERSON><PERSON> } from "common/ui/button";
import { Switch } from "common/ui/inputs/switch";
import { Loader2, Mic } from "lucide-react";
import { useEffect, useState } from "react";
import { FullModal } from "common/components/modals";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { VoiceType } from "types/addons.types";
import { createOne, getOne, updateOne } from "apis/voice.api";
import { restartOne } from "apis/bot.api";
import { IBot } from "store/bot/bot.types";
import VoiceConfig from "./voice.config";
import useVoiceStore from "store/voice/voice.store";

interface VoiceSettingsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  voice: VoiceType;
  setVoice: (voice: VoiceType) => void;
  bot: IBot;
}

export const VoiceSettingsModal: React.FC<VoiceSettingsModalProps> = ({
  showModal,
  setShowModal,
  voice,
  setVoice,
  bot,
}) => {
  const [formData, setFormData] = useState<VoiceType>();
  const [isChanged, setIsChanged] = useState(false);
  const [loading, setLoading] = useState(false);
  const {update_bot_voice} = useVoiceStore()

  const handleIsChange = (value:boolean) => {
    setIsChanged(value)
  }
  const handleFormData = (data:Partial<VoiceType>) => {
    setFormData((prev) => ({
      ...prev,
      ...data,
    }));
  }

  useEffect(() => {
    if (voice) {
      const data = { ...voice };
      setFormData(data);
    }
  }, [voice]);

  const onChangeHandler = (key, value) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }));

    if (voice[key] !== value) {
      setIsChanged(true);
    } else {
      setIsChanged(false);
    }
  };

  const onSaveHandler = async () => {
    setLoading(true);
    const data = await updateOne({
      bot_id: bot.bot_id,
      ...formData,
      voice_active: formData.tts_active || formData.stt_active,
    });
    setLoading(false);
    setIsChanged(false);
    update_bot_voice(data)
    setVoice({
      ...data,
    });
    restartOne({
      bot_id: bot.bot_id,
    });
  };

  const resetVoiceForm = () =>{
    if (voice) {
      const data = { ...voice };
      setFormData(data);
    }
  }
  return (
    <FullModal
      title="Voice Settings"
      isOpen={showModal}
      onClose={() => {setShowModal(false);resetVoiceForm()}}
      loading={loading}
      disabled={!isChanged}
      onSave={onSaveHandler}
      footer
    >
      <div className="space-y-5">
        <Switch
          label="Speech to Text"
          name="speechToText"
          checked={formData?.stt_active}
          onChange={(e) => {
            onChangeHandler("stt_active", e.target.checked);
          }}
        />
          <Switch
          label="Show Speech to Text as Message"
          name="showSpeechToText"
          checked={formData?.show_stt_text}
          onChange={(e) => {
            onChangeHandler("show_stt_text", e.target.checked);
          }}
        />
        <div className="flex items-center justify-between w-full">

          <Switch
          label="Text to Speech"
          name="textToSpeech"
          checked={formData?.tts_active}
          onChange={(e) => {
            onChangeHandler("tts_active", e.target.checked);
          }}
        />
        {formData?.tts_active ? 
            <Select
            value={formData?.tts_gender}
            onValueChange={(value) => {
              onChangeHandler("tts_gender", value);
            }}
          >
            <SelectTrigger className="w-1/2 ">
              <SelectValue placeholder="Voice Gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
            </SelectContent>
          </Select>
        : null}
    
        </div>

 
          {formData?.tts_active ?  
         <>
        <Switch
          label="Custom Voice"
          name="stream"
          checked={formData?.custom_voice_active}
          onChange={(e) => {
            onChangeHandler("custom_voice_active", e.target.checked);
          }}
        />
        {formData?.custom_voice_active ?
       <VoiceConfig 
          handleIsChange={handleIsChange}
          handleFormData={handleFormData}
           botVoice={formData} />
          : null}
          </>
          : null}
      </div>
    </FullModal>
  );
};
