import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "common/ui/sheet";
import { IChildren } from "types/common.types";
import { getAll } from "apis/report.api";
import useBotStore from "store/bot/bot.store";
import { MainTable } from "common/components/tables/main.table";
import searchedArray from "helpers/search";
import { CustomSearch } from "common/ui/inputs/search";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import { FilterXIcon, UploadIcon } from "lucide-react";
import { Button } from "common/ui/button";
import { CSVLink } from "react-csv";
import transformDate from "helpers/transformDate";

interface ReportsProps extends IChildren {}

export const Reports: React.FC<ReportsProps> = ({ children }) => {
  const bot = useBotStore((state) => state.bot);
  const [reports, setReports] = useState([]);
  const [renderedReports, setRenderedReports] = useState([]);
  const [keySearch, setKeySearch] = useState("");
  const [date, setDate] = useState<DateRange | undefined>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    getAll(bot.bot_id).then((data) => {
      if (data?.reports?.length) {
        const reportsData = [...data?.reports]
          .slice()
          .sort((a, b) => {
            return (
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );
          })
          .map((report) => {
            return {
              ...report,
              createdAt: transformDate(report.createdAt),
            };
          });
        setReports(reportsData);
      }
      setLoading(false);
    });
  }, []);

  useEffect(() => {
    let data;
    if (date && date.from && date.to) {
      const filteredData = reports.filter((report) => {
        const reportDate = new Date(report.createdAt);
        return (
          reportDate.getTime() >= date.from.getTime() &&
          reportDate.getTime() <= date.to.getTime()
        );
      });
      data = filteredData;
    } else if (date && date.from && !date.to) {
      const filteredData = reports.filter((report) => {
        const reportDate = new Date(report.createdAt);
        return reportDate.getTime() === date.from.getTime();
      });
      data = filteredData;
    } else {
      data = reports;
    }

    data = searchedArray(keySearch, data);
    setRenderedReports(data);
  }, [date, reports, keySearch]);

  const exportFile = () => {
    const headers = [
      { label: "Phone", key: "phone" },
      { label: "User Name", key: "username" },
      { label: "Email", key: "email" },
      { label: "Complain", key: "complain" },
      { label: "date", key: "date" },
    ];

    let data =[]
    if(renderedReports?.length){
      data = [
        ...[...renderedReports].map((a) => {
          return {
            username: a.username,
            phone: a.phone?.toString(),
            email: a.email,
            complain: a.complain,
            date: a.createdAt,
          };
        }),
      ];
    }


    const csvReport = {
      data: data,
      headers: headers,
      filename: `reports.csv`,
    };

    return csvReport;
  };

  const tableData = () => {
    const data = renderedReports;

    const columns = [
      { name: "Username", key: "username" },
      { name: "Email", key: "email" },
      { name: "Phone", key: "phone" },
      { name: "Complaint", key: "complain" },
      { name: "Rate", key: "rate" },
      { name: "Date", key: "createdAt" },
    ];

    const itemsPerPage = 10;

    return { data, columns, itemsPerPage };
  };

  return (
    <Sheet>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="full">
        <SheetHeader>
          <SheetTitle>Reports</SheetTitle>
          <SheetDescription className="flex justify-between items-center">
            View and Manage your customers reports.
            <CSVLink {...exportFile()}>
              <Button>
                <UploadIcon className="mr-2 h-4 w-4" />
                Export as CSV
              </Button>
            </CSVLink>
          </SheetDescription>
        </SheetHeader>
        <div className="pt-5 space-y-3">
          <div className="flex gap-5">
            <CustomSearch
              placeholder="Find a report..."
              onChange={(value) => setKeySearch(value)}
            />
            <DatePickerWithRange date={date} setDate={setDate} />
            {date && (
              <Button
                variant="outline"
                className="text-white"
                onClick={() => setDate(undefined)}
              >
                <FilterXIcon size={15} />
              </Button>
            )}
          </div>
          <MainTable loading={loading} {...tableData()} />
        </div>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
