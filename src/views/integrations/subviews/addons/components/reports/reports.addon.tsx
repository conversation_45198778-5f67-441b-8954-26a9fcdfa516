import { useEffect, useState } from "react";
import { <PERSON>don } from "../addon";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { createOne, getOne, updateOne } from "apis/reportAddon.api";
import useBotStore from "store/bot/bot.store";
import { ReportAddonType } from "types/addons.types";
import { Reports } from "./reports";
import useLeadsAndReportStore from "store/leadsAndReports/leadsAndReports.store";

export const ReportsAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const {set_reports_active} = useLeadsAndReportStore();
  const [reportAddonData, setReportAddonData] = useState<ReportAddonType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchLoading, setFetchLoading] = useState<boolean>(false);

  useEffect(() => {
    setFetchLoading(true);
    getOne(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setReportAddonData({
          ...data,
        });
        set_reports_active(data.status_active);
      }
      setFetchLoading(false);
    });
  }, []);

  const onReportsSwitchHandler = () => {
    setLoading(true);
    if (!reportAddonData) {
      createOne({
        bot_id: bot.bot_id,
        status_active: true,
        ask_email: true,
        ask_phone: true,
      }).then((data) => {
        setLoading(false);
        setReportAddonData({ ...data });
        set_reports_active(data.status_active);
      });
    } else if (reportAddonData) {
      if (reportAddonData.status_active) {
        updateOne({
          bot_id: bot.bot_id,
          ...reportAddonData,
          status_active: false,
          ask_email: false,
          ask_phone: false,
        }).then((data) => {
          setLoading(false);
          setReportAddonData({ ...data });
          set_reports_active(data.status_active);
        });
      } else {
        updateOne({
          bot_id: bot.bot_id,
          ...reportAddonData,
          status_active: true,
          ask_email: true,
          ask_phone: true,
        }).then((data) => {
          setLoading(false);
          setReportAddonData({ ...data });
          set_reports_active(data.status_active);
        });
      }
    }
  };

  return (
    <>
      <Addon
        function_name={null}
        title="Reports"
        subtitle="Get reports on your bot's performance."
        headerIcon={AlertTriangle}
        enabled={reportAddonData ? reportAddonData.status_active : false}
        onSwitchChange={onReportsSwitchHandler}
        settings={false}
        loading={loading}
        fetchLoading={fetchLoading}
        btn
        btnText="View Reports"
        btnSheet={Reports}
        btnIcon={Eye}
      />
    </>
  );
};
