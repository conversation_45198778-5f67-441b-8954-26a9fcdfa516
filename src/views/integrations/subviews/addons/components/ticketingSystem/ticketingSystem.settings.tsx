import { useEffect, useState } from "react";
import { FullModal } from "common/components/modals";
import { IBot } from "store/bot/bot.types";
import useTicketingStore from "store/ticketing/ticketing.store";
import { 
  createAgent, 
  getAgentDepartment, 
  updateAgent, 
  deleteAgent, 
  deleteDepartment, 
  updateCategories, 
  getEditorsWithAgentDetails,
  getDepartments,
  createDepartmentWithCategories
} from "apis/ticketing.api";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { CreateAgentTab } from "./components/CreateAgentTab";
import { EditAgentTab } from "./components/EditAgentTab";
import { CreateDepartmentTab } from "./components/CreateDepartmentTab";
import { EditDepartmentTab } from "./components/EditDepartmentTab";

interface TicketsModalProps {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  bot: IBot;
}

export const TicketsModel: React.FC<TicketsModalProps> = ({
  showModal,
  setShowModal,

  bot,
}) => {
  const { ticketing } = useTicketingStore();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    timezone: ticketing?.time_zone,
  });

  const [editors, setEditors] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [departmentsToEdit, setDepartmentsToEdit] = useState([]);
  const [editDepartments, setEditDepartments] = useState([]);
  const [addedDepartments, setAddedDepartments] = useState([]);
  const [editSelectedEditors, setEditSelectedEditors] = useState([]);
  const [createDepartments, setCreateDepartments] = useState([]);
  const [createCategories, setCreateCategories] = useState([]);
  const [selectedEditors, setSelectedEditors] = useState([]);
  const [selectedDepartments, setSelectedDepartments] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [activeTab, setActiveTab] = useState('create-agent');
  const [agents, setAgents] = useState([]);
  const [editFormData, setEditFormData] = useState({
    timezone: agents?.find(agent => agent.editor_id === editSelectedEditors[0]?.value)?.support_agent?.time_zone || ticketing?.time_zone
  });

  const getEditors = async () => {
    const data = await getEditorsWithAgentDetails(bot.bot_id);
    if (data && !data.message && data?.length > 0) {
      setEditors([...data]);
    }
  };

  const getDepartment = async()=>{
    const data = await getDepartments(bot.bot_id);
    if (data && !data.error) {
      setDepartments([...data]);
    }
  }

  const getAgentDepartments = async(s)=>{
    const data = await getAgentDepartment(s?.value);
    if (data && !data.error) {
      setEditDepartments([...data]);
    }
  }


  useEffect(() => {
    setSelectedEditors([]);
    setSelectedDepartments([]);
    getEditors();
    getDepartment();
    getAgent();
  }, []);

  useEffect(()=>{
    getEditors();
    setSelectedEditors([]);
    setSelectedDepartments([]);
    setFormData({...formData, timezone:ticketing?.time_zone})

    setEditSelectedEditors([]);
    setAddedDepartments([]);
    setEditDepartments([]);
    setEditFormData({...editFormData ,timezone: ticketing.time_zone})

    setCreateDepartments([]);
    setCreateCategories([]);

    setDepartmentsToEdit([]);
    setSelectedCategories([]);

  }, [activeTab, showModal])

  const getAgent = async()=>{
    const agent = await getEditorsWithAgentDetails(bot.bot_id)
    setAgents(agent);
  };
  
  const deleteModule = ({
    setOnDelete,
    onDelete,
    agent,
  }) => {
    const agentDelete = async()=>{
      await deleteAgent(agent[0]?.value);
      setOnDelete(false);
      setEditSelectedEditors([]);
      setAddedDepartments([]);
      setEditDepartments([]);
      setEditFormData({...editFormData ,timezone: ticketing.time_zone})
    }
    return (
      <FullModal
      className="text-white"
      isOpen={onDelete}
      onClose={() => setOnDelete(false)}
      title="Delete Agent"
      onSave={agentDelete}
      footer
      >
        <div>Are you sure you want to delete &apos;{agent[0]?.label}&apos;?</div>
      </FullModal>
    );
  };
  const deleteDepartmentModule = ({
    setOnDepDelete,
    onDepDelete,
    department_id,
  }) => {
    const depDelete = async()=>{
      await deleteDepartment(department_id[0].value);
      setOnDepDelete(false);
      setDepartmentsToEdit([]);
      setSelectedCategories([]);
      setCreateCategories([]);
      getDepartment();
    }
    return (
      <FullModal
      className="text-white"
      isOpen={onDepDelete}
      onClose={() => setOnDepDelete(false)}
      title="Delete Department"
      onSave={depDelete}
      footer
      >
        <div>Are you sure you want to delete &apos;{department_id[0]?.label}&apos; with it&apos;s Categories?</div>
      </FullModal>
    );
  };

  const isDisabled = () => {
    if (activeTab === 'create-agent') {
      return selectedEditors.length === 0 || selectedDepartments.length === 0
    } else if (activeTab === 'create-department') {
      // return !dep && !depAr;
      return false;
    } else if (activeTab === 'edit-agent') {
      return !editSelectedEditors[0];
    } else if (activeTab === 'edit-department') {
      return false;
    }
    return true; 
  };

  const onSaveHandler = async () => { 
    if(activeTab === 'create-agent'){
      setShowModal(false);
      setLoading(true);
      if(selectedDepartments.length >= 1){
        createAgent({
          bot_id: bot.bot_id,
          time_zone: formData.timezone,
          editor: selectedEditors,
          departments: selectedDepartments,
        })
      } else {
        createAgent({
          bot_id: bot.bot_id,
          time_zone: formData.timezone,
          editor: selectedEditors,
        })
      }
      getEditors();
      setSelectedEditors([]);
      setSelectedDepartments([]);
      setFormData({...formData, timezone:ticketing?.time_zone})
      setLoading(false);
    } else if (activeTab === 'edit-agent'){
      setShowModal(false);
      const dep = [
        ...editDepartments.map(department => department.department_id), 
        ...addedDepartments.map(department => department.value)
      ];
      updateAgent({editor_id: editSelectedEditors[0].value, time_zone: editFormData?.timezone, departments: dep || null});
      setEditSelectedEditors([]);
      setAddedDepartments([]);
      setEditDepartments([]);
      setEditFormData({...editFormData ,timezone: ticketing.time_zone})
    } else if(activeTab === 'create-department'){
      const createCategories = createDepartments.flatMap((dep) => dep.categories);
      await createDepartmentWithCategories({
        bot_id: bot.bot_id, 
        createDepartments,
        createCategories
      })
      setCreateDepartments([]);
      setCreateCategories([]);
      getDepartment();
    } else if(activeTab === 'edit-department'){
      console.log("first", selectedCategories)
      const created = [...createCategories]
      console.log("first", created)
      await updateCategories({
        department_id:departmentsToEdit[0].value, 
        editCategories:selectedCategories, 
        addCategories:created
      })
      setDepartmentsToEdit([]);
      setSelectedCategories([]);
      setCreateCategories([]);
      setShowModal(false);
    }
  };
  
  return (
    <FullModal
    title="Ticketing System Settings"
    isOpen={showModal}
    onClose={() => {
      setShowModal(false);
    }}
    loading={loading}
    onSave={onSaveHandler}
    disabled={isDisabled()}
    footer
    >
      <Tabs defaultValue="create-agent" className="space-y-2" onValueChange={setActiveTab}>
        <TabsList className="flex space-x-4 border-b">
            <TabsTrigger value="create-agent" className="w-full text-center">Create Agent</TabsTrigger>
            <TabsTrigger value="edit-agent" className="w-full text-center">Edit Support Agent</TabsTrigger>
            <TabsTrigger value="create-department" className="w-full text-center">Create Department/ Categories</TabsTrigger>
            <TabsTrigger value="edit-department" className="w-full text-center">Edit Department/ Categories</TabsTrigger>
        </TabsList>
      <div className="space-y-2">
      <TabsContent value="create-agent" className="space-y-4">
        <CreateAgentTab 
          editors={editors} 
          departments={departments} 
          selectedEditors={selectedEditors}
          setSelectedEditors={setSelectedEditors}
          selectedDepartments={selectedDepartments}
          setSelectedDepartments={setSelectedDepartments}
          formData={formData}
          setFormData={setFormData}
        />
        
            
      </TabsContent>
      <TabsContent value="edit-agent" className="space-y-4">
        <EditAgentTab
          editors={editors}
          setEditSelectedEditors={setEditSelectedEditors}
          getAgentDepartments={getAgentDepartments}
          setEditFormData={setEditFormData}
          editSelectedEditors={editSelectedEditors}
          editDepartments={editDepartments}
          setEditDepartments={setEditDepartments}
          departments={departments}
          setAddedDepartments={setAddedDepartments}
          addedDepartments={addedDepartments}
          agents={agents}
          editFormData={editFormData}
          deleteModule={deleteModule}
        />
      </TabsContent>
      <TabsContent value="create-department" className="space-y-4">
        <CreateDepartmentTab
          createDepartments={createDepartments}
          setCreateDepartments={setCreateDepartments}
          departmentsToEdit={departmentsToEdit}
        />
        
      </TabsContent>
      <TabsContent value="edit-department" className="space-y-4">
        <EditDepartmentTab 
          departments={departments}
          setDepartmentsToEdit={setDepartmentsToEdit}
          setSelectedCategories={setSelectedCategories}
          departmentsToEdit={departmentsToEdit}
          selectedCategories={selectedCategories}
          createCategories={createCategories}
          setCreateCategories={setCreateCategories}
          deleteDepartmentModule={deleteDepartmentModule}
        />
        
      </TabsContent>
      </div>
      </Tabs>
    </FullModal>
  );
};
