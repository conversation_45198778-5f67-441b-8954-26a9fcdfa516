import { Tag } from "lucide-react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { Addon } from "../addon";
import { TicketsModel } from "./ticketingSystem.settings";
import useTicketingStore from "store/ticketing/ticketing.store";
import { FullModal } from "common/components/modals";
import { SelectTimezone } from "reactjs-timezone-select";

export const TicketingSystemAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const [showModal, setShowModal] = useState(false);
  const [showTimezoneDialog, setShowTimezoneDialog] = useState(false);
  const { ticketing, create_ticketing_integration, update_ticketing_status } = useTicketingStore();
  const [formData, setFormData] = useState({
    timezone: ticketing?.time_zone,
  });

  const onIntegrationSwitchHandler = async () => {
    if (!ticketing?.ticketing_integration_id) {
      setShowTimezoneDialog(true);
    } else {
      await update_ticketing_status(bot.bot_id);
    }
  };

  const handleTimezoneConfirm = async () => {
    await create_ticketing_integration(bot.bot_id, formData.timezone);
    setShowTimezoneDialog(false);
  };

  return (
    <>
      <TicketsModel
        showModal={showModal}
        setShowModal={setShowModal}
        bot={bot}
      />
      <TimezoneDialog
        showTimezoneDialog = {showTimezoneDialog}
        setShowTimezoneDialog={setShowTimezoneDialog}
        formData={formData}
        setFormData={setFormData}
        handleTimezoneConfirm={handleTimezoneConfirm}
      />
      <Addon
        function_name="Ticketing-System"
        title="Ticketing System"
        subtitle="Efficient Issue Tracking for Seamless Customer Support"
        headerIcon={Tag}
        enabled={ticketing?.ticketing_integration_id && ticketing?.status === "active"? true :false}
        onSwitchChange={onIntegrationSwitchHandler}
        onButtonClick={() => setShowModal(true)}
        isAvailableForce={true}
      />
    </>
  );
};

const TimezoneDialog = ({
  showTimezoneDialog,
  setShowTimezoneDialog,
  formData,
  setFormData,
  handleTimezoneConfirm,
}) => {

  const customStyles = {
    control: (base) => ({
      ...base,
      backgroundColor: '#141416', 
      color: '#fff',              
      borderColor: '#374151',
      boxShadow: 'none',
      '&:hover': { borderColor: '#4b5563' }, 
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: '#141416', 
      color: '#fff',     
    }),
    menuList: (base) => ({
      ...base,
      backgroundColor: '#141416',
      color: '#fff',
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isFocused ? '#4b5563' : '#141416',
      color: '#fff',
      '&:hover': {
        backgroundColor: '#4b5563',
      },
    }),
    singleValue: (base) => ({
      ...base,
      color: '#fff',
    }),
    dropdownIndicator: (base) => ({
      ...base,
      color: '#9CA3AF',
    }),
    indicatorSeparator: (base) => ({
      ...base,
      backgroundColor: '#374151',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 9999,
    }),
  };

  return (
    <FullModal
      className="text-white w-full sm:w-3/4 lg:w-1/2 max-w-md max-h-96 overflow-auto"
      isOpen={showTimezoneDialog}
      onClose={() => setShowTimezoneDialog(false)}
      title="Select Your Time Zone"
      onSave={handleTimezoneConfirm}
      footer
    >
      <div className="grid grid-cols-1 gap-4 text-white">
        <div className="col-span-full py-4 px-6 relative">
          <SelectTimezone
            name="Custom timezone"
            label="Select Timezone"
            value={formData.timezone}
            onChange={({ label, value }) =>
              setFormData({ ...formData, timezone: value })
            }
            selectStyles={customStyles}
            menuPortalTarget={document.body} 
            menuPosition="fixed" 
          />
        </div>
      </div>
    </FullModal>
  );
};
