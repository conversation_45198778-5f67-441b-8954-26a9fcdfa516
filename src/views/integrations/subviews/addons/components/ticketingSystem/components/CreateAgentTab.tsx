import MultiSelectInput, {
    customStyles,
  } from "common/components/MultiSelectInput";
  import { SelectTimezone } from "reactjs-timezone-select";
import { Separator } from '@radix-ui/react-select';

export const CreateAgentTab: React.FC<{ 
    editors, 
    departments,
    setSelectedEditors,
    selectedEditors,
    selectedDepartments,
    setSelectedDepartments,
    formData,
    setFormData,
}> = ({ 
    editors, 
    departments,
    setSelectedEditors,
    selectedEditors,
    selectedDepartments,
    setSelectedDepartments,
    formData,
    setFormData,
}) => {

  return (
    <div className="space-y-2">
      <label>Create Support Agent</label>
      <MultiSelectInput
        data={editors
          .filter((e) => !e.support_agent?.support_agent_id)
          .map((d) => {
            return {
              label: d.email,
              value: d.editor_id,
            };
          })}
        setData={(data) => {}}
        onChange={(e)=>{setSelectedEditors(e)}}
        defualtData={selectedEditors}
        title="Select an editor"
      />
      <>
        <MultiSelectInput
          data={departments
            .map((d) => {
              let label = '';
              if (d?.en && d?.ar) {
                label = `${d.en} (${d.ar})`;
              } else if (d?.en) {
                label = d.en;
              } else if (d?.ar) {
                label = d.ar;
              }
              return {
                label,
                value: d?.department_id,
              };
            })}
          setData={(data) => {}}
          onChange={(e)=>{setSelectedDepartments(e)}}
          title="Select Department/s"
          defualtData={selectedDepartments}
        />
        <Separator />
      </>
      <SelectTimezone
        name="Custom timezone"
        label="Select Timezone"
        value={formData.timezone}
        onChange={({ label, value }) =>
          setFormData({ ...formData, timezone: value })
        }
        selectStyles={customStyles}
      />
      <Separator/>
      <div className="py-4">
        Support Agents:
        <div className="max-h-[50%] overflow-y-auto">
          {editors
            .filter((e) => e.support_agent?.support_agent_id)
            .map((e) => {
              return <div key={e.support_agent.support_agent_id}>{e.email}</div>;
            })}
        </div>
      </div>
      <Separator />
    </div>
  );
};
