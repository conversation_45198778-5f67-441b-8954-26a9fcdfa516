import { getDepartmentCategories } from "apis/ticketing.api";
import Select from "react-select";
import { customStyles } from "common/components/MultiSelectInput";
import { MinusCircleIcon, PlusCircle } from "lucide-react";
import { Input } from "common/ui/inputs/input";
import { useState } from "react";
import { Button } from "common/ui/button";

export const EditDepartmentTab: React.FC<{ 
    departments,
    setDepartmentsToEdit,
    setSelectedCategories,
    departmentsToEdit,
    selectedCategories,
    createCategories,
    setCreateCategories,
    deleteDepartmentModule,
 }> = ({ 
    departments,
    setDepartmentsToEdit,
    setSelectedCategories,
    departmentsToEdit,
    selectedCategories,
    createCategories,
    setCreateCategories,
    deleteDepartmentModule,
  }) => {

    const [onDepDelete, setOnDepDelete] = useState(false);
    const getCategories = async(dep_id)=>{
        const temp = await getDepartmentCategories(dep_id);
        setSelectedCategories(temp);
    }

    const onChangeHandlerCat2 = (value, index, lang) => {
        const newCategories = [...createCategories];
        if(lang){
            newCategories[index][lang] = value;
            newCategories[index].index = departmentsToEdit[0]?.value|| index;
            setCreateCategories(newCategories);
        }
    };

    const addCategoriesFields2 = (i) => {
        setCreateCategories([...createCategories, { en: '', ar: '', index: i }]);
    };


  return (
    <div className="space-y-2">
        <label>Edit Department:</label>
        <Select
            options={departments
              .filter((d) => d?.is_native === false)
              .map((dept) => {
                let label = '';
                if (dept?.en && dept?.ar) {
                  label = `${dept.en} (${dept.ar})`;
                } else if (dept?.en) {
                  label = dept.en;
                } else if (dept?.ar) {
                  label = dept.ar;
                }
                return {label, value: dept.department_id};
                })}
            onChange={(data, i) => {
              setDepartmentsToEdit(data? [data] : []);
              getCategories(data.value)
            }}
            styles={customStyles}
            placeholder="Select a department"
            value={departmentsToEdit}
        />

        {selectedCategories.length>0 && <label>Categories:</label>}
        <div className="flex flex-col space-y-2">
            {selectedCategories
                ?.map((cat, idx) => {
                    let label = '';
                    if (cat?.en && cat?.ar) {
                      label = `${cat.en} (${cat.ar})`;
                    } else if (cat?.en) {
                      label = cat.en;
                    } else if (cat?.ar) {
                      label = cat.ar;
                }
                return (
                    <div key={idx} className="flex items-center justify-between">
                        <span>{label}</span>
                        <button
                            onClick={() => {
                              setSelectedCategories((prev) =>
                                prev.filter((c) => c.category_id !== cat.category_id)
                              );
                            }}
                            className="text-red-500 hover:text-red-700"
                        >
                            <MinusCircleIcon className="w-5 h-5" />
                        </button>
                    </div>
                );
            })}
        </div>
        {departmentsToEdit[0]?.value &&
            <>
            <div className="flex items-center space-x-2">
                <span>Add Categories:</span>
                <PlusCircle className="text-green-500 hover:text-green-800 cursor-pointer" onClick={()=>{addCategoriesFields2(departmentsToEdit[0]?.value)}} />
            </div>
            {createCategories.map((categories, c) => (
                <div key={c} className="space-y-2">
                    <div className="flex items-center space-x-2">
                    <Input
                      type="text"
                      title="Category Name in English:"
                      name={`category_name_en_${c}`}
                      value={createCategories[c]?.en}
                      onChange={(e) => onChangeHandlerCat2(e.target.value, c, 'en')}
                    />
                    <Input
                      type="text"
                      title="Category Name in Arabic:"
                      name={`category_name_ar_${c}`}
                      value={createCategories[c]?.ar}
                      onChange={(e) => onChangeHandlerCat2(e.target.value, c, 'ar')}
                    />
                    <button
                        onClick={() => {
                            setCreateCategories((prev) => {
                              const newCategories = [...prev];
                              newCategories.splice(c, 1);
                              return newCategories;
                            });
                        }}
                        className="text-red-500 hover:text-red-700"
                        >
                        <MinusCircleIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              ))} 
            </>
        }
        <Button
              className="bg-red-500 hover:bg-red-700 p-5" 
              onClick={() => setOnDepDelete(true)}
              disabled={!departmentsToEdit[0]?.value}
        >
              Delete Department
        </Button>
        {deleteDepartmentModule({ setOnDepDelete, onDepDelete,  department_id: departmentsToEdit})}
    </div>
  );
};