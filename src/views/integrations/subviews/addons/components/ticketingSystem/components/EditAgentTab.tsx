import { MinusCircleIcon } from "lucide-react";
import Select from "react-select";
import { SelectTimezone } from "reactjs-timezone-select";
import { Button } from "common/ui/button";
import MultiSelectInput, {
    customStyles,
  } from "common/components/MultiSelectInput";
import { useState } from "react";

export const EditAgentTab: React.FC<{ 
    editors,
    setEditSelectedEditors,
    getAgentDepartments,
    setEditFormData,
    editSelectedEditors,
    editDepartments,
    setEditDepartments,
    departments,
    setAddedDepartments,
    addedDepartments,
    agents,
    editFormData,
    deleteModule,

 }> = ({ 
    editors,
    setEditSelectedEditors,
    getAgentDepartments,
    setEditFormData,
    editSelectedEditors,
    editDepartments,
    setEditDepartments,
    departments,
    setAddedDepartments,
    addedDepartments,
    agents,
    editFormData,
    deleteModule,

 }) => {
  const [onDelete, setOnDelete] = useState(false);

  return (
    <div className="space-y-2">
      <label>Edit Support Agent</label>
      <Select
        options={editors
          .filter((e) => e.support_agent?.support_agent_id)
          .map((e) => {
            return {label: e.email, value: e.editor_id};
          })}
        onChange={(data) => {
          console.log("data", data)
          setEditSelectedEditors(data? [data] : []);
          getAgentDepartments(data);
          setEditFormData({
            timezone: agents?.find(agent => agent.editor_id === data?.value)?.support_agent?.time_zone
          })
        }}
        styles={customStyles}
        placeholder="Select an editor"
        value={editSelectedEditors}
      />
      {editDepartments.length>0 && <label>Selected Departments:</label>}
      <div className="flex flex-col space-y-2">
        {editDepartments
        .map((dept, idx) => {
          let label = '';
          if (dept?.en && dept?.ar) {
            label = `${dept.en} (${dept.ar})`;
          } else if (dept?.en) {
            label = dept.en;
          } else if (dept?.ar) {
            label = dept.ar;
          }
        return (
          <div key={idx} className="flex items-center justify-between">
            <span>{label}</span>
            <button
              onClick={() => {
                setEditDepartments((prev) =>
                  prev.filter((d) => d.department_id !== dept.department_id)
                );
              }}
              className="text-red-500 hover:text-red-700"
            >
              <MinusCircleIcon className="w-5 h-5" />
            </button>
          </div>
        );
        })}
      </div>
      {editSelectedEditors.length>0 && 
        <div>
          <MultiSelectInput
            data={departments
              .filter((dep) => 
                !editDepartments.some((editDep) => editDep.department_id === dep.department_id)
              )
              .map((d) => {
                let label = '';
                if (d?.en && d?.ar) {
                  label = `${d.en} (${d.ar})`;
                } else if (d?.en) {
                  label = d.en;
                } else if (d?.ar) {
                  label = d.ar;
                }
                return {
                  label: label,
                  value: d.department_id
                };
            })}
            setData={(data) => {}}
            onChange={(e)=>{setAddedDepartments(e); console.log(e)}}
            title="Add Department/s"
            defualtData={addedDepartments}
          />
          <SelectTimezone
            name="Custom timezone"
            label="Select Timezone"
            value={editFormData.timezone}
            onChange={({ label, value }) => setEditFormData({ ...editFormData, timezone: value })}
            selectStyles={customStyles}
          />
          <Button
            className="bg-red-500 hover:bg-red-700 p-5" 
            onClick={() => setOnDelete(true)}
            disabled={!editSelectedEditors[0]?.label}
          >
            Delete Agent
          </Button>
        </div>
      }
      {deleteModule({ setOnDelete, onDelete,  agent: editSelectedEditors})}
    </div>
  );
};
