import { Button } from 'common/ui/button';
import { Input } from "common/ui/inputs/input";
import { MinusCircleIcon, Plus, PlusCircle } from 'lucide-react';

export const CreateDepartmentTab: React.FC<{
    createDepartments,
    setCreateDepartments,
    departmentsToEdit,
 }> = ({ 
    createDepartments,
    setCreateDepartments,
    departmentsToEdit,
  }) => {
    const addDepartmentFields = () => {
      setCreateDepartments([...createDepartments, { en: '', ar: '', index: 0 }]);
    };
    const onChangeHandlerDep = (value, index, lang) => {
      const newDepartments = [...createDepartments];
      if(lang){
        newDepartments[index][lang] = value;
        newDepartments[index].index = index;
        setCreateDepartments(newDepartments);
      }
    };
    const onChangeHandlerCat = (value, departmentIndex, categoryIndex, lang) => {
      setCreateDepartments((prevDepartments) => {
        const newDepartments = [...prevDepartments];
    
         if (lang) {
          newDepartments[departmentIndex].categories[categoryIndex][lang] = value;
    
           newDepartments[departmentIndex].categories[categoryIndex].index =
            departmentsToEdit[0]?.value || departmentIndex;
        }
    
         return newDepartments;
      });
    };
    const addCategoriesFields = (departmentIndex) => {
        setCreateDepartments((prevDepartments) => {
          const newDepartments = [...prevDepartments];
      
          newDepartments[departmentIndex].categories = [
            ...(newDepartments[departmentIndex].categories || []),
            { en: '', ar: '' },
          ];
      
          return newDepartments;
        });
      };


  return (
    <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <span>Create Department:</span>
            <PlusCircle
              className="text-green-500 hover:text-green-800 cursor-pointer"
              onClick={addDepartmentFields}
            />
          </div>

          {createDepartments.map((department, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center space-x-2">
                <Input
                  type="text"
                  title="Department Name in English:"
                  name={`department_name_en_${index}`}
                  value={department.en}
                  onChange={(e) => onChangeHandlerDep(e.target.value, index, 'en')}
                />
                <Input
                  type="text"
                  title="Department Name in Arabic:"
                  name={`department_name_ar_${index}`}
                  value={department.ar}
                  onChange={(e) => onChangeHandlerDep(e.target.value, index, 'ar')}
                />
                <button
                  onClick={() => {
                    setCreateDepartments((prev) =>
                      prev.filter((_, depIndex) => depIndex !== index)
                    );
                  }}
                  className="text-red-500 hover:text-red-700"
                >
                  <MinusCircleIcon className="w-5 h-5" />
                </button>
              </div>                
              {department.categories?.map((category, cIndex) => (
                <div key={cIndex} className="pl-4 space-y-2 border-l-4">
                  <div className="flex items-center space-x-2">
                    <Input
                      type="text"
                      title="Category Name in English:"
                      name={`category_name_en_${index}_${cIndex}`}
                      value={category.en}
                      onChange={(e) =>
                        onChangeHandlerCat(e.target.value, index, cIndex,  'en')
                      }
                    />
                    <Input
                      type="text"
                      title="Category Name in Arabic:"
                      name={`category_name_ar_${index}_${cIndex}`}
                      value={category.ar}
                      onChange={(e) =>
                        onChangeHandlerCat(e.target.value, index, cIndex, 'ar')
                      }
                    />
                  <button
                      onClick={() => {
                        setCreateDepartments((prev) => {
                          const newDepartments = [...prev];
                          newDepartments[index].categories = newDepartments[index].categories.filter(
                            (_, catIndex) => catIndex !== cIndex
                          );
                          return newDepartments;
                        });
                      }}
                      className="text-red-500 hover:text-red-700"
                    >
                      <MinusCircleIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              ))}

              <div className="flex justify-end space-x-2">
                <Button onClick={() => addCategoriesFields(index)}>
                  Add Categories <Plus />
                </Button>
              </div>
            
            </div>
          ))}
        </div>
  );
};
