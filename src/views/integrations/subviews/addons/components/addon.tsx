import { usePlanCheck } from "common/hooks/usePlanCheck";
import { Button } from "common/ui/button";
import { Card } from "common/ui/card";
import { Switch } from "common/ui/inputs/switch";
import { Loader2, LucideIcon, Settings } from "lucide-react";
import React from "react";
import { Locked } from "./locked";
import { IChildren } from "types/common.types";
import { Skeleton } from "common/ui/skeleton";

interface AddonProps {
  function_name?: string;
  title: string;
  subtitle: string;
  headerIcon: LucideIcon;
  enabled: boolean;
  settings?: boolean;
  onSwitchChange: () => void;
  onButtonClick?: () => void;
  loading?: boolean;
  fetchLoading?: boolean;
  btn?: boolean;
  btnText?: string;
  btnIcon?: LucideIcon;
  btnSheet?: React.FC<IChildren>;
  isAvailableForce?: boolean;
}

export const Addon: React.FC<AddonProps> = ({
  function_name,
  title,
  subtitle,
  headerIcon,
  enabled,
  onSwitchChange,
  onButtonClick,
  settings = true,
  loading,
  fetchLoading,
  btn = false,
  btnText,
  btnIcon: BtnIcon,
  btnSheet: BtnSheet,
  isAvailableForce,
}) => {
  const isAvailable = usePlanCheck(function_name) || isAvailableForce;

  const handleSwitchChange = () => {
    if(!isAvailable) return;
    onSwitchChange();
  };

  return (
    fetchLoading ? 
    <Skeleton className="h-64" />
    :
    <div className={`relative  ${isAvailable ? "" : "pointer-events-none"}`}>
      <Card title={title} subtitle={subtitle} headerIcon={headerIcon} hr>
        <div className="flex flex-col gap-5 h-32 justify-center">
          <div className="self-center">
            {loading ? (
              <Loader2 size={25} className=" animate-spin" />
            ) : (
              <Switch
                label={enabled ? `Disable ${title}` : `Enable ${title}`}
                name={`enable${title}`}
                checked={enabled}
                onChange={handleSwitchChange}
              />
            )}
          </div>
          {settings && (
            <>
              <hr className="text-white/25" />
              <Button
                onClick={onButtonClick}
                disabled={!enabled}
                variant="outline"
              >
                <Settings className="w-5 h-5 mr-2" /> {title} Settings
              </Button>
            </>
          )}
          {btn && BtnSheet && (
            <>
              <hr className="text-white/25" />
              {enabled ? (
                <BtnSheet>
                  <Button>
                    {BtnIcon && <BtnIcon className="mr-2 h-4 w-4" />}
                    {btnText}
                  </Button>
                </BtnSheet>
              ) : (
                <Button disabled={true}>
                  {BtnIcon && <BtnIcon className="mr-2 h-4 w-4" />}
                  {btnText}
                </Button>
              )}
            </>
          )}
        </div>
        {!isAvailable && <Locked />}
      </Card>
    </div>
  );
};
