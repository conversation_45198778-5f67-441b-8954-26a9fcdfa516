import { ArrowLeft, InfoIcon, ShoppingCart } from "lucide-react";
import { useEffect, useState } from "react";

import { FullModal } from "common/components/modals";
import { Input } from "common/ui/inputs/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { cleanPhoneNumber } from "helpers/phone";
import { Button } from "common/ui/button";
import { PhoneInput } from "common/ui/inputs/phoneInput";
import {
  generatePhoneVerificationCode,
  verifyPhoneNumber,
} from "apis/phone.api";
import toast from "react-hot-toast";
import { generateEmailVerificationCode, verifyEmailCode } from "apis/email.api";
import {
  CartSettings,
  CartSettingsSchema,
  RefinedCartSettingsSchema,
} from "types/addons.types";
import useCartStore from "store/cart/cart.store";
import checkForErrors from "helpers/forms";

export const CartSettingsModal = ({ showModal, setShowModal, bot }) => {
  const { cart, update_one_cart, loading } = useCartStore();
  const [isChanged, setIsChanged] = useState(false);
  const [formData, setFormData] = useState(cart);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [verifyPhone, setVerifyPhone] = useState(false);
  const [verifyEmail, setVerifyEmail] = useState(false);

  const [phoneResetCounter, setPhoneResetCounter] = useState(0);
  const [emailResetCounter, setEmailResetCounter] = useState(0);

  const [phoneVerificationCode, setPhoneVerificationCode] = useState("");
  const [emailVerificationCode, setEmailVerificationCode] = useState("");

  useEffect(() => {
    if (cart) {
      const data = { ...cart };
      setFormData(data);
    }
  }, [cart]);

  useEffect(() => {
    if (phoneResetCounter > 0) {
      const interval = setInterval(() => {
        setPhoneResetCounter((prev) => prev - 1);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [phoneResetCounter]);

  useEffect(() => {
    if (emailResetCounter > 0) {
      const interval = setInterval(() => {
        setEmailResetCounter((prev) => prev - 1);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [emailResetCounter]);

  const onChangeHandler = (key, value) => {
    const error = validateField(key)(value);
    setErrors({ ...errors, [key]: error });
    const cart_to_update = { ...formData };
    cart_to_update[key] = value;
    setFormData(cart_to_update);

    if (cart_to_update[key] !== cart[key]) {
      setIsChanged(true);
    } else {
      setIsChanged(false);
    }
  };

  const validateField =
    (field: keyof CartSettings) =>
    (value: unknown): string => {
      const parsedResult = CartSettingsSchema.pick({
        [field]: true,
      } as any).safeParse({
        [field]: value,
      });
      return parsedResult.success === false
        ? parsedResult.error.errors[0].message
        : "";
    };

  const onSaveHandler = async () => {
    if (!isChanged || !formData.phone_verified || !formData.email_verified)
      return;

    const isErrors = checkForErrors(
      {
        zodSchema: RefinedCartSettingsSchema,
        data: formData,
      },
      setErrors
    );

    if (isErrors) return;

    await update_one_cart({
      bot_id: bot.bot_id,
      ...formData,
      trigger_name: `__cart__trigger__${bot.bot_id}`,
    });
    setIsChanged(false);
    toast.success("E-commerce settings updated successfully");
  };

  const handleClose = () => {
    setFormData(cart);
    setErrors({});
    setShowModal(false);
  };

  const handleSendPhoneVerificationCode = () => {
    const error = validateField("phone_number")(formData.phone_number);
    if (error) return setErrors({ ...errors, phone_number: error });

    setErrors({ ...errors, phone_number: "" });
    setPhoneResetCounter(30);
    generatePhoneVerificationCode({
      bot_id: bot.bot_id,
      phone: formData?.phone_number,
    }).then((data) => {
      setVerifyPhone(true);
      toast("Verification code sent to your phone number", {
        icon: <InfoIcon className="text-blue-500" />,
      });
    });
  };

  const handleResendPhoneVerificationCode = () => {
    if (phoneResetCounter > 0) return;
    handleSendPhoneVerificationCode();
    setPhoneResetCounter(30);
  };
  const handleVerifyPhoneCode = () => {
    verifyPhoneNumber({
      bot_id: bot.bot_id,
      phone: formData.phone_number,
      code: phoneVerificationCode,
    }).then((data) => {
      console.log(data, "--->");
      if (data) {
        if (data.success) {
          update_one_cart({
            bot_id: bot.bot_id,
            ...formData,
            phone_verified: true,
            phone_number: formData.phone_number,
          }).then(() => {
            setVerifyPhone(false);
            setPhoneVerificationCode("");
            toast.success("Phone number verified successfully");
          });
        } else {
          toast.error(data?.message);
        }
      }
    });
  };

  const handleSendEmailVerificationCode = () => {
    const error = validateField("email")(formData.email);
    if (error) return setErrors({ ...errors, email: error });

    setErrors({ ...errors, email: "" });
    setEmailResetCounter(30);
    generateEmailVerificationCode({
      bot_id: bot.bot_id,
      email: formData.email,
    }).then(() => {
      setVerifyEmail(true);
      toast("Verification code sent to your email", {
        icon: <InfoIcon className="text-blue-500" />,
      });
    });
  };

  const handleResendEmailVerificationCode = () => {
    if (emailResetCounter > 0) return;
    handleSendEmailVerificationCode();
    setEmailResetCounter(30);
  };

  const handleVerifyEmailCode = () => {
    verifyEmailCode({
      bot_id: bot.bot_id,
      email: formData.email,
      code: emailVerificationCode,
    }).then((data) => {
      if (data) {
        if (data.success) {
          console.log(data, "--->");

          update_one_cart({
            bot_id: bot.bot_id,
            ...formData,
            email_verified: true,
          });
          setEmailVerificationCode("");
          setVerifyEmail(false);
          toast.success("Email verified successfully");
        } else {
          toast.error(data?.message);
        }
      }
    });
  };
  return (
    <FullModal
      title="E-commerce Settings"
      isOpen={showModal}
      onClose={handleClose}
      loading={loading}
      onSave={onSaveHandler}
      disabled={
        !isChanged || !formData.phone_verified || !formData.email_verified
      }
      footer
    >
      <div className="space-y-4">
        <div className="flex gap-3">
          <Input
            name="vat_ammount"
            title="VAT Ammount"
            placeholder="VAT Ammount"
            value={formData?.vat_ammount}
            onChange={(e) => {
              if (isNaN(+e.target.value)) return;
              onChangeHandler("vat_ammount", +e.target.value);
            }}
            error={errors?.vat_ammount}
          />
          <Input
            name="vat_reg_num"
            title="VAT Registration Number"
            placeholder="VAT Registration Number"
            value={formData?.vat_reg_num}
            onChange={(e) => onChangeHandler("vat_reg_num", e.target.value)}
            error={errors?.vat_reg_num}
          />
        </div>
        <div className="flex gap-3 items-end">
          <Input
            name="delivery"
            title="Delivery Fees"
            value={formData?.cart_delivery}
            onChange={(e) => {
              if (isNaN(+e.target.value)) return;
              onChangeHandler("cart_delivery", +e.target.value);
            }}
            error={errors?.cart_delivery}
          />
          <Select
            value={formData?.cart_currency}
            onValueChange={(value) => {
              onChangeHandler("cart_currency", value);
            }}
          >
            <SelectTrigger className="w-1/2 ">
              <SelectValue placeholder="Currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="JD">JD</SelectItem>
              <SelectItem value="AUD">AUD</SelectItem>
              <SelectItem value="AED">AED</SelectItem>
              <SelectItem value="SAR">SAR</SelectItem>
              <SelectItem value="EGP">EGP</SelectItem>
              <SelectItem value="USD">USD</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={formData?.decimal?.toString()}
            onValueChange={(value) => {
              onChangeHandler("decimal", +value);
            }}
          >
            <SelectTrigger className="w-1/2 ">
              <SelectValue placeholder="Decimal Points" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2">2 Decimal Points</SelectItem>
              <SelectItem value="3">3 Decimal Points</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <hr className="text-white/25" />
        {verifyEmail && !formData?.email_verified ? (
          <div className="flex justify-between gap-5 items-end">
            <Input
              name="verify_email"
              title="Email Verification Code"
              placeholder="Enter Code"
              value={emailVerificationCode}
              onChange={(e) => setEmailVerificationCode(e.target.value)}
            />
            <div>
              <Button
                disabled={emailVerificationCode.length < 3}
                variant="subtle"
                onClick={handleVerifyEmailCode}
              >
                Submit
              </Button>
            </div>
            <Button
              disabled={emailResetCounter > 0}
              variant="outline"
              className="text-xs whitespace-nowrap"
              onClick={handleResendEmailVerificationCode}
            >
              {emailResetCounter > 0
                ? `Resend in ${emailResetCounter}`
                : "Resend code"}
            </Button>
            <ArrowLeft
              size={30}
              onClick={() => setVerifyEmail(false)}
              className=" cursor-pointer mb-1"
            />
          </div>
        ) : (
          <div className="flex justify-between gap-5 items-end">
            <Input
              name="email"
              title="Email"
              placeholder="Email"
              disabled={formData.email_verified}
              value={formData?.email}
              onChange={(e) => onChangeHandler("email", e.target.value)}
              error={errors?.email || errors?.email_verified}
            />
            {formData?.email_verified ? (
              <span className="text-green-500 whitespace-nowrap">
                &#x2713; Verified
              </span>
            ) : (
              <Button
                disabled={
                  Boolean(!errors?.email) && Boolean(errors?.email !== "")
                }
                variant="subtle"
                onClick={handleSendEmailVerificationCode}
              >
                Verify
              </Button>
            )}
          </div>
        )}
        {verifyPhone && !formData?.phone_verified ? (
          <div className="flex justify-between gap-5 items-end">
            <Input
              name="verify_phone"
              title="Phone Verification Code"
              placeholder="Enter Code"
              value={phoneVerificationCode}
              onChange={(e) => setPhoneVerificationCode(e.target.value)}
            />
            <div>
              <Button
                disabled={phoneVerificationCode.length < 3}
                variant="subtle"
                onClick={handleVerifyPhoneCode}
              >
                Submit
              </Button>
            </div>
            <Button
              disabled={phoneResetCounter > 0}
              variant="outline"
              className="text-xs whitespace-nowrap"
              onClick={handleResendPhoneVerificationCode}
            >
              {phoneResetCounter > 0
                ? `Resend in ${phoneResetCounter}`
                : "Resend code"}
            </Button>
            <ArrowLeft
              size={30}
              onClick={() => setVerifyPhone(false)}
              className=" cursor-pointer mb-1"
            />
          </div>
        ) : (
          <div className="flex justify-between gap-5 items-end">
            <PhoneInput
              disabled={formData.phone_verified}
              value={cleanPhoneNumber(formData?.phone_number)}
              onChange={(value) => {
                onChangeHandler("phone_number", value);
              }}
              error={errors?.phone_number || errors?.phone_verified}
            />
            {formData?.phone_verified ? (
              <span className="text-green-500 whitespace-nowrap">
                &#x2713; Verified
              </span>
            ) : (
              <Button
                disabled={
                  Boolean(!errors?.phone_number) &&
                  Boolean(errors?.phone_number !== "")
                }
                variant="subtle"
                onClick={handleSendPhoneVerificationCode}
              >
                Verify
              </Button>
            )}
          </div>
        )}
      </div>
    </FullModal>
  );
};
