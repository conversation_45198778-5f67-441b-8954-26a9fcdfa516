import { ArrowLeft, ShoppingCart } from "lucide-react";
import { useEffect, useState } from "react";
import { Addon } from "../addon";
import useCartStore from "store/cart/cart.store";
import useBotStore from "store/bot/bot.store";
import { CartSettingsModal } from "./cart.settings";

// TODO : change email and phone after verification, if verification code is wrong

export const CartAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const { get_one_cart, cart, create_cart, update_one_cart, loading, fetchLoading } = useCartStore();
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    get_one_cart(bot.bot_id);
  }, []);

  const onCartSwitchHandler = () => {
    if (!cart.cart_id) {
      create_cart({
        bot_id: bot.bot_id,
        enable_api: false,
        email: "",
        phone_number: "",
        phone_verified: false,
        email_verified: false,
        business_name: "",
        vat_ammount: 0,
        cart_active: true,
        cart_currency: "JD",
        currency: "JD",
        cart_delivery: 0,
        cart_icon: "",
        decimal: 2,
        timezone: "",
        vat_reg_num: "",
        trigger_name: `__cart__trigger__${bot.bot_id}`,
      })
    } else {
      if (cart.cart_active) {
        update_one_cart({
          bot_id: bot.bot_id,
          ...cart,
          cart_active: false,
          trigger_name: `__cart__trigger__${bot.bot_id}`,
        })
      } else {
        update_one_cart({
          bot_id: bot.bot_id,
          ...cart,
          cart_active: true,
          trigger_name: `__cart__trigger__${bot.bot_id}`,
        })
      }
    }
  };

  return (
    <>
      <CartSettingsModal
        showModal={showModal}
        setShowModal={setShowModal}
        bot={bot}
      />
      <Addon
        function_name="cart"
        title="E-commerce"
        subtitle="Sell products directly from your bot."
        headerIcon={ShoppingCart}
        enabled={cart ? cart?.cart_active : false}
        onSwitchChange={onCartSwitchHandler}
        onButtonClick={() => {
          setShowModal(true);
        }}
        loading={loading}
        fetchLoading={fetchLoading}
      />
    </>
  );
};
