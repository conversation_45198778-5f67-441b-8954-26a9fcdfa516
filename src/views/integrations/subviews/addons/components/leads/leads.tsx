import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "common/ui/sheet";
import { IChildren } from "types/common.types";
import { getAll } from "apis/lead.api";
import useBotStore from "store/bot/bot.store";
import { DateRange } from "react-day-picker";
import { addDays } from "date-fns";
import searchedArray from "helpers/search";
import { CustomSearch } from "common/ui/inputs/search";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { UploadIcon, FilterXIcon } from "lucide-react";
import { Button } from "common/ui/button";
import { MainTable } from "common/components/tables/main.table";
import transformDate from "helpers/transformDate";
import { CSVLink } from "react-csv";

interface LeadsProps extends IChildren {}

export const Leads: React.FC<LeadsProps> = ({ children }) => {
  const bot = useBotStore((state) => state.bot);
  const [leads, setLeads] = useState([]);
  const [renderedLeads, setRenderedLeads] = useState([]);
  const [keySearch, setKeySearch] = useState("");
  const [date, setDate] = useState<DateRange | undefined>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    getAll(bot.bot_id).then((data) => {
      if (data?.length) {
        setLeads(
          [...data]
            .slice()
            .sort((a, b) => {
              return (
                new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime()
              );
            })
            .map((lead) => {
              return {
                ...lead,
                createdAt: transformDate(lead.createdAt),
              };
            })
        );
      }
      setLoading(false);
    });
  }, []);

  useEffect(() => {
    let data;
    if (date && date.from && date.to) {
      const filteredData = leads.filter((lead) => {
        const leadDate = new Date(lead.createdAt);
        return (
          leadDate.getTime() >= date.from.getTime() &&
          leadDate.getTime() <= date.to.getTime()
        );
      });
      data = filteredData;
    } else if (date && date.from && !date.to) {
      const filteredData = leads.filter((lead) => {
        const leadDate = new Date(lead.createdAt);
        return leadDate.getTime() === date.from.getTime();
      });
      data = filteredData;
    } else {
      data = leads;
    }

    data = searchedArray(keySearch, data);
    setRenderedLeads(data);
  }, [date, leads, keySearch]);

  const exportFile = () => {
    const headers = [
      { label: "Email", key: "email" },
      { label: "Phone", key: "phone" },
      { label: "date", key: "date" },
    ];

    let data =[]
    if(renderedLeads?.length){
      data = [
        ...[...renderedLeads].map((a) => {
          return {
            phone: a.lead_phone?.toString(),
            email: a.lead_email,
            date: a.createdAt,
          };
        }),
      ];
    }


    const csvLeads = {
      data: data,
      headers: headers,
      filename: `leads.csv`,
    };

    return csvLeads;
  };

  const tableData = () => {
    const data = renderedLeads;

    const columns = [
      { name: "Lead Email", key: "lead_email" },
      { name: "Lead Phone", key: "lead_phone" },
      { name: "Date", key: "createdAt" },
    ];

    const itemsPerPage = 10;

    return { data, columns, itemsPerPage };
  };
  return (
    <Sheet>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="full">
        <SheetHeader>
          <SheetTitle>Leads</SheetTitle>
          <SheetDescription className="flex justify-between items-center">
            View and Manage your customers leads.
            <CSVLink {...exportFile()}>
              <Button>
                <UploadIcon className="mr-2 h-4 w-4" />
                Export as CSV
              </Button>
            </CSVLink>
          </SheetDescription>
        </SheetHeader>
        <div className="pt-5 space-y-3">
          <div className="flex gap-5">
            <CustomSearch
              placeholder="Find a lead..."
              onChange={(value) => setKeySearch(value)}
            />
            <DatePickerWithRange date={date} setDate={setDate} />
            {date && (
              <Button
                variant="outline"
                className="text-white"
                onClick={() => setDate(undefined)}
              >
                <FilterXIcon size={15} />
              </Button>
            )}
          </div>
          <MainTable loading={loading} {...tableData()} />
        </div>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
