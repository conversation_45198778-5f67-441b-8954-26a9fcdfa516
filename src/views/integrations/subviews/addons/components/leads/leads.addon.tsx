import { useState, useEffect } from "react";
import { <PERSON>don } from "../addon";
import { <PERSON>, LineChart } from "lucide-react";
import useBotStore from "store/bot/bot.store";
import { createOne, getOne, updateOne } from "apis/leadAddon.api";
import { LeadAddonType } from "types/addons.types";
import { Leads } from "./leads";
import useLeadsAndReportStore from "store/leadsAndReports/leadsAndReports.store";

export const LeadsAddon = () => {
  const bot = useBotStore((state) => state.bot);
  const {set_leads_active} = useLeadsAndReportStore();
  const [lead, setLead] = useState<LeadAddonType>();
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchLoading, setFetchLoading] = useState<boolean>(false);

  useEffect(() => {
    setFetchLoading(true);
    getOne(bot.bot_id).then((data) => {
      if (data && !data.message) {
        setLead({
          ...data,
        });
        set_leads_active(data.active);
      }
      setFetchLoading(false);
    });
  }, []);

  const onLeadSwitchHandler = () => {
    setLoading(true);
    if (!lead) {
      createOne({
        bot_id: bot.bot_id,
        check_email: true,
        check_phone: true,
        active: true,
      }).then((data) => {
        setLoading(false);
        setLead({ ...data });
        set_leads_active(data.active);
      });
    } else if (lead) {
      if (lead.active) {
        updateOne({
          bot_id: bot.bot_id,
          ...lead,
          check_email: false,
          check_phone: false,
          active: false,
        }).then((data) => {
          setLoading(false);
          setLead({ ...data });
          set_leads_active(data.active);
        });
      } else {
        updateOne({
          bot_id: bot.bot_id,
          ...lead,
          check_email: true,
          check_phone: true,
          active: true,
        }).then((data) => {
          setLoading(false);
          setLead({ ...data });
          set_leads_active(data.active);
        });
      }
    }
  };

  return (
    <>
      <Addon
        function_name={null}
        title="Leads"
        subtitle="Get leads from your bot."
        headerIcon={LineChart}
        enabled={lead ? lead.active : false}
        onSwitchChange={onLeadSwitchHandler}
        settings={false}
        loading={loading}
        fetchLoading={fetchLoading}
        btn
        btnText="View Leads"
        btnSheet={Leads}
        btnIcon={Eye}
      />
    </>
  );
};
