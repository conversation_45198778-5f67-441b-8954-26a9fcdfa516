import React from "react";
import {
  <PERSON>es,
  Lock,
  PackagePlus,
  Puzzle,
  SlidersHorizontal,
  Store,
} from "lucide-react";
import { ViewCard } from "common/components/cards";
import { MainPageHeader } from "common/components/headers";
import views from "views";
import useUserPrivilegeStore from "store/priviliges/UserPrivilege.store";
import { IEditorPrivilges } from "store/priviliges/UserPrivilege.types";
import useUserStore from "store/user/user.store";
import useBotStore from "store/bot/bot.store";
import { toast } from "react-hot-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "common/ui/tabs";
import { AddonsView } from "./subviews/addons";
import { PluginsView } from "./subviews/plugins";

interface IntegrationsViewProps {
  setView: (view: string) => void;
}

export const IntegrationsView: React.FC<IntegrationsViewProps> = ({
  setView,
}) => {
  const { planfunction } = useBotStore();
  const user = useUserStore((state) => state.user);
  const { priviliges, isAdmin } = useUserPrivilegeStore();
  const hasEditorAccess = (privilege: keyof IEditorPrivilges | undefined) => {
    return Boolean(priviliges[privilege] || user.user_id === 17 || isAdmin);
  };
  return (
    <div className="space-y-5">
      <MainPageHeader
        title="Integrate your bot"
        description="Boost your bot powers and add features to it."
      />
      {/* <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {hasEditorAccess("addon_privilege") && (
          <ViewCard
            icon={Boxes}
            title="AddOns"
            description="Extend your bot by adding new features to it"
            onClick={() => setView(views.botViews.ADDONS_VIEW)}
            btnText="Start"
            btnIcon={SlidersHorizontal}
          />
        )}
        {hasEditorAccess(undefined) && (
          <ViewCard
            icon={Puzzle}
            title="Plugins"
            description="Install services and integrate them into your chatbot"
            onClick={() => {
              if (planfunction?.plan_id === 3) {
                setView(views.botViews.PLUGINS_VIEW);
              } else {
                toast("You need to upgrade your plan to access this feature", {
                  position: "bottom-center",
                  icon: "🔒",
                });
              }
            }}
            btnText="Start"
            btnIcon={planfunction?.plan_id === 3 ? SlidersHorizontal : Lock}
          />
        )}
      </div> */}
      <Tabs defaultValue="plugins" className="w-full">
        <TabsList className="flex justify-stretch w-full">
          {hasEditorAccess("addon_privilege") && (
            <TabsTrigger className="grow" value="plugins">
              Plugins
            </TabsTrigger>
          )}
          {hasEditorAccess("addon_privilege") && (
            <TabsTrigger className="grow" value="addons">
              Addons
            </TabsTrigger>
          )}
        </TabsList>
        {hasEditorAccess("addon_privilege") && (
          <TabsContent value="addons">
            <AddonsView />
          </TabsContent>
        )}
        {hasEditorAccess("addon_privilege") && (
          <TabsContent value="plugins">
            <PluginsView />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};
