import { useEffect, useState } from "react";
import {
  GeneralDashboard,
  SalesDashboard,
  LiveChatDashboard,
  TicketingDashboard,
} from "./dashboards";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "common/ui/tabs";
import { MainPageHeader } from "common/components/headers";
import SortingMenu from "./components/sortingMenu";
import useCartStore from "store/cart/cart.store";
import useBotStore from "store/bot/bot.store";
import planFunctionChecker from "helpers/planFunctionChecker";
import { DateRange } from "react-day-picker";
import useInternalLiveChatStore from "store/internalLivechat/internalLivechat.store";
import useTicketingStore from "store/ticketing/ticketing.store";
import GeneralDashboardV2 from "./dashboards/generalDashboardV2";

const DashboardView = () => {
  const bot = useBotStore((state) => state.bot);
  const [selectedPeriod, setSelectedPeriod] = useState(1);
  const [submitLoading, setSubmitLoading] = useState(false);
  const isCart = useCartStore((state) => state.cart.cart_active);
  const isLive = !!useInternalLiveChatStore(
    (state) => state.integration?.livechat_integration_id
  );
  const isTicketing = !!useTicketingStore(
    (state) => state.ticketing?.ticketing_integration_id
  );
  const { planfunction } = useBotStore();
  const [filterDate, setFilterDate] = useState<DateRange | undefined>();
  const [period, setPeriod] = useState(null);

  useEffect(() => {
    // const period = +selectedPeriod;

    // if(selectedPeriod != 22){
    //   const period = +selectedPeriod;
    // }
    // FIXME
    if (filterDate?.to && filterDate?.from) {
      const { from, to } = filterDate;
      const fromDay = from?.getDate();
      const fromMonth = from?.getMonth() + 1;
      const fromYear = from?.getFullYear();

      const toDay = to?.getDate();
      const toMonth = to?.getMonth() + 1;
      const toYear = to?.getFullYear();

      setPeriod({
        start_day: fromDay,
        start_month: fromMonth,
        start_year: fromYear,
        end_day: toDay,
        end_month: toMonth,
        end_year: toYear,
      });
    } else if (selectedPeriod !== 22) {
      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() - +selectedPeriod);

      const day = targetDate?.getDate();
      const month = targetDate?.getMonth() + 1;
      const year = targetDate?.getFullYear();

      const endDay = null;
      const endMonth = null;
      const endYear = null;

      setPeriod({
        start_day: day,
        start_month: month,
        start_year: year,
        end_day: endDay,
        end_month: endMonth,
        end_year: endYear,
      });
    }
  }, [selectedPeriod, filterDate]);

  if (planFunctionChecker(planfunction, "transaction_dashboard") === false)
    return (
      <div className="flex items-center justify-center h-full">
        <h1 className="text-2xl font-bold text-center">
          You need to upgrade your plan to access this feature.
        </h1>
      </div>
    );
  return (
    <div className={`${isCart || isLive || isTicketing ? "" : "space-y-5"}`}>
      <MainPageHeader
        title="Dashboard"
        description="View your bot's performance and statistics."
      >
        <div className="space-y-5">
          <SortingMenu
            selectedPeriod={selectedPeriod}
            setSelectedPeriod={setSelectedPeriod}
            setSubmitLoading={setSubmitLoading}
            filterDate={filterDate}
            setFilterDate={setFilterDate}
          />
        </div>
      </MainPageHeader>
      {isCart || isLive || isTicketing ? (
        <Tabs defaultValue="messages" className="w-full mt-3">
          <TabsList>
            <TabsTrigger value="messages">Messages</TabsTrigger>
            {isCart ? <TabsTrigger value="sales">Sales</TabsTrigger> : <></>}
            {isLive ? (
              <TabsTrigger value="liveChat">Live Chat</TabsTrigger>
            ) : (
              <></>
            )}
            {isTicketing ? (
              <TabsTrigger value="ticketing">Ticketing System</TabsTrigger>
            ) : (
              <></>
            )}
          </TabsList>
          <TabsContent value="messages">
            {[403, 804].includes(bot?.bot_id) ? (
              <GeneralDashboard
                selectedPeriod={selectedPeriod}
                submitLoading={submitLoading}
                setSubmitLoading={setSubmitLoading}
                filterDate={filterDate}
              />
            ) : (
              <GeneralDashboardV2 period={period} />
            )}
          </TabsContent>
          {isCart ? (
            <TabsContent value="sales">
              <SalesDashboard
                selectedPeriod={selectedPeriod}
                submitLoading={submitLoading}
                setSubmitLoading={setSubmitLoading}
                filterDate={filterDate}
              />
            </TabsContent>
          ) : (
            <></>
          )}
          {isLive ? (
            <TabsContent value="liveChat">
              <LiveChatDashboard
                selectedPeriod={selectedPeriod}
                submitLoading={submitLoading}
                setSubmitLoading={setSubmitLoading}
                filterDate={filterDate}
              />
            </TabsContent>
          ) : (
            <></>
          )}
          {isTicketing ? (
            <TabsContent value="ticketing">
              <TicketingDashboard
                selectedPeriod={selectedPeriod}
                submitLoading={submitLoading}
                setSubmitLoading={setSubmitLoading}
                filterDate={filterDate}
              />
            </TabsContent>
          ) : (
            <></>
          )}
        </Tabs>
      ) : [403, 804].includes(bot?.bot_id) ? (
        <GeneralDashboard
          selectedPeriod={selectedPeriod}
          submitLoading={submitLoading}
          setSubmitLoading={setSubmitLoading}
          filterDate={filterDate}
        />
      ) : (
        <GeneralDashboardV2 period={period} />
      )}
    </div>
  );
};

export default DashboardView;
