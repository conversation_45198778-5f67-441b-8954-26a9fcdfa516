import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface AgentsMenuProps {
  setAgent: (value: string) =>void;
  agent: string;
  selectedAgent: {
    agent_id: number
    email: string
  }[];
  all?:boolean
}
const AgentsMenu: FC<AgentsMenuProps> = ({ agent, setAgent, selectedAgent, all}) => {

    return (
      <Select
        onValueChange={(value) => {
          setAgent(value);
        }}
        value={agent}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select an agent" />
        </SelectTrigger>
        <SelectContent>
          {all && <SelectItem value="all">All Agents</SelectItem>}
          {selectedAgent.length > 0 ? (
            selectedAgent.map((a) => (
                <SelectItem key={a.agent_id} value={a.agent_id?.toString()}>
                  {`${a.email}`}
                </SelectItem>
              ))
          ) : (
            <SelectItem value="Please select an agent" disabled>Please select an agent</SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  };

export default memo(AgentsMenu);
