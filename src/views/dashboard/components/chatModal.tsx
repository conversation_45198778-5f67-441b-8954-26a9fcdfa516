import { getConversationDetails } from "apis/chatbotReport.api";
import { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import { CSVLink } from "react-csv";
import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@radix-ui/react-tabs";
import { Button } from "common/ui/button";
import { Separator } from "@radix-ui/react-separator";

const ChatModal = ({ chat, onClose }) => {
    const [chatDetails, setChatDetails] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filter, setFilter] = useState('all');
    const bot_id = useBotStore((state) => state.bot.bot_id);

    useEffect(() => {
        if (chat) {
            setLoading(true);
            getConversationDetails(chat.conversation_id, bot_id)
                .then((details) => {
                    if (Array.isArray(details)) {
                        setChatDetails(details);
                    } else {
                        setChatDetails([]);
                    }
                })
                .catch((error) => {
                    console.error("Failed to fetch chat details:", error);
                    setChatDetails([]);
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    }, [chat]);

    const exportFile = () => {
        const headers = [
            { label: 'From', key: 'from' },
            { label: 'User Question', key: 'user_q' },
            { label: 'Bot Answer', key: 'bot_a' },
            { label: 'Answer Category', key: 'category' },
            { label: 'Answer', key: 'answer_type' },
            { label: 'Date', key: 'date' },
        ];

        const filteredData = chatDetails.filter((message) => {
            if (filter === 'live') {
                return message.from === "customer" || message.from === "agent";
            }
            if (filter === 'bot') {
                return message.bot_a || message.user_q;
            }
            return true;
        });

        const data = filteredData.map(message => ({
            from: message.from || '',
            user_q: message.user_q || '',
            bot_a: message.bot_a || '',
            answer_category: message.category || '',
            answer: message.answer_type || '',
            date: new Date(message.date).toLocaleDateString('en-GB'),
        }));

        return {
            data,
            headers,
            filename: `${filter}_chat_details.csv`, 
        };
    };

    const renderMessage = (message, index, arr) => {
        const prevMessage = arr[index - 1];
        const isSwitchingToLive = prevMessage?.user_q && message.from && (message.from === "customer" || message.from === "agent");
        const isSwitchingToBotUser = prevMessage?.from && (prevMessage.from === "customer" || prevMessage.from === "agent") && message.user_q;
    
        const formattedDate = new Date(message.date).toLocaleDateString('en-GB');
        const formattedTime = new Date(message.date).toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
    
        return (
            <React.Fragment key={index}>
                {isSwitchingToLive && (
                    <div className="text-center text-sm text-gray-500 my-2">
                        Switching to live messages...
                    </div>
                )}
                {isSwitchingToBotUser && (
                    <div className="text-center text-sm text-gray-500 my-2">
                        End of live messages.
                    </div>
                )}
                {message.user_q && (
                    <div className="flex mb-4 flex-col items-end">
                        <div className="flex flex-col items-end w-full max-w-[20%]">
                            <strong className="mb-1 text-sm w-[90%] text-gray-30">User</strong>
                            <div className="bg-primary text-white p-3 rounded-t-3xl rounded-bl-3xl w-full shadow-md">
                                <p>{message.user_q}</p>
                            </div>
                            <div className="text-sm w-[90%] text-gray-400 mt-2">
                                <div><strong>{message.category || 'N/A'}: </strong>{message.answer_type || 'N/A'}</div>
                                <div>{formattedTime} {formattedDate}</div>
                            </div>
                        </div>
                    </div>
                )}
                {message.bot_a && (
                    <div className="flex mb-4 flex-col items-start">
                        <div className="flex flex-col items-start w-full max-w-[20%]">
                            <strong className="mb-1 ml-4 text-sm text-gray-300">Bot</strong>
                            <div className="bg-gray-700 text-white p-3 rounded-t-3xl rounded-br-3xl w-full shadow-md">
                                <p>{message.bot_a}</p>
                            </div>
                            <div className="text-sm text-gray-400 mt-2">
                                <div><strong>{message.category || 'N/A'}: </strong>{message.answer_type || 'N/A'}</div>
                                <div>{formattedTime} {formattedDate}</div>
                            </div>
                        </div>
                    </div>
                )}
                {message.from === "customer" && (
                    <div className="flex mb-4 flex-col items-end">
                        <div className="flex flex-col items-end w-full max-w-[20%]">
                            <strong className="mb-1 ml-4 text-sm w-full text-gray-30">Customer</strong>
                            <div className="bg-[#8A4ED3] text-white p-3 rounded-t-3xl rounded-bl-3xl w-full shadow-md">
                                <p>{message.live_message}</p>
                            </div>
                            <div className="text-sm text-gray-400 mt-2 w-full">
                                <div>{formattedTime} {formattedDate}</div>
                            </div>
                        </div>
                    </div>
                )}
                {message.from === "agent" && (
                    <div className="flex mb-4 flex-col items-start">
                        <strong className="mb-1 ml-3 text-sm text-gray-300">Agent</strong>
                        <div className="flex flex-col items-start w-full max-w-[20%]">
                            <div className="bg-[#2D2D33] text-white p-3 rounded-t-3xl rounded-br-3xl w-full shadow-md">
                                <p>{message.live_message}</p>
                            </div>
                            <div className="text-sm text-gray-400 mt-2">
                                <div>{formattedTime} {formattedDate}</div>
                            </div>
                        </div>
                    </div>
                )}
            </React.Fragment>
        );
    };
    

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
            <div className="relative flex h-full min-h-[70vh] max-h-[95vh] w-full max-w-4xl flex-col rounded-xl p-6 shadow-xl bg-themeSecondary text-gray-200">
                <div className="flex justify-between items-center p-4 rounded-lg mb-4 bg-accent/50">
                    <h2 className="text-white text-xl">Chat History</h2>
                    <div className="flex items-center">
                        <CSVLink {...exportFile()}>
                            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-themeSecondary focus:ring-primary">
                                Export as CSV
                            </button>
                        </CSVLink>
                    </div>
                </div>
                <div className="flex-1 overflow-y-auto">
                    <Tabs defaultValue="all" className="flex flex-col h-full">
                        <TabsList className="flex mb-4">
                            <TabsTrigger
                                value="all"
                                className={`w-full rounded-l-lg text-center py-2 border-b-2 ${filter === 'all' ? 'bg-white text-gray-900 border-gray-500' : 'bg-accent text--white border-transparent'} transition-all duration-300`}
                                onClick={() => setFilter('all')}
                            >
                                All Messages
                            </TabsTrigger>
                            <TabsTrigger
                                value="live"
                                className={`w-full text-center py-2 border-b-2 ${filter === 'live' ? 'bg-white text-gray-900 border-gray-500' : 'bg-accent text-white border-transparent'} transition-all duration-300`}
                                onClick={() => setFilter('live')}
                            >
                                Live Messages
                            </TabsTrigger>
                            <TabsTrigger
                                value="bot"
                                className={`w-full text-center py-2  rounded-r-lg  border-b-2 ${filter === 'bot' ? 'bg-white text-gray-900 border-gray-500' : 'bg-accent text-white border-transparent'} transition-all duration-300`}
                                onClick={() => setFilter('bot')}
                            >
                                Bot Messages
                            </TabsTrigger>
                        </TabsList>
                        <div className="bg-accent rounded-lg p-4">
                        <TabsContent value="all" className="flex-1 overflow-y-auto pt-2">
                            {loading ? (
                                <p className="text-center text-white">Loading chat details...</p>
                            ) : (
                                chatDetails.map((message, index, arr) => renderMessage(message, index, arr))
                            )}
                        </TabsContent>
                        <TabsContent value="live" className="flex-1 overflow-y-auto pt-2">
                            {loading ? (
                                <p className="text-center text-white">Loading chat details...</p>
                            ) : (
                                chatDetails
                                    .filter(message => message.from === "customer" || message.from === "agent")
                                    .map((message, index, arr) => renderMessage(message, index, arr))
                            )}
                        </TabsContent>
                        <TabsContent value="bot" className="flex-1 overflow-y-auto pt-2">
                            {loading ? (
                                <p className="text-center text-white">Loading chat details...</p>
                            ) : (
                                chatDetails
                                    .filter(message => message.bot_a || message.user_q)
                                    .map((message, index, arr) => renderMessage(message, index, arr))
                            )}
                        </TabsContent>
                        </div>
                    </Tabs>
                </div>
                <div className="flex justify-end mt-4">
                    <Button
                        className="bg-primary hover:bg-hover text-white rounded-full py-2 px-6"
                        onClick={onClose}
                    >
                        Close
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ChatModal;
