import { cn } from "lib/utils";
import { FC, memo } from "react";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@radix-ui/react-hover-card";
import { Info } from "lucide-react";


interface ChartCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  headerComponent?: React.ReactNode;
  hover?: string
}

const ChartCard: FC<ChartCardProps> = ({
  title,
  children,
  className,
  headerComponent,
  hover
}) => {
  return (
    <div
      className={cn(
        "p-5 pb-0 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 ",
        className
      )}
    >
      <div className="flex justify-between items-center">
        {title}
        {hover && <HoverCard>
          <HoverCardTrigger>
            <Info size={20} className="cursor-pointer text-yellow-500 "/>
          </HoverCardTrigger>
          <HoverCardContent className="w-fit bg-gray-950 text-gray-100 ring-2 ring-gray-500/50 text-sm z-[999]">
            <div className="p-3">
              <pre>{hover}</pre>
            </div>
          </HoverCardContent>
        </HoverCard>}
        {headerComponent && <span className="mr-2">{headerComponent}</span>}
      </div>{" "}
      <hr className="text-white/25" />
      {children}
    </div>
  );
};

export default memo(ChartCard);
