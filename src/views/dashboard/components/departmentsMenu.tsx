import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface DepartmentsMenuProps {
  setSelectedDepartment: (value: string) => void;
  selectedDepartment: string;
  setSelectedDepartmentId: (value: number) => void;
  selectedDepartmentId: number;
  departments: { departmentName: string; departmentId: number }[]; 
  all?: boolean;
}

const DepartmentsMenu: FC<DepartmentsMenuProps> = ({
  setSelectedDepartment,
  selectedDepartment,
  setSelectedDepartmentId,
  selectedDepartmentId,
  departments,
  all,
}) => {
  return (
    <Select
      onValueChange={(value) => {
       
        const [departmentName, departmentId] = value.split("|");
        setSelectedDepartment(departmentName);
        setSelectedDepartmentId(Number(departmentId));
      }}
      value={`${selectedDepartment}|${selectedDepartmentId}`} 
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select a department" />
      </SelectTrigger>
      <SelectContent>
        {all && <SelectItem value="All|0">All Departments</SelectItem>}
        {departments.length > 0 ? (
          departments.map((dept, index) => (
            <SelectItem
              key={index}
              value={`${dept.departmentName}|${dept.departmentId}`}
            >
              {dept.departmentName}
            </SelectItem>
          ))
        ) : (
          <SelectItem value="Please select a department|0" disabled>
            Please select a department
          </SelectItem>
        )}
      </SelectContent>
    </Select>
  );
};

export default memo(DepartmentsMenu);