import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import ChartCard from "../chart.card";
import DialogSelect from "../dialogSelect";
import Bar<PERSON><PERSON> from "views/dashboard/charts/bar.chart";
import { CheckpointsDetails } from "./checkpointDetails";
import useCheckpointStore from "store/checkpoint/checkpoint.store";

interface StartDate {
  start_day: number;
  start_month: number;
  start_year: number;
}
interface EndDate {
  end_day: number;
  end_month: number;
  end_year: number;
}
interface CheckpointsProps {
  startDate: StartDate;
  endDate: EndDate;
}

const CheckpointView: React.FC<CheckpointsProps> = ({ startDate, endDate }) => {
  const {
    getDialogCheckpoints,
    getDialogsWithCheckpoints,
    dialogs,
    dialogCheckpoints,
  } = useCheckpointStore();
  const bot = useBotStore((state) => state.bot);

  const [selectedDialog, setSelectedDialog] = useState(dialogs[0]);
  const [checkpointsStats, setCheckpointsStats] = useState({
    series: [],
    xLabels: [],
  });

  useEffect(() => {
    getDialogsWithCheckpoints(
      bot.bot_id,
      startDate.start_day,
      startDate.start_month,
      startDate.start_year,
      endDate.end_day,
      endDate.end_month,
      endDate.end_year
    );
  }, [startDate, endDate]);


  useEffect(() => {
    getDialogCheckpoints(
      selectedDialog?.dialog_id,
      startDate.start_day,
      startDate.start_month,
      startDate.start_year,
      endDate.end_day,
      endDate.end_month,
      endDate.end_year
    );
  }, [selectedDialog]);


  useEffect(() => {
    if (dialogCheckpoints && dialogCheckpoints.length > 0) {
      const processedData = processCheckpointsForChart(dialogCheckpoints);
      setCheckpointsStats(processedData);
    } else {
      setCheckpointsStats({
        series: [],
        xLabels: [],
      });
    }
  }, [dialogCheckpoints]);

  const processCheckpointsForChart = (checkpoints: any[]) => {
    const channelSet = new Set<string>();
    const tagMap: Record<string, Record<string, number>> = {};

    checkpoints.forEach((checkpoint) => {
      const { channel, tag } = checkpoint;
      channelSet.add(channel);

      if (!tagMap[tag]) {
        tagMap[tag] = {};
      }

      if (!tagMap[tag][channel]) {
        tagMap[tag][channel] = 0;
      }

      tagMap[tag][channel]++;
    });

    const xLabels = Array.from(channelSet).sort();

    const seriesData = Object.entries(tagMap).map(([tag, countsByChannel]) => ({
      name: tag,
      data: xLabels.map((channel) => countsByChannel[channel] || 0),
    }));

    return {
      series: seriesData,
      xLabels,
    };
  };

  return (
    <>
      <ChartCard
        className="pb-10"
        title={`Dialog Checkpoints statistics`}
        headerComponent={
          <>
            <div className="flex items-center space-x-4">
              <span className="text-gray-300 font-medium">
                Select a dialog:
              </span>
              <DialogSelect
                onChangeDialog={setSelectedDialog}
                dialog={selectedDialog}
                dialogs={dialogs}
              />
            </div>
          </>
        }
      >
        <div className="relative">
          <BarChart
            data={[...checkpointsStats?.series]}
            xLabels={[...checkpointsStats?.xLabels]}
          />
          <div>
            <div className="absolute right-0 mr-2">
              <CheckpointsDetails />
            </div>
          </div>
          <div style={{ marginBottom: "20px" }}></div>
        </div>
      </ChartCard>
    </>
  );
};

export { CheckpointView };
