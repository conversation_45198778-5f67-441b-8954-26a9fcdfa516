import { useEffect, useState } from "react";
import {
  <PERSON>et,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "common/ui/sheet";
import useBotStore from "store/bot/bot.store";
import { MainTable } from "common/components/tables/main.table";
import searchedArray from "helpers/search";
import { CustomSearch } from "common/ui/inputs/search";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { DateRange } from "react-day-picker";
import { FilterXIcon, UploadIcon } from "lucide-react";
import { Button } from "common/ui/button";
import { CSVLink } from "react-csv";
import transformDate from "helpers/transformDate";
import useCheckpointStore from "store/checkpoint/checkpoint.store";
import Pagination from "common/ui/pagination";

interface CheckpointsProps {children}

const getWaConversationId = (conversationId: string) => {
  if (!conversationId) return '';
  return conversationId.split('_')[0];
};

const getFbConversationId = (conversationId: string) => {
  if (!conversationId) return '';
  return conversationId.split('_')[1];
};

const Checkpoints: React.FC<CheckpointsProps> = ({ children }) => 
{
  const bot = useBotStore((state) => state.bot);
  const { getAll, checkpoints } = useCheckpointStore();
  const [renderedCheckpoints, setRenderedCheckpoints] = useState<any[]>([]);
  const [keySearch, setKeySearch] = useState("");
  const [date, setDate] = useState<DateRange | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    setLoading(true);
    try {
      getAll(bot.bot_id, page, pageSize);
    } catch (error) {
      console.error("Error fetching checkpoints:", error);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize]);


  useEffect(() => {
    if (checkpoints?.data) {
      const transformedData = checkpoints.data
        .map((checkpoint) => ({
          ...checkpoint,
          createdAt: transformDate(checkpoint?.createdAt),
          dialog_name:
            checkpoint?.bot_designer_dialog?.dialog_name ||
            checkpoint?.dialog_name,
          conversation_id:
            checkpoint?.channel === 'web'
              ? checkpoint?.conversation_id
              : checkpoint?.channel === 'whatsapp'
              ? getWaConversationId(checkpoint?.conversation_id)
              : getFbConversationId(checkpoint?.conversation_id),
        }))
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

      setRenderedCheckpoints(transformedData);
    }
  }, [checkpoints]);

  useEffect(() => {
    if (!checkpoints?.data) return;

    let filteredData = [...checkpoints.data];
    if (date?.from && date?.to) {
      filteredData = filteredData.filter((checkpoint) => {
        const checkpointDate = new Date(checkpoint.createdAt);
        return checkpointDate >= date.from! && checkpointDate <= date.to!;
      });
    } else if (date?.from) {
      filteredData = filteredData.filter((checkpoint) => {
        const checkpointDate = new Date(checkpoint.createdAt);
        return checkpointDate.toDateString() === date.from!.toDateString();
      });
    }

    if (keySearch) {
      filteredData = searchedArray(keySearch, filteredData);
    }

    const transformedData = filteredData
      .map((checkpoint) => ({
        ...checkpoint,
        createdAt: transformDate(checkpoint?.createdAt),
        dialog_name:
          checkpoint?.bot_designer_dialog?.dialog_name ||
          checkpoint?.dialog_name,
        conversation_id:
          checkpoint?.channel === 'web'
            ? checkpoint?.conversation_id
            : checkpoint?.channel === 'whatsapp'
            ? getWaConversationId(checkpoint?.conversation_id)
            : getFbConversationId(checkpoint?.conversation_id),
      }))
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

    setRenderedCheckpoints(transformedData);
  }, [date, keySearch, checkpoints]);

  const exportFile = () => {
    const headers = [
      { label: "Conversation ID", key: "conversation_id" },
      { label: "Channel", key: "channel" },
      { label: "Dialog Name", key: "dialog_name" },
      { label: "Label", key: "tag" },
      { label: "Date", key: "createdAt" },
    ];

    const data = renderedCheckpoints.map((checkpoint) => ({
      conversation_id: checkpoint.conversation_id,
      channel: checkpoint.channel,
      dialog_name:
        checkpoint?.bot_designer_dialog?.dialog_name || checkpoint?.dialog_name,
      tag: checkpoint.tag,
      createdAt: checkpoint.createdAt,
    }));

    return {
      data,
      headers,
      filename: `checkpoints.csv`,
    };
  };

  const tableData = () => {
    const columns = [
      { name: "Conversation ID", key: "conversation_id" },
      { name: "Channel", key: "channel" },
      { name: "Dialog Name", key: "dialog_name" },
      { name: "Label", key: "tag" },
      { name: "Date", key: "createdAt" },
    ];

    return {
      data: renderedCheckpoints,
      columns,
      itemsPerPage: 10,
    };
  };

  const handlePageChange = (selectedPage: number) => {
    setPage(selectedPage);
  };

  return (
    <Sheet>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="full">
        <SheetHeader>
          <SheetTitle>Checkpoints</SheetTitle>
          <SheetDescription className="flex justify-between items-center">
            View and Manage User Checkpoints Details
            <CSVLink {...exportFile()}>
              <Button>
                <UploadIcon className="mr-2 h-4 w-4" />
                Export as CSV
              </Button>
            </CSVLink>
          </SheetDescription>
        </SheetHeader>
        <div className="pt-5 space-y-3">
          <div className="flex gap-5">
            <CustomSearch
              placeholder="Find a checkpoint..."
              onChange={(value) => setKeySearch(value)}
            />
            <DatePickerWithRange date={date} setDate={setDate} />
            {date && (
              <Button
                variant="outline"
                className="text-white"
                onClick={() => setDate(undefined)}
              >
                <FilterXIcon size={15} />
              </Button>
            )}
          </div>
          <MainTable loading={loading} {...tableData()} />
          {checkpoints?.data?.length > 0 && (
            <Pagination
              currentPage={page}
              totalCount={checkpoints?.pagination?.totalItems || 1}
              pageSize={pageSize}
              onPageChange={handlePageChange}
            />
          )}
        </div>
        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export { Checkpoints };