import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON>Footer } from "common/ui/sheet";
import { Eye } from "lucide-react";
import { <PERSON><PERSON> } from "common/ui/button";
import { createOne, getOne, updateOne } from "apis/checkpointDetails.api";
import useBotStore from "store/bot/bot.store";
import { Checkpoints } from "./checkpoint";
import { CheckpointType } from "types/checkpoint.types";

const CheckpointsDetails = () => {
  const bot = useBotStore((state) => state.bot);
  // const [checkpointsData, setCheckpointsData] = useState<CheckpointType | null>(null);
  // const [loading, setLoading] = useState(false);
  // const [fetchLoading, setFetchLoading] = useState<boolean>(false);

  // useEffect(() => {
  //   setFetchLoading(true);
  //   getOne(bot.bot_id).then((data) => {
  //     if (data && !data.message) {
  //       setCheckpointsData({
  //         ...data,
  //       });
  //     }
  //     setFetchLoading(false);
  //   });
  // }, []);

  // const onClickCheckpointsHandler = () => {
  //   setLoading(true);
  //   if (!checkpointsData) {
  //     createOne({
  //       bot_id: bot.bot_id,
  //       active: true,
  //     }).then((data) => {
  //       setLoading(false);
  //       setCheckpointsData({ ...data });
  //     });
  //   } else if (checkpointsData) {
  //     if (checkpointsData.active) {
  //       updateOne({
  //         bot_id: bot.bot_id,
  //         ...checkpointsData,
  //         active: false
  //       }).then((data) => {
  //         setLoading(false);
  //         setCheckpointsData({ ...data });
  //       });
  //     } else {
  //       updateOne({
  //         bot_id: bot.bot_id,
  //         ...checkpointsData,
  //         active: true,
  //         ask_email: true,
  //         ask_phone: true,
  //       }).then((data) => {
  //         setLoading(false);
  //         setCheckpointsData({ ...data });
  //       });
  //     }
  //   }
  // };

  return (
    <Sheet>
      <SheetTrigger asChild>
      <Checkpoints>
        <Button variant="outline" size="default" className="flex items-center">
          <Eye className="mr-2 h-4 w-4" />
          More Details
        </Button>
        </Checkpoints>
      </SheetTrigger>
      <SheetContent className="overflow-y-auto" position="right" size="full">
        <SheetHeader>
          <SheetTitle>Checkpoints</SheetTitle>
          <SheetDescription>
            View and Manage User Checkpoints Details
          </SheetDescription>
        </SheetHeader>

        <SheetFooter></SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export { CheckpointsDetails }