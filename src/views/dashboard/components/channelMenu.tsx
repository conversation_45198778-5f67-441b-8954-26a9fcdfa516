import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface ChannelMenuProps {
  setChannel: (value: string) => void;
  channel: string;
}

const ChannelMenu: FC<ChannelMenuProps> = ({ channel, setChannel }) => {
  // Default value is "0" if channel is not provided
  const defaultValue = "All";

  return (
    <Select
      onValueChange={(value) => {
        setChannel(value);
      }}
      value={channel || defaultValue} // Ensure "0" is the default value
    >
      <SelectTrigger className="w-full">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="All">All Channels</SelectItem>
        <SelectItem value="Whatsapp">Whatsapp</SelectItem>
        <SelectItem value="Facebook">Facebook</SelectItem>
        <SelectItem value="Instagram">Instagram</SelectItem>
        <SelectItem value="Web">Web</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default memo(ChannelMenu);
