import React, { useState, useEffect, useMemo } from 'react';
import { Button } from 'common/ui/button';
import { CSVLink } from 'react-csv';
import { CustomSearch } from 'common/ui/inputs/search';
import { DatePickerWithRange } from 'common/ui/inputs/dateRangePicker';
import { DateRange } from "react-day-picker";
import Pagination from 'common/ui/pagination';
import transformDate from 'helpers/transformDate';
import { getConversationIds, getConversationDetails } from 'apis/chatbotReport.api';
import useBotStore from 'store/bot/bot.store';
import searchedArray from 'helpers/search';
import ChatModal from './chatModal';

const ChatbotReport = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [keySearch, setKeySearch] = useState('');
  const [date, setDate] = useState<DateRange | undefined>();
  const [filteredData, setFilteredData] = useState([]);
  const [originalData, setOriginalData] = useState([]); 
  const [selectedChat, setSelectedChat] = useState(null);
  const [showChatWindow, setShowChatWindow] = useState(false);
  const itemsPerPage = 5;
  const bot_id = useBotStore((state) => state.bot.bot_id);

  useEffect(() => {
    const fetchData = async () => {
      const data = await getConversationIds(bot_id);
      if (Array.isArray(data)) {
        setOriginalData(data); 
        setFilteredData(data); 
      } else {
        setFilteredData([]);
      }
    };
  
    fetchData();
  }, [bot_id]); 

  useEffect(() => {
    let data = [...originalData];

    // search filtering
    if (keySearch) {
      data = searchedArray(keySearch, data, ['conversation_id', 'channel', 'country', 'date']);
    }

    // date filtering
    if (date && date.from && date.to) {
      data = data.filter(chat => {
        const chatDate = new Date(chat.date);
        return (
          chatDate.getTime() >= date.from.getTime() &&
          chatDate.getTime() <= date.to.getTime()
        );
      });
    }

    setFilteredData(data);
  }, [keySearch, date, originalData]); 

  const paginatedData = useMemo(() => {
    if (!filteredData || filteredData.length === 0) return [];
    
    const firstIndex = (currentPage - 1) * itemsPerPage;
    const lastIndex = firstIndex + itemsPerPage;
    return filteredData.slice(firstIndex, lastIndex);
  }, [currentPage, filteredData]);  

  const handlePageChange = page => {
    setCurrentPage(page);
  };

  const exportFile = () => {
    const headers = [
      { label: 'Conversation ID', key: 'conversation_id' },
      { label: 'Channel', key: 'channel' },
      { label: 'Date', key: 'date' },
      { label: 'Time', key: 'time' },
      { label: 'Country', key: 'country' }
    ];

    const data = filteredData?.map(chat => {
      const chatDate = new Date(chat.date);
      const formattedTime = chatDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      const conversationId = chat.channel === 'facebook' || chat.channel === 'instagram'
        ? chat.conversation_id.split('_')[1]
        : chat.channel === 'whatsapp' 
        ? chat.conversation_id.split('_')[0]
        : chat.conversation_id;

      return {
        conversation_id: conversationId,
        channel: chat.channel,
        date: transformDate(chat.date),
        time: formattedTime,
        country: chat.country || 'N/A'
      };
    });

    return {
      data,
      headers,
      filename: 'chatbot_report.csv',
    };
  };

  const handleViewChat = (chat) => {
    setSelectedChat(chat);
    setShowChatWindow(true);
  };

  return (
    <div className="p-4">
      <div className="flex gap-5 pb-4">
        <CustomSearch
          placeholder="Search chat history..."
          onChange={value => setKeySearch(value)}
        />
        <DatePickerWithRange date={date} setDate={setDate} />
        <CSVLink {...exportFile()}>
          <Button>
            Export as CSV
          </Button>
        </CSVLink>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full overflow-hidden">
          <thead className="sticky top-0 z-30 bg-themeSecondary ">
            <tr>
              <th className="px-4 py-5 font-medium text-left text-white">Conversation ID</th>
              <th className="px-4 py-5 font-medium text-left text-white">Channel</th>
              <th className="px-4 py-5 font-medium text-left text-white">Date</th>
              <th className="px-4 py-5 font-medium text-left text-white">Time</th>
              <th className="px-4 py-5 font-medium text-left text-white">Country</th>
              <th className="px-4 py-5 font-medium text-left text-white">Message History</th>
            </tr>
          </thead>

          <tbody className="divide-y divide-white/25 py-2">
            {paginatedData.length > 0 ? (
              paginatedData.map((chat, index) => {
                const chatDate = new Date(chat.date);
                const formattedTime = chatDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                const conversationId = chat.channel === 'facebook' || chat.channel == 'instagram'
                  ? chat.conversation_id.split('_')[1]
                  : chat.channel === 'whatsapp' 
                  ? chat.conversation_id.split('_')[0]
                  : chat.conversation_id;

                return (
                  <tr key={index} className="hover:backdrop-brightness-125">
                    <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                      {conversationId || 'N/A'}
                    </td>
                    <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                      {chat.channel || 'N/A'}
                    </td>
                    <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                      {transformDate(chat.date) || 'N/A'}
                    </td>
                    <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                      {formattedTime || 'N/A'}
                    </td>
                    <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                      {chat.country || 'N/A'}
                    </td>
                    <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                      <Button onClick={() => handleViewChat(chat)}>View</Button>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-4 bg-white/25">
                  No Data was Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {filteredData.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalCount={filteredData.length}
          pageSize={itemsPerPage}
          onPageChange={handlePageChange}
        />
      )}
      {showChatWindow && (
        <ChatModal chat={selectedChat} onClose={() => setShowChatWindow(false)} />
      )}
    </div>
  );
};

export default ChatbotReport;
