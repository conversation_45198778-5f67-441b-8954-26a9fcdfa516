import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface ClosedReasonsMenuProps {
  setReason: (value: string) => void;
  reason: string;
}

const ClosedReasonsMenu: FC<ClosedReasonsMenuProps> = ({ reason, setReason }) => {
  const defaultValue = "All";

  return (
    <Select
      onValueChange={(value) => {
        setReason(value);
      }}
      value={reason || defaultValue}
    >
      <SelectTrigger className="w-full">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="All">All Reasons</SelectItem>
        <SelectItem value="Closed">Closed</SelectItem>
        <SelectItem value="Closed due to agent inactivity">Closed due to agent inactivity</SelectItem>
        <SelectItem value="Closed due to customer inactivity">Closed due to customer inactivity</SelectItem>
        <SelectItem value="Closed due to inactivity">Closed due to inactivity</SelectItem>
        <SelectItem value="Closed due to agent rejection">Closed due to agent rejection</SelectItem>
        <SelectItem value="Closed due to customer">Closed due to customer</SelectItem>
        <SelectItem value="Closed due to agent unavailability">Closed due to agent unavailability</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default memo(ClosedReasonsMenu);
