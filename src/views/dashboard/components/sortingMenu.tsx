import { FC, memo, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { DateRange } from "react-day-picker";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@radix-ui/react-hover-card";
import { Info } from "lucide-react";

interface SortingMenuProps {
  selectedPeriod: number ;
  setSelectedPeriod: (value: number) => void;
  setSubmitLoading: (value: boolean) => void;
  filterDate: DateRange;
  setFilterDate: (value: DateRange) => void;
}

const SortingMenu: FC<SortingMenuProps> = ({
  selectedPeriod,
  setSelectedPeriod,
  setSubmitLoading,
  filterDate,
  setFilterDate,
}) => {
  const [showCalendar, setShowCalendar] = useState(false);

  const handleValueChange = (value: string) => {
    if (value === "calendar") {
      setFilterDate({ from: null, to: null });
      setShowCalendar(true);
      setSelectedPeriod(+22)
    } else {
      setShowCalendar(false);
      setSubmitLoading(true);
      setSelectedPeriod(+value);
      setFilterDate({ from: null, to: null });
    }
  };

  return (
    <>
      <Select
        onValueChange={handleValueChange}
        value={showCalendar ? "calendar" : selectedPeriod.toString()}
      >
        <SelectTrigger className="w-[210px]">
          <SelectValue placeholder="Select a period" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="0">Today</SelectItem>
          <SelectItem value="1">Yesterday</SelectItem>
          <SelectItem value="7">Last 7 Days</SelectItem>
          <SelectItem value="30">Last 30 Days</SelectItem>
          <SelectItem value="90">Last 3 Months</SelectItem>
          <SelectItem value="365">Last Year</SelectItem>
          <SelectItem value="10000">Show All Data</SelectItem>
          <SelectItem value="calendar">Custom Date</SelectItem>
        </SelectContent>
      </Select>
      {showCalendar && (
        <div className="flex items-center space-x-2">
          <DatePickerWithRange
              className="!w-[210px]"
              date={filterDate}
              setDate={setFilterDate}
            />
          <HoverCard>
            <HoverCardTrigger>
              <Info size={20} className="cursor-pointer" />
            </HoverCardTrigger>
            <HoverCardContent className="w-60 bg-black ring-2 ring-gray-500/50 text-sm">
              <div className="p-3">
                Please provide Start and End Date.
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
      
      )}
    </>
  );
};

export default memo(SortingMenu);
