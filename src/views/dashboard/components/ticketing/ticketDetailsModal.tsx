import { getTicket } from "apis/ticketing.api";
import { FullModal } from "common/components/modals";
import { Tooltip } from "common/ui/tooltip";
import { Mail, Phone, User2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Ticket } from "store/ticketing/ticketing.types";
type TProps = {
  showModal: boolean;
  setShowModal: (showModal: boolean) => void;
  ticketUUID: string
};
const TicketDetailsModal: React.FC<TProps> = ({ showModal, setShowModal, ticketUUID }) => {
  const [data, setData] = useState<Ticket | null>(null)
  const [loading, setLoading] = useState(false)
  useEffect(() => {
    if (ticketUUID) {
      setLoading(true)
      getTicket(ticketUUID).then((res) => {
        setData(res)
      }).finally(() => {
        setLoading(false)
      })

    }
  }, [ticketUUID]);
  return (
    <FullModal
      title={"Ticket Details"}
      onClose={() => setShowModal(false)}
      isOpen={showModal}
      disabled={false}
      onSave={() => { }}
      loading={loading}
    >

      <div className=" mx-auto px-2 pb-3 pt-1 rounded-lg shadow-lg w-full">
       

        {loading ? (
            <div className="flex flex-col items-start justify-center w-full p-6  rounded-lg shadow-md animate-pulse">
              <div className="flex items-center justify-between w-full mb-4 border-b pb-4">
                <div className="flex flex-col">
                  <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
                  <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
                </div>
                <div>
                  <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
                </div>
              </div>

              <div className="flex flex-col w-full mb-4">
                <h2 className="text-lg font-semibold  mb-2">Customer Details</h2>
                <div className="flex flex-col gap-1">
                  <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
                  <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
                  <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
                </div>
              </div>

              <div className="w-full">
                <h2 className="text-lg font-semibold  mb-2">Issue Details:</h2>
                <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5"></div>
              </div>
            </div>
          ) : ( 
            <div className="mb-5   rounded-lg">

            <div className="flex justify-between items-center border-b pb-4 mb-4">
              <h1 className="text-3xl font-semibold ">
                {data?.title}
              </h1>
              <p className="text-sm">
                ID: <span className="font-medium">{data?.ticket_uuid}</span>
              </p>
            </div>
  
            <div className="flex  lg:flex-row justify-start items-start lg:items-start mb-4 gap-4">
              <fieldset className="border rounded-md">
                <legend className="ml-2">Customer Details:</legend>
                <section className="  flex   items-start justify-start gap-3  p-2 rounded-md">
               {data?.customer_name && 
               <div className="flex  items-center justify-start gap-2 text-center w-full md:w-auto mb-4 md:mb-0">
               <User2 className="w-5 h-5" />
               <p className=" font-medium text-sm">
                 {data?.customer_name}
               </p>
             </div>
               } 
               {data?.customer_email && 
                     <div className="flex items-center justify-start gap-2 text-center w-full md:w-auto mb-4 md:mb-0">
                     <Mail className="w-5 h-5" />
                     <a
                       href={`mailto:${data?.customer_email}`}
                       className=" font-medium text-sm">
                       {data?.customer_email}
                     </a>
                   </div>
               }
          
  
            {data?.customer_phone && 
                 <div className="flex  items-center justify-start gap-2 text-center w-full md:w-auto">
                 <Phone className="w-5 h-5" />
                 <a
                   href={`tel:${data?.customer_phone}`}
                   className="font-medium text-sm">
                   {data?.customer_phone}
                 </a>
               </div>
            }
           
              </section>
              </fieldset>
  
        
  
  
  
            </div>
            <fieldset className="border w-fit rounded-md p-2">
            <legend className="ml-2">Issue Details:</legend>
            <p className="ml-2 text-base text-wrap leading-6 ">{data?.description}</p>

            </fieldset>
 
          </div>
          )}


        <div className="space-y-4 px-2">
          <h2 className="text-center text-xl  border-b">Conversation</h2>
          {data?.comments?.length === 0 && !loading && <p className="text-center">No Comments Found</p>}
          {!loading && data?.comments?.map((comment) => (
            <div
              key={comment.comment_id}
              className={`flex items-start ${comment.sender !== 'agent' ? 'flex-row-reverse' : ''
                }`}
            >
              {/* Avatar */}

              <Tooltip text={comment.sender}>
                <div
                  className={`w-12 h-12 rounded-full flex-shrink-0 ${comment.sender !== 'agent' ? 'bg-blue-500' : 'bg-green-500'
                    } flex items-center justify-center text-white font-bold`}
                >
                  {comment.sender === 'agent' ? 'A' : comment.sender.charAt(0).toUpperCase()}
                </div>
              </Tooltip>


              {/* Message */}
              <div
                className={`max-w-md p-4 rounded-lg shadow-md text-white ${comment.sender !== 'agent'
                  ? 'bg-blue-500 mr-4 text-right'
                  : 'bg-green-500 ml-4'
                  }`}
              >
                <p className="mb-1">{comment.comment}</p>
                <span className="text-sm text-gray-200">
                  {new Date(comment.createdAt).toLocaleString()}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>



    </FullModal>
  );
};

export default TicketDetailsModal;