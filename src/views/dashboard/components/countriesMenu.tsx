import { FC, memo, useState } from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "lib/utils";
import { Button } from "common/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "common/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "common/ui/popover";

interface countriesMenuProps {
  countries: Record<"value" | "label", string>[];
  onSelect: (value: string) => void;
  value: string;
}

const CountriesMenu: FC<countriesMenuProps> = ({
  countries,
  onSelect,
  value,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between !text-white"
        >
          {value
            ? countries.find(
                (country) =>
                  country?.value?.toLowerCase() === value?.toLowerCase()
              )?.label
            : "Select country..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search country..." />
          <CommandEmpty>No countries found.</CommandEmpty>
          <CommandGroup>
            {countries.map((country) => (
              <CommandItem
                key={country.value}
                onSelect={(currentValue) => {
                  onSelect(currentValue);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value?.toLowerCase() === country?.value?.toLowerCase()
                      ? "opacity-100"
                      : "opacity-0"
                  )}
                />
                {country.label}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default memo(CountriesMenu);
