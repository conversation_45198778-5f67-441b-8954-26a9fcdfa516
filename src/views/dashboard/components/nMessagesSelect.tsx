import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface nMessagesSelectProps {
  onChangeNMessages: (value: number) => void;
  NMessages: number;
}

const NMessagesSelect: FC<nMessagesSelectProps> = ({
  onChangeNMessages,
  NMessages,
}) => {
  return (
    <Select
      onValueChange={(value) => {
        onChangeNMessages(+value);
      }}
      value={NMessages?.toString()}
    >
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select a filter" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="15">Last 15 Messages</SelectItem>
        <SelectItem value="30">Last 30 Messages</SelectItem>
        <SelectItem value="50">Last 50 Messages</SelectItem>
        <SelectItem value="90">Last 90 Messages</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default memo(NMessagesSelect);
