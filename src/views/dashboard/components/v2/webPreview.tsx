import {
  Bot,
  Circle,
  Download,
  EllipsisVerticalIcon,
  File,
  FileText,
  Info,
  Mic2Icon,
  MicIcon,
  MouseIcon,
  PlusIcon,
  VideoIcon,
  SendIcon,
  SmileIcon,
  GlobeIcon,
  X,
} from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { MapCard } from "./cards/mapCard";
import Link from "next/link";
import toast from "react-hot-toast";
import PdfSVG from "common/icons/PdfSVG";
import Accordion from "common/ui/accordion";
import useBotStore from "store/bot/bot.store";
import { Avatar, AvatarFallback, AvatarImage } from "common/ui/avatar";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";
import constant from "constant";
import ChartFactory, { ChartCard } from "./cards/incubatorCharts";

export default function WebPreview({ messages }) {
  const bot = useBotStore((state) => state.bot);

  console.log(messages, "message");
  const renderMessageContent = (message) => {
    switch (message.type) {
      case "text":
        if (message.button_payload) {
          return (
            <p
              onClick={() => {
                navigator.clipboard.writeText(message.button_payload);
                toast.success("Copied to clipboard");
              }}
              title={message.button_payload}
              className="text-sm break-words cursor-pointer"
            >
              {message.content}{" "}
              <MouseIcon size={10} className="text-gray-700 float-right" />
            </p>
          );
        } else {
          return (
            <p className="text-sm break-words">
              {message.content}
              {message.is_voice ? (
                <MicIcon size={10} className="text-gray-700 float-right" />
              ) : (
                ""
              )}
            </p>
          );
        }

      case "attachment":
        const type = message.content?.split(".")?.pop();
        const isImage = ["jpg", "png", "jpeg", "gif", "svg"].includes(type);
        const name = message.content?.split("/").pop();
        //"https://infotointell.fra1.digitaloceanspaces.com/Bots/refactorDuaaTest-861/attachments/TLONLXDXELIFCVXYSLUP.png"
        return (
          <div>
            {isImage ? (
              <img
                src={message.content}
                alt={message.content}
                className="w-[200px] h-[200px] rounded-lg overflow-hidden"
              />
            ) : type.includes("pdf") ? (
              <div className="min-w-[200px] text-sm p-1 bg-white rounded-lg overflow-hidden  text-black">
                <Link
                  key={message.content || ""}
                  href={message.content}
                  target="_blank"
                  className="flex justify-between w-full"
                >
                  <div className="flex gap-2 items-center break-words">
                    <PdfSVG />
                    {name}
                  </div>
                  <Download size={15} className="text-gray-500" />
                </Link>
              </div>
            ) : type.includes("doc") ? (
              <div className="min-w-[200px] text-sm p-1 bg-white rounded-lg">
                <Link
                  key={message.content || ""}
                  href={message.content}
                  target="_blank"
                  className="flex justify-between w-full"
                >
                  <div className="flex gap-2 items-center">
                    <FileText className="text-blue-600" size={15} />
                    {name}
                  </div>
                  <Download size={15} className="text-gray-500" />
                </Link>
              </div>
            ) : (
              <div className="min-w-[200px] text-xs p-1 bg-white rounded-lg">
                <Link
                  key={message.content || ""}
                  href={message.content}
                  target="_blank"
                  className="flex justify-between w-full"
                >
                  <div className="flex gap-2 items-center text-black break-words">
                    <File size={15} />
                    {name}
                  </div>
                  <Download size={15} className="text-gray-500" />
                </Link>
              </div>
            )}
          </div>
        );

      case "attachments":
        const data = JSON.parse(message.content);
        return (
          <div>
            {data.attachments.map((attachment) => {
              return (
                <>
                  {attachment?.type.includes("image") && (
                    <img
                      src={constant.MEDIA_STORAGE_URL + attachment?.path}
                      alt={attachment?.filename}
                      className="w-[200px] h-[200px] rounded-lg overflow-hidden"
                    />
                  )}
                  {attachment?.type.includes("pdf") && (
                    <div className="min-w-[200px] text-sm p-1 bg-white rounded-lg">
                      <Link
                        key={attachment?.path || ""}
                        href={constant.MEDIA_STORAGE_URL + attachment?.path}
                        target="_blank"
                        className="flex justify-between w-full"
                      >
                        <div className="flex gap-2 items-center">
                          <PdfSVG />
                          {attachment?.filename}
                        </div>
                        <Download size={15} className="text-gray-500" />
                      </Link>
                    </div>
                  )}
                  {attachment.type.includes("doc") && (
                    <div className="min-w-[200px] text-sm p-1 bg-white rounded-lg">
                      <Link
                        key={attachment?.path || ""}
                        href={constant.MEDIA_STORAGE_URL + attachment?.path}
                        target="_blank"
                        className="flex justify-between w-full"
                      >
                        <div className="flex gap-2 items-center">
                          <FileText className="text-blue-600" size={15} />
                          {attachment?.filename}
                        </div>
                        <Download size={15} className="text-gray-500" />
                      </Link>
                    </div>
                  )}
                </>
              );
            })}
          </div>
        );

      case "suggested_actions":
        const actions = JSON.parse(message.content);
        return (
          <div className="flex flex-col gap-2">
            {actions.map((action, index) => (
              <button
                key={index}
                onClick={() => {
                  navigator.clipboard.writeText(action.value);
                  toast.success("Copied to clipboard");
                }}
                className="text-sm text-[#CD97FF] break-words cursor-pointer border-2 border-[#CD97FF] rounded-xl w-fit px-2"
              >
                {action.title}
              </button>
            ))}
          </div>
        );

      case "location":
        const location = JSON.parse(message.content);
        return (
          <MapCard
            cardData={{
              lat: location.latitude,
              long: location.longitude,
              title: location.title,
              description: location.description,
            }}
          />
        );

      case "carousel":
        const card = JSON.parse(message.content);
        return <Carousel elements={card} />;

      case "table":
        const table = JSON.parse(message.content);
        return <DynamicTable data={table.data} mapper={table.mapper} />;

      case "chart":
        const chart = JSON.parse(message.content);
        return <ChartCard activity={chart} />;

      case "html_code":
        const ac = JSON.parse(message.content);
        return (
          <div
            className="overflow-hidden"
            dangerouslySetInnerHTML={{ __html: ac.htmlCode || "" }}
          />
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    const chatArea = document.getElementById("chat-area");
    if (chatArea) {
      chatArea.scrollTop = chatArea.scrollHeight;
    }
  }, [messages]);

  return (
    <div className="relative flex flex-col h-[600px] w-full max-w-md mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      <div
        className={`flex px-3 items-center justify-between bg-[#CD97FF] h-16 rounded-t-lg text-white`}
      >
        <div className="flex gap-3 content-center items-center ">
          <img
            src={
              "https://storage.botforce.ai/assets/bot-designer/thumbnail_Client-Logo-Placeholder.png"
            }
            alt=""
            className="w-10 h-10 rounded-full object-contain"
          />
          <div>
            <p className="text-xs">Chat With</p>
            <p className="text-xs font-bold capitalize"> Bot</p>
          </div>
        </div>

        <div className="flex justify-self-end gap-3">
          <div className="group relative cursor-pointer flex justify-center items-center p-1">
            <span
              className={` hidden group-hover:block bg-gray-300
              opacity-20  rounded-full group-hover:absolute inset-0 `}
            ></span>
            <EllipsisVerticalIcon size={20} />
          </div>
          <div className="group relative cursor-pointer flex justify-center items-center p-1 z-[22222]">
            <span
              className={` "hidden group-hover:block bg-gray-300 "
              opacity-20  rounded-full group-hover:absolute inset-0 `}
            ></span>
            <X size={20} />
          </div>
        </div>
      </div>

      {/* Chat Messages Area - Messenger style */}
      <div id="chat-area" className="flex-1  text-black p-4 overflow-y-auto">
        {messages?.map((message) => (
          <React.Fragment key={message.user_interaction_id}>
            {/* User Message */}
            <div className="flex justify-end mb-4">
              <div className="max-w-[70%] rounded-xl p-3 bg-[#CD97FF] text-white ml-auto">
                {renderMessageContent({
                  type: message.type,
                  content: message.message,
                  button_payload: message.button_payload,
                  is_voice: message.is_voice,
                })}
                <p className="text-xs text-white/70 mt-1 text-right">
                  {new Date(message.createdAt).toLocaleDateString("en-GB")}{" "}
                  {new Date(message.createdAt).toLocaleTimeString("en-GB", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>

            {/* Bot Responses */}
            {message.bot_designer_tr_cube?.bot_responses?.map((botResponse) => (
              <div
                key={botResponse.bot_response_id}
                className="flex justify-start mb-4"
              >
                <div className="min-w-[200px] max-w-[70%] rounded-xl p-3 bg-white shadow-sm">
                  {renderMessageContent({
                    type: botResponse.response_type,
                    content: botResponse.response,
                  })}
                  <div className="flex items-center gap-2 mt-1">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <Info className="w-4 h-4 text-gray-400 cursor-pointer" />
                      </HoverCardTrigger>
                      <HoverCardContent className="bg-gray-800 text-white border-none">
                        <span className="font-bold text-sm">Sources:</span>
                        {botResponse.sources?.map((source, i) => (
                          <div
                            className="text-sm mb-2 border-b border-gray-700"
                            key={source.bot_response_source_id}
                          >
                            <p className="font-medium">
                              {i + 1}. {source.source_type}
                            </p>
                            {source?.source_title && (
                              <p className="text-gray-400">
                                {source.source_title}
                              </p>
                            )}
                            {source?.source_id && (
                              <p className="text-xs text-gray-400">
                                ID: {source.source_id}
                              </p>
                            )}
                          </div>
                        ))}
                      </HoverCardContent>
                    </HoverCard>
                    <span className="text-xs text-gray-400">
                      {new Date(message.createdAt).toLocaleTimeString("en-GB", {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </React.Fragment>
        ))}
      </div>

      <div className="flex flex-col dark:bg-slate-800 dark:text-white">
        <div className=" min-h-fit w-full px-5 flex flex-col items-start">
          <hr className="mx-auto w-full mt-5 mb-3 h-[0.25px] border-t-0 bg-gray-400 opacity-100" />
          <div className="flex w-full px-1 items-center justify-between gap-2">
            <span className="group relative cursor-pointer flex justify-center items-center p-1">
              <MicIcon size={22} className={`z-10 text-gray-600`} />
            </span>
            <input
              type="text"
              className="focus:outline-none outline-none border-none w-full"
              placeholder={"Type your message..."}
              defaultValue={""}
              dir={"auto"}
            />

            <span className="group  flex justify-center items-center">
              <SendIcon
                size={25}
                className={` text-gray-600 dark:text-white  cursor-pointer z-10 `}
              />
            </span>
          </div>
        </div>
        <div className="flex justify-between px-2 pt-5 pb-2 text-black">
          <label className="hover:brightness-125 hover:scale-105">
            <input type="checkbox" className="peer hidden" defaultChecked />
            <span
              className="flex items-center gap-1 cursor-pointer text-xs peer-checked:[&>span:nth-of-type(1)]:translate-x-7 peer-checked:[&>span:nth-of-type(1)]:font-thin 
          peer-checked:[&>span:nth-of-type(1)]:opacity-70
          peer-checked:[&>span:nth-of-type(3)]:-translate-x-7
          peer-checked:[&>span:nth-of-type(3)]:font-bold
          active:scale-90 scale-100 transform transition-all duration-200 
          "
            >
              <GlobeIcon size={15} />
              <span
                className={`font-bold transform
          transition-all ease-spring
          duration-[200ms]`}
              >
                AR
              </span>
              <span className={`font-thin opacity-80 `}>|</span>
              <span
                className={`font-thin opacity-80     transform
          transition-all 
          duration-200 ease-spring`}
              >
                EN
              </span>
            </span>
          </label>
          <p className="text-[10px] font-light opacity-70 flex items-center gap-1">
            Powered By
            <a href="https://searchat.com/" target="_blank">
              <img
                src="https://infotointell.fra1.digitaloceanspaces.com/assets/botforce/Group%2048095368%20(1).svg"
                alt=""
                className="w-16"
              />
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
const IsJsonString = (str) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
const Carousel = ({ elements }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  //"[{\"card_type\":\"map\",\"lat\":\"31.971651186709\",\"long\":\"35.833978927754\",\"title\":\"Location\",\"description\":\"Location Description\"}]"
  //"[{"card_type":"standard","image_url":"https://infotointell.fra1.digitaloceanspaces.com/Bots/refactorDuaaTest-861/cardsImages/searchatLogo.png","heading":"hiyo","subheading":" ","voiceData":{"voice_path_male":"","voice_path_female":""},"poster_url":"","video_url":"","actions":[{"type":"postback","label":"1","postback":"1"}]},{"card_type":"standard","image_url":"https://infotointell.fra1.digitaloceanspaces.com/Bots/refactorDuaaTest-861/cardsImages/Logo3.png","heading":"secondo","subheading":" ","voiceData":{"voice_path_male":"","voice_path_female":""},"poster_url":"","video_url":"","actions":[{"type":"postback","label":"ds","postback":"ds"}]}]"

  if (IsJsonString(elements)) elements = JSON.parse(elements);
  return (
    <div className="relative w-[200px] overflow-hidden">
      <div className="flex">
        {elements.map((element, index) => (
          <div
            key={index}
            className="flex-shrink-0 w-[200px] transition-transform duration-300"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {element.card_type === "map" ? (
              <MapCard
                cardData={{
                  lat: element.lat,
                  long: element.long,
                  title: element.title,
                  description: element.description,
                }}
              />
            ) : null}
            {element.card_type === "standard" ? (
              <div className="bg-white rounded-lg overflow-hidden max-w-xs mb-2">
                {element.image_url && (
                  <img
                    src={element.image_url}
                    alt="Uploaded content"
                    className="object-cover w-full h-40"
                  />
                )}
                {element.video_url && (
                  <div className="p-3">
                    <video
                      src={element.video_url}
                      controls
                      className="w-full"
                    ></video>
                  </div>
                )}
                <div className="p-3">
                  <p className="text-sm font-medium">{element.heading}</p>
                  {element.subheading && (
                    <p className="text-sm font-medium">{element.subheading}</p>
                  )}
                </div>

                {element.actions?.length > 0 && (
                  <div className="flex flex-col gap-1">
                    {element.actions.map((button, idx) => (
                      <button
                        key={idx}
                        onClick={() => {
                          navigator.clipboard.writeText(button?.postback);
                          toast.success("Button payload copied successfully");
                        }}
                        title={button?.postback}
                        className="bg-[#CD97FF] text-xs font-medium py-1 px-2 rounded-lg text-white"
                      >
                        {button.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ) : null}
            {/* <div className="bg-white rounded-lg overflow-hidden max-w-xs mb-2">
              {element.image_url && (
                <img
                  src={element.image_url}
                  alt="Uploaded content"
                  className="object-cover w-full h-40"
                />
              )}
              <div className="p-3">
                <p className="text-sm font-medium">{element.title}</p>
              </div>
            </div>
            {element.buttons?.length > 0 && (
              <div className="flex flex-col gap-1">
                {element.buttons.map((button, idx) => (
                  <button
                    key={idx}
                    onClick={() => {
                      navigator.clipboard.writeText(button?.url);
                      toast.success("Button payload copied successfully");
                    }}
                    title={button?.url}
                    className="bg-[#0084FF] text-xs font-medium py-1 px-2 rounded-lg text-white"
                  >
                    {button.title}
                  </button>
                ))}
              </div>
            )} */}
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      {elements.length > 1 && (
        <>
          {currentIndex > 0 && (
            <button
              onClick={() => setCurrentIndex((prev) => prev - 1)}
              className="absolute left-1 top-1/2 -translate-y-1/2 bg-gray-200/80 rounded-full p-1"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="black" strokeWidth="2" />
              </svg>
            </button>
          )}
          {currentIndex < elements.length - 1 && (
            <button
              onClick={() => setCurrentIndex((prev) => prev + 1)}
              className="absolute right-1 top-1/2 -translate-y-1/2 bg-gray-200/80 rounded-full p-1"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M9 18L15 12L9 6" stroke="black" strokeWidth="2" />
              </svg>
            </button>
          )}
        </>
      )}
    </div>
  );
};

const DynamicTable = ({ mapper, data }) => {
  return data && data.length > 0 ? (
    <div
      style={{ overflowX: "auto" }}
      className="bg-white shadow-md rounded my-6 scrollbar w-fit"
    >
      <table id="table" className="divide-y-2 divide-x-2 divide-gray-200">
        <thead>
          {Object.keys(mapper).length ? (
            <tr>
              {Object.keys(mapper).map((column2, index) => (
                <th
                  key={index}
                  style={{ background: "#CD97FF" }}
                  scope="col"
                  className="py-3 px-3 text-left"
                >
                  {column2}
                </th>
              ))}
            </tr>
          ) : data ? (
            <tr>
              {Object.keys(data[0]).map((column2, index) => (
                <th
                  key={index}
                  style={{ background: "#CD97FF" }}
                  scope="col"
                  className="py-3 px-3 text-left"
                >
                  {column2}
                </th>
              ))}
            </tr>
          ) : (
            "No Data"
          )}
        </thead>
        <tbody className="text-gray-700 text-sm font-light">
          {data &&
            data.map((row, i) => (
              <tr key={i} className="border-b border-gray-200">
                {Object.keys(row).map((key, i) => (
                  <td key={i} className="py-3 px-3 text-left">
                    {" "}
                    {row[key]?.toString().startsWith("https") ? (
                      <a
                        className="hover:brightness-110 hover:underline"
                        style={{
                          color: "#CD97FF",
                        }}
                        href={row[key]}
                        target="_blank"
                      >
                        {" "}
                        {row[key]}
                      </a>
                    ) : (
                      row[key]
                    )}
                  </td>
                ))}
              </tr>
            ))}
        </tbody>
      </table>
    </div>
  ) : null;
};
