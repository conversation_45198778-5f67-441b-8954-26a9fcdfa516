import { useJs<PERSON><PERSON><PERSON><PERSON><PERSON>, GoogleMap, MarkerF } from "@react-google-maps/api";
import constant from "constant";
import { isArabic } from "helpers/helper";
import React from "react";
import { useEffect } from "react";
import { useState } from "react";
import { useRef } from "react";

type MapCardProps = {
  cardData: {
    lat: string;
    long: string;
    title: string;
    description: string;
  };
};

export const MapCard = ({ cardData }: MapCardProps) => {
  const mapRef = useRef(null);
  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: constant.GOOGLE_MAP_KEY,
  });
  const [map, setMap] = useState(null);
  const [center, setCenter] = useState({
    lat: 0,
    lng: 0,
  });
  const [zoom, setZoom] = useState(16);
  const [showMarker, setShowMarker] = useState(true);

  const containerStyle = {
    width: "200px",
    height: "200px",
  };
  useEffect(() => {
    setCenter({
      lat: parseFloat(cardData.lat),
      lng: parseFloat(cardData.long),
    });
  }, []);

  const onLoad = React.useCallback(function callback(map: any) {
    // window.google.maps.event.addListener(map, "click", function (event: any) {
    //   setCenter({
    //     lat: event.latLng.lat(),
    //     lng: event.latLng.lng(),
    //   });
    //   setShowMarker(false);
    //   setTimeout(() => setShowMarker(true), 200);
    // });
  }, []);

  const onUnmount = React.useCallback(function callback(map: any) {
    setMap(null);
  }, []);

  return (
    <div className="w-fit p-3 bg-white">
      <div
        className="!cursor-pointer"
        onClick={(e) => {
          window.open(
            `https://www.google.com/maps/search/?api=1&query=${center.lat},${center.lng}`,
            "_blank"
          );
        }}
      >
        {isLoaded ? (
          <GoogleMap
            mapContainerStyle={containerStyle}
            center={center}
            zoom={zoom}
            onLoad={onLoad}
            onUnmount={onUnmount}
            ref={mapRef}
            id="google-map"
            options={{
              disableDefaultUI: true,
              gestureHandling: "none",
              keyboardShortcuts: false,
              draggable: false,
              zoomControl: false,
            }}
          >
            {showMarker ? <MarkerF position={center} /> : null}
          </GoogleMap>
        ) : null}
      </div>
      {cardData?.title && (
        <div
          style={{
            textAlign: isArabic(cardData?.title) ? "right" : "left",
            direction: isArabic(cardData?.title) ? "rtl" : "ltr",
          }}
          className="font-bold text-gray-700 w-[200px]"
          dangerouslySetInnerHTML={{
            __html: cardData?.title.replace(new RegExp("--", "g"), ""),
          }}
        ></div>
      )}
      {cardData?.description && (
        <div
          style={{
            textAlign: isArabic(cardData?.description) ? "right" : "left",
            direction: isArabic(cardData?.description) ? "rtl" : "ltr",
          }}
          className="text-sm text-gray-500 w-[200px]"
          dangerouslySetInnerHTML={{
            __html: cardData?.description
              .replace(new RegExp("--", "g"), "")
              .replace(/\{\{.*?\}\}/g, ""),
          }}
        ></div>
      )}
    </div>
  );
};
