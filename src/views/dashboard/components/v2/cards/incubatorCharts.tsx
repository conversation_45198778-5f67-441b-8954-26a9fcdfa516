import { FC, useMemo } from "react";
import { useEffect, useState } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";

interface ChartDetails {
  x1: number | string;
  x2: number | string;
  y1: number | string;
  y2: number | string;
}
interface ChartData {
  chartType: string;
  x_title: string;
  graph_name: string;
  y_title: string;
  color_set: string;
}
export interface Props {
  graphData?: ChartData;
  detail?: Partial<ChartDetails>[];
}

export type IChartComponent = FC<Props>;

class Chart {
  chart: IChartComponent;

  constructor() {
    this.chart = {} as IChartComponent;
  }

  getChart(): IChartComponent {
    return this.chart;
  }
}

class Bar<PERSON>hart extends Chart {
  constructor() {
    super();
    this.chart = BarChartComponent;
  }
}
class LineChart extends Chart {
  constructor() {
    super();
    this.chart = LineChartComponent;
  }
}
class PieChart extends Chart {
  constructor() {
    super();
    this.chart = PieChartComponent;
  }
}

class ChartFactory {
  static createChart(chart_type: string): Chart {
    switch (chart_type) {
      case "Bar":
        return new BarChart();
      case "Line":
        return new LineChart();
      case "Pie":
        return new PieChart();
      default:
        throw new Error("Invalid Chart type: " + chart_type);
    }
  }
}

export default ChartFactory;

const BarChartComponent: IChartComponent = ({ detail, graphData }) => {
  const [xAxis, setxAxis] = useState<any[]>([]);
  const [yAxis, setyAxis] = useState<any[]>([]);

  useEffect(() => {
    if (detail) {
      setxAxis([]);
      setyAxis([]);

      detail.map((a) => {
        setxAxis((prevXAxis) => [...prevXAxis, a.x1]);
        setyAxis((prevYAxis) => [...prevYAxis, a.y1]);
      });
    }
  }, []);

  const options: Highcharts.Options = {
    chart: {
      type: "column",
    },
    title: {
      text: graphData?.graph_name || "",
    },
    xAxis: {
      categories: xAxis,
      labels: {
        enabled: true, // Set to true to show labels on the x-axis
      },
      title: {
        text: graphData?.x_title || "",
      },
    },
    yAxis: {
      title: {
        text: graphData?.y_title || "",
      },
    },
    series: [
      {
        type: "bar",
        name: "",
        data: yAxis,
        colorByPoint: true,
        colors: graphData?.color_set?.split(","),
        dataLabels: {
          enabled: true, // Set to true to show data labels on the bars
        },
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

const LineChartComponent: IChartComponent = ({ detail, graphData }) => {
  const graphName = graphData?.graph_name || "graph name";
  const xAxisLabel = graphData?.x_title || "x-Axis Label";
  const yAxisLabel = graphData?.y_title || "Y-Axis Label";
  const lineColors = graphData?.color_set?.split(",") || [
    "#ff0000",
    "#00ff00",
    "#0000ff",
  ];

  const options: Highcharts.Options = {
    title: {
      text: graphName,
    },
    xAxis: {
      title: {
        text: xAxisLabel,
      },
    },
    yAxis: {
      title: {
        text: yAxisLabel,
      },
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: true,
          format: "{y}",
        },
        marker: {
          enabled: true,
        },
      },
    },
    series: [
      {
        type: "line",
        name: "",
        data: detail?.map((a) => [a.x1, a.y1]) || [],
        color: {
          linearGradient: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 1,
          },
          stops: lineColors.map((color, index) => [
            index / (lineColors.length - 1),
            color,
          ]),
        },
        lineWidth: 2, // Set the width of the line connecting the data points
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

const PieChartComponent: IChartComponent = ({ detail, graphData }) => {
  const [dataFormat, setDataFormat] = useState<any[]>([]);

  useEffect(() => {
    if (detail) {
      setDataFormat([]);

      detail.map((a) => {
        setDataFormat((prevXAxis) => [...prevXAxis, { name: a.x1, y: a.y1 }]);
      });
    }
  }, []);

  const options = {
    chart: {
      type: "pie",
    },
    title: {
      text: graphData?.graph_name ? graphData?.graph_name : "Sample Pie Chart",
    },
    plotOptions: {
      pie: {
        dataLabels: {
          enabled: true,
          format: "<b>{point.name}</b>: {point.percentage:.1f} %",
        },
      },
    },
    series: [
      {
        name: "Data",
        data: dataFormat,
        colors: graphData?.color_set.split(",") || [
          "#ff0000",
          "#00ff00",
          "#0000ff",
          "#ffff00",
          "#ff00ff",
        ],
      },
    ],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export const ChartCard = ({ activity }) => {
  let typeChart = activity.graphData?.chartType
    ? activity.graphData?.chartType
    : "";
  const Chart = useMemo(
    () => ChartFactory.createChart(typeChart).getChart(),
    [activity]
  );
  return (
    <div className="!w-[300px]">
      <Chart graphData={activity.graphData} detail={activity.detail} />
    </div>
  );
};
