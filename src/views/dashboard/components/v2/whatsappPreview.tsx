import {
  Bot,
  Circle,
  Download,
  EllipsisVerticalIcon,
  File,
  FileText,
  Info,
  Mic2Icon,
  MicIcon,
  MouseIcon,
  Paperclip,
  PaperclipIcon,
  PhoneIcon,
  SendIcon,
  SmileIcon,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { MapCard } from "./cards/mapCard";
import Link from "next/link";
import toast from "react-hot-toast";
import PdfSVG from "common/icons/PdfSVG";
import Accordion from "common/ui/accordion";
import useBotStore from "store/bot/bot.store";
import { Avatar, AvatarFallback, AvatarImage } from "common/ui/avatar";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";

export default function WhatsAppPreview({ messages }) {
  console.log(messages);

  const bot = useBotStore((state) => state.bot);

  const renderMessageContent = (message) => {
    switch (message.type) {
      case "text":
        console.log(message, "message");
        if (message.button_payload) {
          return (
            <p
              onClick={() => {
                navigator.clipboard.writeText(message.button_payload);
                toast.success("Copied to clipboard");
              }}
              title={message.button_payload}
              className="text-sm break-words cursor-pointer"
            >
              {message.content}{" "}
              <MouseIcon size={10} className="text-gray-700 float-right" />
            </p>
          );
        } else {
          return (
            <p className="text-sm break-words">
              {message.content}
              {message.is_voice ? (
                <MicIcon size={10} className="text-gray-700 float-right" />
              ) : (
                ""
              )}
            </p>
          );
        }

      case "image":
        return (
          <div className=" w-[200px] h-[200px] rounded-lg overflow-hidden">
            <img
              src={message.content as string}
              alt="Uploaded content"
              //   fill
              className="object-cover w-full h-full"
            />
          </div>
        );

      case "location":
        const location = JSON.parse(message.content);
        return (
          <MapCard
            cardData={{
              ...location,
              lat: location.latitude,
              long: location.longitude,
            }}
          />
        );

      case "file":
        const attachment = JSON.parse(message.content);
        const type = attachment?.url.split(".").pop();
        return (
          <div className="min-w-[200px] text-sm p-1 bg-white rounded-lg">
            <Link
              key={attachment?.url || ""}
              href={attachment?.url || ""}
              target="_blank"
              className="flex justify-between w-full"
            >
              <div className="flex gap-2 items-center">
                {
                  // get attchment type from url
                  type.includes("pdf") ? (
                    <PdfSVG />
                  ) : type.includes("doc") ? (
                    <FileText className="text-blue-600" size={15} />
                  ) : (
                    <File size={15} />
                  )
                }
                {attachment?.name}
              </div>
              <Download size={15} className="text-gray-500" />
            </Link>
            <span className="pl-5 text-xs text-gray-500">
              {attachment?.caption}
            </span>
          </div>
        );

      case "list":
        const list = JSON.parse(message.content);
        return (
          <div className="bg-white rounded-lg w-[200px]">
            {list.body?.text && (
              <p className="p-1 font-medium">{list.body?.text}</p>
            )}
            <Accordion className="bg-white" title={list.action?.button}>
              <div className="divide-y">
                {list.action?.sections?.map((item, index) => (
                  <div key={index} className="p-2">
                    <p className="text-sm font-bold italic">{item.title}</p>
                    {item.rows?.map((row, index) => (
                      <div
                        onClick={() => {
                          navigator.clipboard.writeText(row.id);
                          toast.success("Copied to clipboard");
                        }}
                        title={row.id}
                        key={index}
                        className="flex items-center justify-between p-1"
                      >
                        <p className="text-sm">{row.title}</p>
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-500">
                            {row.description}
                          </span>
                          <Circle size={15} className="text-gray-500" />
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </Accordion>
          </div>
        );

      case "card":
        const card = JSON.parse(message.content);
        return (
          <>
            <div className="bg-white rounded-lg overflow-hidden max-w-xs mb-2">
              {card?.header?.type === "image" && (
                // <div className="relative h-32">
                <img
                  src={card?.header?.image?.link}
                  alt={""}
                  //   fill
                  className="object-cover"
                />
              )}
              {card?.header?.type === "video" && (
                <video
                  src={card?.header?.video?.link}
                  controls
                  //   fill
                  className="object-cover w-full h-full"
                />
              )}
              <div className="p-3">
                <p className="text-sm font-medium">{card?.body?.text}</p>
              </div>
            </div>
            {card?.action?.buttons && (
              <div className="flex flex-col gap-1">
                {card?.action?.buttons?.map((button, index) => (
                  <button
                    onClick={() => {
                      // copy id
                      navigator.clipboard.writeText(button?.reply?.id);
                      toast.success("Button payload copied successfully");
                    }}
                    title={button?.reply?.id}
                    key={index}
                    className="bg-[#008069] text-xs font-medium py-1 px-2 rounded-lg text-white"
                  >
                    {button?.reply?.title}
                  </button>
                ))}
              </div>
            )}
          </>
        );

      case "video":
        return (
          <div className="relative w-[200px] h-[200px] rounded-lg overflow-hidden">
            <video
              src={message.content}
              controls
              //   fill
              className="object-cover w-full h-full"
            />
          </div>
        );

      case "attachment":
        // "{"url":"https://infotointell.fra1.digitaloceanspaces.com/Bots/refactorDuaaTest-861/attachments/TLONLXDXELIFCVXYSLUP.png","name":"File.jpg"}"
        const attch = JSON.parse(message.content);
        const attchtype = attch?.name?.split(".").pop();
        const isImage = [
          "jpg",
          "jpeg",
          "png",
          "gif",
          "bmp",
          "svg",
          "webp",
        ].includes(attchtype);
        return (
          <div
            onClick={() => window.open(attch?.url, "_blank")}
            title={attch?.url}
            className="flex gap-2 items-center"
          >
            {isImage ? (
              <img
                src={attch?.url}
                alt={attch?.name}
                className="w-[200px] h-[200px] object-cover rounded-lg"
              />
            ) : attchtype.includes("pdf") ? (
              <PdfSVG />
            ) : attchtype.includes("doc") ? (
              <FileText className="text-blue-600" size={15} />
            ) : (
              <File size={15} />
            )}
            {!isImage ? <p className="text-xs">{attch?.name}</p> : null}
          </div>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const chatArea = document.getElementById("chat-area");
    if (chatArea) {
      chatArea.scrollTop = chatArea.scrollHeight;
    }
  }, [messages]);

  return (
    <div className="relative flex flex-col h-[600px] w-full max-w-md mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-[#008069] sticky p-2 flex items-center justify-between text-white">
        <div className="flex items-center gap-3">
          <Avatar className="w-6 h-6">
            <AvatarImage src={bot?.icon} />
            <AvatarFallback>
              <Bot />
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-semibold">
              WhatsApp Bot - {messages?.[0]?.name || "user"}
            </h2>
            <p className="text-xs opacity-75">Online</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <PhoneIcon className="w-5 h-5" />
          <EllipsisVerticalIcon className="w-5 h-5" />
        </div>
      </div>

      {/* Chat Messages Area */}
      <div
        id="chat-area"
        className="flex-1 bg-[#efeae2] text-black p-4 overflow-y-auto"
      >
        {messages?.map((message) => (
          <React.Fragment key={message.user_interaction_id}>
            <div
              key={message.user_interaction_id}
              className={`flex justify-end mb-4`}
            >
              <div
                className={`max-w-[70%] rounded-lg p-2 bg-[#dcf8c6] ml-auto`}
              >
                {renderMessageContent({
                  type: message.type,
                  content: message.message,
                  button_payload: message.button_payload,
                  is_voice: message.is_voice,
                })}
                <p className="text-xs text-gray-500 mt-1 text-right">
                  {new Date(message.createdAt).toLocaleDateString("en-GB")}{" "}
                  {new Date(message.createdAt).toLocaleTimeString("en-GB", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
            {message.bot_designer_tr_cube?.bot_responses?.map((botResponse) => (
              <div
                key={botResponse.bot_response_id}
                className={`flex justify-start gap-1 mb-4`}
              >
                <div
                  className={`min-w-[200px] max-w-[70%] rounded-lg p-2 bg-white break-words`}
                >
                  {renderMessageContent({
                    type: botResponse.response_type,
                    content: botResponse.response,
                  })}
                  <div className="flex items-center gap-2 mt-1">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <Info className=" w-4 h-4 text-gray-500" />
                      </HoverCardTrigger>
                      <HoverCardContent className="bg-gray-200">
                        <span className="font-bold">
                          Sources for this response:{" "}
                          {botResponse.sources?.length}
                        </span>
                        {botResponse.sources?.map((source, i) => (
                          <div
                            className="text-sm mb-2 border-b border-gray-200"
                            key={source.bot_response_source_id}
                          >
                            {i + 1}- {source.source_type} <br />
                            {source?.source_title
                              ? "Title: " + source.source_title
                              : ""}
                            {source?.source_id ? (
                              <div>
                                Id for {source.source_entity}:{" "}
                                {source.source_id}
                              </div>
                            ) : (
                              ""
                            )}
                          </div>
                        ))}
                      </HoverCardContent>
                    </HoverCard>
                    <span className="text-xs text-gray-400">
                      {new Date(message.createdAt).toLocaleTimeString("en-GB", {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </React.Fragment>
        ))}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 p-2 border-t">
        <div className="flex items-center gap-2">
          <button className="p-2 text-[#54656f] hover:text-[#008069]">
            <SmileIcon className="w-6 h-6" />
          </button>

          <button className="p-2 text-[#54656f] hover:text-[#008069]">
            <PaperclipIcon className="w-6 h-6" />
          </button>

          <div className="flex-1 relative">
            <input
              type="text"
              defaultValue={""}
              placeholder="Type a message"
              className="w-full pl-4 pr-12 py-2 rounded-full border focus:outline-none focus:border-[#008069] focus:ring-1 focus:ring-[#008069]"
            />

            <button className="absolute right-2 top-1/2 -translate-y-1/2 text-[#54656f] hover:text-[#008069]">
              <SendIcon className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
