import React, { useEffect, useRef, useState } from "react";

interface SuspenseWrapperProps {
  children: React.ReactNode;
  fetchData: () => Promise<void>;
  dependency: unknown;
  LoadingComponent?: () => JSX.Element;
  className?: string;
}

export const SuspenseWrapper: React.FC<SuspenseWrapperProps> = ({
  children,
  fetchData,
  dependency,
  LoadingComponent,
  className
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [hasFetched, setHasFetched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    if (dependency !== null) {
      setHasFetched(false);
      setIsLoading(false);
      setIsFetching(false);
    }
  }, [dependency]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasFetched && !isFetching) {
          setIsLoading(true);
          setIsFetching(true);
          fetchData()
            .then(() => {
              setHasFetched(true);
              setIsLoading(false);
              setIsFetching(false);
            })
            .catch(() => {
              setIsLoading(false);
            });
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
      observer.disconnect();
    };
  }, [fetchData, hasFetched, isFetching]);

  return (
    <div ref={ref} className={className}>
      {isLoading ? (
        LoadingComponent ? (
          <LoadingComponent />
        ) : (
          <div>Loading...</div>
        )
      ) : (
        children
      )}
    </div>
  );
};
