import { getTrNotAnsweredInfo } from "apis/dashboardTransactions.api";
import { MainTable } from "common/components/tables/main.table";
import { EyeIcon, X } from "lucide-react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";

const NotAnsweredInfoModal = ({ message, onClose, setSelectedChat }) => {
  const bot_id = useBotStore((state) => state.bot.bot_id);

  const [details, setDetails] = useState([]);

  useEffect(() => {
    getTrNotAnsweredInfo({
      bot_id,
      message,
    }).then((res) => {
      setDetails(res);
    });
  }, []);
  return (
    <div className="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-80">
      <div className="relative flex h-full min-h-[50vh] max-h-[85vh] w-full max-w-4xl flex-col rounded-xl p-6 shadow-xl bg-themeSecondary text-gray-200">
        <X onClick={onClose} className="absolute top-4 right-4" />
        <p>
            Not Answered Details For the message: &quot;{message}&quot;
        </p>
        <div className="p-5">
          <MainTable
            actions={[
              {
                label: "View",
                onClick: (data) => {
                  setSelectedChat(data);
                },
                icon: EyeIcon,
              },
            ]}
            data={details}
            columns={[
              {
                key: "conversation_id",
                name: "User",
              },
              {
                key: "channel",
                name: "Channel",
              },
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default NotAnsweredInfoModal;
