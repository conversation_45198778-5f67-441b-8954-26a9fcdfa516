import {
  Bot,
  Circle,
  Download,
  EllipsisVerticalIcon,
  File,
  FileText,
  Info,
  Mic2Icon,
  MicIcon,
  MouseIcon,
  PlusIcon,
  VideoIcon,
  SendIcon,
  SmileIcon,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { MapCard } from "./cards/mapCard";
import Link from "next/link";
import toast from "react-hot-toast";
import PdfSVG from "common/icons/PdfSVG";
import Accordion from "common/ui/accordion";
import useBotStore from "store/bot/bot.store";
import { Avatar, AvatarFallback, AvatarImage } from "common/ui/avatar";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";

export default function FacebookPreview({ messages }) {
  const bot = useBotStore((state) => state.bot);

  console.log(messages, "message");
  const renderMessageContent = (message) => {
    switch (message.type) {
      case "text":
        if (message.button_payload) {
          return (
            <p
              onClick={() => {
                navigator.clipboard.writeText(message.button_payload);
                toast.success("Copied to clipboard");
              }}
              title={message.button_payload}
              className="text-sm break-words cursor-pointer"
            >
              {message.content}{" "}
              <MouseIcon size={10} className="text-gray-700 float-right" />
            </p>
          );
        } else {
          return (
            <p className="text-sm break-words">
              {message.content}
              {message.is_voice ? (
                <MicIcon size={10} className="text-gray-700 float-right" />
              ) : (
                ""
              )}
            </p>
          );
        }

      case "image":
        return (
          <div className=" w-[200px] h-[200px] rounded-lg overflow-hidden">
            <img
              src={message.content as string}
              alt="Uploaded content"
              //   fill
              className="object-cover w-full h-full"
            />
          </div>
        );

      case "file":
        const attachment = message.content;
        const type = attachment?.split(".")?.pop();
        const name = attachment?.split("/")?.pop();
        return (
          <div className="min-w-[200px] text-sm p-1 bg-white rounded-lg">
            <Link
              key={attachment || ""}
              href={attachment || ""}
              target="_blank"
              className="flex justify-between w-full"
            >
              <div className="flex gap-2 items-center">
                {
                  // get attchment type from url
                  type.includes("pdf") ? (
                    <PdfSVG />
                  ) : type.includes("doc") ? (
                    <FileText className="text-blue-600" size={15} />
                  ) : (
                    <File size={15} />
                  )
                }
                {name}
              </div>
              <Download size={15} className="text-gray-500" />
            </Link>
            {/* <span className="pl-5 text-xs text-gray-500">
              {attachment?.caption}
            </span> */}
          </div>
        );

      // NOTE: this is from user
      case "attachment":
        const attachments = JSON.parse(message.content);
        return (
          <div className="flex flex-col gap-2">
            {attachments.map((item, index) => (
              <div
                onClick={() => window.open(item.payload.url, "_blank")}
                title={item.payload.url}
                key={index}
              >
                {item.type === "image" && (
                  <img
                    src={item.payload.url}
                    alt="Uploaded content"
                    className="object-cover w-full h-full"
                  />
                )}
                {item.type === "video" && (
                  <video
                    src={item.payload.url}
                    controls
                    className="object-cover w-full h-full"
                  />
                )}
              </div>
            ))}
          </div>
        );

      //   case "list":
      //     const list = JSON.parse(message.content);
      //     return (
      //       <div className="bg-white rounded-lg w-[200px]">
      //         {list.body?.text && (
      //           <p className="p-1 font-medium">{list.body?.text}</p>
      //         )}
      //         <Accordion className="bg-white" title={list.action?.button}>
      //           <div className="divide-y">
      //             {list.action?.sections?.map((item, index) => (
      //               <div key={index} className="p-2">
      //                 <p className="text-sm font-bold italic">{item.title}</p>
      //                 {item.rows?.map((row, index) => (
      //                   <div
      //                     onClick={() => {
      //                       navigator.clipboard.writeText(row.id);
      //                       toast.success("Copied to clipboard");
      //                     }}
      //                     title={row.id}
      //                     key={index}
      //                     className="flex items-center justify-between p-1"
      //                   >
      //                     <p className="text-sm">{row.title}</p>
      //                     <div className="flex items-center gap-2">
      //                       <span className="text-xs text-gray-500">
      //                         {row.description}
      //                       </span>
      //                       <Circle size={15} className="text-gray-500" />
      //                     </div>
      //                   </div>
      //                 ))}
      //               </div>
      //             ))}
      //           </div>
      //         </Accordion>
      //       </div>
      //     );

      case "template":
        //"{"template_type":"generic","elements":[{"title":"Location","image_url":"https://maps.googleapis.com/maps/api/staticmap?center=31.971651186709,35.833978927754&zoom=13&size=600x300&maptype=roadmap&markers=color:red%7Clabel:C%7C31.971651186709,35.833978927754&key=AIzaSyBl8jqw_9sE9qQv0SHmfhGHqnTGtaGA_u4","buttons":[{"type":"web_url","title":"Open in Google Maps","url":"https://www.google.com/maps/search/?api=1&query=31.971651186709,35.833978927754"}]}]}"
        const card = JSON.parse(message.content);
        return <Carousel elements={card.elements} />;

      // return (
      //   <>
      //     <div className="bg-white rounded-lg overflow-hidden max-w-xs mb-2">
      //       {card.elements?.[0]?.image_url && (
      //         <img
      //           src={card.elements?.[0]?.image_url}
      //           alt="Uploaded content"
      //           //   fill
      //           className="object-cover w-full h-40"
      //         />
      //       )}

      //       <div className="p-3">
      //         <p className="text-sm font-medium">
      //           {card.elements?.[0]?.title}
      //         </p>
      //       </div>
      //     </div>
      //     {card.elements?.[0]?.buttons?.length > 0 && (
      //       <div className="flex flex-col gap-1">
      //         {card.elements?.[0]?.buttons?.map((button, index) => (
      //           <button
      //             onClick={() => {
      //               // copy id
      //               navigator.clipboard.writeText(button?.url);
      //               toast.success("Button payload copied successfully");
      //             }}
      //             title={button?.url}
      //             key={index}
      //             className="bg-[#0084FF] text-xs font-medium py-1 px-2 rounded-lg text-white"
      //           >
      //             {button?.title}
      //           </button>
      //         ))}
      //       </div>
      //     )}
      //   </>
      // );

      case "video":
        return (
          <div className="relative w-[200px] h-[200px] rounded-lg overflow-hidden">
            <video
              src={message.content}
              controls
              //   fill
              className="object-cover w-full h-full"
            />
          </div>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    const chatArea = document.getElementById("chat-area");
    if (chatArea) {
      chatArea.scrollTop = chatArea.scrollHeight;
    }
  }, [messages]);

  return (
    <div className="relative flex flex-col h-[600px] w-full max-w-md mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
      {/* Header - Messenger style */}
      <div className="bg-[#0084FF] sticky p-3 flex items-center justify-between text-white">
        <div className="flex items-center gap-3">
          <Avatar className="w-8 h-8 border-2 border-white">
            <AvatarImage src={bot?.icon} />
            <AvatarFallback className="bg-gray-200">
              <Bot className="text-gray-600" />
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-semibold">{messages?.[0]?.name || "User"}</h2>
            <p className="text-xs opacity-90">Active now</p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <VideoIcon className="w-6 h-6" />
          <EllipsisVerticalIcon className="w-6 h-6" />
        </div>
      </div>

      {/* Chat Messages Area - Messenger style */}
      <div
        id="chat-area"
        className="flex-1 bg-[#f0f2f5] text-black p-4 overflow-y-auto"
      >
        {messages?.map((message) => (
          <React.Fragment key={message.user_interaction_id}>
            {/* User Message */}
            <div className="flex justify-end mb-4">
              <div className="max-w-[70%] rounded-xl p-3 bg-[#0084FF] text-white ml-auto">
                {renderMessageContent({
                  type: message.type,
                  content: message.message,
                  button_payload: message.button_payload,
                  is_voice: message.is_voice,
                })}
                <p className="text-xs text-white/70 mt-1 text-right">
                  {new Date(message.createdAt).toLocaleDateString("en-GB")}{" "}
                  {new Date(message.createdAt).toLocaleTimeString("en-GB", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>

            {/* Bot Responses */}
            {message.bot_designer_tr_cube?.bot_responses?.map((botResponse) => (
              <div
                key={botResponse.bot_response_id}
                className="flex justify-start mb-4"
              >
                <div className="min-w-[200px] max-w-[70%] rounded-xl p-3 bg-white shadow-sm">
                  {renderMessageContent({
                    type: botResponse.response_type,
                    content: botResponse.response,
                  })}
                  <div className="flex items-center gap-2 mt-1">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <Info className="w-4 h-4 text-gray-400 cursor-pointer" />
                      </HoverCardTrigger>
                      <HoverCardContent className="bg-gray-800 text-white border-none">
                        <span className="font-bold text-sm">Sources:</span>
                        {botResponse.sources?.map((source, i) => (
                          <div
                            className="text-sm mb-2 border-b border-gray-700"
                            key={source.bot_response_source_id}
                          >
                            <p className="font-medium">
                              {i + 1}. {source.source_type}
                            </p>
                            {source?.source_title && (
                              <p className="text-gray-400">
                                {source.source_title}
                              </p>
                            )}
                            {source?.source_id && (
                              <p className="text-xs text-gray-400">
                                ID: {source.source_id}
                              </p>
                            )}
                          </div>
                        ))}
                      </HoverCardContent>
                    </HoverCard>
                    <span className="text-xs text-gray-400">
                      {new Date(message.createdAt).toLocaleTimeString("en-GB", {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </React.Fragment>
        ))}
      </div>

      {/* Footer - Messenger style */}
      <div className="bg-white p-3 border-t">
        <div className="flex items-center gap-2">
          <button className="p-2 text-gray-500 hover:text-[#0084FF]">
            <PlusIcon className="w-6 h-6" />
          </button>

          <div className="flex-1 relative">
            <input
              type="text"
              defaultValue={""}
              placeholder="Type a message..."
              className="w-full pl-4 pr-24 py-2 rounded-full bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#0084FF]"
            />

            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
              <button className="text-gray-400 hover:text-[#0084FF]">
                <SmileIcon className="w-6 h-6" />
              </button>
              <button className="text-gray-400 hover:text-[#0084FF]">
                <Mic2Icon className="w-6 h-6" />
              </button>
              <button className="bg-[#0084FF] text-white p-1 rounded-full">
                <SendIcon className="w-6 h-6 p-1" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const Carousel = ({ elements }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  return (
    <div className="relative w-[200px] overflow-hidden">
      <div className="flex">
        {elements.map((element, index) => (
          <div
            key={index}
            className="flex-shrink-0 w-[200px] transition-transform duration-300"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            <div className="bg-white rounded-lg overflow-hidden max-w-xs mb-2">
              {element.image_url && (
                <img
                  src={element.image_url}
                  alt="Uploaded content"
                  className="object-cover w-full h-40"
                />
              )}
              <div className="p-3">
                <p className="text-sm font-medium">{element.title}</p>
              </div>
            </div>
            {element.buttons?.length > 0 && (
              <div className="flex flex-col gap-1">
                {element.buttons.map((button, idx) => (
                  <button
                    key={idx}
                    onClick={() => {
                      navigator.clipboard.writeText(button?.payload);
                      toast.success("Button payload copied successfully");
                    }}
                    title={button?.payload}
                    className="bg-[#0084FF] text-xs font-medium py-1 px-2 rounded-lg text-white"
                  >
                    {button.title}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      {elements.length > 1 && (
        <>
          {currentIndex > 0 && (
            <button
              onClick={() => setCurrentIndex((prev) => prev - 1)}
              className="absolute left-1 top-1/2 -translate-y-1/2 bg-gray-200/80 rounded-full p-1"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="black" strokeWidth="2" />
              </svg>
            </button>
          )}
          {currentIndex < elements.length - 1 && (
            <button
              onClick={() => setCurrentIndex((prev) => prev + 1)}
              className="absolute right-1 top-1/2 -translate-y-1/2 bg-gray-200/80 rounded-full p-1"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M9 18L15 12L9 6" stroke="black" strokeWidth="2" />
              </svg>
            </button>
          )}
        </>
      )}
    </div>
  );
};
