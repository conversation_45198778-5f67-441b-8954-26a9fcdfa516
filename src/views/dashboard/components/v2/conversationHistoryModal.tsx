import { getConversationDetails } from "apis/chatbotReport.api";
import { useState, useEffect } from "react";
import useBotStore from "store/bot/bot.store";
import { CSVLink } from "react-csv";
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@radix-ui/react-tabs";
import { Button } from "common/ui/button";
import { getTrConversationHistory } from "apis/dashboardTransactions.api";
import { CardPreview } from "views/cardsAndSuggestions/components/cardPreview";
import WhatsappPreview from "./whatsappPreview";
import { X } from "lucide-react";
import FacebookPreview from "./facebookPreview";
import WebPreview from "./webPreview";

const ConversationHistoryModal = ({ chat, onClose }) => {
  const [chatDetails, setChatDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all");
  const bot_id = useBotStore((state) => state.bot.bot_id);
  //   TODO:
  // - different channels
  // - pagination????
  // - livechat????

  useEffect(() => {
    if (chat) {
      setLoading(true);
      getTrConversationHistory({
        bot_id,
        conversation_id: chat.conversation_id,
      })
        .then((details) => {
          setChatDetails(details);
        })
        .catch((error) => {
          console.error("Failed to fetch chat details:", error);
          setChatDetails([]);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [chat]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
      <div className="relative flex h-full min-h-[70vh] max-h-[95vh] w-full max-w-4xl flex-col rounded-xl p-6 shadow-xl bg-themeSecondary text-gray-200">
        <X onClick={onClose} className="absolute top-4 right-4" />
        <div className="flex-1 overflow-y-auto">
          {chat.channel === "web" ? (
            <WebHistoryViewer chatDetails={chatDetails} />
          ) : chat.channel === "whatsapp" ? (
            <WhatsappHistoryViewer chatDetails={chatDetails} />
          ) : (
            <FacebookHistoryViewer chatDetails={chatDetails} />
          )}
        </div>
      </div>
    </div>
  );
};

export default ConversationHistoryModal;

const WebHistoryViewer = ({ chatDetails }) => {
  console.log("chatDetails", chatDetails);
  return (
    <div>
      <WebPreview messages={chatDetails} />
    </div>
  );
};

const WhatsappHistoryViewer = ({ chatDetails }) => {
  return (
    <div>
      <WhatsappPreview messages={chatDetails} />
    </div>
  );
};

const FacebookHistoryViewer = ({ chatDetails }) => {
  return (
    <div>
      <FacebookPreview messages={chatDetails} />
    </div>
  );
};
