import { memo, useEffect, useRef } from "react";
import { Info, type LucideIcon } from "lucide-react";
import { AreaChart } from "../charts";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@radix-ui/react-hover-card";


interface Props {
  title?: string;
  subtitle?: string;
  icon?: LucideIcon | React.FC<any>;
  data?: any;
  xLabels?: Array<string>;
  hover?:string
}

export const StatsCard: React.FC<Props> = ({
  title,
  subtitle,
  icon: Icon,
  data,
  xLabels,
  hover,
}) => {
  // const cardRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   const observer = new IntersectionObserver(
  //     ([entry]) => {
  //       if (entry.isIntersecting) {
  //         console.log("in view",title);
  //       }
  //     },
  //     { threshold: 0.1 } // Adjust threshold as needed
  //   );

  //   if (cardRef.current) {
  //     observer.observe(cardRef.current);
  //   }

  //   return () => {
  //     if (cardRef.current) {
  //       observer.unobserve(cardRef.current);
  //     }
  //   };
  // }, []);

  return (
    <div
      // ref={cardRef}
      className="h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden relative"
    >
      <div className="px-5 pb-2 pt-0 flex justify-between items-start">
        <div>
          <div>{title}</div>
          <div className="font-bold text-xl">{subtitle}</div>
        </div>
        {Icon && <Icon size={30} className="mt-1" />}
      </div>
      
      <div className="absolute bottom-4 right-2 pb-2">
        {hover && <HoverCard>
          <HoverCardTrigger>
            <Info size={20} className="cursor-pointer text-yellow-500"/>
          </HoverCardTrigger>
          <HoverCardContent className="w-fit bg-black ring-2 ring-gray-500/50 text-sm z-[999]">
            <div className="p-3">
              {hover}
            </div>
          </HoverCardContent>
        </HoverCard>}
      </div>
      {data && xLabels && <div className="z-10 relative"><AreaChart data={data} xLabels={xLabels} /></div>}
    </div>
  );
};

export default memo(StatsCard);
