import { FC, memo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

interface Dialog {
  dialog_name: string;
  dialog_id: number;
}

interface DialogSelectProps {
  onChangeDialog: (dialog: Dialog) => void;
  dialog: Dialog | null;
  dialogs: Dialog[];
}

const DialogSelect: FC<DialogSelectProps> = ({ onChangeDialog, dialog, dialogs }) => {
  return (
    <Select
      onValueChange={(dialogId) => {
        const selected = dialogs.find(d => d.dialog_id.toString() === dialogId);
        if (selected) {
          onChangeDialog(selected);
        }
      }}
      value={dialog?.dialog_id.toString() ?? ""}
    >
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select a dialog">
          {dialog?.dialog_name}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {dialogs?.map((dialog) => (
          <SelectItem 
            value={dialog.dialog_id.toString()} 
            key={dialog.dialog_id}
          >
            {dialog.dialog_name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default memo(DialogSelect);
