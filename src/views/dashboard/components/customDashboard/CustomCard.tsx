
import React from "react";
import { TCustomDashboard } from "./custom-dashboard";
import * as Icons from "lucide-react"

type TCustomCard = {
  cards: TCustomDashboard[];
};

const CustomCard: React.FC<TCustomCard> = ({ cards }) => {

  return (
    <>
      {cards?.map((card,i) => {
        console.log(card.icon)
        const keys =
          card.queryResults.length > 0 ? Object.keys(card.queryResults[0]) : [];
       const Icon = Icons[card?.icon || "Globe2Icon" ] 
          return (
            <div key={card.title+i} className="h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden">
            <div className="px-5 pb-2 pt-0 flex justify-between items-start">
              <div>
                <div>{card.title}</div>
                <div className="font-bold text-xl">{card.queryResults[0][keys[0]]}</div>
              </div>
              {Icon ? <Icon color="white"  size={30}/> : null } 
            </div>
          </div>
        );
      })}
    </>
  );
};

export default CustomCard;
