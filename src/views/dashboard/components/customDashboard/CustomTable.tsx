import React, { useEffect, useState } from "react";
import { TCustomDashboard } from "./custom-dashboard";
import { MainTable } from "common/components/tables/main.table";
import { Card } from "common/ui/card";
import { Input } from "common/ui/inputs/input";
import {
  Select,
  SelectItem,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";

type TCustomTable = {
  table: TCustomDashboard;
  keys: string[];
};
const CustomTable: React.FC<TCustomTable> = ({ table, keys }) => {
  const [filterdData, setFilterdData] = useState<any[]>([]);
  const [keySearch, setKeySearch] = useState("");
  const [keyFilter, setKeyFilter] = useState("");

  useEffect(() => {
    setFilterdData(table?.queryResults || []);
  }, [table?.queryResults]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    const value = e.target.value || "";
    const data = table?.queryResults;
    const filteredData = data.filter((item) => {
      return keys.some((key) => {
        return item[key].toString().toLowerCase().includes(value.toLowerCase());
      });
    });
    setFilterdData(filteredData);
    setKeySearch(value);
  };
  const handleFilter = (value: string) => {
    const data = table?.queryResults || [];

    if (!data.length) return;

    let sortedData;

    if (value === "createdAt") {
      sortedData = data
        .slice()
        .sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
    } else if (value === "a-z") {
      const firstKey = Object.keys(data[0])[0];
      sortedData = data.slice().sort((a, b) => {
        if (a[firstKey] < b[firstKey]) return -1;
        if (a[firstKey] > b[firstKey]) return 1;
        return 0;
      });
    } else {
      sortedData = data;
    }
    setKeyFilter(value);
    setFilterdData(sortedData);
  };

  return (
    <Card className="rounded-md">
      <p>{table.title}</p>
      <p>{table.description}</p>
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between w-full">
          <div className="w-60">
            <Input
              name="search"
              placeholder="Search..."
              value={keySearch}
              onChange={handleSearch}
            />
          </div>

          <>
            <Select
              onValueChange={(value) => {
                handleFilter(value);
              }}
              value={keyFilter}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All</SelectItem>
                <SelectItem value="createAt">Date</SelectItem>
                <SelectItem value="a-z">A-Z</SelectItem>
              </SelectContent>
            </Select>
          </>
        </div>
        <div className=" max-h-[500px] overflow-y-auto">
          <MainTable
            key={table.title}
            loading={false}
            data={filterdData}
            columns={keys.map((key) => ({ key, name: key }))}
            itemsPerPage={10}
          />
        </div>
      </div>
    </Card>
  );
};

export default CustomTable;
