import { MainTable } from "common/components/tables/main.table";
import constant from "constant";
import { createGetJSON } from "helpers/custom";
import React, { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { IDialog } from "store/dialog/dialog.types";
import StatsCard from "../stats.card";
import { MessageCircle } from "lucide-react";
import LineChart from "../../charts/line.chart";
import { getCustomDashboard } from "apis/custom-dashboard.api";



export type TCustomDashboard = {
  custom_dashboard_id: number;
  title: string;
  icon: string;
  description: string;
  query: string;
  widget_type: string;
  bot_id: number;
  queryResults: IDialog[] | any;
  x_label:string | null;
  y_label:string | null;
};
export type TFilterdCustomDashboard = {
  tables?:TCustomDashboard[];
  lineCharts?:TCustomDashboard[];
  cards?: TCustomDashboard[];
};

const CustomDashboard: React.FC = () => {
  const { bot } = useBotStore();
  const [customDashboards, setCustomDashboards] = useState<TCustomDashboard[]>(
    []
  );


  useEffect(() => {
    const fetchData = async () => {
      if (bot.bot_id) {
        const data = await getCustomDashboard(bot.bot_id);
        setCustomDashboards(data);
      }
    };

    fetchData();
  }, [bot.bot_id]);

  useEffect(() => {
    console.log(customDashboards, "customDashboards");
  }, [customDashboards]);

  return (
    <div className="flex flex-col w-full gap-5 max-w-full overflow-auto ">
      {customDashboards?.map((customDashboard) => {
        const keys =
          customDashboard.queryResults.length > 0
            ? Object.keys(customDashboard.queryResults[0])
            : [];
        const chart_data = customDashboard?.queryResults[0]?.chart_data || "{}";

        const series = [
          {
            name: customDashboard.title,
            type: customDashboard.widget_type === "line-chart" ? "line" : "",
            data: [],
          },
        ];
        let xLabels = [""];
        if (chart_data) {
          const parsedData = JSON.parse(chart_data);
          console.log(parsedData, "parsedData");
          if (parsedData?.length > 0) {
            const convertedData = JSON.parse(chart_data)?.map((point:{x:string,y:string},i:number) => ({
              x: point.x,
              y: Number(point.y) || i,
            }));
            series[0].data = convertedData;
            xLabels = convertedData?.map((point) => point.x);
          }
        }
        switch (customDashboard.widget_type) {
          case "table":
            return (
              <div key={customDashboard.custom_dashboard_id}>
                <p className="font-bold text-xl">{customDashboard.title}</p>
                <p>{customDashboard.description}</p>
                <MainTable
                  key={customDashboard.title}
                  loading={false}
                  data={customDashboard?.queryResults}
                  columns={keys.map((key) => ({ key, name: key }))}
                  itemsPerPage={10}
                />
              </div>
            );
          case "card":
            return (
              <div className="w-1/3" key={customDashboard.custom_dashboard_id}>
                <StatsCard
                  title={customDashboard.title}
                  subtitle={
                    customDashboard.description +
                    ` ${customDashboard.queryResults[0][keys[0]]}`
                  }
                  icon={MessageCircle}
                />
              </div>
            );
          case "line-chart":
            return (
              <div className="w-1/3" key={customDashboard.custom_dashboard_id}>
                {series[0].data.length > 0 ? (
                  <>
                  <p className="text-lg">{customDashboard.title}</p>
                  <p>{customDashboard.description}</p>
                  <LineChart data={series} xLabels={xLabels} />
                  </>
                ) : null}
              </div>
            );
          default:
            return null;
        }
      })}
    </div>
  );
};

export default CustomDashboard;
