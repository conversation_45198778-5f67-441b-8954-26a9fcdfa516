import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "common/ui/button";
import { CSVLink } from "react-csv";
import { CustomSearch } from "common/ui/inputs/search";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { DateRange } from "react-day-picker";
import Pagination from "common/ui/pagination";
import transformDate from "helpers/transformDate";
import {
  getConversationIds,
  getConversationDetails,
} from "apis/chatbotReport.api";
import useBotStore from "store/bot/bot.store";
import searchedArray from "helpers/search";
import ChatModal from "./chatModal";
import { SuspenseWrapper } from "./v2/suspenseWrapper";
import { OneCardLoading } from "../onecardLoading";
import {
  findTRConversation,
  getTrConversations,
} from "apis/dashboardTransactions.api";
import ConversationHistoryModal from "./v2/conversationHistoryModal";
import { RefreshCwIcon } from "lucide-react";

const ConversationHistory = () => {
  const [keySearch, setKeySearch] = useState("");
  const [searched, setSearched] = useState(false);

  const [selectedChat, setSelectedChat] = useState(null);
  const [showChatWindow, setShowChatWindow] = useState(false);
  const itemsPerPage = 5;
  const [data, setData] = useState([]);
  const [paginationData, setPaginationData] = useState(null);
  const [cachedPages, setCachedPages] = useState({});
  const bot_id = useBotStore((state) => state.bot.bot_id);

  const fetchConversations = async (page: number = 1) => {
    const convResp = await getTrConversations(
      {
        bot_id: bot_id,
      },
      page
    );

    if (convResp.data?.length) {
      setData(convResp.data);
      if (page === 1) {
        setCachedPages({ [page]: convResp });
      } else {
        setCachedPages({ ...cachedPages, [page]: convResp });
      }
    }

    setPaginationData(convResp.pagination);
  };

  const handlePageChange = (page) => {
    if (cachedPages?.[page]) {
      setData(cachedPages[page]?.data);
      setPaginationData(cachedPages[page]?.pagination);

      return;
    }
    fetchConversations(page);
  };

  const handleViewChat = (chat) => {
    setSelectedChat(chat);
    setShowChatWindow(true);
  };

  // const handleSearch = (value) => {
  //   findTRConversation({
  //     bot_id,
  //     searchQuery: value,
  //   });
  // };

  useEffect(() => {
    if (keySearch === "" && searched) {
      fetchConversations();
    } else if (keySearch !== "") {
      setSearched(true);

      findTRConversation({
        bot_id,
        searchQuery: keySearch,
      }).then((res) => {
        setData(res);
        setPaginationData(null);
      });
    }
  }, [keySearch, searched]);

  return (
    <SuspenseWrapper
      dependency={null}
      LoadingComponent={OneCardLoading}
      fetchData={fetchConversations}
    >
      <div className="p-4">
        <div className="flex items-center gap-5 pb-4">
          <CustomSearch
            placeholder="Search chat history..."
            onChange={(value) => {
              setKeySearch(value);
            }}
          />
          <RefreshCwIcon
            size={20}
            className="hover:text-primary cursor-pointer mr-5"
            onClick={() => fetchConversations()}
          />
        </div>

        <div className="overflow-x-auto">
          <table className="w-full overflow-hidden">
            <thead className="sticky top-0 z-30 bg-themeSecondary ">
              <tr>
                <th className="px-4 py-5 font-medium text-left text-white">
                  Conversation ID
                </th>
                <th className="px-4 py-5 font-medium text-left text-white">
                  Name
                </th>
                <th className="px-4 py-5 font-medium text-left text-white">
                  Channel
                </th>
                <th className="px-4 py-5 font-medium text-left text-white">
                  Country
                </th>
                <th className="px-4 py-5 font-medium text-left text-white">
                  Last Message At
                </th>
                <th className="px-4 py-5 font-medium text-left text-white">
                  Message History
                </th>
              </tr>
            </thead>

            <tbody className="divide-y divide-white/25 py-2">
              {data.length > 0 ? (
                data.map((chat, index) => {
                  const chatDate = new Date(chat.date);
                  const formattedTime = chatDate.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  });
                  const conversationId =
                    chat.user_contact_id ||
                    (chat.channel === "facebook" || chat.channel == "instagram"
                      ? chat.conversation_id.split("_")[1]
                      : chat.channel === "whatsapp"
                      ? chat.conversation_id.split("_")[0]
                      : chat.conversation_id);

                  return (
                    <tr key={index} className="hover:backdrop-brightness-125">
                      <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                        {conversationId || "N/A"}
                      </td>
                      <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                        {chat.name || "N/A"}
                      </td>
                      <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                        {chat.channel || "N/A"}
                      </td>
                      <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                        {chat.country || "N/A"}
                      </td>
                      <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                        {transformDate(chat.last_interaction) || "N/A"}
                      </td>
                      <td className="max-w-[200px] px-4 py-2 whitespace-break-spaces break-words text-gray-200">
                        <Button onClick={() => handleViewChat(chat)}>
                          View
                        </Button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={6} className="text-center py-4 bg-white/25">
                    No Data was Found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {data.length > 0 && (
          <Pagination
            currentPage={paginationData?.currentPage || 1}
            totalCount={paginationData?.totalItems}
            pageSize={paginationData?.pageSize || 10}
            onPageChange={handlePageChange}
          />
        )}
        {showChatWindow && (
          <ConversationHistoryModal
            chat={selectedChat}
            onClose={() => setShowChatWindow(false)}
          />
        )}
      </div>
    </SuspenseWrapper>
  );
};

export default ConversationHistory;
