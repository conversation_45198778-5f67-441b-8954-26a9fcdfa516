import { memo } from "react";
import type { LucideIcon } from "lucide-react";

export type rateType = {
  average_rating?: number;
  graph?: string;
  rating?: number;
  user_count?: number;
};

export type UpdatedRatingType = {
  RatingData: rateType[];
  totalUsers: number;
  totalRating: number;
};

interface Props {
  title?: string;
  icon?: LucideIcon;
  data?: UpdatedRatingType;
}
const FeedbackCard: React.FC<Props> = ({ title, icon: Icon, data }) => {
  return (
    <div className="h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden">
      <div className="px-5 pb-2 pt-0 flex justify-between items-start">
        {data?.totalUsers !== 0 ? (
          <>
            <div className="w-96">
              <div>
                {title}{" "}
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Total Rating {data?.totalRating}
                </span>
              </div>

              <div className="flex flex-col w-full">
                {data?.RatingData?.map((rate) => {
                  return (
                    <div
                      key={rate.rating}
                      className="flex items-center h-1 my-1.5 "
                    >
                      <p className="text-sm font-medium ">{rate.rating} star</p>
                      <div className="w-2/4 h-1 mx-4 bg-gray-200 rounded dark:bg-gray-700">
                        <div
                          className="h-1 bg-yellow-300 rounded  "
                          style={{
                            width: `${
                              (100 * rate.user_count) / data?.totalUsers
                            }% `,
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {`${rate.user_count} vote`}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        ) : (
          <>
            <div>
              <div>{title}</div>
              <div className="font-bold text-xl">No Feedback yet</div>
            </div>
          </>
        )}

        {Icon && <Icon size={30} className="mt-1" />}
      </div>
    </div>
  );
};

export default memo(FeedbackCard);
