import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "common/ui/button";
import { CSVLink } from "react-csv";
import Pagination from "common/ui/pagination";
import transformDate from "helpers/transformDate";
import useBotStore from "store/bot/bot.store";
import useDynamicFormStore from "store/dynamicForm/dynamicForm.store";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import { MainTable } from "common/components/tables/main.table";
import { getSchemaResponses } from "apis/dynamicForm.api";

interface FormField {
  label: string;
}
interface MapperInterface {
  dynamic_form_schema_id: number;
  form: FormField[];
}

const DynamicFormReport = () => {
  const [schemaToView, setSchemaToView] = useState<MapperInterface>();
  const [tableColumns, setTableColumns] = useState<any>([]);
  const [tableData, setTableData] = useState<any>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;
  const bot_id = useBotStore((state) => state.bot.bot_id);
  const { get_all_dynamic_form_schemas, schemas } = useDynamicFormStore();

  const fetchData = async () => {
    get_all_dynamic_form_schemas(bot_id);
  };

  useEffect(() => {
    fetchData();
  }, [bot_id]);

  const paginatedData = useMemo(() => {
    if (!tableData || tableData.length === 0) return [];
    const firstIndex = (currentPage - 1) * itemsPerPage;
    const lastIndex = firstIndex + itemsPerPage;
    return tableData.slice(firstIndex, lastIndex);
  }, [currentPage, tableData]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  useEffect(() => {
    onChange();
  }, [schemaToView]);

  const getData = async () => {
    const temp = await getSchemaResponses(schemaToView.dynamic_form_schema_id);
    return temp;
  };
  const onChange = async () => {
    if (schemaToView) {
      const updatedColumns = schemaToView.form.map((s) => ({
        key: s.label.trim(),
        name: s.label.trim(),
      }));

      const temp: {
        dynamic_form_response_id: number;
        dynamic_form_schema_id: number;
        response: { label: string; answer: string }[];
        createdAt: any;
      }[] = await getData();

      if (temp.length) {
        const extraColumns = ["Conversation Id", "Channel", "Date"]
          .filter((key) =>
            temp.some((t) => t.response.find((r) => r.label === key))
          )
          .map((key) => ({ key, name: key }));

        setTableColumns([...updatedColumns, ...extraColumns]);

        const transformedData = temp.map((t) =>
          t.response.reduce(
            (acc, obj) => {
              const label = obj.label?.trim();
              acc[label] = obj?.answer || "N/A";
              return acc;
            },
            { date: transformDate(t.createdAt) || "N/A" }
          )
        );
        setTableData(transformedData);
      } else {
        setTableData([]);
      }
    } else {
      setTableColumns([{ key: "", name: "" }]);
      setTableData([]);
    }
  };

  return (
    <div className="p-4">
      <div className="flex gap-5 pb-4">
        <Select
          disabled={!schemas.length}
          onValueChange={async (value) => {
            const form = schemas.find(
              (s) => s.dynamic_form_schema_id == +value
            );
            if (form) {
              setSchemaToView({
                dynamic_form_schema_id: +value,
                form: form.schema.map((fs) => {
                  return {
                    label: fs.label,
                  };
                }),
              });
            }
          }}
          value={String(schemaToView?.dynamic_form_schema_id)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a Report">
              {schemas.find(
                (s) =>
                  s.dynamic_form_schema_id ==
                  schemaToView?.dynamic_form_schema_id
              )?.title || "Select a Report"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {schemas?.map((f) => (
              <SelectItem
                key={f.dynamic_form_schema_id}
                value={String(f.dynamic_form_schema_id)}
              >
                {f?.title || "No Title"}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <CSVLink
          data={tableData}
          headers={tableColumns.map((col) => ({
            label: col.name,
            key: col.key,
          }))}
          filename={"Dynamic_form_report.csv"}
        >
          <Button>Export as CSV</Button>
        </CSVLink>
      </div>

      <div className="overflow-x-auto">
        {schemaToView ? (
          <MainTable data={[...paginatedData]} columns={tableColumns} />
        ) : (
          <p className="p-3 text-center">Please choose a schema </p>
        )}
      </div>
      {tableData.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalCount={tableData.length}
          pageSize={itemsPerPage}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
};

export default DynamicFormReport;
