import {
  getSalesByCategory,
  getSalesByCity,
  getSalesByContinent,
  getSalesByCountry,
  getSalesByDay,
  getSalesByMonth,
  getSalesByOffer,
  getTopSold,
} from "apis/sales.api";
import { apexchartsDataFormConverter, converter } from "helpers/helper";
import { FC, memo, useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import { ChartCard } from "../components";
import BarChart from "../charts/bar.chart";
import { Loading } from "../loading";
import { DateRange } from "react-day-picker";

interface salesDashboardProps {
  selectedPeriod: number;
  submitLoading: boolean;
  setSubmitLoading: (value: boolean) => void;
  filterDate: DateRange;
}

const SalesDashboard: FC<salesDashboardProps> = ({
  selectedPeriod,
  submitLoading,
  setSubmitLoading,
  filterDate
}) => {
  
  const bot = useBotStore((state) => state.bot);
  const [width, setWidth] = useState(window.innerWidth);
  const [topSoldItems, setTopSoldItems] = useState({
    series: [],
    xLabels: [],
  });
  const [topSoldCategory, setTopSoldCategory] = useState({
    series: [],
    xLabels: [],
  });
  const [topSoldOffer, setTopSoldOffer] = useState({
    series: [],
    xLabels: [],
  });
  
  const [salesByContinent, setSalesByContinent] = useState({
    series: [],
    xLabels: [],
  });
  const [salesByCountry, setSalesByCountry] = useState({
    series: [],
    xLabels: [],
  });
  const [salesByCity, setSalesByCity] = useState({
    series: [],
    xLabels: [],
  });
  
  var targetDate// = new Date();
  var endDate// = new Date();

  const getDashboardData = (period, unmounted) => {
    if(selectedPeriod != 22){
      targetDate = new Date();
      const period = +selectedPeriod;
      targetDate.setDate(targetDate.getDate() - period);
    }
    const day = targetDate?.getDate();
    const month = targetDate?.getMonth() + 1;
    const year = targetDate?.getFullYear();
  
    const endDay = endDate?.getDate()
    const endMonth = endDate?.getMonth() + 1
    const endYear = endDate?.getFullYear();
    const bot_id = bot.bot_id;

    if (bot_id !== 63) {
      getTopSold(bot_id, 10, day, month, year, endDay, endMonth, endYear).then((data) => {
        if (!unmounted && Boolean(data?.length)) {
          setTopSoldItems(
            apexchartsDataFormConverter(
              [
                ...data
                  .sort((a, b) => a.n_items - b.n_items)
                  .map((a, i) => {
                    return {
                      item: a.item_title,
                      count: a.n_items,
                    };
                  }),
              ],
              "item"
            )
          );
        } else {
          setTopSoldItems({
            series: [],
            xLabels: [],
          });
        }
      });

      getSalesByCategory(bot_id, day, month, year, endDay, endMonth, endYear).then((data) => {
        console.table(data);
        if (!unmounted && Boolean(data?.length)) {
          setTopSoldCategory(
            apexchartsDataFormConverter(
              [
                ...data
                  .sort((a, b) => a.n_items - b.n_items)
                  .map((a, i) => {
                    return {
                      Category: a.category_name,
                      Count: a.n_items,
                    };
                  }),
              ],
              "Category"
            )
          );
        }
      });

      getSalesByOffer(bot_id, day, month, year, endDay, endMonth, endYear).then((data) => {
        if (!unmounted && Boolean(data?.length)) {
          setTopSoldOffer(
            apexchartsDataFormConverter(
              [
                ...data
                  .sort((a, b) => a.n_items - b.n_items)
                  .map((a, i) => {
                    return {
                      Offer: a.offer_description,
                      Count: a.n_items,
                    };
                  }),
              ],
              "Offer"
            )
          );
        }
      });

      getSalesByContinent(bot_id, day, month, year, endDay, endMonth, endYear).then((data) => {
        if (!unmounted && Boolean(data?.length)) {
          const modifiedData = data
          .sort((a, b) => a.n_items - b.n_items)
          .filter((a) => Boolean(a.continent))
          .map((a) => {
            return {
              Continent: a.continent,
              Count: a.n_items,
            };
          })

          if(Boolean(modifiedData.length)){

            setSalesByContinent(
              apexchartsDataFormConverter(
                [
                  ...modifiedData
                ],
                "Continent"
              )
              );
          }
        } else {
          setSalesByContinent({
            series: [],
            xLabels: [],
          });
        }
      });

      getSalesByCity(bot_id, day, month, year, endDay, endMonth, endYear).then((data) => {
        if (!unmounted && Boolean(data?.length)) {
          const modifiedData = data
          .sort((a, b) => a.n_items - b.n_items)
          .filter((a) => Boolean(a.city))
          .map((a) => {
            return {
              City: a.city,
              Count: a.n_items,
            };
          })

          if(Boolean(modifiedData.length)){
          setSalesByCity(
            apexchartsDataFormConverter(
              [
                ...modifiedData
              ],
              "City"
            )
          );
            }
        }else {
          setSalesByCity({
            series: [],
            xLabels: [],
          });
        }
      });

      getSalesByCountry(bot_id, day, month, year, endDay, endMonth, endYear).then((data) => {
        if (!unmounted && Boolean(data?.length)) {
          const modifiedData = data
          .sort((a, b) => a.n_items - b.n_items)
          .filter((a) => Boolean(a.country))
          .map((a) => {
            return {
              Country: a.country,
              Count: a.n_items,
            };
          })

          if(Boolean(modifiedData.length))
          {setSalesByCountry(
            apexchartsDataFormConverter(
              [
                ...modifiedData
              ],
              "Country"
            )
          )}
        } else {
          setSalesByCountry({
            series: [],
            xLabels: [],
          });
        }
      });
    }

    setSubmitLoading(false);
  };

  useEffect(()=>{
    targetDate = filterDate?.from// || new Date();
    endDate = filterDate?.to;
    // if(filterDate && !filterDate?.to){
    //   filterDate.to = new Date()
    // }
    if(filterDate?.from && filterDate?.to){
      getDashboardData(selectedPeriod, false);
    }
    
  }, [filterDate?.to]);

  const [temp, setTemp] = useState(1);
  
  useEffect(()=>{
    if(selectedPeriod != 22){
      setTemp(selectedPeriod);
    }
  },[selectedPeriod])

  useEffect(() => {
    let unmounted = false;
    setSubmitLoading(true);

    getDashboardData(selectedPeriod, unmounted);
    window.addEventListener("resize", () => {
      setWidth(window.innerWidth);
    });
    return () => {
      unmounted = true;
    };
  }, [temp]);

  return (
    <div className="space-y-5">
      {submitLoading ? (
        <Loading />
      ) : (
        <>
          <ChartCard title="Top Sold Items">
            <BarChart
              data={[...topSoldItems?.series]}
              xLabels={[...topSoldItems?.xLabels]}
            />
          </ChartCard>
          <ChartCard title="Top Sold Categories">
            <BarChart
              data={[...topSoldCategory?.series]}
              xLabels={[...topSoldCategory?.xLabels]}
            />
          </ChartCard>
          <div className="grid lg:grid-cols-2 gap-3">
            <ChartCard title="Top Sold Offers">
              <BarChart
                data={[...topSoldOffer?.series]}
                xLabels={[...topSoldOffer?.xLabels]}
              />
            </ChartCard>
            <ChartCard title="Top Sold By continent">
              <BarChart
                data={[...salesByContinent?.series]}
                xLabels={[...salesByContinent?.xLabels]}
              />
            </ChartCard>
            <ChartCard title="Top Sold By Country">
              <BarChart
                data={[...salesByCountry?.series]}
                xLabels={[...salesByCountry?.xLabels]}
              />
            </ChartCard>
            <ChartCard title="Top Sold By City">
              <BarChart
                data={[...salesByCity?.series]}
                xLabels={[...salesByCity?.xLabels]}
              />
            </ChartCard>
          </div>
        </>
      )}
    </div>
  );
};

export default memo(SalesDashboard);
