import { FC, memo, useCallback } from "react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import {
  getLastTransactionsTable,
  getMostAskedQuestionsPerCountry,
  getTransactionCalculated,
  getTransactionNotAnswered,
} from "apis/transactions.api";
import {
  apexchartsDataFormConverter,
  convert,
  distinct,
  numberWithCommas,
  sortByAsc,
  sortByDesc,
  sum,
  RatingConverter,
} from "helpers/helper";
import { HeatmapChart, SimpleDonutChart } from "../charts";
import {
  Cake,
  CalendarDays,
  MessageCircle,
  Users,
  Voicemail,
  StarIcon,
  FacebookIcon,
  InstagramIcon,
  Globe2Icon,
  EyeIcon,
  RefreshCcwIcon,
  RefreshCwIcon,
  X,
  Info,
} from "lucide-react";
import { CheckpointsDetails } from "../components/checkpoints/checkpointDetails";

import {
  NMessagesSelect,
  StatsCard,
  FeedbackCard,
  DialogSelect,
} from "../components";
import { Loading } from "../loading";
import ChartCard from "../components/chart.card";
import LineChart from "../charts/line.chart";
import Bar<PERSON>hart from "../charts/bar.chart";
import { MainTable } from "common/components/tables/main.table";
import CountriesMenu from "../components/countriesMenu";
import dynamic from "next/dynamic";

import { UpdatedRatingType } from "../components/feedback-rating.card";
import transformDate from "helpers/transformDate";
import JettBookingTable from "../components/jettBookingTable";
import WhatsappSVG from "common/icons/WhatsappSVG";
import CustomDashboard, {
  TFilterdCustomDashboard,
} from "../components/customDashboard/custom-dashboard";
import { getCustomDashboard } from "apis/custom-dashboard.api";
import CustomTable from "../components/customDashboard/CustomTable";
import CustomCard from "../components/customDashboard/CustomCard";
import ChatbotReport from "../components/chatbot.reports";
import { DateRange } from "react-day-picker";
const GeoMap = dynamic(() => import("../charts/geo.chart"), { ssr: false });
import ChatModal from "../components/chatModal";
import { FullModal } from "common/components/modals";
import DynamicFormReport from "../components/dynamic.form.reports";
import useDynamicFormStore from "store/dynamicForm/dynamicForm.store";
import { SuspenseWrapper } from "../components/v2/suspenseWrapper";
import {
  getCheckpointStats,
  getTrAnswered,
  getTrFunctionsRequests,
  getTrHeatMap,
  getTrLastMessages,
  getTrMessagesUsersChannels,
  getTrMessagesUsersCountry,
  getTrMessagesUsersDate,
  getTrStats,
  getTrTopNotAnswered,
  getTrUsersPerYear,
} from "apis/dashboardTransactions.api";
import { formatDistanceToNow } from "date-fns";
import { OneCardLoading } from "../onecardLoading";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "common/ui/inputs/select";
import ConversationHistory from "../components/conversations.history";
import ConversationHistoryModal from "../components/v2/conversationHistoryModal";
import NotAnsweredInfoModal from "../components/v2/notAnsweredInfoModal";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "common/ui/hoverCard";
import { CheckpointView } from "../components/checkpoints/checkPointView";
interface generalDashboardV2Props {
  period: {
    start_day: number;
    start_month: number;
    start_year: number;
    end_day: number;
    end_month: number;
    end_year: number;
  };
}

const weekDays = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];
const hours = [
  "12 AM",
  "1 AM",
  "2 AM",
  "3 AM",
  "4 AM",
  "5 AM",
  "6 AM",
  "7 AM",
  "8 AM",
  "9 AM",
  "10 AM",
  "11 AM",
  "12 PM",
  "1 PM",
  "2 PM",
  "3 PM",
  "4 PM",
  "5 PM",
  "6 PM",
  "7 PM",
  "8 PM",
  "9 PM",
  "10 PM",
  "11 PM",
];

const GeneralDashboardV2: FC<generalDashboardV2Props> = ({ period }) => {
  const bot = useBotStore((state) => state.bot);
  const { get_all_dynamic_form_schemas, schemas } = useDynamicFormStore();

  const [selectedChat, setSelectedChat] = useState(null);
  const [selectedNotAnswered, setSelectedNotAnswered] = useState(null);
  const [stats, setStats] = useState(null);
  const [heatMapData, setHeatMapData] = useState([]);
  const [usersMessagesData, setUsersMessagesData] = useState({
    series: [],
    xLabels: [],
  });

  const [answeredData, setAnsweredData] = useState({
    line: {
      series: [],
      xLabels: [],
    },
    pie: {
      series: [],
      xLabels: [],
    },
  });

  const [usersMessagesChannelsData, setUsersMessagesChannelsData] = useState({
    series: [],
    xLabels: [],
  });

  const [functionsRequestedData, setFunctionsRequestedData] = useState({
    series: [],
    xLabels: [],
  });

  const [usersPerYearData, setUsersPerYearData] = useState({
    series: [],
    xLabels: [],
  });

  const [lastMessages, setLastMessages] = useState([]);

  const [notAnsweredMessages, setNotAnsweredMessages] = useState([]);

  const [messagesPerCountry, setMessagesPerCountry] = useState([]);
  const [messagesPerCountryMap, setMessagesPerCountryMap] = useState([]);

  const [selectedMapParam, setSelectedMapParam] = useState("Messages");

  const [checkpoints, setCheckpoints] = useState([]);
  const [checkpointsStats, setCheckpointsStats] = useState({
    series: [],
    xLabels: [],
  });
  const [selectedDialog, setSelectedDialog] = useState(null);
  const [customDashboard, setCustomDashboard] = useState(null);

  const fetchStats = useCallback(async () => {
    console.log("period received", period);
    if (!period) {
      setStats({});
      return;
    }
    const statsResp = await getTrStats({
      bot_id: bot.bot_id,
      ...period,
    });
    console.log(statsResp, "statsResp");

    const msgsPerPeriodData = statsResp?.filter(
      (s) => s.type === "graph" && s.card === "msg_per_date"
    );

    const usersPerPeriodData = statsResp?.filter(
      (s) => s.type === "graph" && s.card === "users_per_date"
    );

    const date = statsResp?.find((s) => s.card === "date_since_launch")?.date;
    console.log("git here");
    const statsToShow = {
      msgsPerPeriod:
        statsResp?.find((s) => s.type === "number" && s.card === "msg_per_date")
          ?.count || 0,
      msgsPerPeriodGraph: msgsPerPeriodData?.length
        ? apexchartsDataFormConverter(
            [
              ...msgsPerPeriodData.map((a, index) => {
                return {
                  date: a.date,
                  messages: a.count,
                };
              }),
            ],
            "date"
          )
        : {
            series: [],
            xLabels: [],
          },
      voicesPerPeriod:
        statsResp?.find(
          (s) => s.type === "number" && s.card === "voice_per_date"
        )?.count || 0,
      usersPerPeriod:
        statsResp?.find(
          (s) => s.type === "number" && s.card === "users_per_date"
        )?.count || 0,
      usersPerPeriodGraph: usersPerPeriodData?.length
        ? apexchartsDataFormConverter(
            [
              ...usersPerPeriodData.map((a, index) => {
                return {
                  date: a.date,
                  users: a.count,
                };
              }),
            ],
            "date"
          )
        : {
            series: [],
            xLabels: [],
          },
      timeSinceLaunch: date ? formatDistanceToNow(new Date(date)) : "null",
      webSessions:
        statsResp?.find(
          (s) => s.card === "sessions_count" && s.channel === "web"
        )?.count || 0,
      facebookSessions:
        statsResp?.find(
          (s) => s.card === "sessions_count" && s.channel === "facebook"
        )?.count || 0,
      whatsappSessions:
        statsResp?.find(
          (s) => s.card === "sessions_count" && s.channel === "whatsapp"
        )?.count || 0,
      instagramSessions:
        statsResp?.find(
          (s) => s.card === "sessions_count" && s.channel === "instagram"
        )?.count || 0,
    };
    console.log(statsToShow, "staaaaaaaaaaats to show");
    setStats(statsToShow || null);
  }, [bot.bot_id, period]);

  const fetchHeatMap = useCallback(async () => {
    const heatMapResp = await getTrHeatMap({
      bot_id: bot.bot_id,
      ...period,
    });
    const weekDayHeatmapData = [];
    for (let i = 0; i < weekDays?.length; i++) {
      weekDayHeatmapData?.push({
        name: weekDays[i],
        data: hours.map((hour, index) => {
          const obj = heatMapResp?.find(
            (item) => item.weekday === i + 1 && item.hour === index
          );
          return {
            x: hour,
            y: obj?.count || 0,
          };
        }),
      });
    }

    setHeatMapData(weekDayHeatmapData);
  }, [bot.bot_id, period]);

  const fetchMessagesUsers = useCallback(async () => {
    const msgsUsersResp = await getTrMessagesUsersDate({
      bot_id: bot.bot_id,
      ...period,
    });

    if (msgsUsersResp?.length) {
      const chartData = apexchartsDataFormConverter(
        [
          ...msgsUsersResp.map((a, index) => {
            return {
              date: a.date,
              messages: a.messages,
              users: a.users,
              average: a.avg,
            };
          }),
        ],
        "date"
      );

      setUsersMessagesData(chartData);
    }
  }, [bot.bot_id, period]);

  const fetchAnswered = useCallback(async () => {
    const answeredResp = await getTrAnswered({
      bot_id: bot.bot_id,
      ...period,
    });

    const linechartData = answeredResp?.filter((s) => s.graph === "line");

    const pieChartData = answeredResp?.find((s) => s.graph === "pie");

    let lineChart = {
      series: [],
      xLabels: [],
    };

    let pieChart = {
      series: [],
      xLabels: [],
    };

    if (linechartData?.length) {
      lineChart = apexchartsDataFormConverter(
        [
          ...linechartData.map((a, index) => {
            return {
              date: a.date,
              fallbacks: a.fallback_count,
              conflicts: a.conflict_count,
              answered: a.answered_count,
              // answered_pct: a.answered_pct,
            };
          }),
        ],
        "date"
      );
    }

    pieChart = apexchartsDataFormConverter(
      [
        {
          label: "Fallbacks",
          percentage: parseFloat(pieChartData?.fallback_pct?.toFixed(2)),
        },
        {
          label: "Answered",
          percentage: parseFloat(pieChartData?.answered_pct?.toFixed(2)),
        },
        {
          label: "Conflicts",
          percentage: parseFloat(pieChartData?.conflict_pct?.toFixed(2)),
        },
      ],
      "label"
    );

    console.log(pieChart, "pipip");
    setAnsweredData({
      line: lineChart,
      pie: pieChart,
    });
  }, [bot.bot_id, period]);

  const fetchMessagesUsersChannels = useCallback(async () => {
    const messagesChannelsResp = await getTrMessagesUsersChannels({
      bot_id: bot.bot_id,
      ...period,
    });

    if (messagesChannelsResp?.length) {
      const chartData = apexchartsDataFormConverter(
        [...messagesChannelsResp],
        "channel"
      );

      setUsersMessagesChannelsData(chartData);
    }
  }, [bot.bot_id, period]);

  const fetchFunctionsRequested = useCallback(async () => {
    const functionsResp = await getTrFunctionsRequests({
      bot_id: bot.bot_id,
      ...period,
    });

    if (functionsResp?.length) {
      const chartData = apexchartsDataFormConverter(
        [...functionsResp],
        "function_requested"
      );

      setFunctionsRequestedData(chartData);
    }
  }, [bot.bot_id, period]);

  const fetchUsersPerYear = useCallback(async () => {
    const usersPerYearResp = await getTrUsersPerYear(bot.bot_id);
    console.log(usersPerYearResp, "usersPerYearResp");
    if (usersPerYearResp?.length) {
      const chartData = apexchartsDataFormConverter(
        [
          ...usersPerYearResp?.map((d) => {
            return {
              users: d.users,
              year: d.year?.toString(),
            };
          }),
        ],
        "year"
      );

      console.log("chart data for usersperyear", chartData);

      setUsersPerYearData(chartData);
    }
  }, [bot.bot_id]);

  const fetchLastMessages = useCallback(async () => {
    const lastMessagesResp = await getTrLastMessages(bot.bot_id);

    if (lastMessagesResp?.length) {
      setLastMessages(lastMessagesResp);
    }
  }, [bot.bot_id]);

  const fetchTopNotAnswered = useCallback(async () => {
    const notAnsweredResp = await getTrTopNotAnswered({
      bot_id: bot.bot_id,
      ...period,
    });

    if (notAnsweredResp?.length) {
      setNotAnsweredMessages(notAnsweredResp);
    }
  }, [bot.bot_id, period]);

  const fetchMessagesUsersCountry = useCallback(async () => {
    const msgsCountryResp = await getTrMessagesUsersCountry({
      bot_id: bot.bot_id,
      ...period,
    });

    if (msgsCountryResp?.length) {
      const data = msgsCountryResp.map((d) => {
        return {
          Country: d.country,
          Users: d.users,
          Messages: d.messages,
        };
      });
      setMessagesPerCountry(data);
      setMessagesPerCountryMap(data);
    }
  }, [bot.bot_id, period]);

  useEffect(() => {
    if (!messagesPerCountry?.length) return;
    let data = JSON.parse(JSON.stringify(messagesPerCountry));

    data = data?.map((d) => {
      return {
        Country: d?.Country,
        Messages: d?.[selectedMapParam],
      };
    });

    setMessagesPerCountryMap(data);
  }, [selectedMapParam, messagesPerCountry]);

  const openNotAnsweredModel = [
    {
      label: "View",
      onClick: (data) => {
        setSelectedNotAnswered(data.message);
      },
      icon: EyeIcon,
    },
  ];

  const onChangeDialog = (value) => {
    setSelectedDialog(value);
    setCheckpointsStats(prepareData(checkpoints[value]));
  };

  const prepareData = (data) => {
    const tagsArray = [];
    const seriesdata = [];
    if (!data) return { xLabels: [], series: [] };
    for (const set of data) {
      if (!tagsArray.includes(set.tag)) {
        tagsArray.push(set.tag);
      }
      if (seriesdata.find((s) => s.name === set.channel)) {
        seriesdata.find((s) => s.name === set.channel).data.push(set.users);
      } else {
        seriesdata.push({ name: set.channel, data: [set.users] });
      }
    }
    return { xLabels: tagsArray, series: seriesdata };
  };

  const fetchCheckpoints = useCallback(async () => {
    const checkpointsResp = await getCheckpointStats({
      bot_id: bot.bot_id,
      ...period,
    });

    const groupedCheckpointData = checkpointsResp.reduce((acc, checkpoint) => {
      const dialog_name = checkpoint.dialog_name;
      if (!acc[dialog_name]) {
        acc[dialog_name] = [];
      }
      acc[dialog_name].push(checkpoint);
      return acc;
    }, {});
    setCheckpoints(groupedCheckpointData);
    if (selectedDialog) {
      setCheckpointsStats(prepareData(groupedCheckpointData[selectedDialog]));
    } else {
      const firstDialog = Object.keys(groupedCheckpointData)[0];
      setSelectedDialog(firstDialog);
      setCheckpointsStats(prepareData(groupedCheckpointData[firstDialog]));
    }
  }, [bot.bot_id, period]);

  const fetchCustomDashboard = useCallback(async () => {
    const customDashboardResp = await getCustomDashboard(bot.bot_id);
    if (customDashboardResp?.length > 0) {
      const tables =
        customDashboardResp.filter((item) => item.widget_type === "table") ||
        [];
      const cards =
        customDashboardResp.filter((item) => item.widget_type === "card") || [];
      const lineCharts =
        customDashboardResp.filter(
          (item) => item.widget_type === "line-chart"
        ) || [];
      setCustomDashboard({ tables, cards, lineCharts });
    }
  }, [bot.bot_id]);

  useEffect(() => {
    fetchCustomDashboard();
  }, [bot.bot_id]);

  return (
    <div className="space-y-5">
      {/* {submitLoading ? (
        <Loading />
      ) : ( */}
      <>
        <SuspenseWrapper
          fetchData={fetchStats}
          dependency={period}
          LoadingComponent={Loading}
        >
          <div className="grid lg:grid-cols-3 gap-5 py-5">
            <StatsCard
              title="Messages per period"
              subtitle={stats?.msgsPerPeriod || "0"}
              icon={MessageCircle}
              data={
                stats?.msgsPerPeriodGraph?.series?.length
                  ? stats?.msgsPerPeriodGraph.series[0]
                  : stats?.msgsPerPeriodGraph?.series
              }
              xLabels={stats?.msgsPerPeriodGraph?.xLabels}
            />
            <StatsCard
              title="Voices per period"
              subtitle={stats?.voicesPerPeriod || "0"}
              icon={Voicemail}
            />
            <StatsCard
              title="Number Of Users Per Period"
              subtitle={stats?.usersPerPeriod || "0"}
              icon={Users}
              data={
                stats?.usersPerPeriodGraph?.series?.length
                  ? stats?.usersPerPeriodGraph?.series[1]
                  : stats?.usersPerPeriodGraph?.series
              }
              xLabels={stats?.usersPerPeriodGraph?.xLabels}
            />
            <StatsCard
              title="Time since launch"
              subtitle={stats?.timeSinceLaunch}
              icon={Cake}
            />
            <StatsCard
              title="Whatsapp Sessions"
              subtitle={stats?.whatsappSessions || "0"}
              icon={WhatsappSVG}
            />
            <StatsCard
              title="Facebook Sessions"
              subtitle={stats?.facebookSessions || "0"}
              icon={FacebookIcon}
            />
            <StatsCard
              title="Instagram Sessions"
              subtitle={stats?.instagramSessions || "0"}
              icon={InstagramIcon}
            />
            <StatsCard
              title="Web Sessions"
              subtitle={stats?.webSessions || "0"}
              icon={Globe2Icon}
            />
            {/* {bot?.bot_id === 666 ? (
              <StatsCard
                title="Total Sessions Excluding Whatsapp"
                subtitle={
                  numberWithCommas(totalSessions)
                    ? numberWithCommas(totalSessions)
                    : 0
                }
                icon={Globe2Icon}
              />
            ) : null} */}
            {customDashboard?.cards?.length > 0 ? (
              <CustomCard cards={customDashboard.cards} />
            ) : null}
          </div>
        </SuspenseWrapper>

        {bot?.bot_id === 666 ? <JettBookingTable /> : null}

        <SuspenseWrapper
          dependency={null}
          fetchData={() => get_all_dynamic_form_schemas(bot.bot_id)}
          LoadingComponent={OneCardLoading}
        >
          {schemas?.length ? (
            <ChartCard title="Dynamic Form Report">
              <DynamicFormReport />
            </ChartCard>
          ) : (
            <div></div>
          )}
        </SuspenseWrapper>
        <SuspenseWrapper
          dependency={period}
          fetchData={fetchCheckpoints}
          LoadingComponent={OneCardLoading}
        >
          {Object.keys(checkpoints)?.length ? (
            // <ChartCard
            //   className="pb-10"
            //   title={`Dialog Checkpoints statistics`}
            //   headerComponent={
            //     <>
            //       <div className="flex items-center space-x-4">
            //         <span className="text-gray-300 font-medium">
            //           Select a dialog:
            //         </span>
            //         <DialogSelect
            //           onChangeDialog={onChangeDialog}
            //           dialog={selectedDialog}
            //           dialogs={Object.keys(checkpoints)}
            //         />
            //       </div>
            //     </>
            //   }
            // >
            //   <div className="relative">
            //     <BarChart
            //       data={[...checkpointsStats?.series]}
            //       xLabels={[...checkpointsStats?.xLabels]}
            //     />
            //     <div className="absolute right-0 mr-2">
            //       <CheckpointsDetails />
            //     </div>
            //     <div style={{ marginBottom: "20px" }}></div>
            //   </div>
            // </ChartCard>
            <CheckpointView
              startDate={{
                start_day: period.start_day,
                start_month: period.start_month,
                start_year: period.start_year,
              }}
              endDate={{
                end_day: period.end_day,
                end_month: period.end_month,
                end_year: period.end_year,
              }}
            />
          ) : null}
        </SuspenseWrapper>
        <SuspenseWrapper
          dependency={period}
          fetchData={fetchHeatMap}
          LoadingComponent={OneCardLoading}
        >
          <ChartCard title="Messages Per Weekday / Hour">
            <HeatmapChart data={[...heatMapData]} />
          </ChartCard>
        </SuspenseWrapper>
        <SuspenseWrapper
          dependency={period}
          fetchData={fetchMessagesUsers}
          LoadingComponent={OneCardLoading}
        >
          <ChartCard title="Messages / Users Per Day">
            <LineChart
              data={[...usersMessagesData.series]}
              xLabels={[...usersMessagesData.xLabels]}
            />
          </ChartCard>
        </SuspenseWrapper>

        {customDashboard?.lineCharts?.map((lineChart) => {
          const chart_data = lineChart?.queryResults[0]?.chart_data || "{}";

          const series = [
            {
              name: lineChart.title,
              type: lineChart.widget_type === "line-chart" ? "line" : "",
              data: [],
            },
          ];
          let xLabels = [""];
          if (chart_data) {
            const parsedData = JSON.parse(chart_data);
            if (parsedData?.length > 0) {
              const convertedData = JSON.parse(chart_data)?.map(
                (point: { x: string; y: string }, i: number) => ({
                  x: point.x,
                  y: Number(point.y) || i,
                })
              );
              series[0].data = convertedData;
              xLabels = convertedData?.map((point) => point.x);
            }
          }
          return (
            <ChartCard key={lineChart.title} title={lineChart.title}>
              {series[0]?.data.length > 0 ? (
                <>
                  <p className="text-lg">{lineChart.description}</p>
                  <LineChart
                    data={series}
                    xLabels={xLabels}
                    xLabel={lineChart.x_label}
                    yLabel={lineChart.y_label}
                  />
                </>
              ) : null}
            </ChartCard>
          );
        })}
        <SuspenseWrapper
          dependency={period}
          fetchData={fetchAnswered}
          LoadingComponent={OneCardLoading}
        >
          <div className="grid lg:grid-cols-2 gap-3">
            <ChartCard
              title="Trend of Answered Requests %"
              headerComponent={<RequestsInfoCard />}
              className="flex-dd1"
            >
              <BarChart
                colors={["#8F8F8F", "#9F6FFC", "#67F7FF"]}
                data={[...answeredData.line.series]}
                xLabels={[...answeredData.line.xLabels]}
              />
            </ChartCard>
            <ChartCard
              headerComponent={<RequestsInfoCard />}
              title="Requests Answered"
              className="flex-s"
            >
              <SimpleDonutChart
                data={[...answeredData.pie.series]}
                xLabels={[...answeredData.pie.xLabels]}
              />
            </ChartCard>
          </div>
        </SuspenseWrapper>

        <SuspenseWrapper
          dependency={period}
          fetchData={fetchMessagesUsersChannels}
          LoadingComponent={OneCardLoading}
        >
          <ChartCard title="Messages / Users Per Channel">
            <BarChart
              data={[...usersMessagesChannelsData.series]}
              xLabels={[...usersMessagesChannelsData.xLabels]}
            />
          </ChartCard>
        </SuspenseWrapper>
        <SuspenseWrapper
          dependency={period}
          fetchData={fetchFunctionsRequested}
          LoadingComponent={OneCardLoading}
        >
          <ChartCard title="Messages Per Functions Requested">
            <BarChart
              data={[...functionsRequestedData.series]}
              xLabels={[...functionsRequestedData.xLabels]}
            />
          </ChartCard>
        </SuspenseWrapper>
        <SuspenseWrapper
          dependency={null}
          fetchData={fetchUsersPerYear}
          LoadingComponent={OneCardLoading}
        >
          <ChartCard title="Users Per Year">
            <BarChart
              data={[...usersPerYearData.series]}
              xLabels={[...usersPerYearData.xLabels]}
            />
          </ChartCard>
        </SuspenseWrapper>
        <ConversationHistory />
        <div className="grid lg:grid-cols-2 gap-3">
          <SuspenseWrapper
            dependency={null}
            fetchData={fetchLastMessages}
            LoadingComponent={OneCardLoading}
          >
            <ChartCard
              className="pb-10"
              title="Last 15 Messages"
              headerComponent={
                <RefreshCwIcon
                  size={14}
                  className="hover:text-primary cursor-pointer"
                  onClick={fetchLastMessages}
                />
              }
            >
              <div className="flex flex-col gap-2">
                <div className="max-h-[500px] overflow-y-auto">
                  {/* TODO: ???show chat history for last message */}
                  <MainTable
                    data={[...lastMessages]}
                    columns={[
                      {
                        key: "message",
                        name: "Question",
                      },
                    ]}
                  />
                </div>
              </div>
            </ChartCard>
          </SuspenseWrapper>
          <SuspenseWrapper
            dependency={period}
            fetchData={fetchTopNotAnswered}
            LoadingComponent={OneCardLoading}
          >
            <ChartCard className="pb-10" title="Not Answered Messages">
              <div className="flex flex-col gap-2">
                <div className="max-h-[500px] overflow-y-auto">
                  <MainTable
                    actions={openNotAnsweredModel}
                    data={[...notAnsweredMessages]}
                    columns={[
                      {
                        key: "message",
                        name: "Question",
                      },
                      {
                        key: "count",
                        name: "Count",
                      },
                      {
                        key: "result_status",
                        name: "Type",
                      },
                    ]}
                  />
                </div>
              </div>
            </ChartCard>

            {selectedChat && (
              <ConversationHistoryModal
                chat={selectedChat}
                onClose={() => setSelectedChat(null)}
              />
            )}

            {selectedNotAnswered && (
              <NotAnsweredInfoModal
                message={selectedNotAnswered}
                onClose={() => setSelectedNotAnswered(null)}
                setSelectedChat={setSelectedChat}
              />
            )}
          </SuspenseWrapper>
          {customDashboard?.tables?.map((table) => {
            const keys =
              table.queryResults.length > 0
                ? Object.keys(table.queryResults[0])
                : [];
            return (
              <div key={table.title} className="w-full">
                <CustomTable table={table} keys={keys} />
              </div>
            );
          })}
        </div>
        <SuspenseWrapper
          fetchData={fetchMessagesUsersCountry}
          dependency={period}
          LoadingComponent={OneCardLoading}
          className="grid lg:grid-cols-2 gap-3"
        >
          <ChartCard className="pb-10" title="Messages / Users Per Country">
            <div className=" max-h-[500px] overflow-y-auto">
              <MainTable
                data={[...sortByDesc(messagesPerCountry, "Messages")]}
                columns={[
                  {
                    key: "Country",
                    name: "Country",
                  },
                  {
                    key: "Messages",
                    name: "Messages",
                  },
                  {
                    key: "Users",
                    name: "Users",
                  },
                ]}
              />
            </div>
          </ChartCard>
          <ChartCard
            title={`${selectedMapParam} Per Country`}
            headerComponent={
              <div className="flex items-center space-x-4">
                <span className="text-gray-300 font-medium">
                  Select data parameter:
                </span>
                <Select
                  onValueChange={(value) => {
                    setSelectedMapParam(value);
                  }}
                  value={selectedMapParam}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select a parameter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={"Messages"} key={"Messages"}>
                      Messages
                    </SelectItem>
                    <SelectItem value={"Users"} key={"Users"}>
                      Users
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            }
          >
            <GeoMap data={messagesPerCountryMap} />
          </ChartCard>
        </SuspenseWrapper>
      </>
      {/* )} */}
    </div>
  );
};

export default GeneralDashboardV2;

const RequestsInfoCard = () => {
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <Info className="w-4 h-4 text-gray-400 cursor-pointer" />
      </HoverCardTrigger>
      <HoverCardContent className="bg-accent bg-opacity-90 border-none">
        <div className="font-bold text-sm">
          Requests count for messages that initiate a new flow (new dialog, new
          faq, ...etc)
        </div>
        <div className="text-sm">
          Messages within a dialog are not counted here
        </div>
      </HoverCardContent>
    </HoverCard>
  );
};
