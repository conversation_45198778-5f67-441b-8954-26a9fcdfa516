import { FC, memo } from "react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import {
  apexchartsDataFormConverter,
  sortByDesc,
} from "helpers/helper";
import { CSVLink } from 'react-csv';
import * as XLSX from 'xlsx';
import { HeatmapChart } from "../charts";
import {
  CalendarDays,
  MessageCircle,
  Clock,
  User,
  CircleDashed,
  X,
  Hourglass,
  Star,
} from "lucide-react";

import { StatsCard } from "../components";
import { Loading } from "../loading";
import ChartCard from "../components/chart.card";
import BarChart from "../charts/bar.chart";
import { getAgentsDataCount, getLiveCalculated, getQueueDataCount, getSessionDataCount } from "apis/internalLiveChat.api";
import * as signalR from "@microsoft/signalr";
import AgentsIdMenu from "../components/agentsIdMenu";
import { MainTable } from "common/components/tables/main.table";
import SimpleDonutChart from "../charts/simpleDonut.chart";
import * as Menubar from '@radix-ui/react-menubar';
import { MoreVertical } from 'lucide-react';
import { DateRange } from "react-day-picker";
import LineChart from "../charts/line.chart";
import ChannelMenu from "../components/channelMenu";
import ClosedReasonsMenu from "../components/closedReasonsMenu";


interface liveChatDashboardProps {
  selectedPeriod: number;
  submitLoading: boolean;
  setSubmitLoading: (value: boolean) => void;
  filterDate: DateRange;
}

const LiveChatDashboard: FC<liveChatDashboardProps> = ({
  selectedPeriod,
  submitLoading,
  setSubmitLoading,
  filterDate,
  
}) => {
  const bot = useBotStore((state) => state.bot);
  const [messagePerAgent, setMessagePerAgent] = useState({
    series: [],
    xLabels: [],
  });
  const [channelData, setChannelData] = useState({
    series: [],
    xLabels: [],
  });
  const [closedChart, setClosedChart] = useState({
    series: [],
    xLabels: [],
  });
  const [closedChartPerAgent, setClosedChartPerAgent] = useState({
    series: [],
    xLabels: [],
  });
  const [sessionsPerDayChart, setSessionPerDayChart] = useState({
    series: [],
    xLabels: [],
  });
  const [avgWaitingTimeChart, setAvgWaitingTimeChart] = useState({
    series: [],
    xLabels: [],
  });
  const [heapMapAgent, setHeapMapAgent] = useState({
    series: [],
    xLabels: [],
  });
  const [weekDayHoursHeatMap, setWeekDayHoursHeatMap] = useState([]);
  const [agentHeatMap, setAgentHeatMap] = useState([]);
  const [sessionsPerDay, setSessionsPerDay] = useState(null);
  const [averagePendingTime, setAveragePendingTime] = useState([]);
  const [activeChats, setActiveChats] = useState(null);
  const [activeAgent, setActiveAgent] = useState(null);
  const [avgWaitingTime, setAvgWaitingTime] = useState("");
  const [dropped, setDropped] = useState(null);
  const [closed, setClosed] = useState(null);
  const [waiting, setWaiting] = useState(null);
  const [pending, setPending] = useState(null);
  const [selectedAgents, setSelectedAgents] = useState([]);
  const [channel, setChannel] = useState("All");
  const [reason, setReason] = useState("All");
  const [selectedActiveAgent, setSelectedActiveAgent] = useState([]);
  const [agent, setAgent] = useState("all");
  const [activeAgentSelected, setActiveAgentSelected] = useState("all");
  const [selectedAgentRating, setSelectedAgentRating] = useState("all");
  const [closedReasonsPerAgents, setClosedReasonsPerAgents] = useState([]);
  const [activeAgentStatus, setActiveAgentStatus] = useState([]);
  const [agentRatingStatus, setAgentRatingStatus] = useState([]);
  const [agentStatusTable, setAgentStatusTable] = useState([]);
  const [agentRatingTable, setAgentRatingTable] = useState([]);
  const [activeAgentStatusTable, setActiveAgentStatusTable] = useState([]);
  const [avgRating, setAvgRating] = useState([])
  const [agentReasonTrue, setAgentReasonTrue] = useState(false);

  const initValues =async()=>{
    const agentsData = await getAgentsDataCount(bot.bot_id)
    const sessionsData = await getSessionDataCount(bot.bot_id)
    const queueData = await getQueueDataCount(bot.bot_id)
    setActiveChats(sessionsData.activeSessions);
    setActiveAgent(agentsData.activeAgents);
    setDropped(queueData.droppedQueues);
    setWaiting(queueData.waitingQueues);
    setPending(sessionsData.pendingSessions);
    setClosed(sessionsData.closedSessions);
  }

  var targetDate //= new Date();
  var endDate //= new Date();
  const getData = () => {
    if(selectedPeriod != 22){
      targetDate = new Date();
      const period = +selectedPeriod;
      targetDate.setDate(targetDate.getDate() - period);
    }
    const day = targetDate?.getDate();
    const month = targetDate?.getMonth() + 1;
    const year = targetDate?.getFullYear();

    const weekDays = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const hours = [
      "12 AM",
      "1 AM",
      "2 AM",
      "3 AM",
      "4 AM",
      "5 AM",
      "6 AM",
      "7 AM",
      "8 AM",
      "9 AM",
      "10 AM",
      "11 AM",
      "12 PM",
      "1 PM",
      "2 PM",
      "3 PM",
      "4 PM",
      "5 PM",
      "6 PM",
      "7 PM",
      "8 PM",
      "9 PM",
      "10 PM",
      "11 PM",
    ];
 
    const endDay = endDate?.getDate()
    const endMonth = endDate?.getMonth() + 1
    const endYear = endDate?.getFullYear()
    const bot_id = bot.bot_id;
    getLiveCalculated(bot_id, day, month, year, endDay, endMonth, endYear).then(async (data) => {
      setSubmitLoading(false);
      const weekDayHeatmapData = [];
      const agentActivityHeatmapData = [];
      //calender
      const heatmapData = data?.filter((a) => a?.graph === "agent_session_per_day");
      //avg waiting time (card)
      const tempAvgWaitingTime = data?.filter((a) => a?.graph === "avg_waiting_time");
      //session / channel (chart)
      const sessionsCounterData = data?.filter((a) => a?.graph === "channelsDistribution");
      //Sessions per day (card)
      const sessionPerDay = data?.filter((a) => a?.graph === "session_per_day");
      //Session /ِ Agent (chart)
      const agentSessions = data?.filter((a) => a.graph === "agent_sessions");
      //Closed Reasons (chart)
      const closedReasons = data?.filter((a) => a.graph === "closed_reasons");
      //table
      const AgentStatus = data?.filter((a) => a.graph === "agent_status")
      //get Agents (sent to the select menu) 
      setSelectedAgents(data?.filter((a) => a.graph === "get_agent"));
      //Closed Reasons per Agent (chart)
      const tempAgentsData = data?.filter((a) => a.graph === "closed_reasons_per_agent")
      setClosedReasonsPerAgents(tempAgentsData);
      //active Agents calender
      const activeAgentChart = data?.filter((a) => a.graph === "active_agents");  
      //active Agents line chart
      const activeAgentLineChart = data?.filter((a) => a.graph === "active_agents_chart");
      //active Agents table
      const temp = data?.filter((a) => a.graph === "active_agents_times");
      
      setActiveAgentStatus(temp);
      //active agents data 
      setSelectedActiveAgent(data?.filter((a) => a.graph === "get_active_agents"));
      //average pending time
      setAveragePendingTime(data?.filter((a) => a.graph === "total_pending_time"));
      //rating card
      setAvgRating(data?.filter((a)=> a.graph === "rating"));
      //agent rating table 
      const agentRating = data?.filter((a)=>a.graph === "agent_rating");
      setAgentRatingStatus(agentRating);
      

      const channelSessions = sessionsCounterData?.map((a) => {
        return {
          channel: a.channel,
          sessions: a.recordCount,
        };
      });
      var avg = 0;
      sessionPerDay.map((a)=>{
        avg +=a.sum;
      })
      avg = avg/sessionPerDay.length || 0;
      setSessionsPerDay(Math.floor(avg).toString())
      setAvgWaitingTime(tempAvgWaitingTime[0]?.averageTimeInSeconds || "00:00:00");
      if (agentSessions.length > 0) {
        setMessagePerAgent(
          apexchartsDataFormConverter(
            [
              ...agentSessions.map((a) => {
                return {
                sessions: a.number_of_sessions_per_agent,
                agents: a.email
                };
              }),
            ],
            "agents"
          )
        );
      } else {
        setMessagePerAgent({
          series: [],
          xLabels: [],
        });
      }
      if (channelSessions.length > 0) {
        setChannelData(
          apexchartsDataFormConverter(
            [
              ...channelSessions.map((a) => {
                return {
                  sessions: a.sessions,
                  channel:  a.channel.charAt(0).toUpperCase() + a.channel.slice(1)
                };
              }),
            ],
            "channel"
          )
        );
      } else {
        setChannelData({
          series: [],
          xLabels: [],
        });
      }

      if (closedReasons.length > 0) {
        setClosedChart(
          apexchartsDataFormConverter(
            [
              ...closedReasons.map((a) => {
                return {
                  sessions: a.recordCount,
                  status: a.status.charAt(0).toUpperCase() + a.status.slice(1)
                };
              }),
            ],
            "status"
          )
        );
      } else {
        setClosedChart({
          series: [],
          xLabels: [],
        });
      }
      if (activeAgentLineChart.length > 0) {
        setHeapMapAgent(
          apexchartsDataFormConverter(
            [
              ...activeAgentLineChart.map((a) => {
                return {
                  sum: a.sum,
                  date: a.date
                };
              }),
            ],
            "date"
          )
        );
      } else {
        setHeapMapAgent({
          series: [],
          xLabels: [],
        });
      }

      if (tempAvgWaitingTime.length > 0) {
        setAvgWaitingTimeChart(
          apexchartsDataFormConverter(
            [
              ...tempAvgWaitingTime.map((a) => {
                return {
                  averageTimeInSeconds: a.averageTimeInSeconds,
                  createdAt: a.createdAt
                };
              }),
            ],
            "createdAt"
          )
        );
      } else {
        setAvgWaitingTimeChart({
          series: [],
          xLabels: [],
        });
      }

      if(sessionPerDay.length > 0){
        setSessionPerDayChart(
          apexchartsDataFormConverter(
            [
              ...sessionPerDay.map((a)=>{
                return{
                  count: a.sum,
                  day: a.weekday 
                };
              })
            ],
            "day"
          )
        )
      } else {
        setSessionPerDayChart({
          series: [],
          xLabels: [],
        })
      }

   
      for (var i = 0; i < weekDays?.length; i++) {
        weekDayHeatmapData?.push({
          name: weekDays[i],
          data: hours.map((hour, index) => {
            const obj = heatmapData?.find((item) => {
              return item.weekday === weekDays[i] && item.hour === index;
            });
            return {
              x: hour,
              y: obj?.sum || 0,
            };
          }),
        });
      }
      setWeekDayHoursHeatMap(weekDayHeatmapData);

      for (var i = 0; i < weekDays?.length; i++) {
        agentActivityHeatmapData?.push({
          name: weekDays[i],
          data: hours.map((hour, index) => {
            const obj = activeAgentChart?.find((item) => {
              return item.weekday === weekDays[i] && item.hour === index;
            });
            return {
              x: hour,
              y: obj?.sum || 0,
            };
          }),
        });
      }
      setAgentHeatMap(agentActivityHeatmapData);


      setAgentStatusTable([]);
      setAgentStatusTable([
        ...AgentStatus.map((a) => {
          return {
            email: a.email,
            closed_Sessions: a.closed_sessions || "0",
            closed: a.averageClosedTime || "00:00:00",
            wait: a.averageWaitingTime || "00:00:00",
            number_of_sessions: a.number_of_sessions || "0",
            closed_due_to_agent: a.closed_due_to_agent_rejection || "0",
            WorkingHours: a.working_hours || "00:00:00",
            createdAt: a.createdAt,
            average_pending_time: a.average_pending_time || "00:00:00",
            avg_rate:a.avg_rating || "0"
          };
        }),
      ]);

      setAgentRatingData(agentRating);

    setAgentData(tempAgentsData);

      setActiveAgentData(temp);

    });
  };
  
  useEffect(() => {
    setAgentData();
  }, [agent, channel, reason]);

  useEffect(()=>{
    targetDate = filterDate?.from //|| new Date();
    endDate = filterDate?.to;
    // if(filterDate && !filterDate?.to){
    //   filterDate.to = new Date()
    // }
    if(filterDate?.from && filterDate?.to){
      getDashboardData();
    }
  }, [filterDate?.to]); 


  

  const setAgentData =(closedReasonsPerAgent = closedReasonsPerAgents)=>{
    let closedAgentChart;
    if(reason === "All"){
      setAgentReasonTrue(false);
      if (agent === "all" && channel === "All") {
        let temp = closedReasonsPerAgent;
        const statusMap = new Map();
      
        temp.forEach(item => {
          if (statusMap.has(item.status)) {
            const existingItem = statusMap.get(item.status);
            existingItem.recordCount += item.recordCount;
          } else {
            statusMap.set(item.status, { ...item });
          }
        });
      
        closedAgentChart = Array.from(statusMap.values());
  
      } else if(agent === "all" && channel !== "All"){
        let temp = closedReasonsPerAgent;
        const statusMap = new Map();
      
        temp.forEach(item => {
          if(item.channel.toLowerCase() === channel.toLowerCase()){
            if (statusMap.has(item.status)) {
  
              const existingItem = statusMap.get(item.status);
              existingItem.recordCount += item.recordCount;
            } else {
  
              statusMap.set(item.status, { ...item });
            }
          }
        });
      
        closedAgentChart = Array.from(statusMap.values());
      } else if(agent !== "all" && channel === "All"){
        let temp = closedReasonsPerAgent;
        const statusMap = new Map();
      
        temp.forEach(item => {
          if(item.agent_id === parseInt(agent, 10)){
            if (statusMap.has(item.status)) {
              const existingItem = statusMap.get(item.status);
              existingItem.recordCount += item.recordCount;
            } else {
  
              statusMap.set(item.status, { ...item });
            }
          }
        });
      
        closedAgentChart = Array.from(statusMap.values());
      } else if(agent !== "all" && channel !== "All"){
        let temp = closedReasonsPerAgent;
        const statusMap = new Map();
        temp.forEach(item => {
          if(item.agent_id === parseInt(agent, 10) && item.channel.toLowerCase() === channel.toLowerCase()){
            if (statusMap.has(item.status)) {
              const existingItem = statusMap.get(item.status);
              existingItem.recordCount += item.recordCount;
            } else {
              statusMap.set(item.status, { ...item });
            }
          }
        });
        closedAgentChart = Array.from(statusMap.values());
      }
      if (closedAgentChart?.length > 0) {
        setClosedChartPerAgent(
          apexchartsDataFormConverter(
            [
              ...closedAgentChart.map((a) => {
                return {
                  sessions: a.recordCount,
                  status: a.status.charAt(0).toUpperCase() + a.status.slice(1),
                  channel: a.channel,
                  agent_id: a.agent_id
                };
              }),
            ],
            "status"
          )
        );
      } else {
        setClosedChartPerAgent({
          series: [],
          xLabels: [],
        });
      }
    } else if(reason !== "All"){
      setAgentReasonTrue(false);
      if(agent === "all" && channel === "All"){

        let temp = closedReasonsPerAgent;
        const statusMap = new Map();
        temp.forEach(item => {
          if(item.status.toLowerCase() === reason.toLowerCase()){
            if (statusMap.has(item.channel)) {
  
              const existingItem = statusMap.get(item.channel);
              existingItem.recordCount += item.recordCount;
            } else {
  
              statusMap.set(item.channel, { ...item });
            }
          }
        });
      
        closedAgentChart = Array.from(statusMap.values());
        if (closedAgentChart?.length > 0) {
          setClosedChartPerAgent(
            apexchartsDataFormConverter(
              [
                ...closedAgentChart.map((a) => {
                  return {
                    sessions: a.recordCount,
                    status: a.status.charAt(0).toUpperCase() + a.status.slice(1),
                    channel: a.channel,
                    agent_id: a.agent_id,
                    email: a.email.charAt(0).toUpperCase() + a.status.slice(1),
                  };
                }),
              ],
              "channel"
            )
          );
        } else {
          setClosedChartPerAgent({
            series: [],
            xLabels: [],
          });
        }
      } else if(agent === "all" && channel !== "All"){
        closedAgentChart = closedReasonsPerAgent;
        closedAgentChart = closedAgentChart.filter(item => 
          item.status.toLowerCase() === reason.toLowerCase()
        )
        closedAgentChart = closedAgentChart.filter(item => 
          item.channel === channel.toLowerCase()
        )

        if (closedAgentChart?.length > 0) {
          setClosedChartPerAgent(
            apexchartsDataFormConverter(
              [
                ...closedAgentChart.map((a) => {
                  return {
                    sessions: a.recordCount,
                    status: a.status.charAt(0).toUpperCase() + a.status.slice(1),
                    channel: a.channel,
                    agent_id: a.agent_id,
                    email: a.email.charAt(0).toUpperCase() + a.email.slice(1),
                  };
                }),
              ],
              "email"
            )
          );
        } else {
          setClosedChartPerAgent({
            series: [],
            xLabels: [],
          });
        }

      } else if(agent !== "all" && channel === "All"){
        closedAgentChart = closedReasonsPerAgent;
        
        closedAgentChart = closedAgentChart.filter(item => 
          item.status.toLowerCase() === reason.toLowerCase()
        )
        closedAgentChart = closedAgentChart.filter(item => 
          item.agent_id === parseInt(agent, 10)
        )

        if (closedAgentChart?.length > 0) {
          setClosedChartPerAgent(
            apexchartsDataFormConverter(
              [
                ...closedAgentChart.map((a) => {
                  return {
                    sessions: a.recordCount,
                    status: a.status.charAt(0).toUpperCase() + a.status.slice(1),
                    channel: a.channel,
                    agent_id: a.agent_id,
                    email: a.email.charAt(0).toUpperCase() + a.status.slice(1),
                  };
                }),
              ],
              "channel"
            )
          );
        } else {
          setClosedChartPerAgent({
            series: [],
            xLabels: [],
          });
        }
      } else if(agent !== "all" && channel !== "All"){
        setAgentReasonTrue(true);      
        closedAgentChart = closedReasonsPerAgent;
        
        closedAgentChart = closedAgentChart.filter(item => 
          item.channel.toLowerCase() === channel.toLowerCase()
        )
        closedAgentChart = closedAgentChart.filter(item => 
          item.status.toLowerCase() === reason.toLowerCase()
        )
        closedAgentChart = closedAgentChart.filter(item => 
          item.agent_id === parseInt(agent, 10)
        )
        setClosedChartPerAgent({...closedAgentChart})
      }
    }
  }

  useEffect(()=>{
    setActiveAgentData();
  }, [activeAgentSelected])


  const setActiveAgentData =(activeAgentsStatus = activeAgentStatus)=>{

    const activeAgentDataChart = activeAgentsStatus.filter(item => 
      activeAgentSelected === "all" ||
      item.agent_id === parseInt(activeAgentSelected, 10)
    );
    
    setActiveAgentStatusTable([]);
      setActiveAgentStatusTable([
        ...activeAgentDataChart.map((a) => {
          return {
            email: a.email,
            date: a.date,
            time: a.time,
            status: a.status,
            agent_id: a.agent_id, 
            timeZone: a.time_zone
          };
        }),
      ]);
  }

  const setAgentRatingData =(agentsRatingStatus = agentRatingStatus)=>{
    const rating = agentsRatingStatus.filter(item => 
      selectedAgentRating === "all" ||
      item.agent_id === parseInt(selectedAgentRating, 10)
    )
    setAgentRatingTable([]);
      setAgentRatingTable([
        ...rating.map((a)=>{
          return{
            email: a.email,
            average_rating: a.rating,
            rating_msg: a.rating_msg?.charAt(0).toUpperCase() + a.rating_msg?.slice(1) || "No Rating Message"
          }  
        })
      ])
  }

  useEffect(()=>{
    setAgentRatingData();
  },[selectedAgentRating])


  const getDashboardData = () => {
    setSubmitLoading(true);
    getData();
    initValues();
  };
  const [temp, setTemp] = useState(1);
  
  useEffect(()=>{
    if(selectedPeriod != 22){
      setTemp(selectedPeriod);
    }
  },[selectedPeriod])

  useEffect(() => {
    getDashboardData();
  }, [temp]);

  

  useEffect(() => {
    const userId = `internalLivechatDashboard__${bot.bot_id}`;
    const socketServer = "https://i2i-messaging.azurewebsites.net";
    const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot.bot_id}&type=server`;
    
    const _connection = new signalR.HubConnectionBuilder()
      .withUrl(negotiationURL)
      .build();

      _connection
      .start()
      .then(() => console.log("Connection established"))
      .catch((err) => {
        console.error("Connection error: ", err);
      });

    _connection.on("chatMessage", (message) => {
      console.log("msg", message);
      if(message.changed === 'session'){
        setClosed(message.closedSessions); 
        setPending(message.pendingSessions);
        setActiveChats(message.activeSessions);
      }
      if(message.changed === 'agent'){
        setActiveAgent(message.activeAgents);
      }
      else if(message.changed === 'queue'){
        setWaiting(message.waitingQueues);
        setDropped(message.droppedQueues);
      } 
    });

    return () => {
      _connection.stop().then(() => console.log("Connection stopped"));
    };
  }, []);  

  const tableColumns = [
      {
        key: "email",
        name: "Agent Email",
      },
      {
        key: "closed_Sessions",
        name: "Number of Closed Sessions",
      },
      {
        key: "closed",
        name: "Average Session Time", 
      },
      {
        key: "wait",
        name: "Average Waiting Time",
      },
      {
        key: "number_of_sessions",
        name: "Total Number Of Sessions",
      },
      {
        key: "closed_due_to_agent",
        name: "Rejected Sessions",
      },
      {
        key: "WorkingHours",
        name: "Working Hours",
      },
      {
        key: "createdAt",
        name: "Date"
      },
      {
        key: "average_pending_time",
        name: "Average Pending Time"
      },
      {
        key: "avg_rate",
        name: "Average Rating"
      },
    ];

  const agentRatingColumn =[
    {
      key: "email",
      name: "Agent Email", 
    },
    {
      key: "average_rating",
      name: "Average Rating"
    },
    {
      key: "rating_msg",
      name: "Rating Message"
    }
  ]

  const agentTableColumns =[
    {
      key: "email",
      name: "Agent Email",
    },
    {
      key: "date",
      name: "Date",
    },
    {
      key: "time",
      name: "Time Period"
    },
    {
      key: "status",
      name: "Status",
    },
    {
      key: "timeZone",
      name: "Time Zone",
    },
  ]
  

  return (
    <div className="space-y-5">
      {submitLoading ? (
        <Loading />
      ) : (
        <>
        <div className="pt-3">
          <h2 className="w-full pr-2 pl-2 mb-4 text-center">
            <span className="text-3xl font-bold text-gray-400">View your agents now</span>
          </h2>
          <div className="grid lg:grid-cols-3 gap-5 px-5 pb-5 pt-4 border border-gray-600 rounded-md">
            <StatsCard
              title="Active Agents"
              subtitle={activeAgent}
              icon={User}
            />
            <StatsCard
              title="Dropped Queues"
              subtitle={dropped}
              icon={MessageCircle}
            />
            <StatsCard
              title="Closed Sessions"
              subtitle={closed}
              icon={X}
            />
            <StatsCard
              title="Waiting Queues"
              subtitle={waiting}
              icon={Hourglass}
            />
            <StatsCard
              title="Pending sessions"
              subtitle={pending}
              icon={CircleDashed}
            />
            <StatsCard
              title="Current Active Chats"
              subtitle= {activeChats}
              icon={MessageCircle}
            />
          </div>
        </div>
          <div className="grid lg:grid-cols-1 gap-5">

            <div className="grid lg:grid-cols-2 gap-5">
              <StatsCard
                title="Average Waiting Time"
                subtitle={avgWaitingTime}
                icon={Clock}
                // data={
                //   avgWaitingTimeChart.series?.length
                //     ? avgWaitingTimeChart.series[0]
                //     : [...avgWaitingTimeChart.series]   
                // }
                // xLabels={[...avgWaitingTimeChart.xLabels]}
              />
              <StatsCard
                title="Sessions per day"
                subtitle={sessionsPerDay}
                icon={CalendarDays}
                // data={
                //   sessionsPerDayChart.series?.length
                //     ? sessionsPerDayChart.series[0]
                //     : [...sessionsPerDayChart.series]   
                // }
                // xLabels={[...sessionsPerDayChart.xLabels]}
              />
              <StatsCard
                title="Average Pending Time"
                subtitle={averagePendingTime[0]?.time?.toString() || "00:00:00"}
                icon={CircleDashed}
              /> 
              <StatsCard
                title="Average Rating"
                subtitle={avgRating[0]?.average_rating?.toString() || "0"}
                icon={Star}
              />
            </div>
          </div>
          {
            sessionsPerDayChart?.series[0]?.data?.length > 1 &&
          <ChartCard title="Sessions Per day">
            <LineChart
              data={[...sessionsPerDayChart.series]}
              xLabels={[...sessionsPerDayChart.xLabels]}
            />
          </ChartCard>
          }
          <ChartCard title="Sessions Per Weekday / Hour">
              <HeatmapChart data={weekDayHoursHeatMap || []} />
          </ChartCard>
          <ChartCard title="Active Agents / Hour">
              <HeatmapChart data={agentHeatMap || []} />
              <LineChart
                data={[...heapMapAgent.series]}
                xLabels={[...heapMapAgent.xLabels]}
              />
          </ChartCard>
          <div className="grid gap-3">
            <div className="grid xlg:grid-cols-2 gap-3">

            { channelData.series.length ? <ChartCard title="Sessions / Channel" className="p-5">
                 <SimpleDonutChart
                  data={[...channelData.series]}
                  xLabels={
                    channelData.xLabels
                      ? [...channelData.xLabels]
                      : []
                  }
                  channel ={true}
                />
                {/* <BarChart
                  data={[...channelData.series]}
                  xLabels={[...channelData.xLabels]}
                /> */}
              </ChartCard>: null
              }

              <ChartCard title="Session /ِ Agent">
                <BarChart
                  data={[...messagePerAgent.series]}
                  xLabels={[...messagePerAgent.xLabels]}
                />
              </ChartCard>

              <div className="grid xlg:grid-cols-2 gap-3" ></div>
              
              {/* the stored procedure needs some work */}
              <ChartCard title="Closed Sessions Report" className="p-5">
                <div className="flex space-x-4 w-full" >
                  <AgentsIdMenu selectedAgent={selectedAgents} setAgent={setAgent} agent={agent} all={true}/>
                  <ChannelMenu channel={channel} setChannel={setChannel}/>
                  <ClosedReasonsMenu reason={reason} setReason={setReason}/>
                </div>
                {/* {
                  agent && */}
                {/* // <BarChart */}
                {/* //   data={[...closedChartPerAgent.series]}
                //   xLabels={[...closedChartPerAgent.xLabels]}
                // /> */}
                {
                  !agentReasonTrue ? 
                  <SimpleDonutChart
                    data={[...closedChartPerAgent.series]}
                    xLabels={
                      closedChartPerAgent.xLabels
                        ? [...closedChartPerAgent.xLabels]
                        : []
                    }
                  />
                   :
                  <StatsCard
                    subtitle={closedChartPerAgent[0]?.recordCount || "0"}
                  />
                }
              </ChartCard>

              <ChartCard className="pb-10" title="Agents Reports">
                <ExportMenu
                  tableData={agentStatusTable}
                  tableColumns={tableColumns}
                  fileName={'Agents_Reports'}
                />
                <div className=" max-h-[500px] overflow-y-auto">
                  <MainTable
                    data={[...sortByDesc(agentStatusTable, "Messages")]}
                    columns={tableColumns}
                  />
                </div>
              </ChartCard>

              <ChartCard className="pb-10" title="Agents Activity">
                <AgentsIdMenu selectedAgent={selectedActiveAgent} setAgent={setActiveAgentSelected} agent={activeAgentSelected} all={true}/>
                <ExportMenu
                  tableData={activeAgentStatusTable}
                  tableColumns={agentTableColumns}
                  fileName={'Agents_Activity'}
                />
                <div className=" max-h-[500px] overflow-y-auto">
                  <MainTable
                    data={[...sortByDesc(activeAgentStatusTable, "Messages")]}
                    columns={agentTableColumns}
                  />
                </div>
              </ChartCard>

              <ChartCard className="pb-10" title="Agents Ratings">
              <AgentsIdMenu selectedAgent={selectedAgents} setAgent={setSelectedAgentRating} agent={selectedAgentRating} all={true}/>
                <ExportMenu
                  tableData={agentRatingTable}
                  tableColumns={agentRatingColumn}
                  fileName={'Agents_Ratting'}
                />
                <div className=" max-h-[500px] overflow-y-auto">
                  <MainTable
                    data={[...sortByDesc(agentRatingTable, "Messages")]}
                    columns={agentRatingColumn}
                  />
                </div>
              </ChartCard>
            </div>            
          </div>
        </>
      )}
    </div>
  );
  
};
const ExportMenu = ({ tableData, tableColumns, fileName }) => {
  const exportToExcel = ( tableData ) => {
    const worksheet = XLSX.utils.json_to_sheet(tableData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  
    XLSX.writeFile(workbook, `${fileName}.xlsx`);
  };
  return (
    <div className="flex justify-end relative">
      <Menubar.Root>
        <Menubar.Menu>
          <Menubar.Trigger asChild>
            <button aria-label="Export options" className="relative">
              <MoreVertical size={24} />
            </button>
          </Menubar.Trigger>
          <Menubar.Content className="absolute z-50 mt-2 bg-black text-white shadow-lg rounded-md w-48 right-0">
            <Menubar.Item
              onSelect={() => { exportToExcel([...sortByDesc(tableData, "Messages")]) }}
              className="block px-4 py-2 text-sm hover:bg-gray-800 cursor-pointer"
            >
              Export to Excel
            </Menubar.Item>
            <Menubar.Item>
              <CSVLink
                data={([...sortByDesc(tableData, "Messages")])}
                headers={tableColumns}
                filename={`${fileName}.csv`}
                className="block px-4 py-2 text-sm hover:bg-gray-800 cursor-pointer"
              >
                Export to CSV
              </CSVLink>
            </Menubar.Item>
          </Menubar.Content>
        </Menubar.Menu>
      </Menubar.Root>
    </div>
  );
};

export default memo(LiveChatDashboard);
