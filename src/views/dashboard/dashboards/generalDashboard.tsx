import { FC, memo } from "react";
import { useEffect, useState } from "react";
import useBotStore from "store/bot/bot.store";
import {
  getLastTransactionsTable,
  getMostAskedQuestionsPerCountry,
  getTransactionCalculated,
  getTransactionNotAnswered,
} from "apis/transactions.api";
import {
  apexchartsDataFormConverter,
  convert,
  distinct,
  numberWithCommas,
  sortByAsc,
  sortByDesc,
  sum,
  RatingConverter,
} from "helpers/helper";
import { HeatmapChart, SimpleDonutChart } from "../charts";
import {
  Cake,
  CalendarDays,
  MessageCircle,
  Users,
  Voicemail,
  StarIcon,
  FacebookIcon,
  InstagramIcon,
  Globe2Icon,
  EyeIcon,
} from "lucide-react";
import { CheckpointsDetails } from "../components/checkpoints/checkpointDetails";

import {
  NMessagesSelect,
  StatsCard,
  FeedbackCard,
  DialogSelect,
} from "../components";
import { Loading } from "../loading";
import ChartCard from "../components/chart.card";
import LineChart from "../charts/line.chart";
import BarChart from "../charts/bar.chart";
import { MainTable } from "common/components/tables/main.table";
import CountriesMenu from "../components/countriesMenu";
import dynamic from "next/dynamic";

import { UpdatedRatingType } from "../components/feedback-rating.card";
import transformDate from "helpers/transformDate";
import JettBookingTable from "../components/jettBookingTable";
import WhatsappSVG from "common/icons/WhatsappSVG";
import CustomDashboard, {
  TFilterdCustomDashboard,
} from "../components/customDashboard/custom-dashboard";
import { getCustomDashboard } from "apis/custom-dashboard.api";
import CustomTable from "../components/customDashboard/CustomTable";
import CustomCard from "../components/customDashboard/CustomCard";
import ChatbotReport from "../components/chatbot.reports";
import { DateRange } from "react-day-picker";
const GeoMap = dynamic(() => import("../charts/geo.chart"), { ssr: false });
import ChatModal from "../components/chatModal";
import { FullModal } from "common/components/modals";
import DynamicFormReport from "../components/dynamic.form.reports";
import useDynamicFormStore from "store/dynamicForm/dynamicForm.store";
import { getSchemaResponses } from "apis/dynamicForm.api";
import { CheckpointView } from "../components/checkpoints/checkPointView";

interface generalDashboardProps {
  selectedPeriod: number;
  submitLoading: boolean;
  setSubmitLoading: (value: boolean) => void;
  filterDate: DateRange;
}

interface StartDate {
  start_day: number;
  start_month: number;
  start_year: number;
}
interface EndDate {
  end_day: number;
  end_month: number;
  end_year: number;
}

const GeneralDashboard: FC<generalDashboardProps> = ({
  selectedPeriod,
  submitLoading,
  setSubmitLoading,
  filterDate,
}) => {
  const { get_all_dynamic_form_schemas, schemas } = useDynamicFormStore();
  const bot = useBotStore((state) => state.bot);
  const [dynamicFormData, setDynamicFormData] = useState([]);

  const fetchSchemaData = async () => {
    get_all_dynamic_form_schemas(bot.bot_id);
  };

  useEffect(() => {
    fetchSchemaData();
  }, [bot.bot_id]);

  const getSchemaData = async (id) => {
    const temp = await getSchemaResponses(id);
    return temp;
  };

  useEffect(() => {
    const fetchData = async () => {
      const allData = await Promise.all(
        schemas.map(async (form) => {
          const data = await getSchemaData(form.dynamic_form_schema_id);
          const filteredData = Array.isArray(data)
            ? data.filter((item) => {
                if (!item.createdAt) return false;
                
                const itemDate = new Date(item.createdAt);
                const itemTime = itemDate.getTime();
                const now = new Date();
                const todayStart = new Date(now.setHours(0, 0, 0, 0)).getTime();
                const todayEnd = new Date(now.setHours(23, 59, 59, 999)).getTime();
                const period = Number(selectedPeriod);
  
                switch (period) {
                  case 0:
                    return itemTime >= todayStart && itemTime <= todayEnd;
                  
                  case 1:
                    const yesterdayStart = todayStart - 86400000;
                    const yesterdayEnd = todayEnd - 86400000;
                    return itemTime >= yesterdayStart && itemTime <= yesterdayEnd;
                  
                  case 7:
                    return itemTime >= todayStart - (6 * 86400000) && itemTime <= todayEnd;
                  
                  case 30:
                    return itemTime >= todayStart - (29 * 86400000) && itemTime <= todayEnd;
                  
                  case 90:
                    return itemTime >= todayStart - (89 * 86400000) && itemTime <= todayEnd;
                  
                  case 365:
                    return itemTime >= todayStart - (364 * 86400000) && itemTime <= todayEnd;
                  
                  case 10000:
                    return true;
                  
                  default:
                    if (filterDate?.from || filterDate?.to) {
                      const fromDate = filterDate?.from ? new Date(filterDate.from) : null;
                      const toDate = filterDate?.to ? new Date(filterDate.to) : null;
                      
                      if (fromDate) fromDate.setHours(0, 0, 0, 0);
                      if (toDate) toDate.setHours(23, 59, 59, 999);
                      
                      const fromTime = fromDate?.getTime() || 0;
                      const toTime = toDate?.getTime() || Date.now();
                      
                      return itemTime >= fromTime && itemTime <= toTime;
                    }
                    return true;
                }
              })
            : [];
          
          return {
            id: form.dynamic_form_schema_id,
            data: filteredData,
          };
        })
      );
      setDynamicFormData(allData);
    };

    fetchData();
  }, [schemas, filterDate, selectedPeriod]);


  const [transactionsDataGrouped, setTransactionsDataGrouped] = useState([]);
  const [width, setWidth] = useState(window.innerWidth);
  const [nMessages, setNMessages] = useState(15);
  const [totalTransactions, setTotalTransaction] = useState("");
  const [avgTransactions, setAvgTransactions] = useState(null);
  const [daysSinceLaunch, setDaysSinceLaunch] = useState(null);
  const [isLaunch, setIsLaunch] = useState(false);
  const [transactionAnswered, setTransactionAnswered] = useState({
    series: [],
    xLabels: [],
  });
  const [transactionsByCategory, setTransactionsByCategory] = useState({
    series: [],
    xLabels: [],
  });
  const [transactionPerDay, setTransactionPerDay] = useState({
    series: [],
    xLabels: [],
  });

  const [trendOfAnswered, setTrendOfAnswered] = useState({
    series: [],
    xLabels: [],
  });
  const [usersPerYear, setUsersPerYear] = useState({
    series: [],
    xLabels: [],
  });
  const [checkpoints, setCheckpoints] = useState([]);
  const [selectedDialog, setSelectedDialog] = useState("");
  const [checkpointsStats, setCheckpointsStats] = useState({
    series: [],
    xLabels: [],
  });

  const [lastTransactions, setlastTransactions] = useState([]);
  const [transactionNotAnswered, setTransactionNotAnswered] = useState([]);
  const [transactionNotAnsweredIds, setTransactionNotAnsweredIds] = useState(
    []
  );
  const [selectedId, setSelectedId] = useState([]);
  const [showIdsWindow, setShowIdsWindow] = useState(false);
  const [conversations, setConversations] = useState([]);
  const [ratingData, setRatingData] = useState<UpdatedRatingType>(null);
  const [transactionsByFunctionRequested, setTransactionsByFunctionRequested] =
    useState({
      series: [],
      xLabels: [],
    });
  const [totalVoiceTransaction, setTotalVoiceTransaction] = useState("");
  const [countryTransactions, setCountryTransactions] = useState([]);
  const [trendOfUserMessages, setTrendOfUserMessages] = useState({
    series: [],
    xLabels: [],
  });
  const [channelData, setChannelData] = useState({
    series: [],
    xLabels: [],
  });
  const [countries, setCountries] = useState([]);
  const [
    topQuestionPerCountryGroupedData,
    setTopQuestionPerCountryGroupedData,
  ] = useState([]);
  const [topQuestionPerCountry, setTopQuestionPerCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [weekDayHoursHeatMap, setWeekDayHoursHeatMap] = useState([]);
  const [sessionsCount, setSessionsCount] = useState([]);
  const [webSessions, setWebSessions] = useState(0);
  const [totalSessions, setTotalSessions] = useState(0);
  const [checkPointStartDate, setStartDate] = useState<StartDate>({
    start_day: new Date().getDate() - 1,
    start_month: new Date().getMonth() + 1,
    start_year: new Date().getFullYear(),
  });
  const [checkPointEndDate, setEndDate] = useState<EndDate>({
    end_day: new Date().getDate(),
    end_month: new Date().getMonth() + 1,
    end_year: new Date().getFullYear(),
  });

  useEffect(() => {
    convertDateRangeToComponents();
  }, [filterDate, selectedPeriod]);

  const convertDateRangeToComponents = () => {
    const now = new Date();
    switch (selectedPeriod) {
      case 0:
        setStartDate({
          start_day: now.getDate(),
          start_month: now.getMonth() + 1,
          start_year: now.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return;

      case 1:
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        setStartDate({
          start_day: yesterday.getDate(),
          start_month: yesterday.getMonth() + 1,
          start_year: yesterday.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return;

      case 7:
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(now.getDate() - 7);
        setStartDate({
          start_day: sevenDaysAgo.getDate(),
          start_month: sevenDaysAgo.getMonth() + 1,
          start_year: sevenDaysAgo.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return;
      case 30:
        const lastMonth = new Date(now);
        lastMonth.setDate(now.getDate() - 30);
        setStartDate({
          start_day: lastMonth.getDate(),
          start_month: lastMonth.getMonth() + 1,
          start_year: lastMonth.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return;
      case 90:
        const lastThreeMoths = new Date(now);
        lastThreeMoths.setDate(now.getDate() - 90);
        setStartDate({
          start_day: lastThreeMoths.getDate(),
          start_month: lastThreeMoths.getMonth() + 1,
          start_year: lastThreeMoths.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return;
      case 365:
        const lastYear = new Date(now);
        lastYear.setDate(now.getDate() - 365);
        setStartDate({
          start_day: lastYear.getDate(),
          start_month: lastYear.getMonth() + 1,
          start_year: lastYear.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return {};
      case 10000:
        const allData = new Date(now);
        allData.setDate(now.getDate() - 10000);
        setStartDate({
          start_day: allData.getDate(),
          start_month: allData.getMonth() + 1,
          start_year: allData.getFullYear(),
        });
        setEndDate({
          end_day: now.getDate(),
          end_month: now.getMonth() + 1,
          end_year: now.getFullYear(),
        });
        return;
      case 22:
        const fromDate = new Date(filterDate.from);
        const toDate = new Date(filterDate.to);
        setStartDate({
          start_day: fromDate.getUTCDate() + 1,
          start_month: fromDate.getUTCMonth() + 1,
          start_year: fromDate.getUTCFullYear(),
        });
        setEndDate({
          end_day: toDate.getUTCDate() + 1,
          end_month: toDate.getUTCMonth() + 1,
          end_year: toDate.getUTCFullYear(),
        });
        return;
    }
  };


  var currentDate = new Date();
  var targetDate; //= new Date();
  var endDate; //= new Date();

  const getData = () => {
    const period = +selectedPeriod;

    if (selectedPeriod != 22) {
      targetDate = new Date();
      const period = +selectedPeriod;
      targetDate.setDate(targetDate.getDate() - period);
    }

    const day = targetDate?.getDate();
    const month = targetDate?.getMonth() + 1;
    const year = targetDate?.getFullYear();

    const endDay = endDate?.getDate();
    const endMonth = endDate?.getMonth() + 1;
    const endYear = endDate?.getFullYear();
    const bot_id = bot.bot_id;
    const weekDays = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const hours = [
      "12 AM",
      "1 AM",
      "2 AM",
      "3 AM",
      "4 AM",
      "5 AM",
      "6 AM",
      "7 AM",
      "8 AM",
      "9 AM",
      "10 AM",
      "11 AM",
      "12 PM",
      "1 PM",
      "2 PM",
      "3 PM",
      "4 PM",
      "5 PM",
      "6 PM",
      "7 PM",
      "8 PM",
      "9 PM",
      "10 PM",
      "11 PM",
    ];
    getTransactionNotAnswered(
      bot_id,
      day,
      month,
      year,
      endDay,
      endMonth,
      endYear
    ).then((data) => {
      if (data) {
        setTransactionNotAnswered(
          data?.filter((a) => a?.graph === "number_of_qustions")
        );
        setTransactionNotAnsweredIds(
          data?.filter((a) => a?.graph === "conversation_ids")
        );
      } else {
        setTransactionNotAnswered([]);
        setTransactionNotAnsweredIds([]);
      }
    });
    getTransactionCalculated(
      bot_id,
      day,
      month,
      year,
      endDay,
      endMonth,
      endYear
    ).then((data) => {
      setSubmitLoading(false);
      const weekDayHeatmapData = [];
      const heatmapData = data?.filter((a) => a?.graph === "heatmap");
      const overallData = data?.find((a) => a?.graph === "overall");
      const rateData = data?.filter((a) => a?.graph === "rate");
      const trendsData = data
        ?.filter((a) => a.graph === "trends")
        ?.slice(0, 10)
        ?.reverse();
      const total_users_per_year = data?.filter(
        (a) => a?.graph === "total_users_per_year"
      );
      const checkpointData = data?.filter((a) => a.graph === "checkpoints");

      const functionRequestedData = data?.filter(
        (a) => a.graph === "per_function"
      );
      const channelDataArr = data?.filter((a) => a.graph === "per_channel");
      const perCountryData = data?.filter((a) => a.graph === "per_country");
      const launchDate = new Date(
        data?.find((a) => a.graph === "since_launch")?.date
      );
      const _launchDate = data?.find((a) => a.graph === "since_launch")?.date;
      if (_launchDate) {
        setIsLaunch(true);
      } else {
        setIsLaunch(false);
      }

      const daysSinceLaunch = Math.round(
        (currentDate.getTime() - launchDate.getTime()) / (1000 * 3600 * 24)
      );

      const sessionsCounterData = data?.filter(
        (a) => a.graph === "sessions_channel"
      );
      const channelSessions = sessionsCounterData?.map((a) => {
        return {
          channel: a.tr_channel,
          sessions: a.total_session_count,
        };
      });
      setSessionsCount(channelSessions);
      const trendDates = trendsData?.map((a) => a.date);

      for (var i = 0; i < weekDays?.length; i++) {
        weekDayHeatmapData?.push({
          name: weekDays[i],
          data: hours.map((hour, index) => {
            const obj = heatmapData?.find(
              (item) => item.weekday === i + 1 && item.hour === index
            );
            return {
              x: hour,
              y: obj?.messages || 0,
            };
          }),
        });
      }

      setUsersPerYear(() => {
        const total_users_per_year_Data = [
          {
            name: "Total Users per Year",
            data: total_users_per_year?.map((item) => item.users),
          },
        ]; //|| [];
        const xLabels =
          total_users_per_year?.map((item) => item.year?.toString()) || [];
        return { series: total_users_per_year_Data, xLabels };
      });
      setTransactionsDataGrouped(data);
      setTotalTransaction(overallData?.messages);
      setConversations(overallData?.users);
      setTotalVoiceTransaction(overallData?.voice_messages);
      setRatingData(RatingConverter(rateData));

      const groupedCheckpointData = checkpointData.reduce((acc, checkpoint) => {
        const dialog_name = checkpoint.dialog_name;
        if (!acc[dialog_name]) {
          acc[dialog_name] = [];
        }
        acc[dialog_name].push(checkpoint);
        return acc;
      }, {});
      setCheckpoints(groupedCheckpointData);
      if (selectedDialog) {
        setCheckpointsStats(prepareData(groupedCheckpointData[selectedDialog]));
      } else {
        const firstDialog = Object.keys(groupedCheckpointData)[0];
        setSelectedDialog(firstDialog);
        setCheckpointsStats(prepareData(groupedCheckpointData[firstDialog]));
      }

      if (trendsData.length > 0) {
        setTrendOfAnswered(
          apexchartsDataFormConverter(
            [
              ...trendsData.map((a, index) => {
                return {
                  date: trendDates[index],
                  percentage: parseFloat(a.answeredPer.toFixed(2)),
                };
              }),
            ],
            "date"
          )
        );

        setTransactionPerDay(
          apexchartsDataFormConverter(
            [
              ...trendsData.map((a, index) => {
                return {
                  date: trendDates[index],
                  messages: a.messages,
                  users: a.users,
                };
              }),
            ],
            "date"
          )
        );

        setTrendOfUserMessages(
          apexchartsDataFormConverter(
            [
              ...trendsData.map((a, index) => {
                return {
                  date: trendDates[index],
                  average: a.avgPerUser
                    ? parseFloat(a.avgPerUser?.toFixed(2))
                    : 0,
                };
              }),
            ],
            "date"
          )
        );
      } else {
        setTrendOfAnswered({
          series: [],
          xLabels: [],
        });
        setTransactionPerDay({
          series: [],
          xLabels: [],
        });
        setTrendOfUserMessages({
          series: [],
          xLabels: [],
        });
      }

      if (functionRequestedData.length > 0) {
        setTransactionsByFunctionRequested(
          apexchartsDataFormConverter(
            [
              ...functionRequestedData.map((a, index) => {
                return {
                  function: a.function_requested,
                  messages: a.messages,
                };
              }),
            ],
            "function"
          )
        );
      } else {
        setTransactionsByFunctionRequested({
          series: [],
          xLabels: [],
        });
      }
      if (channelDataArr.length > 0) {
        const webUsers = channelDataArr.find(
          (a) => a.tr_channel === "web"
        )?.users;
        setWebSessions(webUsers);
        const fbSessions =
          channelSessions.find((a) => a.channel === "facebook")?.sessions || 0;
        const instaSessions =
          channelSessions.find((a) => a.channel === "instagram")?.sessions || 0;

        const sessionsTotalExceptWa = fbSessions + instaSessions + webUsers;
        console.log(sessionsTotalExceptWa);
        setTotalSessions(sessionsTotalExceptWa);
        setChannelData(
          apexchartsDataFormConverter(
            [
              ...channelDataArr.map((a, index) => {
                return {
                  channel: a.tr_channel,
                  messages: a.messages,
                  users: a.users,
                };
              }),
            ],
            "channel"
          )
        );
      } else {
        setChannelData({
          series: [],
          xLabels: [],
        });
      }

      setCountryTransactions([]);
      setTimeout(() => {
        setCountryTransactions([
          ...perCountryData.map((a) => {
            return {
              Country: a.tr_country,
              Messages: a.messages,
              Users: a.users,
            };
          }),
        ]);
      }, 100);

      setTransactionAnswered(
        apexchartsDataFormConverter(
          [
            {
              label: "No",
              ccount: parseFloat(
                (1 - parseFloat(overallData?.answeredPer?.toFixed(2))).toFixed(
                  2
                )
              ),
            },
            {
              label: "Yes",
              ccount: parseFloat(overallData?.answeredPer?.toFixed(2)),
            },
          ],
          "label"
        )
      );

      setCountries(perCountryData?.map((a) => a.tr_country));
      setWeekDayHoursHeatMap(weekDayHeatmapData);
      setDaysSinceLaunch(daysSinceLaunch);
      setAvgTransactions(
        +period === 0
          ? Math.round(overallData?.messages)
          : targetDate.getTime() > launchDate.getTime()
          ? Math.round(
              overallData?.messages /
                Math.round(
                  (currentDate.getTime() - targetDate.getTime()) /
                    (1000 * 3600 * 24)
                )
            )
          : Math.round(overallData?.messages / daysSinceLaunch)
      );
    });
  };

  useEffect(() => {
    targetDate = filterDate?.from || new Date();
    endDate = filterDate?.to;
    if (filterDate?.from && filterDate?.to) {
      getDashboardData();
    }
  }, [filterDate?.to]);

  const calculateCountriesStats = (d, country) => {
    targetDate?.setHours(0, 0, 0, 0);
    endDate?.setHours(0, 0, 0, 0);
    var data = d.filter((a) => {
      const date = new Date(`${a.year}-${a.month}-${a.day}`);
      date.setHours(0, 0, 0, 0);
      return (
        date.getTime() >= targetDate?.getTime() &&
        date.getTime() <= endDate?.getTime() &&
        a.tr_country.toLowerCase() === "jordan"
      );
    });

    const questions = distinct(data, "user_q").map((a) => a.user_q);
    const results = [];
    for (var i = 0; i < questions?.length; i++) {
      const count = sum(
        data.filter((a) => a.user_q === questions[i]),
        "count"
      );
      results.push({
        question: questions[i],
        count: count,
      });
    }
    setTopQuestionPerCountry(sortByDesc(results, "count").slice(0, 10));
  };
  const getDashboardData = () => {
    const bot_id = bot.bot_id;
    setSubmitLoading(true);
    getLastTransactionsTable(bot_id).then((data) => {
      if (Boolean(data)) {
        setlastTransactions(data);
      }
    });
    getData();
    getMostAskedQuestionsPerCountry(bot_id).then((data) => {
      if (Boolean(data?.length)) {
        setTopQuestionPerCountryGroupedData(data);
      }
    });
  };

  const [temp, setTemp] = useState(1);

  useEffect(() => {
    if (selectedPeriod != 22) {
      setTemp(selectedPeriod);
    }
  }, [selectedPeriod]);

  useEffect(() => {
    let unmounted = false;

    if (!selectedCountry) {
      setSelectedCountry(
        countries?.filter((a) => {
          return Boolean(a);
        })[0]
      );
    }
    if (transactionsDataGrouped?.length) {
      if (!unmounted) {
        getData();
      }
      calculateCountriesStats(
        topQuestionPerCountryGroupedData,
        selectedCountry
          ? selectedCountry
          : countries?.filter((a) => {
              return Boolean(a);
            })[0]
      );
    } else {
      if (!unmounted) {
        getDashboardData();
      }
      window.addEventListener("resize", () => {
        setWidth(window.innerWidth);
      });
    }

    return () => {
      unmounted = true;
    };
  }, [temp]);

  const onChangeNMessages = (value) => {
    const bot_id = bot.bot_id;
    setNMessages(+value);
    getLastTransactionsTable(bot_id, +value).then((data) => {
      if (Boolean(data?.length)) {
        setlastTransactions(data);
      }
    });
  };
  const prepareData = (data) => {
    const tagsArray = [];
    const seriesdata = [];
    if (!data) return { xLabels: [], series: [] };
    for (const set of data) {
      if (!tagsArray.includes(set.tag)) {
        tagsArray.push(set.tag);
      }
      if (seriesdata.find((s) => s.name === set.channel)) {
        seriesdata.find((s) => s.name === set.channel).data.push(set.users);
      } else {
        seriesdata.push({ name: set.channel, data: [set.users] });
      }
    }
    return { xLabels: tagsArray, series: seriesdata };
  };

  const onChangeDialog = (value) => {
    setSelectedDialog(value);
    setCheckpointsStats(prepareData(checkpoints[value]));
  };

  const [customDashboard, setCustomDashboard] =
    useState<TFilterdCustomDashboard>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (bot.bot_id) {
        const data = await getCustomDashboard(bot.bot_id);
        if (data?.length > 0) {
          const tables =
            data.filter((item) => item.widget_type === "table") || [];
          const cards =
            data.filter((item) => item.widget_type === "card") || [];
          const lineCharts =
            data.filter((item) => item.widget_type === "line-chart") || [];
          setCustomDashboard({ tables, cards, lineCharts });
          get_all_dynamic_form_schemas(bot.bot_id);
        }
      }
    };

    fetchData();
  }, [bot.bot_id]);

  const openNotAnsweredModel = [
    {
      label: "View",
      onClick: (data) => {
        // window.location.hash = "#chatbotReports";
        setShowIdsWindow(true);
        const ids = transactionNotAnsweredIds
          .map((s) => {
            if (s.Question.trim() === data.Question.trim()) {
              return s;
            }
            return null;
          })
          .filter(Boolean);
        setSelectedId(ids);
      },
      icon: EyeIcon,
    },
  ];

  return (
    <div className="space-y-5">
      {submitLoading ? (
        <Loading />
      ) : (
        <>
          {/* <CustomDashboard /> */}
          <div className="grid lg:grid-cols-3 gap-5">
            <StatsCard
              title="Messages per period"
              subtitle={
                numberWithCommas(totalTransactions)
                  ? numberWithCommas(totalTransactions)
                  : 0
              }
              icon={MessageCircle}
              data={
                transactionPerDay.series?.length
                  ? transactionPerDay.series[0]
                  : [...transactionPerDay.series]
              }
              xLabels={transactionPerDay.xLabels.length>1 && [...transactionPerDay.xLabels]}
              hover="User Initiated Messages"
            />
            <StatsCard
              title="Voices per period"
              subtitle={
                totalVoiceTransaction
                  ? numberWithCommas(totalVoiceTransaction)
                  : "0"
              }
              icon={Voicemail}
              hover="User Initiated Voice notes"
            />
            <StatsCard
              title="Number Of Users Per Period"
              subtitle={
                numberWithCommas(conversations)
                  ? numberWithCommas(conversations)
                  : 0
              }
              hover="Unique Chatbot User During Selected Time Period"
              icon={Users}
              data={
                transactionPerDay.series?.length
                  ? transactionPerDay.series[1]
                  : [...transactionPerDay.series]
              }
              xLabels={transactionPerDay.xLabels.length>1 && [...transactionPerDay.xLabels]}
            />
            <StatsCard
              title="Average per day"
              subtitle={
                numberWithCommas(avgTransactions)
                  ? numberWithCommas(avgTransactions)
                  : 0
              }
              icon={CalendarDays}
              hover="Average of User-Initiated Messages"
            />
            <StatsCard
              title="Time since launch"
              subtitle={
                isLaunch
                  ? daysSinceLaunch >= 365
                    ? `${convert(daysSinceLaunch)}`
                    : `${numberWithCommas(daysSinceLaunch)} days`
                  : "Not published yet"
              }
              hover="Since First Publish"
              icon={Cake}
            />
            {/* <FeedbackCard
              title="Customer Rating"
              icon={StarIcon}
              data={ratingData}
            /> */}
            <StatsCard
              title="Whatsapp Sessions"
              subtitle={
                numberWithCommas(
                  sessionsCount.find((a) => a.channel === "whatsapp")?.sessions
                )
                  ? numberWithCommas(
                      sessionsCount.find((a) => a.channel === "whatsapp")
                        ?.sessions
                    )
                  : 0
              }
              icon={WhatsappSVG}
              hover="User Initiated Whatsapp Sessions"
            />
            <StatsCard
              title="Facebook Sessions"
              subtitle={
                numberWithCommas(
                  sessionsCount.find((a) => a.channel === "facebook")?.sessions
                )
                  ? numberWithCommas(
                      sessionsCount.find((a) => a.channel === "facebook")
                        ?.sessions
                    )
                  : 0
              }
              icon={FacebookIcon}
              hover="User Initiated Facebook Sessions"
            />
            <StatsCard
              title="Instagram Sessions"
              subtitle={
                numberWithCommas(
                  sessionsCount.find((a) => a.channel === "instagram")?.sessions
                )
                  ? numberWithCommas(
                      sessionsCount.find((a) => a.channel === "instagram")
                        ?.sessions
                    )
                  : 0
              }
              icon={InstagramIcon}
              hover="User Initiated Instagram Sessions"
            />
            <StatsCard
              title="Web Sessions"
              subtitle={
                numberWithCommas(webSessions)
                  ? numberWithCommas(webSessions)
                  : 0
              }
              icon={Globe2Icon}
              hover="User Initiated Web Sessions"
            />

            {schemas.map((s, index) => {
              const matchedForm = dynamicFormData.find(
                (d) => d.id === s.dynamic_form_schema_id
              );
              return (
                <StatsCard
                  key={index}
                  title={s?.title || ""}
                  subtitle={matchedForm?.data?.length || "0"}
                  // icon={Clipboard}
                  hover="Custom Chatbot Labels"
                />
              );
            })}

            {bot?.bot_id === 666 ? (
              <StatsCard
                title="Total Sessions Excluding Whatsapp"
                subtitle={
                  numberWithCommas(totalSessions)
                    ? numberWithCommas(totalSessions)
                    : 0
                }
                icon={Globe2Icon}
              />
            ) : null}

            {customDashboard?.cards?.length > 0 ? (
              <CustomCard cards={customDashboard.cards} />
            ) : null}
          </div>
          {bot?.bot_id === 666 ? <JettBookingTable /> : null}
          {Object.keys(checkpoints)?.length ? (
            // <ChartCard
            //   className="pb-10"
            //   title={`Dialog Checkpoints statistics`}
            //   headerComponent={
            //     <>
            //       <div className="flex items-center space-x-4">
            //         <span className="text-gray-300 font-medium">
            //           Select a dialog:
            //         </span>
            //         <DialogSelect
            //           onChangeDialog={onChangeDialog}
            //           dialog={selectedDialog}
            //           dialogs={Object.keys(checkpoints)}
            //         />
            //       </div>
            //     </>
            //   }
            // >
            //   <div className="relative">
            //     <BarChart
            //       data={[...checkpointsStats?.series]}
            //       xLabels={[...checkpointsStats?.xLabels]}
            //     />
            //     <div className="absolute right-0 mr-2">
            //       <CheckpointsDetails />
            //     </div>
            //     <div style={{ marginBottom: "20px" }}></div>
            //   </div>
            // </ChartCard>
            <CheckpointView startDate={checkPointStartDate} endDate={checkPointEndDate}/>
          ) : null}
          <div className="grid gap-3">
            <ChartCard 
              title="Messages Per Weekday / Hour"
              hover={
                "Lighter colors show higher values, while darker colors show lower ones. \nRead each row from left to right to see the trends by weekday."
              }
            >
              <HeatmapChart data={[...weekDayHoursHeatMap]} />
            </ChartCard>
            <div className="grid lg:grid-cols-2 gap-3">
              <ChartCard title="Messages / Users Per Day">
                <LineChart
                  data={[...transactionPerDay.series]}
                  xLabels={[...transactionPerDay.xLabels]}
                />
              </ChartCard>
              <ChartCard title="Trend of Messages Per User">
                <LineChart
                  data={[...trendOfUserMessages.series]}
                  xLabels={[...trendOfUserMessages.xLabels]}
                />
              </ChartCard>

              {customDashboard?.lineCharts?.map((lineChart) => {
                const chart_data =
                  lineChart?.queryResults[0]?.chart_data || "{}";

                const series = [
                  {
                    name: lineChart.title,
                    type: lineChart.widget_type === "line-chart" ? "line" : "",
                    data: [],
                  },
                ];
                let xLabels = [""];
                if (chart_data) {
                  const parsedData = JSON.parse(chart_data);
                  if (parsedData?.length > 0) {
                    const convertedData = JSON.parse(chart_data)?.map(
                      (point: { x: string; y: string }, i: number) => ({
                        x: point.x,
                        y: Number(point.y) || i,
                      })
                    );
                    series[0].data = convertedData;
                    xLabels = convertedData?.map((point) => point.x);
                  }
                }
                return (
                  <ChartCard key={lineChart.title} title={lineChart.title}>
                    {series[0]?.data.length > 0 ? (
                      <>
                        <p className="text-lg">{lineChart.description}</p>
                        <LineChart
                          data={series}
                          xLabels={xLabels}
                          xLabel={lineChart.x_label}
                          yLabel={lineChart.y_label}
                        />
                      </>
                    ) : null}
                  </ChartCard>
                );
              })}
            </div>

            <ChartCard title="Trend of Answered Questions %">
              <LineChart
                data={[...trendOfAnswered.series]}
                xLabels={[...trendOfAnswered.xLabels]}
              />
            </ChartCard>
            <div className="grid lg:grid-cols-2 gap-3">
              <ChartCard title="Messages / Users Per Channel">
                <BarChart
                  data={[...channelData?.series]}
                  xLabels={[...channelData?.xLabels]}
                />
              </ChartCard>
              <ChartCard title="Messages Answered">
                <SimpleDonutChart
                  data={[...transactionAnswered.series]}
                  xLabels={
                    transactionAnswered.xLabels
                      ? [...transactionAnswered.xLabels]
                      : []
                  }
                />
              </ChartCard>
            </div>
            <ChartCard title="Messages By Function Requested">
              <BarChart
                data={[...transactionsByFunctionRequested?.series]}
                xLabels={[...transactionsByFunctionRequested?.xLabels]}
              />
            </ChartCard>
            <ChartCard title="Total Users Per Year">
              <BarChart
                data={usersPerYear?.series || []}
                xLabels={usersPerYear?.xLabels || []}
              />
            </ChartCard>
            <div className="grid lg:grid-cols-2 gap-3">
              <ChartCard className="pb-10" title="Not Answered">
                <div className=" max-h-[500px] overflow-y-auto">
                  <MainTable
                    actions={openNotAnsweredModel}
                    data={transactionNotAnswered}
                    columns={[
                      {
                        key: "Question",
                        name: "Question",
                      },
                      {
                        key: "Count",
                        name: "Count",
                      },
                    ]}
                  />
                </div>
              </ChartCard>
              <NotAnswered
                selectedId={selectedId}
                showIdsWindow={showIdsWindow}
                setShowIdsWindow={setShowIdsWindow}
              />
              <ChartCard
                className="pb-10"
                title={`Last ${nMessages} Messages`}
                headerComponent={
                  <NMessagesSelect
                    onChangeNMessages={onChangeNMessages}
                    NMessages={nMessages}
                  />
                }
              >
                <div className="flex flex-col gap-2">
                  <div className=" max-h-[500px] overflow-y-auto">
                    <MainTable
                      data={[...lastTransactions]}
                      columns={[
                        {
                          key: "user_q",
                          name: "Question",
                        },
                        {
                          key: "bot_a",
                          name: "Answer",
                        },
                      ]}
                    />
                  </div>
                </div>
              </ChartCard>
              <ChartCard className="pb-10" title="Messages / Users Per Country">
                <div className=" max-h-[500px] overflow-y-auto">
                  <MainTable
                    data={[...sortByDesc(countryTransactions, "Messages")]}
                    columns={[
                      {
                        key: "Country",
                        name: "Country",
                      },
                      {
                        key: "Messages",
                        name: "Messages",
                      },
                      {
                        key: "Users",
                        name: "Users",
                      },
                    ]}
                  />
                </div>
              </ChartCard>
              <ChartCard
                className="pb-10"
                title="Top 10 Asked Questions Per Country"
                headerComponent={
                  <CountriesMenu
                    value={selectedCountry}
                    countries={[
                      ...countries
                        ?.filter((a) => {
                          return Boolean(a);
                        })
                        .map((a) => {
                          return {
                            value: a,
                            label: a,
                          };
                        }),
                    ]}
                    onSelect={(value) => {
                      setSelectedCountry(value);
                      calculateCountriesStats(
                        topQuestionPerCountryGroupedData,
                        value
                      );
                    }}
                  />
                }
              >
                <div className=" max-h-[500px] overflow-y-auto">
                  <MainTable
                    data={[...topQuestionPerCountry]}
                    columns={[
                      {
                        key: "question",
                        name: "Question",
                      },
                      {
                        key: "count",
                        name: "Count",
                      },
                    ]}
                  />
                </div>
              </ChartCard>
            </div>
            <div id="chatbotReports">
              <ChartCard title="Chatbot User Report">
                <ChatbotReport />
              </ChartCard>
            </div>
            {schemas.length ? (
              <ChartCard title="Dynamic Report">
                <DynamicFormReport />
              </ChartCard>
            ) : (
              <div></div>
            )}
            <ChartCard title="Messages per country">
              <GeoMap data={countryTransactions} />
            </ChartCard>

            {customDashboard?.tables?.map((table) => {
              const keys =
                table.queryResults.length > 0
                  ? Object.keys(table.queryResults[0])
                  : [];
              return (
                <div key={table.title} className="w-full">
                  <CustomTable table={table} keys={keys} />
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};

export default memo(GeneralDashboard);

const NotAnswered = ({ selectedId, showIdsWindow, setShowIdsWindow }) => {
  const [showChatWindow, setShowChatWindow] = useState(false);
  const [selectedChat, setSelectedChat] = useState(null);
  console.log("ids", selectedId);
  const openChatWindow = [
    {
      label: "View",
      onClick: (data) => {
        setSelectedChat(data);
        setShowChatWindow(true);
      },
      icon: EyeIcon,
    },
  ];
  return (
    <FullModal
      isOpen={showIdsWindow}
      onClose={() => setShowIdsWindow(false)}
      title="Not Answered Conversations"
    >
      <div>
        <div>
          <MainTable
            actions={openChatWindow}
            data={selectedId}
            columns={[
              {
                key: "Question",
                name: "Question",
              },
              {
                key: "channel",
                name: "Channel",
              },
              {
                key: "conversation_id",
                name: "Conversation id",
              },
            ]}
          />
          {showChatWindow && (
            <ChatModal
              chat={selectedChat}
              onClose={() => setShowChatWindow(false)}
            />
          )}
        </div>
      </div>
    </FullModal>
  );
};
