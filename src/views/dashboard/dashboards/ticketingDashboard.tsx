import { getTicketingOverview } from "apis/ticketing.api";
import { FC, memo, useEffect, useMemo, useState } from "react";
import { DateRange } from "react-day-picker";
import useBotStore from "store/bot/bot.store";
import { Loading } from "../loading";
import { MainTable } from "common/components/tables/main.table";
import { AlarmCheck, CheckCircle2, TargetIcon, TicketIcon, View } from "lucide-react";
import { StatsCard, ChartCard } from "../components";
import { XCircle, StarIcon } from "lucide-react";
import { CSVLink } from "react-csv";
import { Button } from "common/ui/button";
import { CustomSearch } from "common/ui/inputs/search";
import SimpleDonutChart from "../charts/simpleDonut.chart";
import ChannelMenu from "../components/channelMenu";
import DepartmentsMenu from "../components/departmentsMenu";
import { apexchartsDataFormConverter } from "helpers/helper";
import { <PERSON><PERSON><PERSON>, Heatmap<PERSON>hart } from "../charts";
import Pagination from "common/ui/pagination";
import TicketDetailsModal from "../components/ticketing/ticketDetailsModal";
import * as signalR from "@microsoft/signalr";

const transformDateAndTime = (dateString: string) => {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const formattedDate = `${day}/${month}/${year}`;
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const formattedTime = `${hours}:${minutes}`;
  return { formattedDate, formattedTime };
};

interface TicketingDashboardProps {
    selectedPeriod: number;
    submitLoading: boolean;
    setSubmitLoading: (value: boolean) => void;
    filterDate: DateRange;
}

const TicketingDashboard: FC<TicketingDashboardProps> = ({
    selectedPeriod,
    submitLoading,
    setSubmitLoading,
    filterDate,
}) => {

    const bot_id = useBotStore((state) => state.bot.bot_id);
    var targetDate;
    var endDate;

    const [initData, setInitData] = useState({
        ticketsStatus: [],
        ticketsDepartment: [],
        ticketsCategories: [],
        ticketsAgents: [],
        ticketsRatings: [],
    });

    const [totalTickets, setTotalTickets] = useState(0);
    const [closedTickets, setClosedTickets] = useState(0);
    const [resolvedTickets, setResolvedTickets] = useState(0);
    const [averageRating, setAverageRating] = useState(0);
    const [avgTimeToResolve, setAvgTimeToResolve] = useState(0);
    const [ticketDetailsTable, setTicketDetailsTable] = useState([]);
    const [searchTerm, setSearchTerm] = useState(""); 
    const [channel, setChannel] = useState("All");
    const [channelForStatus, setChannelForStatus] = useState("All");
    const [channelForAgents, setChannelForAgents] = useState("All");
    const [channelForRating, setChannelForRating] = useState("All");
    const [selectedDepartment, setSelectedDepartment] = useState("All");
    const [selectedDepartmentId, setSelectedDepartmentId] = useState(0); 
    const [departments, setDepartments] = useState<{ departmentName: string; departmentId: number }[]>([]);
    const [ticketsHeatMap,setTicketsHeatMap] = useState([])
    const [maxCategory, setMaxCategory] = useState("")
    const [currentPage, setCurrentPage] = useState(1);
    const [ratingsTable, setRatingsTable] = useState([]);
    const [currentRatingPage, setCurrentRatingPage] = useState(1);
    const itemsPerPage = 5;
    const ratingsPerPage = 5;

    const [ticketsDepartments, setTicketsDepartments] = useState({
    series: [],
        xLabels: [],
      });
    const [ticketsCategories, setTicketsCategories] = useState({
        series: [],
        xLabels: [],
      });
    const [ticketsStatus, setTicketsStatus] = useState({
    series: [],
    xLabels: [],
    });
    const [ticketsChannel, setTicketsChannel] = useState({
        series: [],
        xLabels: [],
      });
    const [ticketsPerAgent, setTicketsPerAgent] = useState({
        series: [],
        xLabels: [],
    });
    const [ratingsPerAgent, setRatingsPerAgent] = useState({
        series: [],
        xLabels: [],
    });


  
    const [incomingActivity, setIncomingActivity] = useState(null);


    useEffect(() => {
          const socketServer = "https://i2i-messaging.azurewebsites.net";
          const userId = `ticketing__bot_${bot_id}`;
              const negotiationURL = `${socketServer}/api?userId=${userId}&conversation_id=${userId}&channel=webchat&bot_id=${bot_id}&type=server`;
              
              const _connection = new signalR.HubConnectionBuilder()
                .withUrl(negotiationURL)
                .build();
          
                _connection
                .start()
                .then(() => console.log("Connection established"))
                .catch((err) => {
                  console.error("Connection error: ", err);
                });
          
                _connection.on("chatMessage", async (message) => {
                    console.log("chatMessage message", message);
                    setIncomingActivity(message)
                  });
          
              return () => {
                _connection.stop().then(() => console.log("Connection stopped"));
              };
      }, [bot_id]);
  

    const getTicketingData = () => {
        if (selectedPeriod !== 22) {
            targetDate = new Date();
            const period = +selectedPeriod;
            targetDate.setDate(targetDate.getDate() - period);
        }

        const day = targetDate?.getDate();
        const month = targetDate?.getMonth() + 1;
        const year = targetDate?.getFullYear();
    
        const endDay = endDate?.getDate()
        const endMonth = endDate?.getMonth() + 1
        const endYear = endDate?.getFullYear();
        
        getTicketingOverview(
            bot_id,
            day,
            month,
            year,
            endDay,
            endMonth,
            endYear
        ).then(async (data) => {
            setSubmitLoading(false);

            
            // the data i need from the API
            const totalTickets = data?.filter((a) => a.hasOwnProperty("TotalTickets"));
            const ticketsByStatus = data?.filter((a) => a.hasOwnProperty("TicketsByStatus"));
            const ratings = data?.filter((a) => a.hasOwnProperty("AvgRating"));
            const ticketsByDepartment = data?.filter((a) => a.hasOwnProperty("DepartmentName"));
            const ticketsByCategories = data?.filter((a) => a.hasOwnProperty("CategoryName"));
            const heatmapData = data?.filter((a) => a?.graph === "tickets_per_day");
            const ticketsByChannel = data?.filter((a) => a.hasOwnProperty("TicketsByChannel"));
            const avgResolveTime = data?.filter((a) => a.hasOwnProperty("AvgTimeToResolveInMinutes"));
            const agentTickets = data?.filter((a) => a.hasOwnProperty("TicketsPerAgent"));
            const agentRatings = data?.filter((a) => a.hasOwnProperty("RatingsPerAgent"));
            const ratingsTable = data?.filter((a) => a.hasOwnProperty("Rating"));

            // cache the data to use later on without calling the API
            setInitData({
                ticketsStatus: ticketsByStatus,
                ticketsDepartment: ticketsByDepartment,
                ticketsCategories: ticketsByCategories,
                ticketsAgents: agentTickets,
                ticketsRatings: agentRatings
            });
            
            // total tickets 
            setTotalTickets(totalTickets[0]?.TotalTickets || 0);

            // top category 
            const maximumCategory = (ticketsByCategories && ticketsByCategories.length > 0) 
            ? ticketsByCategories.reduce((max, current) => {
                return current.TicketsByCategory > max.TicketsByCategory ? current : max;
            }, ticketsByCategories[0])
            : {};

            setMaxCategory(maximumCategory?.CategoryName || "-");

    
            // number of closed tickets 
            if (ticketsByStatus.length > 0) {
                const closed = ticketsByStatus.reduce((sum, a) => sum + (a.status === "closed" ? a.TicketsByStatus : 0), 0);
                setClosedTickets(closed);
            } else {
                setClosedTickets(0);
            }

            // number of resolved tickets
            if (ticketsByStatus.length > 0) {
                const resolved = ticketsByStatus.reduce((sum, a) => sum + (a.status === "resolved" ? a.TicketsByStatus : 0), 0);
                setResolvedTickets(resolved);
            } else {
                setResolvedTickets(0);
            }

            // avg time to resolve a ticket
            setAvgTimeToResolve(avgResolveTime.length > 0 ? avgResolveTime[0]?.AvgTimeToResolveInMinutes || 0 : 0)

            // average rating for bot
            setAverageRating(ratings.length > 0 ? ratings[0]?.AvgRating || 0 : 0);
    
            // detailed table for each ticket
            const ticketDetails = data?.filter((a) => a.hasOwnProperty("Title"));
    
            const transformedTickets = ticketDetails.map(ticket => {
                const { formattedDate, formattedTime } = transformDateAndTime(ticket.Date);
                return { ...ticket, Date: formattedDate, Time: formattedTime };
            });
    
            setTicketDetailsTable(transformedTickets);
            setRatingsTable(ratingsTable);             
               

            // heatmap data
            const weekDayHeatmapData = []; 
            const weekDays = [ 
                "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" 
            ];
            const hours = [ 
                "12 AM", "1 AM",  "2 AM", "3 AM", "4 AM", "5 AM", "6 AM", "7 AM", "8 AM", "9 AM", "10 AM", "11 AM",
                "12 PM", "1 PM", "2 PM", "3 PM",  "4 PM", "5 PM", "6 PM", "7 PM", "8 PM", "9 PM", "10 PM", "11 PM",
            ];

            for (var i = 0; i < weekDays?.length; i++) {
                weekDayHeatmapData?.push({
                  name: weekDays[i],
                  data: hours.map((hour, index) => {
                    const obj = heatmapData?.find((item) => {
                      return item.weekday === weekDays[i] && item.hour === index;
                    });
                    return {
                      x: hour,
                      y: obj?.ticket_count || 0,
                    };
                  }),
                });
                
              }
              setTicketsHeatMap(weekDayHeatmapData);

              // tickets by channel pie chart data
              if (ticketsByChannel?.length > 0) {
                    setTicketsChannel(
                        apexchartsDataFormConverter(
                            ticketsByChannel
                                .map((m) => ({
                                    ticketNumber: m.TicketsByChannel || 0,
                                    channel: m.channel?.charAt(0).toUpperCase() + m.channel?.slice(1) || "",
                                })),
                            "channel"
                        )
                    );
                } else {
                    setTicketsChannel({
                        series: [],
                        xLabels: [],
                    });
                }  

                // avg ratings per agent bar chart
                if (agentRatings.length > 0) {
                    const seriesData = [];
                    const uniqueRatings = new Map(); 
                
                    agentRatings.forEach((rating) => {
                        const agentID = rating.SupportAgentID;
                        const agentEmail = rating.SupportAgentEmail ?? "No Email";
                
                        if (agentID !== null) {
                            if (uniqueRatings.has(agentID)) {
                                uniqueRatings.get(agentID).data[0] += rating.RatingsPerAgent;
                            } else {
                                uniqueRatings.set(agentID, {
                                    name: agentEmail,
                                    data: [rating.RatingsPerAgent],
                                });
                            }
                        }
                    });
                
                    seriesData.push(...uniqueRatings.values());
                
                    setRatingsPerAgent({
                        series: seriesData,
                        xLabels: seriesData.map((item) => item.name),
                    });
                } else {
                    setRatingsPerAgent({
                        series: [],
                        xLabels: [],
                    });
                }

                // tickets per agent bar chart
                if (agentTickets.length > 0) {
                    const uniqueTickets = new Map(); 
                    const seriesData = [];
                
                    agentTickets.forEach((ticket) => {
                        const agentID = ticket.SupportAgentID;
                        const agentEmail = ticket.SupportAgentEmail ?? "Unknown Email"; 
                
                        if (agentID !== null) {
                            if (uniqueTickets.has(agentID)) {
                                uniqueTickets.get(agentID).data[0] += ticket.TicketsPerAgent;
                            } else {
                                uniqueTickets.set(agentID, {
                                    name: agentEmail,
                                    data: [ticket.TicketsPerAgent], 
                                });
                            }
                        }
                    });
                
                    seriesData.push(...uniqueTickets.values());
                
                    setTicketsPerAgent({
                        series: seriesData,
                        xLabels: seriesData.map((item) => item.name),
                    });
                } else {
                    setTicketsPerAgent({
                        series: [],
                        xLabels: [],
                    });
                }

                // tickets by departments pie chart data
                if (ticketsByDepartment?.length > 0) {
                    const uniqueDepartments = new Map();
                    const seriesData = [];
                
                    ticketsByDepartment.forEach((ticket) => {
                        const departmentID = ticket.DepartmentID;
                        const departmentName = ticket.DepartmentName?.charAt(0).toUpperCase() + ticket.DepartmentName?.slice(1) || "";
                        const ticketNumber = ticket.TicketsPerDepartment || 0;
                
                        if (departmentID !== null && ticket.channel !== null) {
                            const channelName = ticket.channel?.charAt(0).toUpperCase() + ticket.channel.slice(1) || "";
                
                            if (uniqueDepartments.has(departmentID)) {
                                uniqueDepartments.get(departmentID).ticketNumber += ticketNumber;
                            } else {
                                uniqueDepartments.set(departmentID, {
                                    ticketNumber: ticketNumber,
                                    departmentName: departmentName,
                                    channel: channelName,
                                });
                            }
                        }
                    });
                
                    const formattedData = apexchartsDataFormConverter(
                        [...uniqueDepartments.values()],
                        "departmentName"
                    );
                
                    setTicketsDepartments(formattedData);
                } else {
                    setTicketsDepartments({
                        series: [],
                        xLabels: [],
                    });
                }
                
                // department names and Ids for the dropdown menu
                if (ticketsByDepartment?.length > 0) {
                    const uniqueDepartments = new Set();
                    
                    const departmentList = ticketsByDepartment
                        .filter((d) => {
                            if (uniqueDepartments.has(d.DepartmentID)) {
                                return false;
                            } else {
                                uniqueDepartments.add(d.DepartmentID);
                                return true;
                            }
                        })
                        .map((d) => ({
                            departmentName: d.DepartmentName,
                            departmentId: d.DepartmentID,
                        }));

                    setDepartments(departmentList);
                } else {
                    setDepartments([]);
                }

                if (ticketsByStatus?.length > 0) {
                    const uniqueStatus = new Map(); 
                    const seriesData = [];
                
                    ticketsByStatus.forEach((ticket) => {
                        const status = ticket.status?.charAt(0).toUpperCase() + ticket.status?.slice(1) || "";
                        const ticketNumber = ticket.TicketsByStatus || 0;
                        
                        if (status !== "" && ticket.channel !== null) {
                            const channelName = ticket.channel?.charAt(0).toUpperCase() + ticket.channel?.slice(1) || "";
                
                            if (uniqueStatus.has(status)) {
                                uniqueStatus.get(status).ticketNumber += ticketNumber;
                            } else {
                                uniqueStatus.set(status, {
                                    ticketNumber: ticketNumber,
                                    status: status,
                                    channel: channelName,
                                });
                            }
                        }
                    });
                
                    const formattedData = apexchartsDataFormConverter(
                        [...uniqueStatus.values()],
                        "status"
                    );
                
                    setTicketsStatus(formattedData);
                } else {
                    setTicketsStatus({
                        series: [],
                        xLabels: [],
                    });
                }
                             
        });
    
    };

    const populateCharts = () => {
        const agentRatings = initData['ticketsRatings']
        const ticketsByDepartment = initData['ticketsDepartment']
        const ticketsByCategories = initData['ticketsCategories']
        const ticketsByStatus = initData['ticketsStatus']
        const agentTickets = initData['ticketsAgents']

        // avg ratings per agent bar chart
        if (agentRatings.length > 0) {
            const filteredRatings = agentRatings.filter(
                (rating) =>
                    rating.SupportAgentID !== null && 
                    (channelForRating === "All" || rating.channel.toLowerCase() === channelForRating.toLowerCase())
            );
        
            if (filteredRatings.length > 0) {
                const uniqueRatings = new Map(); 
                const seriesData = [];
        
                filteredRatings.forEach((rating) => {
                    const agentID = rating.SupportAgentID;
                    const agentEmail = rating.SupportAgentEmail ?? "Unknown Email";
        
                    if (uniqueRatings.has(agentID)) {
                        uniqueRatings.get(agentID).data[0] += rating.RatingsPerAgent;
                    } else {
                        uniqueRatings.set(agentID, {
                            name: agentEmail,
                            data: [rating.RatingsPerAgent],
                        });
                    }
                });
        
                seriesData.push(...uniqueRatings.values());
        
                setRatingsPerAgent({
                    series: seriesData,
                    xLabels: seriesData.map((item) => item.name),
                });
            } else {
                setRatingsPerAgent({
                    series: [],
                    xLabels: [],
                });
            }
        } else {
            setRatingsPerAgent({
                series: [],
                xLabels: [],
            });
        }  

        // tickets per agent bar chart
        if (agentTickets.length > 0) {
            const filteredTickets = agentTickets.filter(
                (ticket) =>
                  ticket.SupportAgentID !== null && 
                  (channelForAgents === "All" || ticket.channel.toLowerCase() === channelForAgents.toLowerCase())
              );
          
            if (filteredTickets.length > 0) {
              const uniqueTickets = new Map(); 
              const seriesData = [];
          
              filteredTickets.forEach((ticket) => {
                const agentID = ticket.SupportAgentID;
                const agentEmail = ticket.SupportAgentEmail ?? "Unknown Email"; 
          
                if (uniqueTickets.has(agentID)) {
                  uniqueTickets.get(agentID).data[0] += ticket.TicketsPerAgent;
                } else {
                  
                  uniqueTickets.set(agentID, {
                    name: agentEmail,
                    data: [ticket.TicketsPerAgent], 
                  });
                }
              });
          
              seriesData.push(...uniqueTickets.values());

              setTicketsPerAgent({
                series: seriesData,
                xLabels: seriesData.map((item) => item.name),
              });
            } else {
              setTicketsPerAgent({
                series: [],
                xLabels: [],
              });
            }
          } else {
            setTicketsPerAgent({
              series: [],
              xLabels: [],
            });
          } 

         // tickets by departments pie chart data
         if (ticketsByDepartment?.length > 0) {
            const filteredTickets = ticketsByDepartment.filter(
                (ticket) =>
                    ticket.DepartmentID !== null && 
                    (channel === "All" || ticket.channel.toLowerCase() === channel.toLowerCase())
            );
        
            if (filteredTickets?.length > 0) {
                const uniqueDepartments = new Map(); 
                const seriesData = [];
        
                filteredTickets.forEach((ticket) => {
                    const departmentID = ticket.DepartmentID;
                    const departmentName = ticket.DepartmentName?.charAt(0).toUpperCase() + ticket.DepartmentName?.slice(1) || "";
                    const ticketNumber = ticket.TicketsPerDepartment || 0;
        
                    if (uniqueDepartments.has(departmentID)) {
                        uniqueDepartments.get(departmentID).ticketNumber += ticketNumber;
                    } else {
                        uniqueDepartments.set(departmentID, {
                            ticketNumber: ticketNumber,
                            departmentName: departmentName,
                            channel: ticket.channel?.charAt(0).toUpperCase() + ticket.channel.slice(1) || "",
                        });
                    }
                });
        
                const formattedData = apexchartsDataFormConverter(
                    [...uniqueDepartments.values()],
                    "departmentName"
                );
        
                setTicketsDepartments(formattedData);
            } else {
                setTicketsDepartments({
                    series: [],
                    xLabels: [],
                });
            }
        } else {
            setTicketsDepartments({
                series: [],
                xLabels: [],
            });
        }

        // department names and Ids for the dropdown menu
        if (ticketsByDepartment?.length > 0) {
            const uniqueDepartments = new Set();
            
            const departmentList = ticketsByDepartment
                .filter((d) => {
                    if (uniqueDepartments.has(d.DepartmentID)) {
                        return false;
                    } else {
                        uniqueDepartments.add(d.DepartmentID);
                        return true;
                    }
                })
                .map((d) => ({
                    departmentName: d.DepartmentName,
                    departmentId: d.DepartmentID,
                }));

            setDepartments(departmentList);
        } else {
            setDepartments([]);
        }

        // filter category based on department id
        const ticketsByCategory = ticketsByCategories.filter((category) => category.DepartmentID === selectedDepartmentId);
        
        // tickets by category pie chart data
        if (ticketsByCategory?.length > 0) {
            const filteredTicketsByCategory = ticketsByCategory.filter(
                (ticket) =>
                    ticket.CategoryID !== null && 
                    (channel === "All" || ticket.channel.toLowerCase() === channel.toLowerCase())
            );
        
            if (filteredTicketsByCategory?.length > 0) {
                const uniqueCategories = new Map(); // Use Map for unique categories
                const seriesData = [];
        
                filteredTicketsByCategory.forEach((ticket) => {
                    const categoryID = ticket.CategoryID;
                    const categoryName = ticket.CategoryName?.charAt(0).toUpperCase() + ticket.CategoryName?.slice(1) || "";
                    const ticketNumber = ticket.TicketsByCategory || 0;
        
                    if (uniqueCategories.has(categoryID)) {
                        uniqueCategories.get(categoryID).ticketNumber += ticketNumber;
                    } else {
                        uniqueCategories.set(categoryID, {
                            ticketNumber: ticketNumber,
                            categoryName: categoryName,
                            channel: ticket.channel?.charAt(0).toUpperCase() + ticket.channel?.slice(1) || "",
                        });
                    }
                });
        
                const formattedData = apexchartsDataFormConverter(
                    [...uniqueCategories.values()],
                    "categoryName"
                );
        
                setTicketsCategories(formattedData);
            } else {
                setTicketsCategories({
                    series: [],
                    xLabels: [],
                });
            }
        } else {
            setTicketsCategories({
                series: [],
                xLabels: [],
            });
        }

        // tickets by status pie chart
        if (ticketsByStatus?.length > 0) {
            const filteredTickets = ticketsByStatus.filter(
                (ticket) =>
                    ticket.status !== null && 
                    (channelForStatus === "All" || ticket.channel.toLowerCase() === channelForStatus.toLowerCase())
            );
        
            if (filteredTickets.length > 0) {
                const uniqueStatus = new Map(); 
                const seriesData = [];
        
                filteredTickets.forEach((ticket) => {
                    const status = ticket.status?.charAt(0).toUpperCase() + ticket.status?.slice(1) || "";
                    const ticketNumber = ticket.TicketsByStatus || 0;
        
                    if (uniqueStatus.has(status)) {
                        uniqueStatus.get(status).ticketNumber += ticketNumber;
                    } else {
                        uniqueStatus.set(status, {
                            ticketNumber: ticketNumber,
                            status: status,
                            channel: ticket.channel?.charAt(0).toUpperCase() + ticket.channel?.slice(1) || "",
                        });
                    }
                });
        
                const formattedData = apexchartsDataFormConverter(
                    [...uniqueStatus.values()],
                    "status"
                );

                setTicketsStatus(formattedData);
            } else {
                setTicketsStatus({
                    series: [],
                    xLabels: [],
                });
            }
        } else {
            setTicketsStatus({
                series: [],
                xLabels: [],
            });
        }


    }

    useEffect(()=>{
        targetDate = filterDate?.from 
        endDate = filterDate?.to;
        if(filterDate?.from && filterDate?.to){
          getTicketingData();
        }
        
      }, [filterDate?.to,incomingActivity]);
    
    useEffect(() => {
        if (selectedPeriod !== 22) {
            getTicketingData();
        }
    }, [selectedPeriod,incomingActivity]);

    useEffect(() => {
        populateCharts()
    }, [channelForAgents, channel, channelForRating, channelForStatus, selectedDepartmentId, selectedDepartment])

    const handleSearchChange = (searchTerm: string) => {
        setSearchTerm(searchTerm);
        setCurrentPage(1);
    };

    const filteredTickets = ticketDetailsTable.filter(ticket => {
        return Object.keys(ticket).some(key => {
            if (key !== "Date" && key !== "Time") {
                const value = ticket[key];
                if (typeof value === "string") {
                    return value.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (typeof value === "number") {
                    return value.toString().includes(searchTerm);
                }
            }
            return false;
        });
    });

    const filteredRatings = ratingsTable.filter(rating => {
        return Object.keys(rating).some(key => {
            if (key !== "Date" && key !== "Time") {
                const value = rating[key];
                if (typeof value === "string") {
                    return value.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (typeof value === "number") {
                    return value.toString().includes(searchTerm);
                }
            }
            return false;
        });
    });

    const paginatedTickets = useMemo(() => {
        const firstIndex = (currentPage - 1) * itemsPerPage;
        const lastIndex = firstIndex + itemsPerPage;
        return filteredTickets.slice(firstIndex, lastIndex);
    }, [currentPage, filteredTickets]);

    const paginatedRatings = useMemo(() => {
        const firstIndex = (currentRatingPage - 1) * ratingsPerPage;
        const lastIndex = firstIndex + ratingsPerPage;
        return filteredRatings.slice(firstIndex, lastIndex);
    }, [currentRatingPage, filteredRatings]);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const handleRatingPageChange = (page: number) => {
        setCurrentRatingPage(page);
    };

    const ticketDetailColumns = [
        { key: "TicketUUID", name: "Ticket ID" },
        { key: "ConversationID", name: "Conversation ID"},
        { key: "Title", name: "Title" },
        { key: "Channel", name: "Channel"},
        { key: "SupportAgentEmail", name: "Support Agent Email" },
        { key: "Department", name: "Department" },
        { key: "category", name: "Category" },
        { key: "Date", name: "Date" },
        { key: "Time", name: "Time" },
    ];

    const exportFile = () => {
        const headers = [
            { label: "Ticket ID", key: "TicketUUID" },
            { label: "Conversation ID", key: "ConversationID"},
            { label: "Title", key: "Title" },
            { label: "Channel", key: "Channel" },
            { label: "Support Agent Email", key: "SupportAgentEmail" },
            { label: "Department", key: "Department" },
            { label: "Category", key: "category"},
            { label: "Date", key: "Date" },
            { label: "Time", key: "Time" },
        ];

        const data = ticketDetailsTable.map(ticket => ({
            "TicketUUID": ticket["TicketUUID"],
            "ConversationID": ticket["ConversationID"],
            "Title": ticket["Title"],
            "Channel": ticket["Channel"],
            "SupportAgentEmail": ticket["SupportAgentEmail"],
            "Department": ticket["Department"],
            "category": ticket["category"],
            "Date": ticket["Date"],
            "Time": ticket["Time"],
        }));

        return {
            data,
            headers,
            filename: 'ticketing_report.csv',
        };
    };

    const ratingColumns = [
        { key: "TicketUUID", name: "Ticket ID" },
        { key: "SupportAgentEmail", name: "Support Agent Email" },
        { key: "Rating", name: "Rating" },
        { key: "RatingMessage", name: "Rating Message" },
    ];

    const exportRatingsFile = () => {
        const headers = [
            { label: "Ticket ID", key: "TicketUUID" },
            { label: "Support Agent Email", key: "SupportAgentEmail" },
            { label: "Rating", key: "Rating" },
            { label: "Rating Message", key: "RatingMessage" },
        ];

        const data = ratingsTable.map(rating => ({
            "TicketUUID": rating["TicketUUID"],
            "SupportAgentEmail": rating["SupportAgentEmail"],
            "Rating": rating["Rating"],
            "RatingMessage": rating["RatingMessage"],
        }));

        return {
            data,
            headers,
            filename: 'ratings_report.csv',
        };
    };
    
    const [showModal, setShowModal] = useState(false);
    const [ticketUUID, setSelectedTicketUUID] = useState(null);

    const ticketsTableActions = [
        {
            label: "View",
            onClick: (item) => {
                console.log(item)
                setSelectedTicketUUID(item.TicketUUID)
              setShowModal(true);
            },
            icon: View,
          },

      ];

    return (
        <div className="space-y-5">
            <TicketDetailsModal setShowModal={setShowModal}  showModal={showModal} ticketUUID={ticketUUID}/>
            {submitLoading ? (
                <Loading />
            ) : (
                <>
                    <div className="pt-3">
                        <div className="grid lg:grid-cols-3 gap-5 px-5 pb-5 pt-4 border border-gray-600 rounded-md">
                            <StatsCard
                                title="Total Number of Tickets"
                                subtitle={String(totalTickets)}
                                icon={TicketIcon}
                            />
                            <StatsCard
                                title="Top Category"
                                subtitle={maxCategory ? maxCategory : "-"}
                                icon={TargetIcon}
                            />
                            <StatsCard
                                title="Average Time to Resolve a Ticket in Minutes"
                                subtitle={String(avgTimeToResolve)}
                                icon={AlarmCheck}
                            />
                            {/* <StatsCard
                                title="Total Number of Resolved Tickets"
                                subtitle={String(resolvedTickets)}
                                icon={CheckCircle2}
                            /> */}
                            <StatsCard
                                title="Total Number of Closed Tickets"
                                subtitle={String(closedTickets)}
                                icon={XCircle}
                            />
                            <StatsCard
                                title="Average Rating"
                                subtitle={String(averageRating.toFixed(2))}
                                icon={StarIcon}
                            />
                        </div>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                    <ChartCard title="Tickets per Department / Category" className="p-5">
                        <div className="flex space-x-4 w-full">
                            <DepartmentsMenu 
                                setSelectedDepartment={setSelectedDepartment}
                                selectedDepartment={selectedDepartment}
                                setSelectedDepartmentId={setSelectedDepartmentId}
                                selectedDepartmentId={selectedDepartmentId}
                                departments={departments}
                                all={true}
                            />
                            <ChannelMenu channel={channel} setChannel={setChannel}/>
                        </div>
                          {selectedDepartment === "All" && ticketsDepartments.series.length >0 ? (
                            <SimpleDonutChart 
                                data={ticketsDepartments.series || []} 
                                xLabels={ticketsDepartments.xLabels || []} 
                                subtitle={"Departments"} 
                            />
                        ) : ticketsCategories.series.length >0 ?  (
                            <SimpleDonutChart 
                                data={ticketsCategories.series || []} 
                                xLabels={ticketsCategories.xLabels || []} 
                                subtitle={"Categories"} 
                            />
                        )
                         :
                        <div className="flex items-center justify-center space-x-4 w-full py-5">
                        <p className="font-semibold ">No tickets were found for this period/channel</p>
                        </div>
                        }
                  
                    </ChartCard> 

                    <ChartCard title="Tickets per Status">
                        <div className="flex space-x-4 w-full">
                            <ChannelMenu channel={channelForStatus} setChannel={setChannelForStatus}/>
                        </div>
              

                     {ticketsStatus.series?.length > 0   ?  
                        
                        <SimpleDonutChart
                            data={ticketsStatus.series|| []}
                            xLabels={ticketsStatus.xLabels || []}
                            subtitle="Status"
                            colorsArr={["#FF7F50", "#87CEFA", "#32CD32", "#FFB6C1", "#FFD700", "#8A2BE2"]}
                        />


                        :
                        <div className="flex items-center justify-center space-x-4 w-full py-5">
                        <p className="font-semibold ">No tickets were found for this period/channel</p>
                        </div>
                          }



                    </ChartCard>
                    </div>                
                    <ChartCard title="Tickets Per Weekday / Hour">
                        <HeatmapChart data={ticketsHeatMap || []} />
                    </ChartCard>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                        <ChartCard title="Tickets per Agent">
                            <div className="flex space-x-4 w-full">
                                <ChannelMenu channel={channelForAgents} setChannel={setChannelForAgents}/>
                            </div>
                            <BarChart
                                data={ticketsPerAgent.series || []} 
                                xLabels={ticketsPerAgent.xLabels || []}
                            />
                        </ChartCard>
                        <ChartCard title="Average Rating per Agent">
                            <div className="flex space-x-4 w-full">
                                <ChannelMenu channel={channelForRating} setChannel={setChannelForRating}/>
                            </div>
                            <BarChart
                                data={ratingsPerAgent.series || []} 
                                xLabels={ratingsPerAgent.xLabels || []}
                                colors={["#FF6FB6", "#6FFFB0"]}
                            />
                        </ChartCard>
                    </div>

                    <div className="grid lg:grid-cols-1 gap-5">
                        <ChartCard className="pb-10" title="Ticket Details">
                        <div className="flex gap-5 pb-4 items-center">
                            <CustomSearch
                                placeholder="Search tickets..."
                                onChange={handleSearchChange}
                            />

                            <CSVLink {...exportFile()}>
                                <Button style={{ padding: '8px 16px', whiteSpace: 'nowrap', fontSize: '16px' }}>
                                    Export as CSV
                                </Button>
                            </CSVLink>
                        </div>
                            <div className="max-h-[500px] overflow-y-auto">
                                <MainTable
                                    data={paginatedTickets || []}
                                    columns={ticketDetailColumns}
                                    actions={ticketsTableActions}
                                />
                            </div>
                            {filteredTickets.length > 0 && (
                                <Pagination
                                    currentPage={currentPage}
                                    totalCount={filteredTickets.length}
                                    pageSize={itemsPerPage}
                                    onPageChange={handlePageChange}
                                />
                            )}
                        </ChartCard>
                    </div>

                    <ChartCard title="Tickets per Channel" className="p-5">
                        <SimpleDonutChart 
                            data={ticketsChannel.series || []} 
                            xLabels={ticketsChannel.xLabels || []} 
                            channel={true}
                        />
                    </ChartCard>
                    <ChartCard className="pb-10" title="Ticket Ratings">
                            <div className="flex gap-5 pb-4 items-center">
                                <CustomSearch
                                    placeholder="Search ratings..."
                                    onChange={handleSearchChange}
                                />

                                <CSVLink {...exportRatingsFile()}>
                                    <Button>Export as CSV</Button>
                                </CSVLink>
                            </div>
                            <div className="max-h-[500px] overflow-y-auto">
                                <MainTable
                                    data={paginatedRatings || []} 
                                    columns={ratingColumns}
                                    actions={ticketsTableActions}
                                />
                            </div>
                            {filteredRatings.length > 0 && (
                                <Pagination
                                    currentPage={currentRatingPage}
                                    totalCount={filteredRatings.length}
                                    pageSize={ratingsPerPage}
                                    onPageChange={handleRatingPageChange}
                                />
                            )}
                        </ChartCard>
                </>
            )}
        </div>
    );
};

export default memo(TicketingDashboard);