import { FC, memo } from "react";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface BarChartProps {
  data: ApexAxisChartSeries;
  xLabels: Array<string>;
  colors?: Array<string>;
}

const BarChart: FC<BarChartProps> = ({ data, xLabels, colors }) => {
  return (
    <Chart
      options={{
        chart: {
          height: 350,
          type: "bar",
          background: "transparent",
        },
        plotOptions: {
          bar: {
            borderRadius: 5,
            // columnWidth: "50%",
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          width: 2,
        },
        colors: colors? colors : ["#9F6FFC", "#67F7FF"],
        theme: {
          mode: "dark",
        },
        grid: {
          show: false,
        },
        xaxis: {
          labels: {
            rotate: -45,
          },
          categories: xLabels,
          tickPlacement: "on",
        },
        yaxis: {},
        noData: {
          text: "No Data Available",
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
          style: {
            color: "#FFF",
            fontSize: "14px",
            fontFamily: "Source Sans",
          },
        },
      }}
      series={data}
      type="bar"
      height={200}
    />
  );
};

export default memo(BarChart);