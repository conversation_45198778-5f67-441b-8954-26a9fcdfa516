import { FC } from "react";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface AreaChartProps {
  //   data: ApexAxisChartSeries;
  data: any;
  xLabels: Array<string>;
}

const AreaChart: FC<AreaChartProps> = ({ data, xLabels }) => {
  return (
    <Chart
      options={{
        chart: {
          type: "area",
          //   height: 20,
          sparkline: {
            enabled: true,
          },
          offsetY: 3,
        },
        stroke: {
          curve: "smooth",
          width: 2,
        },
        fill: {
          opacity: 0.3,
        },
        xaxis: {
          categories: xLabels,
        },
        colors: ["#9F6FFC"],
        tooltip: {
          enabled: true,
          theme: "dark",
        },
        noData: {
          text: "No Data Available",
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
          style: {
            color: "#FFF",
            fontSize: "14px",
            fontFamily: "Source Sans",
          },
        },
      }}
      series={[data]}
      type="area"
      height={35}
    />
  );
};

export default AreaChart;
