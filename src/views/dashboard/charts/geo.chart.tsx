import React, { useRef, useEffect, useState, useLayoutEffect } from "react";
import * as am5 from "@amcharts/amcharts5";
import * as am5map from "@amcharts/amcharts5/map";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import am5geodata_worldLow from "@amcharts/amcharts5-geodata/worldLow";
import countries from "data/countries.json";

const GeoMap = ({ data }) => {
  useLayoutEffect(() => {
    var root = am5.Root.new("chartdiv");
    root.setThemes([am5themes_Animated.new(root)]);

    var chart = root.container.children.push(am5map.MapChart.new(root, {}));

    var polygonSeries = chart.series.push(
      am5map.MapPolygonSeries.new(root, {
        geoJSON: am5geodata_worldLow,
        exclude: ["AQ"],
      })
    );

    //code added where I changed the color of map
    polygonSeries.mapPolygons.template.setAll({
      tooltipText: "{name}",
      interactive: true,
      fill: am5.color(0x5f5f63),
      templateField: "polygonSettings",
    });

    var colors = am5.ColorSet.new(root, {});
    polygonSeries.mapPolygons.template.states.create("hover", {
      fill: am5.color(0x8d64dd),
    });

    var bubbleSeries = chart.series.push(
      am5map.MapPointSeries.new(root, {
        valueField: "value",
        calculateAggregates: true,
        polygonIdField: "id",
      })
    );

    var circleTemplate = am5.Template.new({});

    bubbleSeries.bullets.push(function (root, series, dataItem) {
      var container = am5.Container.new(root, {});

      var circle = container.children.push(
        am5.Circle.new(
          root,
          {
            radius: 20,
            fillOpacity: 0.7,
            fill: am5.color(0x8d64dd),
            cursorOverStyle: "pointer",
            tooltipText: `{name}: [bold]{value}[/]`,
          }
          //   circleTemplate
        )
      );

      var countryLabel = container.children.push(
        am5.Label.new(root, {
          text: "{name}",
          paddingLeft: 5,
          populateText: true,
          fontWeight: "bold",
          fontSize: 13,
          centerY: am5.p50,
        })
      );

      circle.on("radius", function (radius) {
        countryLabel.set("x", radius);
      });

      return am5.Bullet.new(root, {
        sprite: container,
        dynamic: true,
      });
    });

    bubbleSeries.bullets.push(function (root, series, dataItem) {
      return am5.Bullet.new(root, {
        sprite: am5.Label.new(root, {
          text: "{value.formatNumber('#.')}",
          fill: am5.color(0xffffff),
          populateText: true,
          centerX: am5.p50,
          centerY: am5.p50,
          textAlign: "center",
        }),
        dynamic: true,
      });
    });

    bubbleSeries.set("heatRules", [
      {
        target: circleTemplate,
        dataField: "Messages",
        min: 10,
        max: 100,
        minValue: 0,
        maxValue: Math.max.apply(
          Math,
          data.map(function (o) {
            return o?.Messages;
          })
        ),
        key: "radius",
      },
    ]);

    const results = [];

    for (var i = 0; i < data?.length; i++) {
      results.push({
        id: countries.find((a) =>
          a.name?.toLowerCase()?.includes(data[i]?.Country?.toLowerCase())
        )?.code,
        name: data[i]?.Country,
        value: data[i].Messages,
      });
    }

    bubbleSeries.data.setAll(results.filter((a) => a.id));

    // setInterval(function() {
    //   updateData();
    // }, 2000)
    return () => root.dispose();
  }, [data]);

  return <div id="chartdiv" style={{ width: "100%", height: "500px" }}></div>;
};
export default GeoMap;
