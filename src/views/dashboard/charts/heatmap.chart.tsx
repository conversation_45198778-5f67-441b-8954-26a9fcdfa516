import { FC } from "react";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface heatmapchartProps {
  data: ApexAxisChartSeries;
  xLabels?: Array<string>;
}

const HeatmapChart: FC<heatmapchartProps> = ({ data, xLabels }) => {
  return (
    <>
      {data?.length ? (
        <Chart
          options={{
            chart: {
              // height: 300,
              type: "heatmap",
              background: "transparent",
            },
            plotOptions: {
              heatmap: {
                // shadeIntensity: 0.5,
                radius: 1,
                // useFillColorAsStroke: true,
                distributed: true,
                shadeIntensity: 0.9,

                // enableShades: false,
                // colorScale: {
                //   ranges: [
                //     {
                //       from: 0,
                //       to: 40,
                //       color: "#B080FF", // Lighter shade of purple
                //     },
                //     {
                //       from: 41,
                //       to: 80,
                //       color: "#A550FF",
                //     },
                //     {
                //       from: 81,
                //       to: 120,
                //       color: "#9431FF",
                //     },
                //     {
                //       from: 121,
                //       to: 200,
                //       color: "#8200FF",
                //     },
                //     {
                //       from: 201,
                //       to: 300000,
                //       color: "#000",
                //       name: "> 201",
                //     },
                //   ],
                // },
              },
            },
            dataLabels: {
              enabled: true,
              style: {
                colors: ["#fff"],
                fontSize: "10px",
              },
            },
            // xaxis: {
            //   type: "category",
            //   categories: xLabels,
            // },
            // colors: ["#9F6FFC"],
            theme: {
              mode: "dark",
              monochrome: {
                enabled: true,
                color: "#9F6FFC",
                shadeTo: "dark",
              },
            },
            grid: {
              show: false,
            },
            noData: {
              text: "No Data Available",
              align: "center",
              verticalAlign: "middle",
              offsetX: 0,
              offsetY: 0,
              style: {
                color: "#FFF",
                fontSize: "14px",
                fontFamily: "Source Sans",
              },
            },
          }}
          series={data}
          type="heatmap"
          height={300}
        />
      ): null}
    </>
  );
};

export default HeatmapChart;
