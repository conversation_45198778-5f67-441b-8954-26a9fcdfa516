import { FC, memo } from "react";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface SimpleDonutChartProps {
  data: any;
  xLabels: Array<string>;
  channel?: boolean;
  colorsArr?: Array<string>;
  subtitle?: string;  
}

const SimpleDonutChart: FC<SimpleDonutChartProps> = ({ data, xLabels, channel, colorsArr, subtitle }) => {
  const colorMap = {
    facebook: "#4267B2",
    instagram: "#C13584",
    whatsapp: "#25D366",
    web: "#666666",
    default: ["#9F6FFC", "#67F7FF", "#FFC46F", "#6F9FFF", "#FF6F9F", "#FFF67F"] // Default color if no match is found
  };
  const chartColors = xLabels.map((label, x) => colorMap[label.toLowerCase()] || colorMap.default[x]);
  return (
    <>
      {" "}
      {xLabels.length ? (
        <Chart
          options={{
            chart: {
              type: "donut",
              toolbar: {
                show: true,
              },
              background: "transparent",
            },
            colors: colorsArr ? colorsArr : channel ? chartColors : ["#9F6FFC", "#67F7FF", "#FFC46F", "#6F9FFF","6F6FFC", "#FF6F9F", "#FFF67F"] ,
            stroke: {
              width: 2,
            },
            dataLabels: {
              enabled: true,
            },
            theme: {
              mode: "dark",
            },
            responsive: [
              {
                breakpoint: 480,
                options: {
                  chart: {
                    width: 250,
                  },
                  legend: {
                    position: "bottom",
                  },
                },
              },
            ],
            labels: xLabels || [],
            subtitle: {
              text: subtitle || '',   
              align: 'right',          
              offsetX: -30,
              offsetY:0,
              style: {
                fontSize: '14px',
                fontWeight: 'normal',
                fontFamily: 'Source Sans',
                color: '#fff',
              },
            },
            noData: {
              text: "No Data Available",
              align: "center",
              verticalAlign: "middle",
              offsetX: 0,
              offsetY: 0,
              style: {
                color: "#FFF",
                fontSize: "14px",
                fontFamily: "Source Sans",
              },
            },
          }}
          series={data[0]?.data}
          type="donut"
          height={200}
        />
      ) : null}
    </>
  );
};

export default memo(SimpleDonutChart);