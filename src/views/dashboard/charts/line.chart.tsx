import { FC, memo } from "react";
import dynamic from "next/dynamic";
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface LineChartProps {
  data: ApexAxisChartSeries;
  xLabels: Array<string>;
  xLabel?: string;
  yLabel?: string;
}

const LineChart: FC<LineChartProps> = ({ data, xLabels,xLabel,yLabel }) => {
  return (
    <Chart
      options={{
        chart: {
          height: 350,
          type: "area",
          background: "transparent",
          toolbar: {
            show: true,
          },
        },

        colors: ["#9F6FFC", "#8F8F8F" ,"#67F7FF"],
        dataLabels: {
          enabled: true,
        },
        stroke: {
          curve: "smooth",
        },

        grid: {
          show: false,
        },
        theme: {
          mode: "dark",
        },
        markers: {
          size: 1,
        },
        xaxis: {
          categories: xLabels,
          title: {
            text: xLabel || "",      
          },
        },
        yaxis: {
          title: {
            text: yLabel || "",      
          },
        },
        legend: {
          formatter: function (seriesName, opts) {
            return [
              '<span class="legend-label capitalize">',
              seriesName,
              "</span>",
            ].join("");
          },
        },
        noData: {
          text: "No Data Available",
          align: "center",
          verticalAlign: "middle",
          offsetX: 0,
          offsetY: 0,
          style: {
            color: "#FFF",
            fontSize: "14px",
            fontFamily: "Source Sans",
          },
        },
      }}
      series={data}
      type="area"
      height={300}
    />
  );
};

export default memo(LineChart);
