import React from "react";

export const Loading = () => {
  return (
    <div className="grid lg:grid-cols-3 gap-5">
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
    </div>
  );
};
