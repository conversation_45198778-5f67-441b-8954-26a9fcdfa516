import React from "react";

export const OneCardLoading = () => {
  return (
    <div className="w-full h-[300px] animate-pulse bg-accent">
      {/* <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div>
      <div className="animate-pulse h-[127px] pt-5 pb-1 bg-accent rounded-xl shadow-lg hover:bg-accent/75 space-y-2 overflow-hidden"></div> */}
    </div>
  );
};
