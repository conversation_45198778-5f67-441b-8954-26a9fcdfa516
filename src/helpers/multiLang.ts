import { isArabic } from "helpers/helper";

// from "" OR "__ar__:{one} __en__:{two}" TO {ar: "one", en: "two"}
const transformTextToLanguageObj = (text: string, trim: boolean = true) => {
  if (typeof text !== "string") return text;
  if (!text?.includes("__ar__")) {
    if (isArabic(text)) return { ar: text, en: "" };
    return { ar: "", en: text };
  } else {
    // Define regular expressions to match Arabic and English questions within curly braces
    const arabicRegexOld = /__ar__:{([\s\S]*?)}\s+/;
    const arabicRegexNew = /__ar__:##([\s\S]*?)##\s+/;
    const englishRegexOld = /__en__:{([\s\S]*?)}(\s*__|$)/;
    const englishRegexNew = /__en__:##([\s\S]*?)##(\s*__|$)/;

    // Use regular expressions to extract questions
    const arabicMatchOld = text.match(arabicRegexOld);
    const arabicMatchNew = text.match(arabicRegexNew);
    const englishMatchOld = text.match(englishRegexOld);
    const englishMatchNew = text.match(englishRegexNew);
    const arabicMatch = arabicMatchOld || arabicMatchNew;
    const englishMatch = englishMatchOld || englishMatchNew;
    // Extract the questions from the matches
    const arabicQuestion = trim
      ? arabicMatch
        ? arabicMatch[1].trim()
        : ""
      : arabicMatch
      ? arabicMatch[1]
      : "";
    const englishQuestion = trim
      ? englishMatch
        ? englishMatch[1].trim()
        : ""
      : englishMatch
      ? englishMatch[1]
      : "";
    return { ar: arabicQuestion || "", en: englishQuestion || "" };
  }
};
const extractLangInOneLine = (text: string) => {
  if (typeof text !== "string") return text;
  if (!text?.includes("__ar__")) {
    return text;
  } else {
    // Define regular expressions to match Arabic and English questions within curly braces
    const arabicRegexOld = /__ar__:{([\s\S]*?)}\s+/;
    const arabicRegexNew = /__ar__:##([\s\S]*?)##\s+/;
    const englishRegexOld = /__en__:{([\s\S]*?)}(\s*__|$)/;
    const englishRegexNew = /__en__:##([\s\S]*?)##(\s*__|$)/;

    // Use regular expressions to extract questions
    const arabicMatchOld = text.match(arabicRegexOld);
    const arabicMatchNew = text.match(arabicRegexNew);
    const englishMatchOld = text.match(englishRegexOld);
    const englishMatchNew = text.match(englishRegexNew);
    const arabicMatch = arabicMatchOld || arabicMatchNew;
    const englishMatch = englishMatchOld || englishMatchNew;
    // Extract the questions from the matches
    const arabicQuestion = arabicMatch ? arabicMatch[1].trim() : "";
    const englishQuestion = englishMatch ? englishMatch[1].trim() : "";
    return arabicQuestion + " - " + englishQuestion;
    // { ar: arabicQuestion || "", en: englishQuestion || "" };
  }
};

// from {ar: "one", en: "two"} TO "__ar__:{one} __en__:{two}"
const template = (ar: string, en: string) =>
  `__ar__:##${ar}## __en__:##${en}##`;
// hanle change
const setValuesBasedOnLanguage = (
  text: string,
  langObj: Record<"ar" | "en", string>,
  lang
) => {
  if (lang == "ar") return { ...langObj, ar: text };
  return { ...langObj, en: text };
};
// from {ar: "one", en: "two"} TO "__ar__:{one} __en__:{two}"
const transformLangObjToText = (langObj: Record<"ar" | "en", string>) => {
  return template(langObj.ar, langObj.en);
};

// Custom for this sheet
const prepareSuggestions = (suggestions) => {
  return suggestions.map((suggestion) => {
    const trigger = transformTextToLanguageObj(suggestion.trigger);
    const alternatives = suggestion.alternatives.map((alternative) =>
      transformTextToLanguageObj(alternative)
    );
    return {
      ...suggestion,
      trigger,
      alternatives,
    };
  });
};

const getTextBasedOnLanguageForConnectors = (textEncoded: "", lang) => {
  if (!textEncoded.includes("__ar__")) {
    return textEncoded;
  } else {
    // Define regular expressions to match Arabic and English questions within curly braces
    const arabicRegexOld = /__ar__:{([\s\S]*?)}\s+/;
    const arabicRegexNew = /__ar__:##([\s\S]*?)##\s+/;
    const englishRegexOld = /__en__:{([\s\S]*?)}(\s*__|$)/;
    const englishRegexNew = /__en__:##([\s\S]*?)##(\s*__|$)/;

    // Use regular expressions to extract questions
    const arabicMatchOld = textEncoded.match(arabicRegexOld);
    const arabicMatchNew = textEncoded.match(arabicRegexNew);
    const englishMatchOld = textEncoded.match(englishRegexOld);
    const englishMatchNew = textEncoded.match(englishRegexNew);
    const arabicMatch = arabicMatchOld || arabicMatchNew;
    const englishMatch = englishMatchOld || englishMatchNew;
    console.log("✅✅✅");
    console.log(
      arabicMatchOld,
      arabicMatchNew,
      englishMatchOld,
      englishMatchNew
    );
    console.log("✅✅✅");
    // Extract the questions from the matches
    const arabicQuestion = arabicMatch ? arabicMatch[1].trim() : "";
    const englishQuestion = englishMatch ? englishMatch[1].trim() : "";

    if (lang === "ar") return arabicQuestion;
    return englishQuestion;
  }
};

export {
  transformTextToLanguageObj,
  setValuesBasedOnLanguage,
  transformLangObjToText,
  getTextBasedOnLanguageForConnectors,
  extractLangInOneLine,
};
