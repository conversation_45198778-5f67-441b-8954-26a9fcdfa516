import auth from "./auth";

function deleteAllCookies() {
  document.cookie.replace(/(?<=^|;).+?(?=\=|;|$)/g, (name) => {
    const domains = window.location.hostname
      .split(/\.(?=[^\.]+\.)/)
      .reduceRight((acc, val, i, arr) => {
        if (i) {
          arr[i] = "." + val + acc;
        } else {
          arr[i] = "";
        }
        return arr;
      }, []);

    domains.forEach((domain) => {
      document.cookie = `${name}=;max-age=0;path=/;domain=${domain}`;
    });

    return "";
  });

  localStorage.clear();
}

const postSignup = (token) => {
  auth.authenticate(token, () => {
    setTimeout(() => {
      window.location.pathname = "/";
    }, 300);
  });
};
const postLogin = (token) => {
  auth.authenticate(token, () => {
    window.location.pathname = "/";
  });
};

const postLogout = () => {
  setTimeout(() => {
    window.location.pathname = "/login";
    deleteAllCookies();
  }, 300);
};

const postNewPassword = () => {
  setTimeout(() => {
    window.location.pathname = "/login";
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.delete("token");
    urlParams.delete("user_id");
  }, 300);
};

export { postSignup, postLogin, postLogout, postNewPassword };
