import toast from "react-hot-toast";
import { rateType } from "views/dashboard/components/feedback-rating.card";

const numberWithCommas = (x) =>
  (x || "").toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

const isArabic = (text: string) => {
  const result = /[\u0600-\u06FF\u0750-\u077F]/.test(text);
  return result;
};

const convert = (daysNumber) => {
  var start = new Date();
  var end = new Date();
  end.setDate(start.getDate() + daysNumber);

  var years = end.getFullYear() - start.getFullYear();
  start.setFullYear(start.getFullYear() + years);

  var months = end.getMonth() - start.getMonth();
  start.setMonth(start.getMonth() + months);

  var days = Math.floor(
    (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
  );

  var result = years + " Year";
  if (years !== 1) {
    result += "s";
  }
  if (months > 0) {
    result += "\n" + months + " Month";
    if (months !== 1) {
      result += "s";
    }
  }
  if (days > 0) {
    result += "\n" + days + " Day";
    if (days !== 1) {
      result += "s";
    }
  }
  return result;
};

const converter = (data) => {
  const arr = [];
  if (data.length) {
    arr.push(Object.keys(data[0]));
    data.map((a) => {
      arr.push(arr[0].map((key) => a[key]));
    });
  }
  return arr;
};

const distinct = (arr, by) =>
  arr.reduce((acc, current) => {
    const x = acc.find((item) => item[by] === current[by]);
    if (!x) {
      return acc.concat([current]);
    } else {
      return acc;
    }
  }, []);

const distinctMultiple = (arr, by) =>
  arr.reduce((acc, current) => {
    const x = acc.find((item) =>
      by.every((prop) => item[prop] === current[prop])
    );
    if (!x) {
      return acc.concat([current]);
    } else {
      return acc;
    }
  }, []);

const sum = (arr, by) =>
  arr.reduce((accumulator, object) => {
    return accumulator + +object[by];
  }, 0);

const sortByDesc = (arr, by) => arr.sort((a, b) => b[by] - a[by]);
const sortByAsc = (arr, by) => arr.sort((a, b) => a[by] - b[by]);

const apexchartsDataFormConverter = (data, xAxis) => {
  const keys = Object.keys(data[0])?.filter((a) => a !== xAxis) || [];
  const xLabels = data.map((a) => a[xAxis]?.slice(0, 40));
  const series = [];
  for (var i = 0; i < keys.length; i++) {
    const key = keys[i];
    series.push({
      name: key,
      data: [...data?.map((a) => a[key])],
    });
  }

  return {
    xLabels,
    series,
  };
};

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const copyToClipboard = (e, message: string) => {
  e.stopPropagation();
  navigator.clipboard.writeText(message);
  toast.success("Link Copied Succesfully");
};

const RatingConverter = (data) => {
  const result = Array.from({ length: 5 }, (_, index) => ({
    graph: "rate",
    average_rating: 0,
    rating: index + 1,
    user_count: 0,
  }));
  let totalRating = 0;
  let totalUsers = 0;

  data?.forEach((item: rateType) => {
    totalRating = item.rating * item.user_count + totalRating;
    totalUsers = item.user_count + totalUsers;
    const rating = item.rating;
    const userCount = item.user_count;
    result[rating - 1].average_rating = item.average_rating;
    result[rating - 1].user_count = userCount;
  });
  totalRating = Math.round((totalRating / totalUsers) * 10) / 10;
  const UpdatedRate = {
    RatingData: result,
    totalUsers: totalUsers,
    totalRating: totalRating,
  };

  return UpdatedRate;
};

export {
  numberWithCommas,
  convert,
  converter,
  distinct,
  sum,
  sortByDesc,
  sortByAsc,
  isArabic,
  apexchartsDataFormConverter,
  sleep,
  distinctMultiple,
  copyToClipboard,
  RatingConverter,
};
