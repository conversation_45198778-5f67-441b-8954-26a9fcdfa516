
const transformDate = (dateString: string) => {
  const date = new Date(dateString);
  // const utcDate = new Date(date.toISOString().split("T")[0]); // Converts to UTC and splits to get the date part
  const formattedDate = date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  });

  return formattedDate;
};

export function convertToJordanTime(dateString: string): string {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string');
  }
  
  const jordanOffset = 3 * 60; // Jordan is UTC+3
  const jordanTime = new Date(date.getTime() + jordanOffset * 60000);
  
  const pad = (num: number) => String(num).padStart(2, '0');
  
  const year = jordanTime.getUTCFullYear();
  const month = pad(jordanTime.getUTCMonth() + 1);
  const day = pad(jordanTime.getUTCDate());
  const hours = pad(jordanTime.getUTCHours());
  const minutes = pad(jordanTime.getUTCMinutes());
  const seconds = pad(jordanTime.getUTCSeconds());
  const milliseconds = String(jordanTime.getUTCMilliseconds()).padStart(3, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+03:00`;
}


export default transformDate;
