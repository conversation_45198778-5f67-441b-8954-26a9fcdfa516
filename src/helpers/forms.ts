import  { ZodSchema } from "zod";

type checkingObjType = {
    zodSchema:  ZodSchema
    data: any
}

const checkForErrors = (checkingObj:checkingObjType, setErrors ) => {
    const parsed = checkingObj.zodSchema.safeParse(checkingObj.data);
    if (parsed.success === false) {
      const error = parsed.error;
      let newErrors = {};
      for (const issue of error.issues) {
        newErrors = {
          ...newErrors,
          [issue.path[0]]: issue.message,
        };
      }
      console.log(newErrors);
      setErrors(newErrors);
      return true
    }
    setErrors({});
    return false
  }

export default checkForErrors;