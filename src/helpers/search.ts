const searchedArray = (keySearch: string, array: any[], keys?: string[]) => {
  if (keySearch.length && array?.length >0) {
    var bots_to_view = [...array];
    const filtered = bots_to_view?.filter((entry) =>
      (keys || Object.keys(entry)).some(
        (key) =>
          (typeof entry[key] === "string" &&
            entry[key]
              .toLowerCase()
              .normalize("NFKD")
              .replace(/[\u064b-\u065f]/g, "")
              .includes(
                keySearch
                  .toLowerCase()
                  .normalize("NFKD")
                  .replace(/[\u064b-\u065f]/g, "")
              )) ||
          (typeof entry[key] === "number" &&
            entry[key].toString().includes(keySearch.toString()))
      )
    );
    return [...filtered];
  } else if (array?.length >0) {
    return [...array];
  }
};

export default searchedArray;
