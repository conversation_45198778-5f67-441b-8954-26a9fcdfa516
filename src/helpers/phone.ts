import countryCodes from "data/countryCodes.json";

function cleanPhoneNumber(phoneNumber) {
  let matchingCode = null;

  phoneNumber = phoneNumber
    .replaceAll(" ", "")
    .replaceAll("-", "")
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("+", "");

  // Iterate through the country codes to find a matching code
  for (let i = 0; i < countryCodes.length; i++) {
    const code = countryCodes[i];
    const dialCode = code.dial_code?.replace("+", "").replace(" ", "");

    // Check if the phone number starts with a repeated dial code
    for (let j = 2; j <= 4; j++) {
      const repeatedCode = phoneNumber.slice(0, j);
      if (repeatedCode.repeat(2) === phoneNumber.slice(0, j * 2)) {
        if (dialCode === repeatedCode) {
          matchingCode = code;
          break;
        }
      }
    }

    if (matchingCode) {
      break;
    }
  }

  if (matchingCode) {
    const dialCode = matchingCode.dial_code.replace("+", "");
    phoneNumber = phoneNumber.replaceAll(dialCode, "");
    phoneNumber = phoneNumber.replaceAll("+", "");
    phoneNumber = matchingCode.dial_code + " " + phoneNumber;

    return phoneNumber;
  }

  const code = getCountryCode(phoneNumber);
  if (code) {
    phoneNumber = phoneNumber
      .replaceAll(code.dial_code.replace("+", "").replace(" ", ""), "")
      .replaceAll("+", "");
    phoneNumber = code.dial_code + " " + phoneNumber;
  }

  return phoneNumber;
}

function getCountryCode(phoneNumber) {
  let matchingCode = null;

  phoneNumber = phoneNumber
    .replaceAll(" ", "")
    .replaceAll("-", "")
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("+", "");

  for (let i = 0; i < countryCodes.length; i++) {
    const code = countryCodes[i];
    const dialCode = code.dial_code?.replace("+", "").replace(" ", "");

    for (let j = 2; j <= 4; j++) {
      const countryCode = phoneNumber.slice(0, j);

      if (dialCode === countryCode) {
        matchingCode = code;
        break;
      }
    }

    if (matchingCode) {
      break;
    }
  }

  return matchingCode;
}

export { cleanPhoneNumber, getCountryCode };
