var characters =
  "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
const generateStorageId = (len: number = characters.length) => {
  var result = [];
  var charactersLength = characters.length;
  for (var i = 0; i < len; i++) {
    result.push(
      characters.charAt(Math.floor(Math.random() * charactersLength))
    );
  }
  return result.join("");
};
export default generateStorageId;
