const handleUploadImg = (ImgID, file) => {
  const imgElement = document.getElementById(ImgID) as HTMLImageElement;
  imgElement.src = file === "" ? "" : URL.createObjectURL(file);
  return file === "" ? "" : URL.createObjectURL(file);
};
const handleUploadVid = (VidID, file) => {
  const vidElement = document.getElementById(VidID) as HTMLVideoElement;
  vidElement.src = file === "" ? "" : URL.createObjectURL(file);
  return file === "" ? "" : URL.createObjectURL(file);
};

export { handleUploadImg, handleUploadVid };
