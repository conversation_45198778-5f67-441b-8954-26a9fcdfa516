if (typeof window !== "undefined") {
  var token = localStorage.getItem("token");

  // localStorage.removeItem("token");
}

const auth = {
  isAuthenticated() {
    if (typeof window === "undefined") return false;

    if (token) return token;

    return false;
  },
  authenticate(jwt, cb) {
    if (typeof window !== "undefined") localStorage.setItem("token", jwt);
    setTimeout(() => {
      cb();
    }, 300);
  },
  getHeaderToken() {
    if (this.isAuthenticated()) {
      return "Token ".concat(token);
    }
    return undefined;
  },
  setToken(jwt) {
    token = jwt;
  },
};

export default auth;
