interface IGenesys {
    genesys_integration_id: number;
    organization_id: string,
    deployment_id: string,
    target_Address: string,
    createdAt?: string;
    updatedAt?: string;
    bot_id: number,
  }
   
  interface GenesysSlice {
    genesys: IGenesys;
    message?: string;
    loading?: boolean;
  
    get_one_genesys: (bot_id: number) => Promise<void>;
    update_one_genesys: (data: any) => Promise<void>;
    create_one_genesys: (data: any) => Promise<void>;
    delete_one_genesys: (bot_id: number) => Promise<void>;
  }
  
  export type { GenesysSlice, IGenesys };
  