import * as api from "apis/genesys.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import GenesysState from "./genesys.state";
import { GenesysSlice } from "./genesys.types";

const genesysStore = (set, get) => ({
  ...GenesysState,
  message: "",
  loading: false,

  get_one_genesys: async (bot_id: number) => {
    const data = await api.getgenesysIntegration(bot_id)
      if (data && data?.message) {
        set({
          message: data?.message,
          genesys: GenesysState.genesys,
        });
      } else {
        set({
          genesys: data,
        });
      }
  },

  update_one_genesys: async (lc: any) => {
    set({ loading: true });
    const data = await api.updateOne(lc)
      if (data && data?.message) {
        toast.error(data?.message);
        set({
          message: data?.message,
        });
        return;
      } else {
        set({
          genesys: data,
          message: "",
        });
        toast.success("genesys connection updated successfully");
      }
    set({ loading: false });
  },

  create_one_genesys: async (lc: any) => {
    set({ loading: true });
    const data = await api.createOne(lc)
      if (data && data?.message) {
        toast.error(data?.message);
        set({
          message: data?.message,
        });
        return;
      } else {
        set({
          genesys: data,
          message: "",
        });
        toast.success("genesys connection created successfully");
      }
    set({ loading: false });
  },

  delete_one_genesys: async (bot_id: number) => {
    const data = await api.deleteOne({ bot_id })
      if (data && data?.message) {
        set({
          message: data?.message,
          genesys: GenesysState.genesys,
        });
        toast.success(data?.message);
      }
  },
});

const useGenesysStore = create(
  persist<GenesysSlice>(genesysStore, {
    name: "genesys",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useGenesysStore;
