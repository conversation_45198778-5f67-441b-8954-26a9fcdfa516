interface ISmtpIntegration {
    smtp_id: number;
    host: string;
    username: string;
    password: string;
    bot_id: number;
    email: string;
    port: string;
    secure: boolean;
  }
  
  interface SmtpSlice {
    Smtp: ISmtpIntegration;
    message?: string;
    loading?: boolean;
  
    get_one_Smtp: (bot_id: number) => Promise<void>;
    create_one_Smtp: (
      Smtp: Omit<ISmtpIntegration, "smtp_id">
    ) => Promise<void>;
    update_one_Smtp: (
      Smtp: Partial<ISmtpIntegration>
    ) => Promise<void>;
    delete_one_Smtp: (bot_id: number) => Promise<void>;
  }
  
  export type { ISmtpIntegration, SmtpSlice };
  