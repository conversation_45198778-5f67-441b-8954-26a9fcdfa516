import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import * as api from "apis/smtpIntegration.api";

import SmtpState from "./smtp.state";
import { ISmtpIntegration, SmtpSlice } from "./smtp.types";

const SmtpStore = (set, get) => ({
  ...SmtpState,
  message: "",
  loading: true,

  get_one_Smtp: async (bot_id: number) => {
    api.getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          Smtp: SmtpState.Smtp,
        });
      } else {
        set({
          Smtp: data,
        });
      }
    });
  },

  create_one_Smtp: async (
    Smtp: Omit<ISmtpIntegration, "smtp_id">
  ) => {
    api.createOne(Smtp).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          Smtp: data,
        });
        toast.success("Smtp created successfully");
      }
    });
  },
  update_one_Smtp: async (Smtp: Partial<ISmtpIntegration>) => {
    api.updateOne(Smtp).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          Smtp: data,
        });
        toast.success("Smtp updated successfully");
      }
    });
  },

  delete_one_Smtp: async (bot_id: number) => {
    api.deleteOne({ bot_id }).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          Smtp: SmtpState.Smtp,
        });
        toast.success(data?.message);
      }
    });
  },
});

const useSmtpStore = create(
  persist<SmtpSlice>(SmtpStore, {
    name: "Smtp_integration",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useSmtpStore;
