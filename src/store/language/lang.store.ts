import { create } from "zustand";

type langType = "en" | "ar";

export interface LangStoreState {
  lang: string;
  changeLang: () => void;
}

const initialState = {
  lang: "en",
};

const LangStore = create<LangStoreState>()((set) => ({
  ...initialState,
  changeLang: () =>
    set({ lang: LangStore.getState().lang === "en" ? "ar" : "en" }),
}));

const useLangStore = () => LangStore((state) => state);
export { LangStore, useLangStore };
