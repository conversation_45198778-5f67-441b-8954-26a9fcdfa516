import { IBot } from "store/bot/bot.types";

type TicketCommentAttachment = {
  attachment_id: number;
  comment_id: number;
  url: string;
  name: string;
  type: string;
};

type TicketComment = {
  comment_id: number;
  ticket_id: number;
  support_agent_id: number;
  sender: "customer" | "agent";
  comment: string;
  has_attachments: boolean;
  attachments: TicketCommentAttachment[];
  support_agent: SupportAgent;
  createdAt: string;
};

type SupportAgent = {
  support_agent_id: number;
  editor: {
    editor_id: number;
    name: string;
    email: string;
    phone: string;
  };
  bot_id: number;
  status: string;
  time_zone: string;
  editor_id: number;
};

type Ticket = {
  ticket_id: number;
  ticket_uuid: string;
  bot_id: number;
  channel: string;
  conversation_id: string;
  status: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  category_id: number;
  ticketing_integraion_id: number;
  support_agent_id: number;
  time_zone: string;
  title: string;
  description: string;
  isLastCommentByCustomer?: boolean;
  comments?: TicketComment[];
  bot?: IBot & { bot_icon: string };
  category: {
    category_id: number;
    category: string;
  };
  createdAt?:string;
};

interface TicketingSlice {
  ticket: Ticket;
  ticketing: {
    ticketing_integration_id: number;
    status: string;
    time_zone: string;
    bot_id: number;
  };
  support_agent: SupportAgent;

  message?: string;
  loading?: boolean;

  get_ticket_by_uuid: (uuid: string) => Promise<Ticket | { message: string }>;

  get_one_ticketing: (bot_id: number) => Promise<void>;

  get_support_agent: (user_id: number, bot_id: number) => Promise<void>;

  update_ticketing_status: (bot_id: number) => Promise<void>;
  
  create_ticketing_integration: (bot_id: number, time_zone: string) => Promise<void>;

}

export type { TicketingSlice, Ticket, TicketComment, TicketCommentAttachment, SupportAgent };
