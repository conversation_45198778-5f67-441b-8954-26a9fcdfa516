import * as api from "apis/ticketing.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";
import TicketingState from "./ticketing.state";
import { TicketingSlice } from "./ticketing.types";

const ticketingStore = (set, get) => ({
  ...TicketingState,
  message: "",
  loading: false,

  get_ticket_by_uuid: async (uuid: string) => {
    try {
      const data = await api.getTicket(uuid);
      if (data && data?.error) {
        return {
            message: data.error
        }
      } else {
        return data;
      }
    } catch (error) {
        return {
            message: error
        }
    }
  },

  get_one_ticketing : async (bot_id: number) => {
    try {
      const data = await api.getTicketingIntegration(bot_id);
      if (data && data?.message) {
        set({
          message: data?.message,
          ticketing: TicketingState.ticketing,
        });
      } else {
        set({
          ticketing: data,
        });
      }
    } catch (error) {
      set({
        message: error,
      });
    }
  },

  get_support_agent: async (user_id: number, bot_id: number) => {
    try {
      const data = await api.getSupportAgent({ user_id, bot_id });
      if (data && data?.error) {
        set({
          message: data?.error,
          support_agent: TicketingState.support_agent,
        });
      } else {
        set({
          support_agent: data,
        });
      }
    } catch (error) {
      set({
        message: error,
      });
    }
  },

  update_ticketing_status: async (bot_id)=>{
    const response = await api.updateIntegration({bot_id: bot_id});
    if(response && response?.error){
      set({
        message: response?.error
      })
    } else {
      set({
        ticketing: response
      })
    }
  },

  create_ticketing_integration: async (bot_id, time_zone) => {
    const response = await api.createIntegration(bot_id, time_zone);
    if (response && response?.error) {
      set({ message: response?.error });
    } else {
      set({ 
        ticketing: response
      });
    }
  },

});

const useTicketingStore = create(
  persist<TicketingSlice>(ticketingStore, {
    name: "ticketing",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useTicketingStore;
