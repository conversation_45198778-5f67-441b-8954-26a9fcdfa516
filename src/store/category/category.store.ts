import * as api from "apis/category.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";
import toast from "react-hot-toast";
import CategoryState from "./category.state";
import { CategorySlice } from "./category.types";

const categoryStore = (set, get) => ({
  ...CategoryState,
  message: "",
  loading: true,

  get_all_categories: async (bot_id: number) => {
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          categories: data ? data : [],
        });
      }
  },

  create_one_category: async (category: any) => {
    const data = await api.createOne(category)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          categories: [...get().categories, data],
          category: data,
        });
      }
  },

  update_one_category: async (category: any) => {
    const data = await api.updateOne(category)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          categories: get().categories.map((cat) =>
            cat.category_id === data.category_id ? data : cat
          ),
          category: data,
        });
      }
  },

  delete_one_category: async (category: any) => {
    const data = await api.deleteOne({ category_id: category })
      if (data && data?.message) {
        set({
          categories: get().categories.filter(
            (cat) => cat.category_id !== category
          ),
        });
      }
  },

  create_many_categories: async (categories: any) => {
    const data = await api.createMany(categories)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          categories: [...get().categories, ...data],
        });
      }
  },
});

const useCategoryStore = create(
  persist<CategorySlice>(categoryStore, {
    name: "categories",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useCategoryStore;
