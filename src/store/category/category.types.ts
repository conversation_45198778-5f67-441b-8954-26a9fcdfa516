interface ICategory {
  category_id: number;
  category_description: string;
  category_name: string;
  category_icon: string;
  createdAt?: string;
  updatedAt?: string;
  bot_id: number;
  lemmatized_category_name?: string;
}

interface CategorySlice {
  category: ICategory;
  categories: ICategory[];
  message?: string;
  loading?: boolean;

  get_all_categories: (bot_id: number) => Promise<void>;
  create_one_category: (category: any) => Promise<void>;
  update_one_category: (category: ICategory) => Promise<void>;
  delete_one_category: (category: any) => Promise<void>;
  create_many_categories: (categories: any) => Promise<void>;
}

export type { ICategory, CategorySlice };
