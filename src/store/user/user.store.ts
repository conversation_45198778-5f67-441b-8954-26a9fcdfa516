import {
  ForgotPasswordInfo,
  ResetPasswordInfo,
  User,
  UserLoginInfo,
  UserRegisterInfo,
  UserSlice,
} from "store/user/user.types";
import { create } from "zustand";
import UserState from "./user.state";
import * as api from "apis/user.api";
import {
  postLogin,
  postLogout,
  postNewPassword,
  postSignup,
} from "helpers/post.process";
import { persist } from "zustand/middleware";
import { resetAllStores } from "../reset.all";
import toast from "react-hot-toast";

const userStore = (set, get) => ({
  user: UserState,
  message: "",
  loading: false,
  login: (user: UserLoginInfo) => {
    api.login(user).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
        toast.error(data.message);
      } else {
        postLogin(data.user.token);
        set({ user: data.user, loading: true, message: "" });
        toast.success("Login Successful");
      }
    });
  },
  signup: (user: UserRegisterInfo) => {
    api.register(user).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
        toast.error(data.message);
        if (data?.user) {
          postSignup(data.user.token);
          set({ user: data.user, loading: true, message: "" });
          toast.success("Registration Succesfull");
        }
      }
    });
  },
  forgot_password: async (email: ForgotPasswordInfo) => {
    set({loading: true})
    const data = await api.forgot(email)
      if (data?.message.includes("successfully")) {
         set({ message: data?.message });
      } else if (data?.message) {
         toast.error(data.message);
      } else {
        toast.error("Something went wrong");
      }
      set({loading: false})
  },
  reset_password: async (user: ResetPasswordInfo) => {
    set({loading: true})
    const data = await api.setPassword(user)
      if(data && data?.message.includes("successfully")) {
        toast.success("Password changed successfully");
        postNewPassword();
      } else {
        toast.error("Something went wrong");
      }
      set({loading: false})
  },
  logout: () => {
    const user = get().user;
    api.logout({ user_id: user.user_id }).then(() => {
      postLogout();
      // set({ user: UserState, loading: false, message: "" });
      resetAllStores();
    });
  },
  update_profile: async (user: User) => {
    api.updateProfile(user).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data.message);
      } else {
        set({ user: data, message: "" });
        // toast.success("Profile Updated");
      }
    });
  },
});

const useUserStore = create(
  persist<UserSlice>(userStore, {
    name: "user",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useUserStore;
