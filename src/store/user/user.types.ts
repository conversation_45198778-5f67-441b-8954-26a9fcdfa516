import { z } from "zod";

export interface User {
  user_id: number;
  user_name?: string;
  email: string;
  email_verification: boolean;
  name: string;
  admin: boolean;
  title: string;
  photo: string;
  langauge: string;
  department: string;
  mobile: string;
  token: string;
  company_name: string;
}

export const LoginUserSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email")
    .refine((value) => Boolean(value), {
      message: "Email is required.",
    }),
  password: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 charecters long"),
});

export type UserLoginInfo = z.infer<typeof LoginUserSchema>;

export const ForgotPasswordSchema = z.object({
  email: z.string().min(1, "Email is required").email("Invalid email"),
});

export type ForgotPasswordInfo = z.infer<typeof ForgotPasswordSchema>;

export const ResetPasswordSchema = z.object({
  user_id: z.string(),
  password: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 charecters long"),
  confirm_password: z.string().min(1, "Password Confirmation is required"),
});

export const RefinedResetPasswordSchema = ResetPasswordSchema.refine(
  (data) => data.password === data.confirm_password,
  {
    path: ["confirm_password"],
    message: "Passwords do not match",
  }
);

export type ResetPasswordInfo = z.infer<typeof ResetPasswordSchema>;

export const RegisterUserSchema = z.object({
  user_name: z.string().min(1, "Name is required").max(18),
  company_name: z.string().min(1, "Company Name is required").max(18),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Invalid email")
    .refine((value) => Boolean(value), {
      message: "Email is required.",
    }),
  password: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 charecters long"),
  confirm_password: z.string().min(1, "Password Confirmation is required"),
});

export const RefinedRegisterUserSchema = RegisterUserSchema.refine(
  (data) => data.password === data.confirm_password,
  {
    path: ["confirm_password"],
    message: "Passwords do not match",
  }
);

export type UserRegisterInfo = z.infer<typeof RegisterUserSchema>;

export interface UserSlice {
  user: User;
  message?: string;
  loading?: boolean;
  login: (user: UserLoginInfo) => void;
  signup: (user: UserRegisterInfo) => void;
  forgot_password: (email: ForgotPasswordInfo) => Promise<void>;
  reset_password: (user: ResetPasswordInfo) => Promise<void>;
  logout: () => void;
  update_profile: (user: User) => Promise<void>;
}
