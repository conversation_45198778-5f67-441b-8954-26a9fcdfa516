interface searchSettingsSlice {
  searchSettings: ISearchSettings;

  message?: string;
  loading?: boolean;

  get_one_searchSettings: (bot_id: number) => Promise<void>;
  create_one_searchSettings: (
    searchSettings: Omit<ISearchSettings, "search_setting_id">
  ) => Promise<void>;
  update_one_searchSettings: (
    searchSettings: Omit<ISearchSettings, "search_setting_id">
  ) => Promise<void>;
}

type SearchCrud = Omit<ISearchSettings, "search_setting_id">;

interface ISearchSettings {
  kb_type: string;
  search_setting_id: number;
  engine: string;
  bot_id: number;
}

export type { searchSettingsSlice, ISearchSettings, SearchCrud };
