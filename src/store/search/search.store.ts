import * as api from "apis/searchSettings.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import SearchState from "./search.state";
import { SearchCrud, searchSettingsSlice } from "./search.types";

const SearchSettingsStore = (set, get) => ({
  ...SearchState,
  message: "",
  loading: false,

  get_one_searchSettings: async (bot_id: number) => {
    api.getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          searchSettings: SearchState.searchSettings,
        });
      } else {
        set({
          searchSettings: data,
        });
      }
    });
  },

  create_one_searchSettings: async (searchSettings: SearchCrud) => {
    set({ loading: true })
    const data = await api.createOne(searchSettings)
    if (data && data?.message) {
      set({ message: data?.message });
      toast.error(data?.message);
      return;
    } else {
      set({
        searchSettings: data,
      });
      toast.success("Search Settings updated successfully");
    }
    set({ loading: false })
  },

  update_one_searchSettings: async (searchSettings: SearchCrud) => {
    set({ loading: true })
    api.updateOne(searchSettings).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          searchSettings: data,
        });
        toast.success("Search Settings updated successfully");
      }
      set({ loading: false })
    });
  },
});

const useSearchSettingsStore = create(
  persist<searchSettingsSlice>(SearchSettingsStore, {
    name: "searchSettings",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useSearchSettingsStore;
