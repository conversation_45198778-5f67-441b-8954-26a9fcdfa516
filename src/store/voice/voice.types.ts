
 type TVoice = {
  value: string;
  category: string;
  gender: "Female" | "Male";
  country: string;
  language: "English" | "ARABIC";
  sample: string;
};
type TBotVoice = {
  bot_id: number;
  bot_voice_id: number;
  createdAt?: string;
  show_stt_text: boolean;
  stt_active: boolean;
  tts_active: boolean;
  tts_gender: "male" | "female";
  updatedAt?: string;
  voice_active: boolean;
  custom_voice_active: boolean;
  female_voice:string;
  male_voice:string;
};



type VoiceSlice ={
  botVoice: TBotVoice;
  voices: TVoice[];
  selectedVoices: any[];
  message?: string;
  loading?: boolean;
  voiceData:TVoice[]
  setSelectedVoices: (value:string,index:number) => void
  get_bot_voice: (bot_id: number) => Promise<void>;
  update_bot_voice:(data: TBotVoice) => void;

}

export type { TVoice, VoiceSlice,TBotVoice };
