import { isArabic } from "helpers/helper";
import { TVoice } from "./voice.types";
import { getSpeechText } from "apis/tts.api";
import generateStorageId from "helpers/generateStorageId";

const generateVoicePath = async (selectedVoices: TVoice[],answer: string, gender: string, botFileName: string) => {
    const AnswerLanguage = isArabic(answer) ? "ar" : "en";
    const audioData = selectedVoices?.filter((voice) => {
      return (
        voice.gender.toLocaleLowerCase() === gender.toLocaleLowerCase() &&
        voice.language.toLocaleLowerCase() === AnswerLanguage.toLocaleLowerCase()
      );
    });
  
    const audio = await getSpeechText({
      text: answer,
      voice: audioData[0]?.value,
      object_name: "BOTS/" + botFileName + "/" + generateStorageId(10) + ".mp3",
    });
  
    return audio.remote_path;
  };

  export {generateVoicePath}