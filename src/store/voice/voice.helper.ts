
import { isArabic } from "helpers/helper";

function concatenateURLs(arURL: string, enURL: string): string {
  return `ar:${arURL}_en:${enURL}`;
}

function extractMultiLangFromString(concatenatedString: string): { ar: string; en: string } {
  const regex = /ar:(.*?)_en:(.*)/;
  const matches = concatenatedString?.match(regex);
  return matches ? { ar: matches[1], en: matches[2] } : { ar: '', en: '' };
}

type TFormData = {
  answer: string;
  voice_path_female: string;
  voice_path_male: string;
};

function handleMultiVoice(formData: TFormData, audioUrl: string, gender: 'female' | 'male'): string {
  const voicePathKey = gender === 'female' ? 'voice_path_female' : 'voice_path_male';
  const voicePaths = extractMultiLangFromString(formData[voicePathKey]);

  const arabicUrl = isArabic(formData.answer) ? audioUrl : voicePaths.ar || '';
  const englishUrl = isArabic(formData.answer) ? voicePaths.en || '' : audioUrl;

  return concatenateURLs(arabicUrl, englishUrl);
}

const removeBetweenSymbols = (inputString: string) => {
  if (typeof inputString !== 'string' || inputString.length === 0) {
    return inputString;
  }
  const doubleHyphensRegex = /--.*?--/g;
  const dollarAndCurlyRegex = /\$\{.*?\}|\$.*?{.*?}/g;
  const doubleAtSignsRegex = /@@(.*?)@@/g // Keep what's inside @@

  let result = inputString.replace(doubleHyphensRegex, '');
  result = result.replace(dollarAndCurlyRegex, '');
  result = result.replace(doubleAtSignsRegex, '$1');
  return result;
};


export { concatenateURLs, extractMultiLangFromString, handleMultiVoice,removeBetweenSymbols };
