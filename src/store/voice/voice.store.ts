import * as api from "apis/voice.api";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import VoiceState from "./voice.state";
import {TBotVoice, TVoice, VoiceSlice } from "./voice.types";
import { extractMultiLangFromString } from "./voice.helper";

const VoiceStore = (set, get) => ({
  ...VoiceState,
  message: "",
  loading: false,

  get_bot_voice: async (bot_id: number) => {
    const data  = await api.getOne(bot_id);
    set({ loading: true });
    if (data && data?.message) {
      set({ message: data?.message });
    } else {
      const voice = data
      const voiceData = get().voiceData 

      const filterVoices = (gender: string, language: string):TVoice[] =>
        voiceData?.filter(
          (voice) =>
            voice.gender.toLowerCase() === gender &&
            voice?.language.toLowerCase() === language
        );
      const filterVoice = (value: string) => {
        return voiceData.find((voice) => voice?.value === value);
      };
  
      const femaleVoice = extractMultiLangFromString(voice?.female_voice || "");
      const maleVoice = extractMultiLangFromString(voice?.male_voice || "");
      const botData = [
        {
          id: "maleVoiceAR",
          value: maleVoice?.ar || "ar-KW-Saad",
          voices: filterVoices("male", "arabic"),
          language: "ar",
          gender: "male",
          sample:
            filterVoice(maleVoice?.ar)?.sample ||
            "https://infotointell.fra1.digitaloceanspaces.com/tts/026f8b4c-a9b5-4024-bc03-1d9e2f850c73.mp3",
        },
        {
          id: "maleVoiceEN",
          value: maleVoice?.en || "en-GB-James",
          voices: filterVoices("male", "english"),
          language: "en",
          gender: "male",
          sample:
            filterVoice(maleVoice?.en)?.sample ||
            "https://infotointell.fra1.digitaloceanspaces.com/tts/28a89183-ff5f-4ada-acf4-f3fe901ace3b.mp3",
        },
        {
          id: "femaleVoiceAR",
          value: femaleVoice?.ar || "ar-KW-Hind",
          voices: filterVoices("female", "arabic"),
          language: "ar",
          gender: "female",
          sample:
            filterVoice(femaleVoice?.ar)?.sample ||
            "https://infotointell.fra1.digitaloceanspaces.com/tts/51a16ced-9699-47ae-8be7-902c81924a9b.mp3",
        },
        {
          id: "femaleVoiceEN",
          value: femaleVoice?.en || "en-GB-Emma",
          voices: filterVoices("female", "english"),
          language: "en",
          gender: "female",
          sample:
            filterVoice(femaleVoice?.en)?.sample ||
            "https://infotointell.fra1.digitaloceanspaces.com/tts/fe19679a-e347-4e1a-93a0-be080368cfad.mp3",
        },
      ];


      set({ loading: false });
      set({
        botVoice: data ? data : {},
        selectedVoices:botData,
      });
    }
  },
  update_bot_voice:(data: TBotVoice) => {
      set({
        botVoice: data ? data : {},
      });
  },

  setSelectedVoices: (value:string,index:number) => {
    const prev = get()?.selectedVoices
    const voiceData = get()?.voiceData
      const filteredVoice = [...prev];
      const updatedVoice = { ...filteredVoice[index] };
      const filterdSample = voiceData?.filter((voice) => {
        return voice.value === value;
      })[0]?.sample;
      updatedVoice.value = value;
      updatedVoice.sample = filterdSample;
      filteredVoice[index] = updatedVoice;
    set({ selectedVoices: filteredVoice });
  },
});

const useVoiceStore = create(
  persist<VoiceSlice>(VoiceStore, {
    name: "Voices",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useVoiceStore;
