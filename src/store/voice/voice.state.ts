import { TBotVoice, TVoice } from "./voice.types";

export const voiceData:<PERSON><PERSON><PERSON>[]= [
  {
    value: "ar-KW-Saad",
    category: "GCC",
    gender: "Male",
    country: "Kw",
    language: "ARABIC",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/026f8b4c-a9b5-4024-bc03-1d9e2f850c73.mp3'
  },
  {
    value: "ar-DZ-Omar",
    category: "NA",
    gender: "Male",
    country: "Algeria",
    language: "ARABIC",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/850e656e-aa17-4dd1-b4b7-8105e76ae2b0.mp3'
  },
  {
    value: "ar-KW-Hind",
    category: "GCC",
    gender: "Female",
    country: "Kw",
    language: "ARABIC",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/51a16ced-9699-47ae-8be7-902c81924a9b.mp3'
  },

  {
    value: "ar-BH-Zainab",
    category: "GCC",
    gender: "Female",
    country: "BAH",
    language: "ARABIC",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/4aea869f-a1c8-4e22-838d-dbcca01a46e5.mp3'
  },

  {
    value: "en-GB-James",
    category: "GB",
    gender: "Male",
    country: "ENG-1",
    language: "English",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/28a89183-ff5f-4ada-acf4-f3fe901ace3b.mp3'
  },
  {
    value: "en-GB-Emma",
    category: "US",
    gender: "Female",
    country: "US-1",
    language: "English",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/fe19679a-e347-4e1a-93a0-be080368cfad.mp3'
  },
  {
    value: "en-US-William",
    category: "US",
    gender: "Male",
    country: "US-3",
    language: "English",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/044d7a72-8c6b-4d3c-b813-53bcd50d43a8.mp3'
  },
  {
    value: "en-US-Jessica",
    category: "US",
    gender: "Female",
    country: "US-3",
    language: "English",
    sample:'https://infotointell.fra1.digitaloceanspaces.com/tts/87576cb4-9bae-48fd-b02f-726d33cf98ac.mp3'
  },
]
const VoiceState = {
  botVoice:{
    bot_id: 0,
    bot_voice_id: 0,
    createdAt: '',
    show_stt_text: false,
    stt_active: false,
    tts_active: false,
    tts_gender: "male" ,
    updatedAt: '',
    voice_active: false,
    custom_voice_active: false,
    female_voice:'',
    male_voice:'',
  } as TBotVoice,
  voices: [],
  voiceData:voiceData,
  selectedVoices:[]
};

export default VoiceState;
