interface TOPICSlice {
  topic: ITopic;
  topics: ITopic[];

  message?: string;
  loading?: boolean;
  fetchLoading?: boolean;

  get_all_topics: (bot_id: number) => Promise<void>;
  create_one_topic: (topic: any) => Promise<void>;
  update_one_topic: (topic: any) => Promise<void>;
  delete_one_topic: (data: any) => Promise<void>;
  set_topic: (id: string) => void;
}

interface ITopic {
  topic_id: number;
  bot_id: number;
  display_name: string;
  keywords: string[];
  lemmatized_keywords: string[];
  createdAt: string;
  updatedAt: string;
}

export type { TOPICSlice, ITopic };
