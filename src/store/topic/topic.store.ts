import * as api from "apis/topic.api";
import { create } from "zustand";
import toast from "react-hot-toast";
import { persist } from "zustand/middleware";
import topicState from "./topic.state";
import { TOPICSlice } from "./topic.types";
const TOPICStore = (set, get) => ({
  ...topicState,
  message: "",
  loading: false,
  fetchLoading: false,

  get_all_topics: async (bot_id: number) => {
    set({ fetchLoading: true });
    const data = await api.getAll(bot_id);
    if (data?.message) {
      set({ message: data?.message });
    } else {
      set({ topics: data || [] });
    }
    set({ fetchLoading: false });
  },
  set_topic: (id: string) => {
    const foundTopic = get().topics.find((t) => t.topic_id === +id);
    set({ topic: foundTopic });
  },
  create_one_topic: async (topic: any) => {
    set({ loading: true });
    const data = await api.createOne(topic);
    if (data?.message) {
      set({ message: data?.message });
    } else {
      toast.success("Topic created successfully");
      set({ topics: [...get().topics, data], loading: false });
    }
    set({ loading: false });
  },
  update_one_topic: async (topic: any) => {
    set({ loading: true });
    await api.updateOne(topic).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        const dataWithoutUpdated = get().topics.filter(
          (t) => t.topic_id !== topic.topic_id
        );
        set({ topics: [...dataWithoutUpdated, data], loading: false });
      }
    });
    set({ loading: false });
  },
  delete_one_topic: async (data: any) => {
    set({ loading: true });
    await api.deleteOne(data).then((res) => {
      // if (data?.message) {
      //   set({ message: data?.message });
      // } else {
      const dataWithoutDeleted = get().topics.filter(
        (t) => t.topic_id !== data.topic_id
      );
   

      set({ topics: dataWithoutDeleted, loading: false });
      // }
    });
    set({ loading: false });
  },
});

const useTOPICStore = create(
  persist<TOPICSlice>(TOPICStore, {
    name: "topic",
    partialize: (state) => {
      const { message, loading, fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useTOPICStore;
