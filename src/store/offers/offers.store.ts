import * as api from "apis/offers.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import OfferState from "./offers.state";
import { OfferSlice } from "./offers.types";

const offerStore = (set, get) => ({
  ...OfferState,
  message: "",
  loading: true,

  get_all_offers: async (bot_id: number) => {
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          OFFERS: data ? data : [],
        });
      }
  },

  create_one_offer: async (offer: any) => {
    const data = await api.createOne(offer)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          OFFERS: [...get().OFFERS, data],
          OFFER: data,
        });
      }
      return data;
  },

  update_one_offer: async (offer: any) => {
    const data = await api.updateOne(offer)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          OFFERS: get().OFFERS.map((a) =>
            a.offer_id === data.offer_id ? data : a
          ),
          OFFER: data,
        });
      }
      return data;
  },
  delete_one_offer: async (offer: any) => {
    const data = await api.deleteOne({ offer_id: offer })
      if (data && data?.message) {
        set({
          OFFERS: get().OFFERS.filter((a) => a.offer_id !== offer),
        });
      }
  },
});

const useOfferStore = create(
  persist<OfferSlice>(offerStore, {
    name: "offers",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useOfferStore;
