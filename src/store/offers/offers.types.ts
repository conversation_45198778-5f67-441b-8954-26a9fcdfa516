// [{\"offer_id\":110,\"offer_status\":\"active\",\"offer_description\":\"10 percent off\",\"fixed_value\":0,\"offer_type\":\"Percentage\",\"offer_icon\":\"\",\"offer_percentage\":0.1,\"offer_date_start\":\"2023-01-24T13:31:35.555Z\",\"offer_date_end\":\"2023-05-31T00:00:00.000Z\",\"createdAt\":\"2023-01-24T13:32:16.693Z\",\"updatedAt\":\"2023-03-16T09:17:06.906Z\",\"bot_id\":669},{\"offer_id\":111,\"offer_status\":\"active\",\"offer_description\":\"minus 5 jds\",\"fixed_value\":5,\"offer_type\":\"Fixed\",\"offer_icon\":\"\",\"offer_percentage\":0,\"offer_date_start\":\"2023-03-16T09:17:08.908Z\",\"offer_date_end\":\"2023-03-31T00:00:00.000Z\",\"createdAt\":\"2023-03-16T09:17:28.861Z\",\"updatedAt\":\"2023-03-16T09:17:28.861Z\",\"bot_id\":669}]"

interface IOffer {
  offer_id: number;
  offer_status: string;
  offer_description: string;
  fixed_value: number;
  offer_type: string;
  offer_icon: string;
  offer_percentage: number;
  offer_date_start: string;
  offer_date_end: string;
  createdAt: string;
  updatedAt: string;
  bot_id: number;
}

interface OfferSlice {
  OFFER: IOffer;
  OFFERS: IOffer[];

  message?: string;
  loading?: boolean;

  get_all_offers: (bot_id: number) => Promise<void>;
  create_one_offer: (offer: any) => Promise<any>;
  update_one_offer: (offer: any) => Promise<any>;
  delete_one_offer: (offer: any) => Promise<void>;
}

export type { IOffer, OfferSlice };
