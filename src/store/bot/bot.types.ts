import { z } from "zod";
interface IBot {
  bot_category: string | null;
  bot_id: number | null;
  bot_name: string | null;
  bot_type: string | null;
  deleted: boolean | null;
  description: string | null;
  domain: string | null;
  fallback_dialog: any | null;
  fallback_message: string | null;
  file_name: string | null;
  icon: string | null;
  is_template: boolean | null;
  language: string | null;
  mock_endpoint: string | null;
  plan_id: number | null;
  service_secret: string | null;
  status: string | null;
  user_id: number | null;
}

interface BotSlice {
  bot: IBot;
  bots: IBot[];
  design: any;
  planfunction: any;

  message?: string;
  loading?: boolean;

  get_all_bots: (user_id: number) => Promise<void>;
  get_one_bot: (bot_id: number) => Promise<void>;
  create_bot: (bot: IBot) => Promise<void>;
  update_one_bot: (bot: any) => Promise<void>;
  delete_one_bot: (bot: any) => Promise<void>;
  duplicate_bot: (bot: IBot) => Promise<void>;
}

const UpdateBotSchema = z.object({
  user_id: z.number(),
  bot_name: z.string(),
  description: z.string(),
  language: z.string(),
});

type UpdateBotInfo = z.infer<typeof UpdateBotSchema>;

export type { BotSlice, IBot, UpdateBotInfo };
export { UpdateBotSchema };
