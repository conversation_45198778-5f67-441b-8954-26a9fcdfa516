import * as api from "apis/bot.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";
import { BotSlice, IBot } from "./bot.types";
import BotState from "./bot.state";
import toast from "react-hot-toast";

const botStore = (set, get) => ({
  ...BotState,
  message: "",
  loading: true,
  get_all_bots: async (user_id: number) => {
    const data = await api.getAll(user_id);
    if (data?.message) {
      set({ message: data?.message });
    } else {
      set({ bots: data || [], loading: false });
    }
  },
  get_one_bot: async (bot_id: number) => {
    const data = await api.getOne(bot_id);
    if (data?.message) {
      set({ message: data?.message });
    } else {
      set({
        bot: data?.bot || {},
        design: data?.design || {},
        planfunction: data?.planfunction || {},
      });
    }
  },
  create_bot: async (bot: any) => {
    const data = await api.createOne(bot);
    if (data?.message) {
      set({ message: data?.message });
      toast.error(data?.message);
    } else {
      set({
        bot: data?.bot || {},
        design: data?.design || {},
        planfunction: data?.planfunction || {},
      });
    }
  },
  update_one_bot: async (bot: any) => {
    const data = await api.updateOne(bot);
    if (data?.message) {
      toast.error(data?.message);
      set({ message: data?.message });
    } else {
      set({
        bot: data?.bot || {},
        design: data?.design || {},
        planfunction: data?.planfunction || {},
      });
    }
  },
  delete_one_bot: async (bot: any) => {
    const data = await api.deleteOne(bot);
    if (data?.message) {
      set({ message: data?.message });
      toast.success(data?.message);
    }
  },
  duplicate_bot: async (bot: IBot) => {
    const data = await api.duplicateBot(bot);
    if (data?.message) {
      set({ message: data?.message });
      toast.error(data?.message);
    } else {
      set({
        bot: data?.bot || {},
        design: data?.design || {},
        planfunction: data?.planfunction || {},
      });
    }
  },
});

const useBotStore = create(
  persist<BotSlice>(botStore, {
    name: "bot",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useBotStore;
