import { create } from "zustand";
import { IEditorPrivilges, UserPrivilegeSlice } from "./UserPrivilege.types";
import UserPrivilegeState from "./UserPrivilege.state";
import { persist } from "zustand/middleware";

const UserPrivilegeStore = (set, get) => ({
  ...UserPrivilegeState,
  message: "",
  loading: false,

  set_user_priviliges: (priviliges: IEditorPrivilges) => {
    set({
      priviliges: priviliges,
    });
  },
  set_is_admin: (isAdmin: boolean) => {
    set({
      isAdmin: isAdmin,
    });
  },
});

const useUserPrivilegeStore = create(
  persist<UserPrivilegeSlice>(UserPrivilegeStore, {
    name: "UserPrivileges",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useUserPrivilegeStore;
