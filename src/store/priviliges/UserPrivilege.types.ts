interface IEditorPrivilges {
  appearance_privilege: boolean;
  builder_privilege: boolean;
  store_privilege: boolean;
  transaction_dashboard_privilege: boolean;
  sales_dashboard_privilege: boolean;
  deployment_privilege: boolean;
  editor_privilege: boolean;
  lead_privilege: boolean;
  report_privilege: boolean;
  addon_privilege: boolean;
  dialog_privilege: boolean;
  agent_privilege: boolean;
  ticketing_privilege: boolean;
}

interface UserPrivilegeSlice {
  priviliges: IEditorPrivilges;
  isAdmin: boolean;
  message?: string;
  loading?: boolean;

  set_user_priviliges: (priviliges: IEditorPrivilges) => void;
  set_is_admin: (isAdmin: boolean) => void;
}

export type { IEditorPrivilges, UserPrivilegeSlice };
