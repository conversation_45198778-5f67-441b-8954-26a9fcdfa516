import { create } from "zustand";
import toast from "react-hot-toast";
import { persist } from "zustand/middleware";

import * as api from "apis/unstructuredResources.api";
import unstructuredResourcesState from "./unstructuredResources.state";
import { IUnsterecteredResourcesSlice } from "./unstructuredResources.types";

const unstructuredResourcesStore = (set, get) => ({
  ...unstructuredResourcesState,
  message: "",
  loading: false,

  get_all_unstructuredResources: async (bot_id: number) => {
    api.getAll(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          unstructuredResources:
            unstructuredResourcesState.unstructuredResources,
        });
      } else {
        set({ unstructuredResources: data, loading: false });
      }
    });
  },

  create_one_unstructuredResource: async (unstructuredResource: any) => {
    set({ loading: true });
    const data = await api.createOne(unstructuredResource)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          unstructuredResources: [...get().unstructuredResources, data],
        });
      }
      set({ loading: false });
  },

  create_many_unstructuredResources: async (unstructuredResources: any) => {
    set({ loading: true });
    const data = await api.createMany(unstructuredResources)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        toast.success("Files uploaded successfully");
        set({
          // unstructuredResources: [...get().unstructuredResources, data],
          // loading: false,
        });
      }
      set({ loading: false });
  },

  delete_one_unstructuredResource: async (unstructuredResource_id: number) => {
    set({ loading: true });
    const data = await api.deleteOne({ resource_id: unstructuredResource_id })
      if (data && data?.message) {
        set({
          message: data?.message,
          unstructuredResources: get().unstructuredResources.filter(
            (unstructuredResource) =>
              unstructuredResource.resource_id !== unstructuredResource_id
          ),
        });
        toast.success("File deleted successfully");
      }
      set({ loading: false });
  },

  delete_many_unstructuredResources: async (unstructuredResources: any) => {
    set({ loading: true });
    const data = await api.deleteMany(unstructuredResources)
      if (data && data?.message) {
        set({
          message: data?.message,
        });
        toast.success("Files deleted successfully");
      }
      set({ loading: false });
  },
});

const useUnstructuredResourcesStore = create(
  persist<IUnsterecteredResourcesSlice>(unstructuredResourcesStore, {
    name: "unstructuredResources",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useUnstructuredResourcesStore;
