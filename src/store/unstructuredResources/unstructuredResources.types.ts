interface IUnsterecteredResourcesSlice {
  unstructuredResources: IUnstructuredResource[];
  message?: string;
  loading?: boolean;

  create_one_unstructuredResource: (unstructuredResource: any) => Promise<void>;
  create_many_unstructuredResources: (
    unstructuredResources: any
  ) => Promise<void>;
  delete_one_unstructuredResource: (
    unstructuredResource_id: number
  ) => Promise<void>;
  delete_many_unstructuredResources: (
    unstructuredResources: any
  ) => Promise<void>;
  get_all_unstructuredResources: (bot_id: number) => Promise<void>;
}

interface IUnstructuredResource {
  resource_id: number;
  resource_name: string;
  resource_type: string;
  resource_url: string;
  bot_id: number;
}

export type { IUnsterecteredResourcesSlice, IUnstructuredResource };
