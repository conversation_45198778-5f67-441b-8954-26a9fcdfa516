import * as api from "apis/llmIntegration.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import LLMS<PERSON> from "./llm.state";
import { LLMCreate, LLMSlice, LLMUpdate } from "./llm.types";

const LLMStore = (set:any , get: any) => ({
  ...LLMState,
  message: "",
  loading: true,

  get_one_LLM: async (bot_id: number) => {
    api.getLLMIntegration(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          llm: LLMState.llm,
        });
      } else {
        set({
          llm: data,
        });
      }
    });
  },

  create_one_LLM: async (llm: LLMCreate) => {
    api.setLLMIntegration(llm).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          llm: data,
        });
        toast.success("LLM created successfully");
      }
    });
  },

  update_one_LLM: async (llm: LLMUpdate) => {
    api.updateLLMIntegration(llm).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          llm: data,
        });
        toast.success("LLM updated successfully");
      }
    });
  },

  delete_one_LLM: async (bot_id: number, LLM_integration_id: number) => {
    api.deleteLLMIntegration({ bot_id, LLM_integration_id }).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          llm: LLMState.llm,
        });
        toast.success(data?.message);
      }
    });
  },
});

const useLLMStore = create(
  persist<LLMSlice>(LLMStore, {
    name: "LLM_integration",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useLLMStore;
