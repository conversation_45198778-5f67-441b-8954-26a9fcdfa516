import constant from "../../constant";
const LLMState = {
  llm: {
    LLM_integration_id: 0,
    bot_id: 0,
    llm_type: "openai",
    llm_model: "gpt-4o-mini",
    llm_key: "",
    llm_temperature: 0.5,
    chunk_size: 2500,
    chunk_overlap: 250,
    chat_history: true,
    store_url: constant.RAG_STORE_URL,
    store_type: "chroma",
    collection_name: "",
    status_active: false,
    personal_vector_db: false,
    chunk_methodology: "recursive",
    top_k: 20,
    persona: "",
    llm_embedding_model: "",
    fetch_k: 50,
    lambda_mult: 0.5,
  },
};

export default LLMState;
