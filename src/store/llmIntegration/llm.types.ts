interface LLM {
    LLM_integration_id: number,
    bot_id: number,
    llm_type: string,
    llm_model: string,
    llm_key: string,
    llm_temperature: number,
    chunk_size: number,
    chunk_overlap: number,
    chat_history: boolean,
    store_url: string,
    status_active: boolean,
    store_type: string,
    collection_name: string,
    personal_vector_db: boolean,
    top_k: number,
    chunk_methodology: string,
    persona: string,
    llm_embedding_model: string,
    fetch_k: number,
    lambda_mult: number
}

type LLMCreate = Omit<LLM, "LLM_integration_id">;
type LLMUpdate = Pick<LLM, "bot_id" | "LLM_integration_id"> & Partial<Omit<LLM, "bot_id" | "LLM_integration_id">>;


interface LLMSlice {
  llm: LLM;
  message?: string;
  loading?: boolean;

  get_one_LLM: (bot_id: number) => Promise<void>;
  create_one_LLM: (gpt: LLMCreate) => Promise<void>;
  update_one_LLM: (gpt: LLMUpdate) => Promise<void>;
  delete_one_LLM: (bot_id: number, LLM_integration_id) => Promise<void>;
}

export type { LL<PERSON>lice, LLM, LLMCreate, LLMUpdate };
