import * as api from "apis/faq.api";
import { create } from "zustand";
import toast from "react-hot-toast";
import { persist } from "zustand/middleware";

import FAQState from "./faq.state";
import { FAQdelete, FAQSlice } from "./faq.types";
const FAQStore = (set, get) => ({
  ...FAQState,
  message: "",
  loading: false,
  fetchLoading: false,

  get_all_faqs: async (bot_id: number) => {
    set({fetchLoading: true})
    const data = await api.getAll(bot_id)
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQs: data || [] });
      }
      set({fetchLoading: false})
  },
  get_all_faqs_first: (bot_id: number) => {
    api.getFirst(bot_id).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQs: data, loading: false });
      }
    });
  },
  get_one_faq: (faq_id: number) => {
    api.getOne(faq_id).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQ: data, loading: false });
      }
    });
  },
  create_one_faq: async (faq: any) => {
   const data = await api.createOne(faq)
      if (data?.message) {
        set({ message: data?.message });
      } else {
        toast.success("FAQ created successfully");
        set({ FAQs: [...get().FAQs, data], FAQ: data, loading: false });
      }
  },
  update_one_faq: (faq: any) => {
    api.updateOne(faq).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQs: [...get().FAQs, data], FAQ: data.faq, loading: false });
      }
    });
  },
  update_many_faqs: async (faqs: any) => {
    api.updateMany(faqs).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQs: data, loading: false });
      }
    });
  },
  delete_one_faq: async (faq: FAQdelete) => {
    api.deleteOne(faq).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
        toast.success(data?.message);
      }
    });
  },
  create_many_faqs: async (faqs: any) => {
    api.createMany(faqs).then((data) => {
      if (data) {
        if (data?.message) {
          set({ message: data?.message });
        } else {
          set({ FAQs: Array.isArray(get().FAQs) ? [...get().FAQs, ...data] : data, loading: false });
        }
      }
    });
  },
  delete_many_faqs: async (faqs: any) => {
    api.deleteMany(faqs).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQs: data, loading: false });
      }
    });
  },
  delete_all_faqs: async (bot_id: any) => {
    api.deleteAll({bot_id}).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        set({ FAQs: [], loading: false });
      }
    });
  },
  scrape_faqs: (faqs: any) => {
    api.scrapeFaqs(faqs).then((data) => {
      if (data) {
        if (data?.message) {
          set({ message: data?.message });
        } else {
          set({ FAQs: [...get().FAQs, ...data], loading: false });
        }
      }
    });
  },
  import_faqs_pdf: (formData: any, bot_id: number) => {
    api.importFaqsPdf(formData, bot_id).then((data) => {
      if (data) {
        if (data?.message) {
          set({ message: data?.message });
        } else {
          set({ FAQs: [...get().FAQs, ...data], loading: false });
        }
      }
    });
  },


  update_one_faq_voice: (faq: any) => {
    api.updateOneVoice(faq).then((data) => {
      if (data?.message) {
        set({ message: data?.message });
      } else {
        set({ FAQs: [...get().FAQs, data], FAQ: data.faq, loading: false });
      }
    });
  },
});

const useFAQStore = create(
  persist<FAQSlice>(FAQStore, {
    name: "faqs",
    partialize: (state) => {
      const { message, loading,fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useFAQStore;
