import { z } from "zod";

interface FAQSlice {
  FAQ: IFAQ | {};
  FAQs: IFAQ[];

  message?: string;
  loading?: boolean;
  fetchLoading?: boolean;

  get_all_faqs: (bot_id: number) => Promise<void>;
  get_all_faqs_first: (bot_id: number) => void;
  get_one_faq: (faq_id: number) => void;
  create_one_faq: (faq: CreateFAQInfo) => Promise<void>;
  update_one_faq: (faq: any) => void;
  update_one_faq_voice: (faq: any) => void;
  update_many_faqs: (faqs: any) => Promise<void>;
  delete_one_faq: (faq: FAQdelete) => Promise<void>;
  create_many_faqs: (faqs: any) => Promise<void>;
  delete_many_faqs: (faqs: {
    bot_id: number;
    faqs: string[]
  }) => Promise<void>;
  delete_all_faqs: (faqs: any) => Promise<void>;
  scrape_faqs: (faqs: any) => void;
  import_faqs_pdf: (formData: any, bot_id: number) => void;
}

interface IFAQ {
  answer: string;
  bot_id: number;
  createdAt: string;
  faq_id: number;
  hidden: false;
  parent_id: number | null;
  question: string;
  question_key_num: number;
  voice_path_male: string;
  voice_path_female: string;
  lemmatized_question: string;
  updatedAt: string;
  topic_id: number | null;
}

const CreateFAQSchema = z.object({
  user_id: z.number(),
  voice_path_male:  z.string(),
  voice_path_female: z.string(),
  // check if question is empty after trimming
  question: z
    .string()
    .min(1, "Question is required")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Question is required"),
  answer: z
    .string()
    .min(1, "Answer is required")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Answer is required"),
});

type CreateFAQInfo = z.infer<typeof CreateFAQSchema>;

type FAQdelete = {
  bot_id: number;
  faq_id: number;
  user_id: number;
};

export type { FAQSlice, CreateFAQInfo, FAQdelete, IFAQ };
export { CreateFAQSchema };
