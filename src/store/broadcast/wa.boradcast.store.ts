import * as api from "apis/broadcast.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import BroadcastState from "./wa.boradcast.state";
import { BroadcastSlice } from "./wa.boradcast.types";

const broadcastStore = (set, get) => ({
  ...BroadcastState,
  message: "",
  loading: false,

  create_one_broadcast: async (lc: any) => {
    set({ loading: true });
    const data = await api.createOne(lc);
    if (data && data?.message) {
      // console.log(data)
      // toast.error(data?.message);
      // set({
      //   message: data?.message,
      // });
      return;
    } else {
      set({
        //   broadcast: data,
        message: "",
      });
      toast.success("broadcast connection created successfully");
    }
    set({ loading: false });
  },

  get_braodcasts: async (bot_id: number) => {
    const data = await api.getbroadcasts(bot_id);
    if (data && data?.message) {
      set({
        message: data?.message,
      });
    } else {
      set({
        broadcast: data,
      });
    }
  },
  get_braodcasts_logs: async (broadcast_id: number) => {
    const data = await api.getbroadcastLogs(broadcast_id);
    if (data && data?.message) {
      set({
        message: data?.message,
      });
    } else {
      set({
        broadcastLogs: data,
      });
    }
  },
  get_wa_phone_numbers: async (bot_id: number) => {
    const data = await api.getWaPhoneNumbers(bot_id);
    if (data && data?.message) {
      set({
        message: data?.message,
      });
    } else {
      set({
        phoneNumbers: data,
      });
    }
  },
});

const useBroadcastStore = create(
  persist<BroadcastSlice>(broadcastStore, {
    name: "broadcast",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useBroadcastStore;
