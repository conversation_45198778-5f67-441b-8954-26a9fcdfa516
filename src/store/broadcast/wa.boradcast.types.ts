interface IBroadcast {
  [x: string]: any;
  broadcast_id: number;
  broadcast_name: string;
  message: string;
  image_url: string;
  image_id: string;
  sent_count: number;
  template: string;
  createdAt?: string;
  updatedAt?: string;
  bot_id: number;
}
interface IBroadcastLogs {
  [x: string]: any;
  phone_number: string;
  status: string;
  broadcast_id: number;
}
interface IWaPhonenumbers {
  [x: string]: any;
  tr_session: string;
  date: string;
  messages_number: number;
}

interface BroadcastSlice {
  broadcast: IBroadcast;
  broadcastLogs: IBroadcastLogs[];
  phoneNumbers: IWaPhonenumbers[];
  message?: string;
  loading?: boolean;

  get_braodcasts: (bot_id: number) => Promise<void>;
  get_braodcasts_logs: (broadcast_id: number) => Promise<void>;
  get_wa_phone_numbers: (bot_id: number) => Promise<void>;
  create_one_broadcast: (data: any) => Promise<void>;
}

export type { BroadcastSlice, IBroadcast };
