import * as api from "apis/dynamicForm.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import DynamicFormState from "./dynamicForm.state";
import { DynamicFormSlice } from "./dynamicForm.types";

const DynamicFormStore = (set: any, get: any) => ({
  ...DynamicFormState,
  message: "",
  loading: true,

  get_all_dynamic_form_schemas: async (bot_id: number) => {
    api.getAll(bot_id).then((schemas) => {
      if (schemas)
        set({
          schemas,
        });
    });
  },

  create_one_dynamic_form: async (payload: {
    bot_id: number;
    schema: Record<"label", string>[];
    title: string;
  }) => {
    api.createSchema(payload).then((data) => {
      if (data) {
        const schemas = get().schemas;
        set({
          schemas: [...schemas, data],
        });
        toast.success("Dynamic Report created successfully");
      } else {
        toast.error(data?.message);
      }
    });
  },
  delete_dynamic_form_schemas: async (bot_id: number,dynamic_form_schema_id:number) => {
    api.deleteSchema(bot_id,dynamic_form_schema_id).then((message) => {
      const schemas = get().schemas?.filter((schema)=>schema.dynamic_form_schema_id !==dynamic_form_schema_id) || [];
        set({
          schemas,
        });
        toast.success("Dynamic Report deleted successfully");
    });
  },
});

const useDynamicFormStore = create(
  persist<DynamicFormSlice>(DynamicFormStore, {
    name: "dynamic_form_schema_id",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useDynamicFormStore;
