interface DynamicForm {
  dynamic_form_schema_id: number;
  bot_id: number;
  deleted: boolean;
  schema: object;
}

type DynamicFormCreate = Omit<DynamicForm, "bot_id" | "schema">;

export interface DynamicFormSchema {
  dynamic_form_schema_id: number;
  bot_id: number;
  deleted: boolean;
  title: string;
  schema: Record<"label", string>[];
}

interface DynamicFormSlice {
  schemas: DynamicFormSchema[];
  message?: string;
  loading?: boolean;

  get_all_dynamic_form_schemas: (bot_id: number) => Promise<void>;
  create_one_dynamic_form: (payload: {
    bot_id: number;
    schema: Record<"label", string>[];
    title: string;
  }) => Promise<void>;
  delete_dynamic_form_schemas: (bot_id: number,delete_dynamic_form_schemas:number) => Promise<void>;

}

export type { DynamicFormSlice, DynamicForm, DynamicFormCreate };
