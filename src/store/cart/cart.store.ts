import * as api from "apis/cart.api";
import { create } from "zustand";
import helper from "./cart.helper";

import { persist } from "zustand/middleware";
import toast from "react-hot-toast";
import CartState from "./cart.state";
import { CartSlice } from "./cart.types";
const cartStore = (set, get) => ({
  ...CartState,
  message: "",
  loading: false,
  fetchLoading: false,

  get_one_cart: async (cart_id: number) => {
    set({ fetchLoading: true });
    const data = await api.getOne(cart_id)
      if (data && data?.message) {
        set({ message: data?.message, cart: helper.initCartObject() });
      } else {
        set({
          cart: data,
        });
      }
      set({ fetchLoading: false });
  },

  create_cart: async (cart: any) => {
    set({ loading: true });
    const data = await api.createOne(cart)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          cart: data,
        });
      }
      set({ loading: false });
  },

  update_one_cart: async (cart: any) => {
    set({ loading: true });
    const data = await api.updateOne(cart)
      if (data && data?.message) {
        toast.error(data?.message);
        set({ message: data?.message });
      } else {
        set({
          cart: data,
        });
      }
      set({ loading: false });
  },
});

const useCartStore = create(
  persist<CartSlice>(cartStore, {
    name: "cart",
    partialize: (state) => {
      const { message, loading, fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useCartStore;
