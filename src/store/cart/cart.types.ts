interface ICart {
  cart_id: number | null;
  cart_active: boolean | null;
  email: string | null;
  bot_id: number | null;
  phone_number: string | null;
  phone_verified: boolean | null;
  email_verified: boolean | null;
  enable_api: boolean | null;
  vat_ammount: number | null;
  trigger_name: string | null;
  decimal: number | null;
  cart_currency: string | null;
  cart_delivery: number | null;
  cart_icon: string | null;
  timezone: string | null;
  business_name: string | null;
  vat_reg_num: string | null;
  currency?: string | null;
}

type CartSlice = {
  cart: ICart;
  message?: string;
  loading?: boolean;
  fetchLoading?: boolean;

  get_one_cart: (cart_id: number) => Promise<void>;
  create_cart: (cart: Omit<ICart, "cart_id">) => Promise<void>;
  update_one_cart: (cart: ICart) => Promise<void>;
};

export type { CartSlice, ICart };
