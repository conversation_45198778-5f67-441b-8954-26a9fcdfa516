import { z } from "zod";

interface TriggerSlice {
  trigger: ITrigger | {};
  triggers: ITrigger[];

  message?: string;
  loading?: boolean;

  get_all_triggers: (bot_id: number) => Promise<void>;
  getTriggerbydialoId: (bot_id: number) => Promise<void>
  get_one_trigger: (trigger_id: number) => Promise<void>;
  create_one_trigger: (trigger: any) => Promise<void>;
  update_one_trigger: (trigger: any) => Promise<void>;
  update_many_triggers: (triggers: any) => Promise<void>;
  delete_one_trigger: (obj: {
    trigger_id: number;
    user_id: number;
  }) => Promise<void>;
  delete_many_triggers: (triggers: any) => Promise<void>;
  create_many_triggers: (triggers: any) => Promise<void>;
}

interface ITrigger {
  bot_id: number;
  createdAt: string;
  trigger: string;
  trigger_id: number;
  trigger_name: string;
  trigger_type: string;
  updatedAt: string;
  is_main: boolean;
  url: string;
  lemmatized_trigger: string;
  dialog_id: number;
}

const CreateTriggerSchema = z.object({
  user_id: z.number(),
  trigger: z
    .string()
    .min(1, "Question is required")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Question is required"),
  trigger_name: z.string(),
});

type CreateTriggerInfo = z.infer<typeof CreateTriggerSchema>;

export type { TriggerSlice, ITrigger, CreateTriggerInfo };
export { CreateTriggerSchema };
