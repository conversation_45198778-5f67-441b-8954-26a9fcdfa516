import { create } from "zustand";
import toast from "react-hot-toast";
import { persist } from "zustand/middleware";
import TriggerState from "./trigger.state";
import { TriggerSlice } from "./trigger.types";
import * as api from "apis/trigger.api";

const TriggerStore = (set, get) => ({
  ...TriggerState,
  message: "",
  loading: true,

  get_all_triggers: async (bot_id: number) => {
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message, triggers: TriggerState.triggers });
      } else {
        set({ triggers: data, loading: false });
      }
  },

  getTriggerbydialoId: async (dialog_id: number) => {
    const data = await api.getAll(dialog_id)
      if (data && data?.message) {
        set({ message: data?.message, triggers: TriggerState.triggers });
      } else {
        set({ triggers: data, loading: false });
      }
  },

  get_one_trigger: async (trigger_id: number) => {
    const data = await api.getOne(trigger_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          trigger: data,
          triggers: [...get().triggers, data],
          loading: false,
        });
      }
  },

  create_one_trigger: async (trigger: any) => {
    const data = await api.createOne(trigger)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        toast.success("Trigger created successfully");
        set({
          triggers: [...get().triggers, data],
          trigger: data,
          loading: false,
        });
      }
  },

  update_one_trigger: async (trigger: any) => {
    const data = await api.updateOne(trigger)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          triggers: [...get().triggers, data],
          trigger: data,
          loading: false,
        });
      }
  },

  update_many_triggers: async (triggers: any) => {
    const data = await api.updateMany(triggers)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          triggers: data,
          loading: false,
        });
      }
  },

  delete_one_trigger: async (trigger_id) => {
    const data = await api.deleteOne(trigger_id)
      if (data && data?.message) {
        set({ message: data?.message });
      }
  },

  delete_many_triggers: async (triggers: any) => {
    const data = await api.deleteMany(triggers)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          triggers: data,
          loading: false,
        });
      }
  },
  create_many_triggers: async (triggers: any) => {
    const data = await api.createMany(triggers)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          triggers: [...get().triggers, ...data],
          loading: false,
        });
      }
  },
});

const useTriggerStore = create(
  persist<TriggerSlice>(TriggerStore, {
    name: "triggers",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useTriggerStore;
