import { z } from "zod";

interface IBadWord {
  bad_word_id: number;
  bot_id: number;
  bad_word: string;
}
interface IBadWordConfig {
    active: boolean,
    pool: "both" | "bot",
    answer: string,
    voice_path_female: string,
    voice_path_male: string
}
interface BadWordSlice {
  badWords: IBadWord[];
  badWord: IBadWord;
  loading?: boolean;
  message?: string;
  fetchLoading?: boolean;

  get_all_badwords: (bot_id: number) => Promise<void>;
  get_one_badword: (badword_id: number) => Promise<void>;
  create_one_badword: (badword: any) => Promise<void>;
  update_one_badword: (badword: any) => Promise<void>;
  create_many_badwords: (badwords: any) => Promise<void>;
  delete_one_badword: (badword_id: number) => Promise<void>;
  delete_all_badwords: (bot_id: number) => Promise<void>;
  delete_many_badwords: (badwords: any) => Promise<void>;
}

const CreateBadWordSchema = z.object({
  bot_id: z.number(),
  bad_word: z
    .string()
    .min(1, "Bad Word is required")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Bad Word is required"),
});

type CreateBadWordType = z.infer<typeof CreateBadWordSchema>;

export type {
  BadWordSlice,
  IBadWord,
  CreateBadWordType,
  IBadWordConfig,
};

export {
    CreateBadWordSchema,
}
