import { create } from "zustand";
import * as api from "apis/badWord.api";
import { BadWordSlice } from "./badword.types";
import { badWordState } from "./badword.state";
import { persist } from "zustand/middleware";
import { toast } from "react-hot-toast";

const badWordStore = (set, get) => ({
  ...badWordState,
  message: "",
  loading: false,
  fetchLoading: false,

  get_all_badwords: async (bot_id: number) => {
    set({ fetchLoading: true });
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({ badWords: data || [] });
      }
    set({ fetchLoading: false });
  },

  get_one_badword: async (badword_id: number) => {
    const data = await api.getOne(badword_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({ badWord: data, loading: false });
      }
  },

  create_one_badword: async (badword: {bad_word:string}) => {
    set({ loading: true });
    const data = await api.createOne(badword)
      if (data && data?.message) {
        set({ message: data?.message,loading: false,
        });
        toast.error(data?.message);
      } else {
        set({
          badWords: [...get().badWords, data],
          badWord: data,
          loading: false,
        });
        toast.success("Bad Word created successfully");
      };
  },

  update_one_badword: async (badword: any) => {
    set({ loading: true });
    const data = await api.updateOne(badword)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          badWords: get().badWords.map((badword) =>
            badword.bad_word_id === data.bad_word_id ? data : badword
          ),
          badWord: data,
          loading: false,
        });
      }
    
  },

  create_many_badwords: async (badwords: any) => {
    set({ loading: true });
    const data = await api.createMany(badwords)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({ badWords: [...get().badWords, ...data], loading: false });
      }
  },

  delete_one_badword: async (bad_word_id: number) => {
    set({ loading: true });
    const data = await api.deleteOne({ bad_word_id })
      if (data && data?.message) {
        set({
          badWords: get().badWords.filter(
            (badword) => badword.bad_word_id !== bad_word_id
          ),
          loading: false,
        });
      } else {
      }
  },

  delete_all_badwords: async (bot_id) => {
    set({ loading: true });
    const data = await api.deleteAll({ bot_id })
      if (data && data?.message) {
        set({ badWords: [], loading: false });
      } else {
      }
    },


  delete_many_badwords: async (badwords: any) => {
    const data = await api.deleteMany(badwords);
    if (data && data?.message) {
      set({
        message: data?.message,
      });
    } else {
      set({
        badWords: data,
      });
    }
  },
});

const useBadWordStore = create(
  persist<BadWordSlice>(badWordStore, {
    name: "badwords",
    partialize: (state) => {
      const { message, loading,fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useBadWordStore;
