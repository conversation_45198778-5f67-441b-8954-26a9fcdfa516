interface ITrello {
  trello_plugin_id: number;
  status_active: boolean;
  key: string;
  token: string;
}

interface ITrelloSlice {
  trello: ITrello;
  message?: string;
  loading?: boolean;

  get_one_trello: (bot_id: number) => Promise<void>;
  update_one_trello: (data: any) => Promise<void>;
  create_one_trello: (data: any) => Promise<void>;
  delete_one_trello: (bot_id: number) => Promise<void>;
}
export type { ITrello, ITrelloSlice };
