import * as api from "apis/trelloPlugin.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import TrelloState from "./trello.state";
import { ITrelloSlice } from "./trello.types";

const trelloStore = (set, get) => ({
  ...TrelloState,
  message: "",
  loading: false,

  get_one_trello: async (bot_id: number) => {
    api.getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          trello: TrelloState.trello,
        });
      } else {
        set({
          trello: data,
        });
      }
    });
  },

  update_one_trello: async (trello: any) => {
    set({ loading: true });
    const data = await api.updateOne(trello)
      if (data && data?.message) {
        set({
          message: data?.message,
        });
        toast.error(data?.message);
      } else {
        set({
          trello: data,
        });
        toast.success("Trello connection updated successfully");
      }
      set({ loading: false });

  },

  create_one_trello: async (trello: any) => {
    set({ loading: true });
   const data = await api.createOne(trello)
      if (data && data?.message) {
        set({
          message: data?.message,
        });
        toast.error(data?.message);
      } else {
        set({
          trello: data,
        });
        toast.success("Trello connection created successfully");
      }
      set({ loading: false });
  },

  delete_one_trello: async (bot_id: number) => {
    set({ loading: true });
    const data = await api.deleteOne({ bot_id })
      if (data && data?.message) {
        set({
          message: data?.message,
          trello: TrelloState.trello,
        });
      }
      set({ loading: false });
  },
});

const useTrelloStore = create(
  persist<ITrelloSlice>(trelloStore, {
    name: "trello",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useTrelloStore;
