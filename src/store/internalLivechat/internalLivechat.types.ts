import { User } from "store/user/user.types";

interface InternalLCIntegration {
  livechat_integration_id: number;
  status: string;
  max_sessions_per_agent: number;
  createdAt?: string;
  updatedAt?: string;
  bot_id: number;
  time_zone: string;
}

type InternalLivechatAgent = {
  agent_id: number;
  editor_id: number;
  bot_id: number;
  status: string;
  current_active_chats: number;
  user: User;
  time_zone: string;
};

interface InternalLiveChatSlice {
  integration: InternalLCIntegration;
  agent: InternalLivechatAgent;

  message?: string;
  loading?: boolean;

  get_internal_livechat: (bot_id: number) => Promise<void>;
  get_agent: (bot_id: number, user_id: number) => Promise<void>;
  set_agent: (agent: InternalLivechatAgent) => void;
  create_internal_livechat: (data: { bot_id: number; max_sessions_per_agent: number }) => Promise<void>;
  update_internal_livechat: (data: { livechat_integration_id: number; max_sessions_per_agent?: number; status?: string; time_zone?: string }) => Promise<void>;
}

export type { InternalLiveChatSlice, InternalLCIntegration };
