import * as api from "apis/internalLiveChat.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import { InternalLiveChatSlice } from "./internalLivechat.types";
import InternalLiveChatState from "./internalLivechat.state";

const liveChatStore = (set, get) => ({
  ...InternalLiveChatState,
  message: "",
  loading: false,

  get_internal_livechat: async (bot_id: number) => {
    const data = await api.getInternalLivechatIntegration(bot_id)
      if (data && data?.error) {
        set({
          message: data?.error,
          integration: InternalLiveChatState.integration,
        });
      } else {
        set({
          integration: data,
        });
      }
  },

  get_agent: async (bot_id: number, user_id: number) => {
    const data = await api.getAgent(bot_id, user_id);
    if (data && data?.error) {
      set({
        message: data?.error,
        agent: InternalLiveChatState.agent,
      });
    } else {
      set({
        agent: data,
      });
    }
  },

  set_agent: (agent) => {
    set({ agent });
  },

  create_internal_livechat: async (data) => {
    const response = await api.createInternalLivechatIntegration(data);
    if (response && response?.error) {
      set({ message: response?.error });
    } else {
      set({ integration: response });
    }
  },

  update_internal_livechat: async (data) => {
    const response = await api.updateInternalLivechatIntegration(data);
    if (response && response?.error) {
      set({ message: response?.error });
    } else {
      set({ integration: response });
    }
  },
});

const useInternalLiveChatStore = create(
  persist<InternalLiveChatSlice>(liveChatStore, {
    name: "internalLivechat",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useInternalLiveChatStore;
