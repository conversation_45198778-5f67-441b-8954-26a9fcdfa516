import * as api from "apis/specifications.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import ItemSpecificationState from "./itemSpecifications.state";
import { ItemSpecificationSlice } from "./itemSpecifications.types";

const itemSpecificationStore = (set, get) => ({
  ...ItemSpecificationState,
  message: "",
  loading: false,

  get_all_item_specifications: async (bot_id: number) => {
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          SPECIFICATIONS: data ? data : [],
        });
      }
  },
  create_one_item_specification: async (itemSpecification: any) => {
    set({ loading: true });
    const data = await api.createOne(itemSpecification)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          SPECIFICATIONS: [...get().SPECIFICATIONS, data],
          SPECIFICATION: data,
        });
      }
    set({ loading: false });
  },
  update_one_item_specification: async (itemSpecification: any) => {
    set({ loading: true });
    const data = await api.updateOne(itemSpecification)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          SPECIFICATIONS: get().SPECIFICATIONS.map((a) =>
            a.feature_id === data.feature_id ? data : a
          ),
          SPECIFICATION: data,
        });
      }
    set({ loading: false });
  },
  delete_one_item_specification: async (itemSpecification: any) => {
    const data = await api.deleteOne({ feature_id: itemSpecification })
      if (data && data?.message) {
        set({
          SPECIFICATIONS: get().SPECIFICATIONS.filter(
            (a) => a.feature_id !== itemSpecification
          ),
        });
      }
  },
});

const useItemSpecificationStore = create(
  persist<ItemSpecificationSlice>(itemSpecificationStore, {
    name: "itemSpecifications",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useItemSpecificationStore;
