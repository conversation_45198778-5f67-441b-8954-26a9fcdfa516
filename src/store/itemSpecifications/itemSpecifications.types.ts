// {\"feature_id\":7680,\"feature_name\":\"caret\",\"feature_type\":null,\"createdAt\":\"2023-03-19T09:26:39.654Z\",\"updatedAt\":\"2023-03-19T09:26:39.654Z\",\"bot_id\":669,\"feature_unit\":null,\"feature_description\":null}

interface IFeature {
  feature_id: number;
  feature_name: string;
  feature_type: string;
  createdAt: string;
  updatedAt: string;
  bot_id: number;
  feature_unit: string;
  feature_description: string;
}

interface ItemSpecificationSlice {
  SPECIFICATION: IFeature;
  SPECIFICATIONS: IFeature[];
  message?: string;
  loading?: boolean;

  get_all_item_specifications: (bot_id: number) => Promise<void>;
  create_one_item_specification: (itemSpecification: any) => Promise<void>;
  update_one_item_specification: (itemSpecification: any) => Promise<void>;
  delete_one_item_specification: (itemSpecification: any) => Promise<void>;
}

export type { IFeature, ItemSpecificationSlice };
