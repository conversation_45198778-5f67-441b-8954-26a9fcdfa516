import { IBadWordConfig } from "store/badword/badword.types";
import { botConfigType } from "types/botconfig.types";

type TBotConfig = {
  bot_config_id: number;
  stand_by_messages: { message: string; timeout: number }[];

  welcome_dialog: string;
  suggested_actions: string;
  welcome_message_set: { type: "message"; data: string }[];

  globals: string;
  bad_word_config:IBadWordConfig;

};
interface BotConfigSlice {
  botConfig: botConfigType;
  loading?: boolean;
  message?: string;
  fetchLoading?: boolean;
  get_bot_config: (bot_id: number) => Promise<void>;
  update_bot_config: (botConfig: Partial<botConfigType>) => Promise<void>;
}

export type { TBotConfig, BotConfigSlice };

