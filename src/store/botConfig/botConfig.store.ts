import { create } from "zustand";
import * as api from "apis/config.api";

import { persist } from "zustand/middleware";
import { toast } from "react-hot-toast";
import { botConfigState } from "./botConfig.state";
import { BotConfigSlice } from "./botConfig.types";
import { botConfigType } from "types/botconfig.types";

const botConfigStore = (set, get) => ({
  ...botConfigState,
  message: "",
  loading: false,
  fetchLoading: false,

  get_bot_config: async (bot_id: number) => {
    const data = await api.getConfig(bot_id);
    if (data && data?.message) {
      set({ message: data?.message });
    } else {
      set({ botConfig: data, loading: false });
    }
  },
  update_bot_config: async (botConfig: Partial<botConfigType>) => {
    set({ loading: true });
    const data = await api.updateConfig(botConfig);
    if (data && data?.message) {
      set({ message: data?.message });
      toast.error("An Error has occurred while updating");
    } else {
      toast.success("Badword Config updated successfully");
      set({ botConfig: data, loading: false });
    }
    set({ loading: false });
  },
});

const useBotConfigStore = create(
  persist<BotConfigSlice>(botConfigStore, {
    name: "botConfig",
    partialize: (state) => {
      const { message, loading, fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useBotConfigStore;
