
const initialBogConfig = {
  addToCartMessage: "",
  afterSendingVerificationCodeMessage: "",
  askAboutItemSpecsMessage: "",
  askAboutReservationDateMessage: "",
  bot_category: null,
  bot_config_id: 0,
  bot_icon: '',
  bot_id: 0,
  bot_name: '',
  bot_type: '',
  busniessClosedMessage: '',
  cartAlreadyCheckedOutMessage: '',
  checkoutCartMessage: '',
  checkoutCartSuggestionMessage: '',
  checkoutMessage: '',
  confirmSendingVerificationCodeMessage: '',
  conflictMessage: '',
  continueMessage: '',
  createdAt: '',
  deleteCartMessage: '',
  deleteMessage: '',
  deleted: false,
  description: '',
  distanceMessage: '',
  domain:  null,
  dropCartSuggestionMessage: '',
  fallback_dialog: '',
  fallback_message: '',
  file_name: '',
  is_template: false,
  itemCardTourAskAboutSpecsMessage: '',
  itemCardTourDescriptionMessage: '',
  itemCardTourItemPriceMessage: '',
  itemCardTourShareMessage: '',
  itemCardTourTtsMessage: '',
  itemsfoundMessage: '',
  language: '',
  mainTourCloseChatbotWindowMessage: '',
  mainTourMainMenuMessage: '',
  mainTourMicMessage: '',
  mainTourResizeChatbotWindowMessage: '',
  mainTourTextInputMessage: '',
  mainTourToggleMicLanguageMessage: '',
  mock_endpoint:  null,
  outOfContextMessage: '',
  plan_id: 0,
  processAlreadyProceedMessage: '',
  processCanceledMessage: '',
  quantityMessage: '',
  rejectSendingVerificationCodeButtonMessage: '',
  requestAddressMessage: '',
  requestItemNoteMessage: '',
  requestNameMessage: '',
  requestNumberOfGuestsMessage: '',
  reservedSuccessfullyMessage: '',
  service_secret: '',
  stand_by_messages: '',
  status: '',
  suggested_actions: [],
  tableNotAvailableMessage: '',
  updatedAt: '',
  user_id: 0,
  verifyPhoneMessage: '',
  verifySuccessfulMessage: '',
  welcome_dialog: '',
  welcome_message_set: [],
  bad_word_config:  null,
  wrongVerificationCodeButtonMessage: '',
};

const botConfigState = {
  botConfig: { ...initialBogConfig },
};

export { botConfigState, initialBogConfig };
