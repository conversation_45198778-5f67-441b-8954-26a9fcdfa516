import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import * as api from "apis/sendGridIntegration.api";

import SendgridState from "./sendgrid.state";
import { ISendgridIntegration, SendgridSlice } from "./sendgrid.types";

const SendgridStore = (set, get) => ({
  ...SendgridState,
  message: "",
  loading: true,

  get_one_sendgrid: async (bot_id: number) => {
    api.getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          sendgrid: SendgridState.sendgrid,
        });
      } else {
        set({
          sendgrid: data,
        });
      }
    });
  },

  create_one_sendgrid: async (
    sendgrid: Omit<ISendgridIntegration, "sendgrid_integration_id">
  ) => {
    api.createOne(sendgrid).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          sendgrid: data,
        });
        toast.success("Sendgrid created successfully");
      }
    });
  },
  update_one_sendgrid: async (sendgrid: Partial<ISendgridIntegration>) => {
    api.updateOne(sendgrid).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          sendgrid: data,
        });
        toast.success("Sendgrid updated successfully");
      }
    });
  },

  delete_one_sendgrid: async (bot_id: number) => {
    api.deleteOne({ bot_id }).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          sendgrid: SendgridState.sendgrid,
        });
        toast.success(data?.message);
      }
    });
  },
});

const useSendgridStore = create(
  persist<SendgridSlice>(SendgridStore, {
    name: "sendgrid_integration",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useSendgridStore;
