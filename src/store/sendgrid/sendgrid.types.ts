interface ISendgridIntegration {
  sendGrid_id: number;
  email: string;
  apiKey: string;
  bot_id: number;
}

interface SendgridSlice {
  sendgrid: ISendgridIntegration;
  message?: string;
  loading?: boolean;

  get_one_sendgrid: (bot_id: number) => Promise<void>;
  create_one_sendgrid: (
    sendgrid: Omit<ISendgridIntegration, "sendGrid_id">
  ) => Promise<void>;
  update_one_sendgrid: (
    sendgrid: Partial<ISendgridIntegration>
  ) => Promise<void>;
  delete_one_sendgrid: (bot_id: number) => Promise<void>;
}

export type { ISendgridIntegration, SendgridSlice };
