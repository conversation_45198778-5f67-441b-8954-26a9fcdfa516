interface ICalendlyIntegration {
  calendly_integration_id: number;
  refresh_token: string;
  owner: string;
  organization: string;
  status_active: boolean;
  bot_id: number;
}

interface CalendlySlice {
  calendly: ICalendlyIntegration;
  message?: string;
  loading?: boolean;

  get_one_calendly: (bot_id: number) => Promise<void>;
  create_one_calendly: (
    calendly: Omit<ICalendlyIntegration, "calendly_integration_id">
  ) => Promise<void>;
  update_one_calendly: (
    calendly: Partial<ICalendlyIntegration>
  ) => Promise<void>;
  delete_one_calendly: (bot_id: number) => Promise<void>;
}

export type { ICalendlyIntegration, CalendlySlice };
