import * as api from "apis/calendlyIntegration.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import CalendlyState from "./calendly.state";
import { CalendlySlice, ICalendlyIntegration } from "./calendly.types";

const CalendlyStore = (set, get) => ({
  ...CalendlyState,
  message: "",
  loading: false,

  get_one_calendly: async (bot_id) => {
    set({ loading: true });
    const response = await api.getOne(bot_id);
    if (response && response?.message) {
      set({ message: response.message, calendly: CalendlyState.calendly });
    } else {
      set({ calendly: response });
    }
    set({ loading: false });
  },

  create_one_calendly: async (
    calendly: Omit<ICalendlyIntegration, "calendly_integration_id">
  ) => {
    set({ loading: true });
    const response = await api.createOne(calendly);
    if (response && response?.message) {
      set({ message: response.message });
      toast.error(response.message);
    } else {
      set({ calendly: response });
      toast.success("Calendly connected successfully");
    }
    set({ loading: false });
  },

  update_one_calendly: async (calendly) => {
    set({ loading: true });
    const response = await api.updateOne(calendly);
    if (response && response?.message) {
      set({ message: response.message });
      // toast.error(response.message);
    } else {
      set({ calendly: response });
    }
    set({ loading: false });
  },

  delete_one_calendly: async (bot_id) => {
    set({ loading: true });
    const data = await api.deleteOne({ bot_id });
    if (data && data?.message) {
      set({
        message: data?.message,
        calendly: CalendlyState.calendly,
      });
      toast.success(data?.message);
    }

    set({ loading: false });
  },
});

const useCalendlyStore = create(
  persist<CalendlySlice>(CalendlyStore, {
    name: "calendly_integration",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useCalendlyStore;
