interface IGPT {
  gpt_integration_id: number;
  openai_key: string;
  status_active: boolean;
  bot_id: number;
}

type GPTCreate = Omit<IGPT, "gpt_integration_id">;
type GPTUpdate = Partial<IGPT>;

interface GPTSlice {
  gpt: IGPT;
  message?: string;
  loading?: boolean;

  get_one_gpt: (bot_id: number) => Promise<void>;
  create_one_gpt: (gpt: GPTCreate) => Promise<void>;
  update_one_gpt: (gpt: GPTUpdate) => Promise<void>;
  delete_one_gpt: (bot_id: number) => Promise<void>;
}

export type { GPTSlice, IGPT, GPTCreate, GPTUpdate };
