import * as api from "apis/gpt.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import GPTState from "./gpt.state";
import { GPTCreate, GPTSlice, GPTUpdate } from "./gpt.types";

const GPTStore = (set, get) => ({
  ...GPTState,
  message: "",
  loading: true,

  get_one_gpt: async (bot_id: number) => {
    api.getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          gpt: GPTState.gpt,
        });
      } else {
        set({
          gpt: data,
        });
      }
    });
  },

  create_one_gpt: async (gpt: GPTCreate) => {
    api.createOne(gpt).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          gpt: data,
        });
        toast.success("GPT created successfully");
      }
    });
  },

  update_one_gpt: async (gpt: GPTUpdate) => {
    api.updateOne(gpt).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
        return;
      } else {
        set({
          gpt: data,
        });
        toast.success("GPT updated successfully");
      }
    });
  },

  delete_one_gpt: async (bot_id: number) => {
    api.deleteOne({ bot_id }).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          gpt: GPTState.gpt,
        });
        toast.success(data?.message);
      }
    });
  },
});

const useGPTStore = create(
  persist<GPTSlice>(GPTStore, {
    name: "gpt_integration",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useGPTStore;
