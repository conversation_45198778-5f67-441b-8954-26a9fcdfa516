interface ICheckpoint {
  bot_designer_dialog: {
    dialog_name: string;
  };
  dialog_name: string;
  dialog_checkpoint_id: number;
  conversation_id: string;
  tag: string;
  bot_id: number;
  channel: string;
  dialog_id: number;
  createdAt: string;
  updatedAt: string;
}

interface Pagination {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface IDialog {
  dialog_id: number;
  dialog_name: string;
}

interface CheckpointSlice {
  checkpoint: ICheckpoint;
  checkpoints: {
    data: ICheckpoint[];
    pagination: Pagination;
  };

  message?: string;
  loading?: boolean;
  dialogs?: IDialog[];
  dialogCheckpoints?: ICheckpoint[];
  start_day?: number;
  start_month?: number;
  start_year?: number;
  end_day?: number;
  end_month?: number;
  end_year?: number;
  page?: number;
  pageSize?: number;

  getAll: (
    bot_id: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number,
    page?: number,
    pageSize?: number
  ) => Promise<void>;
  createOne: (
    data: Omit<
      ICheckpoint,
      "dialog_checkpoint_id" | "createdAt" | "updatedAt" | "bot_designer_dialog"
    >
  ) => Promise<void>;
  updateCheckpoint: (data: ICheckpoint) => Promise<void>;
  deleteOne: (dialog_checkpoint_id: number) => Promise<void>;
  getOne: (
    dialog_checkpoint_id: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number
  ) => Promise<void>;
  getDialogsWithCheckpoints: (
    bot_id: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number
  ) => Promise<void>;
  getDialogCheckpoints: (
    dialog_id: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number,
    page?: number,
    pageSize?: number
  ) => Promise<void>;
  search: (
    data: ICheckpoint,
    page?: number,
    pageSize?: number
  ) => Promise<void>;
}

export type { ICheckpoint, CheckpointSlice };
