import * as api from "apis/checkpoints.api";
import { create } from "zustand";
import { sleep } from "helpers/helper";
import { persist } from "zustand/middleware";
import toast from "react-hot-toast";
import CheckpointState from "./checkpoint.state";
import { CheckpointSlice, ICheckpoint } from "./checkpoint.types";

const checkpointStore = (set, get) => ({
  ...CheckpointState,
  dialogs: [],
  dialogCheckpoints: [],
  message: "",
  loading: false,
  
  getAll: async (
    bot_id: number,
    page?: number,
    pageSize?: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number,
  ) => {
    set({ loading: true });
    return api
      .getAll(
        bot_id,
        page,
        pageSize,
        start_day,
        start_month,
        start_year,
        end_day,
        end_month,
        end_year,
      )
      .then((data) => {
        if (data && data?.message) {
          set({ message: data?.message, loading: false });
        } else {
          set({
            checkpoints: data ? data : [],
            loading: false,
          });
        }
      });
  },

  createOne: (
    data: Omit<
      ICheckpoint,
      "dialog_checkpoint_id" | "createdAt" | "updatedAt" | "bot_designer_dialog"
    >
  ) => {
    set({ loading: true });
    return api.createOne(data).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
        toast.error(data?.message);
      } else {
        set({
          checkpoint: data,
          loading: false,
        });
        toast.success("Dialog created successfully");
      }
    });
  },

  updateCheckpoint: (data: any) => {
    set({ loading: true });
    return api.updateCheckpoint(data).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
        toast.error(data?.message);
      } else {
        set({
          checkpoint: data,
          loading: false,
        });
        toast.success("Dialog updated successfully");
      }
    });
  },

  deleteOne: async (dialog_checkpoint_id: number) => {
    return api.deleteOne(dialog_checkpoint_id).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.success(data?.message);
      } else {
        set({
          checkpoint: data,
        });
      }
    });
  },

  getOne: async (dialog_checkpoint_id: number) => {
    set({ loading: true });
    await sleep(1000);
    return api.getOne(dialog_checkpoint_id).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
        toast.error(data?.message);
      } else {
        set({
          checkpoint: data,
          loading: false,
        });
      }
    });
  },

  getDialogsWithCheckpoints: async (
    bot_id: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number
  ) => {
    return api
      .getDialogsWithCheckpoints(
        bot_id,
        start_day,
        start_month,
        start_year,
        end_day,
        end_month,
        end_year
      )
      .then((data) => {
        if (data && data?.message) {
          set({ message: data?.message });
          toast.success(data?.message);
        } else {
          set({
            dialogs: data,
          });
        }
      });
  },
  getDialogCheckpoints: async (
    dialog_id: number,
    start_day?: number,
    start_month?: number,
    start_year?: number,
    end_day?: number,
    end_month?: number,
    end_year?: number,
  ) => {
    return api
      .getDialogCheckpoints(
        dialog_id,
        start_day,
        start_month,
        start_year,
        end_day,
        end_month,
        end_year,
      )
      .then((data) => {
        if (data && data?.message) {
          set({ message: data?.message });
          toast.success(data?.message);
        } else {
          set({
            dialogCheckpoints: data,
          });
        }
      });
  },
  search: (data: any, page?: number, pageSize?: number) => {
    return api.search(data, page, pageSize).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.success(data?.message);
      } else {
        set({
          checkpoint: data,
        });
      }
    });
  },
});

const useCheckpointStore = create(
  persist<CheckpointSlice>(checkpointStore, {
    name: "checkpoint",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useCheckpointStore;
