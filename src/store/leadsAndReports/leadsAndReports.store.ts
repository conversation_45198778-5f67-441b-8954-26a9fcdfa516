import { create } from "zustand";

import { persist } from "zustand/middleware";
import toast from "react-hot-toast";
import LeadsAndReportsState from "./leadsAndReports.state";
import { LeadsAndReportsSlice } from "./leadsAndReports.types";
import { getOne } from "apis/leadAddon.api";
import { getOne as getOneReport } from "apis/reportAddon.api";

const LeadsAndReportStore = (set, get) => ({
  ...LeadsAndReportsState,
  message: "",
  loading: true,

  set_leads_active: async (leadsActive: boolean) => {
    set({ leadsActive: leadsActive });
  },
  set_reports_active: async (reportsActive: boolean) => {
    set({ reportsActive: reportsActive });
  },

  get_leads_status: async (bot_id) => {
    getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          leadsActive: data?.active,
        });
      }
    });
  },

  get_reports_status: async (bot_id) => {
    getOneReport(bot_id).then((data) => {
        if (data && data?.message) {
            set({ message: data?.message });
        } else {
            set({
            reportsActive: data?.status_active,
            });
        }
        }
    );
    },
});

const useLeadsAndReportStore = create(
  persist<LeadsAndReportsSlice>(LeadsAndReportStore, {
    name: "leadsAndReports",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useLeadsAndReportStore;
