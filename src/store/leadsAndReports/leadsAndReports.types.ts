interface LeadsAndReportsSlice {
    leadsActive: boolean;
    reportsActive: boolean;
    loading?: boolean;
    message?: string;

    set_leads_active: (leadsActive: boolean) => void;
    set_reports_active: (reportsActive: boolean) => void;

    get_leads_status: (bot_id: number) => Promise<void>;
    get_reports_status: (bot_id: number) => Promise<void>;
}

export type {
    LeadsAndReportsSlice
}