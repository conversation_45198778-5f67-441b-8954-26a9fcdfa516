import { ITrigger } from "store/trigger/trigger.types";

interface IDialogConfigurationStep {
  block_type: string;
  node_id: string;
  name: string;
  handler: string;
  handlerParams: any[];
  position?: {
    x: number;
    y: number;
  };
  path?: string;
  node?: number;
  step?: number;
}

interface IDialogConfigurationEdge {
  source: string;
  sourceHandle: string;
  target: string;
  targetHandle: string | null;
  type: string;
  data: any;
  id: string;
}

export interface IDialogExpiryConfig {
  expiry_min: number,
    check_at: "each_message" | "start_of_dialog",
    action: {
      type: "send_message" | "trigger_dialog",
      config: {
        message: string,
        dialog: {
          dialog_id?: number,
          dialog_name?: string,
          path: string,
          lang: "__ar__:##ar## __en__:##en##",
        }
      },
    },
}

interface IDialogConfiguration {
  entities: any;
  dialogExpiry?: IDialogExpiryConfig;
  steps: IDialogConfigurationStep[];
  edges: IDialogConfigurationEdge[];
}

interface IDialog {
  dialog_id: number;
  url: string;
  dialog_name: string;
  dialog_image: string;
  dialog_status: string;
  createdAt: string;
  updatedAt: string;
  bot_id: number;
  configuration?: IDialogConfiguration;
  topic_id: number | null;
}

interface DialogSlice {
  dialog: IDialog;
  dialogs: IDialog[];
  dialogTriggers: ITrigger[];
  message?: string;
  loading?: boolean;

  getAll: (bot_id: number) => Promise<void>;
  getOne: (bot_id: number) => Promise<void>;
  createOne: (data: any) => Promise<void>;
  updateOne: (data: any) => Promise<void>;
  deleteOne: (dialog_id: number) => Promise<void>;
  getTriggers: (dialog_id: number) => Promise<void>;
}

export type { IDialog, DialogSlice, IDialogConfigurationStep };
