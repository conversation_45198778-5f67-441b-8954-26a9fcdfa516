import * as api from "apis/dialog.api";
import { create } from "zustand";
import { sleep } from "helpers/helper";
import { persist } from "zustand/middleware";
import toast from "react-hot-toast";
import DialogState from "./dialog.state";
import { DialogSlice } from "./dialog.types";


const dialogStore = (set, get) => ({
  ...DialogState,
  message: "",
  loading: false,

  getAll: async (bot_id: number) => {
    set({ loading: true });
    return api.getAll(bot_id).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
      } else {
        set({
          dialogs: data ? data : [],
          loading: false,
        });
      }
    });
  },

  getOne: async (dialog_id: any) => {
    set({ loading: true });
    await sleep(1000);
    return api.getOne(dialog_id).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
        toast.error(data?.message);
      } else {
        set({
          dialog: data,
          loading: false,
        });
      }
    });
  },

  getTriggers: async (dialog_id: number) => {
    return api.getTriggers(dialog_id).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          dialogTriggers: data
        });
      }
    });
  },

  createOne: (data: any) => {
    set({ loading: true });
    return api.createOne(data).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
        toast.error(data?.message);
      } else {
        set({
          dialog: data,
          loading: false,
        });
        toast.success("Dialog created successfully");
      }
    });
  },

  updateOne: (data: any) => {
    set({ loading: true });
    return api.updateOne(data).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message, loading: false });
        toast.error(data?.message);
      } else {
        set({
          dialog: data,
          loading: false,
        });
        toast.success("Dialog updated successfully");
      }
    });
  },

  deleteOne: (dialog_id: number) => {
    return api.deleteOne({dialog_id:dialog_id}).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.success(data?.message);
      } else {
        set({
          dialog: data,
        });
      }
    });
  },
});

const useDialogStore = create(
  persist<DialogSlice>(dialogStore, {
    name: "dialogs",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useDialogStore;
