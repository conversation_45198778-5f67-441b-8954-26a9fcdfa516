import * as api from "apis/livechat.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import LiveChatState from "./livechat.state";
import { LiveChatSlice } from "./livechat.types";

const liveChatStore = (set, get) => ({
  ...LiveChatState,
  message: "",
  loading: false,

  get_one_livechat: async (bot_id: number) => {
    const data = await api.getLiveChatIntegration(bot_id)
      if (data && data?.message) {
        set({
          message: data?.message,
          livechat: LiveChatState.livechat,
        });
      } else {
        set({
          livechat: data,
        });
      }
  },

  update_one_livechat: async (lc: any) => {
    set({ loading: true });
    const data = await api.updateOne(lc)
      if (data && data?.message) {
        toast.error(data?.message);
        set({
          message: data?.message,
        });
        return;
      } else {
        set({
          livechat: data,
          message: "",
        });
        toast.success("LiveChat connection updated successfully");
      }
    set({ loading: false });
  },

  create_one_livechat: async (lc: any) => {
    set({ loading: true });
    const data = await api.createOne(lc)
      if (data && data?.message) {
        toast.error(data?.message);
        set({
          message: data?.message,
        });
        return;
      } else {
        set({
          livechat: data,
          message: "",
        });
        toast.success("LiveChat connection created successfully");
      }
    set({ loading: false });
  },

  delete_one_livechat: async (bot_id: number) => {
    const data = await api.deleteOne({ bot_id })
      if (data && data?.message) {
        set({
          message: data?.message,
          livechat: LiveChatState.livechat,
        });
        toast.success(data?.message);
      }
  },
});

const useLiveChatStore = create(
  persist<LiveChatSlice>(liveChatStore, {
    name: "livechat",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useLiveChatStore;
