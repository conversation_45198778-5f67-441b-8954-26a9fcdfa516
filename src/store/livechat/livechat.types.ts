interface ILiveChat {
  livechat_integration_id: number;
  client_id: string;
  client_secret: string;
  personal_access_token: string;
  encoded_personal_access_token: string;
  refresh_token: string;
  organization_id: string;
  createdAt?: string;
  updatedAt?: string;
  bot_id: number;
}

interface LiveChatSlice {
  livechat: ILiveChat;
  message?: string;
  loading?: boolean;

  get_one_livechat: (bot_id: number) => Promise<void>;
  update_one_livechat: (data: any) => Promise<void>;
  create_one_livechat: (data: any) => Promise<void>;
  delete_one_livechat: (bot_id: number) => Promise<void>;
}

export type { LiveChatSlice, ILiveChat };
