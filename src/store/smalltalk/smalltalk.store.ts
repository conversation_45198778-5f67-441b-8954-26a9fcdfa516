import { create } from "zustand";
import * as api from "apis/smallTalk.api";
import { SmallTalkSlice } from "./smalltalk.types";
import smalltalkState from "./smalltalk.state";
import { persist } from "zustand/middleware";

const smallTalkStore = (set, get) => ({
  ...smalltalkState,
  message: "",
  loading: false,
  fetchLoading: false,

  get_all_smalltalks: async (bot_id: number) => {
    set({ fetchLoading: true });
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({ smallTalks: data || [] });
      }
    set({ fetchLoading: false });
  },

  get_one_smalltalk: async (smalltalk_id: number) => {
    const data = await api.getOne(smalltalk_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({ smallTalk: data, loading: false });
      }
  },

  create_one_smalltalk: async (smalltalk: any) => {
    set({ loading: true });
    const data = await api.createOne(smalltalk)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          smallTalks: [...get().smallTalks, data],
          smallTalk: data,
          loading: false,
        });
      };
  },

  update_one_smalltalk: async (smalltalk: any) => {
    set({ loading: true });
    const data = await api.updateOne(smalltalk)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          smallTalks: get().smallTalks.map((smalltalk) =>
            smalltalk.small_talk_id === data.small_talk_id ? data : smalltalk
          ),
          smallTalk: data,
          loading: false,
        });
      }
    
  },

  create_many_smalltalks: async (smalltalks: any) => {
    set({ loading: true });
    const data = await api.createMany(smalltalks)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({ smallTalks: [...get().smallTalks, ...data], loading: false });
      }
  },

  delete_one_smalltalk: async (small_talk_id: number) => {
    set({ loading: true });
    const data = await api.deleteOne({ small_talk_id })
      if (data && data?.message) {
        set({
          smallTalks: get().smallTalks.filter(
            (smalltalk) => smalltalk.small_talk_id !== small_talk_id
          ),
          loading: false,
        });
      } else {
      }
  },

  delete_all_smalltalks: async (bot_id) => {
    set({ loading: true });
    const data = await api.deleteAll({ bot_id })
      if (data && data?.message) {
        set({ smallTalks: [], loading: false });
      } else {
      }
    },


  delete_many_smalltalks: async (smalltalks: any) => {
    const data = await api.deleteMany(smalltalks);
    if (data && data?.message) {
      set({
        message: data?.message,
      });
    } else {
      set({
        smallTalks: data,
      });
    }
  },
});

const useSmallTalkStore = create(
  persist<SmallTalkSlice>(smallTalkStore, {
    name: "smalltalks",
    partialize: (state) => {
      const { message, loading,fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useSmallTalkStore;
