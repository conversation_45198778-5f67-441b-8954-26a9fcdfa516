import { z } from "zod";

interface ISmallTalk {
  small_talk_id: number;
  bot_id: number;
  question: string;
  answer: string;
  voice_path_male: string;
  voice_path_female: string;
}

interface SmallTalkSlice {
  smallTalks: ISmallTalk[];
  smallTalk: ISmallTalk;
  loading?: boolean;
  message?: string;
  fetchLoading?: boolean;

  get_all_smalltalks: (bot_id: number) => Promise<void>;
  get_one_smalltalk: (smalltalk_id: number) => Promise<void>;
  create_one_smalltalk: (smalltalk: any) => Promise<void>;
  update_one_smalltalk: (smalltalk: any) => Promise<void>;
  create_many_smalltalks: (smalltalks: any) => Promise<void>;
  delete_one_smalltalk: (smalltalk_id: number) => Promise<void>;
  delete_all_smalltalks: (bot_id: number) => Promise<void>;
  delete_many_smalltalks: (smalltalks: any) => Promise<void>;
}

const CreateSmallTalkSchema = z.object({
  bot_id: z.number(),
  question: z
    .string()
    .min(1, "Question is required")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Question is required"),
  answer: z
    .string()
    .min(1, "Answer is required")
    .refine((value) => {
      return value.trim().length > 0;
    }, "Answer is required"),
});

type CreateSmallTalkType = z.infer<typeof CreateSmallTalkSchema>;

export type {
  SmallTalkSlice,
  ISmallTalk,
  CreateSmallTalkType,
};

export {
    CreateSmallTalkSchema,
}
