interface IItem {
  item_id: number;
  bot_id: number;
  item_title: string;
  item_description: string;
  item_price: string;
  item_qty: number;
  item_unit: string;
  currency: string;
  item_instock: boolean;
  item_condition: string;
  category_id: number;
  item_icons: string[];
  is_cart: boolean;
  item_options: ItemOption[];
  item_features: ItemFeature[];
  item_url: string;
  item_hide: boolean;
  show_url: boolean;
  price_hide: boolean;
  item_height: number;
  item_width: number;
  item_weight: number;
  sku: string;
  lemmatized_item_title: string;
  voice_path_male: string;
  voice_path_female: string;
}

interface ItemOption {
  item_id: number;
  item_option_id: number;
  option_title: string;
  option_description: string;
  option: Option[];
}

interface Option {
  name: string;
  price: string;
}

interface ItemFeature {
  item_feature_id: number;
  feature_value: string;
  createdAt: string;
  updatedAt: string;
  item_id: number;
  feature_id: number;
  bot_id: number;
}

interface ItemSlice {
  item: IItem;
  items: IItem[];
  message?: string;
  loading?: boolean;

  get_all_items: (bot_id: number) => Promise<void>;
  create_one_item: (Item: any) => Promise<void>;
  update_one_item: (Item: any) => Promise<void>;
  delete_one_item: (Item: any) => Promise<void>;
  create_many_items: (Items: any) => Promise<void>;
  delete_many_items: (Items: any) => Promise<void>;
}

export type { IItem, ItemSlice };
