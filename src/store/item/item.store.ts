import * as api from "apis/item.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import ItemState from "./item.state";
import { ItemSlice } from "./item.types";

const itemStore = (set, get) => ({
  ...ItemState,
  message: "",
  loading: true,

  get_all_items: async (bot_id: number) => {
    const data = await api.getAll(bot_id)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          items: data ? data : [],
        });
      }
  },

  create_one_item: async (item: any) => {
    const data = await api.createOne(item)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          items: [...get().items, data],
          item: data,
        });
      }
  },
  update_one_item: async (item: any) => {
    const data = await api.updateOne(item)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else if (data) {
        set({
          items: get().items.map((a) =>
            a.item_id === data.item_id ? data : a
          ),
          item: data,
        });
      }
  },
  delete_one_item: async (item: any) => {
    const data = await api.deleteOne({ item_id: item })
      if (data && data?.message) {
        // set({ message: data?.message });
        set({
          items: get().items.filter((a) => a.item_id !== item),
        });
      } else {
      }
  },

  create_many_items: async (items: any) => {
    const data = await api.createMany(items)
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          items: [...get().items, ...data],
        });
      }
  },

  delete_many_items: async (items: any) => {
    const data = await api.deleteMany(items)
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          items: data,
        });
      }
  }
});

const useItemStore = create(
  persist<ItemSlice>(itemStore, {
    name: "items",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useItemStore;
