const itemOption = {
  item_id: 0,
  item_option_id: 0,
  option_title: "",
  option_description: "",
  option: [
    {
      name: "",
      price: "",
    },
  ],
};

const itemFeature = {
  item_feature_id: 0,
  feature_value: "",
  createdAt: "",
  updatedAt: "",
  item_id: 0,
  feature_id: 0,
  bot_id: 0,
};
const initItemObject = () => {
  return {
    item_id: 0,
    bot_id: 0,
    item_title: "",
    item_description: "",
    item_price: "0",
    item_qty: 0,
    item_unit: "",
    currency: "",
    item_instock: false,
    item_condition: "",
    category_id: 0,
    item_icons: [""],
    is_cart: false,
    item_options: [itemOption],
    item_features: [itemFeature],
    item_url: "",
    item_hide: false,
    show_url: false,
    price_hide: false,
    item_height: 0,
    item_width: 0,
    item_weight: 0,
    sku: "",
    lemmatized_item_title: "",
    voice_path_male:"",
    voice_path_female:"",
  };
};

export default { initItemObject, itemFeature, itemOption };
