import * as api from "apis/log.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import LogState from "./logs.state";
import { LogSlice } from "./logs.types";

const logStore = (set, get) => ({
  ...LogState,
  message: "",
  loading: false,

  get_all_logs: async (bot_id: number) => {
    const data = await api.getAll(bot_id);
    set({ loading: true });
    if (data && data?.message) {
      set({ message: data?.message });
    } else {
      set({ loading: false });
      set({
        LOGS: data ? data : [],
      });
    }
  },
});

const useLogStore = create(
  persist<LogSlice>(logStore, {
    name: "logs",
    partialize: (state) => {
      const { message, loading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useLogStore;
