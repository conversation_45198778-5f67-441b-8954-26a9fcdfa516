// [{\"log_id\":110,\"log_status\":\"active\",\"log_description\":\"10 percent off\",\"fixed_value\":0,\"log_type\":\"Percentage\",\"log_icon\":\"\",\"log_percentage\":0.1,\"log_date_start\":\"2023-01-24T13:31:35.555Z\",\"log_date_end\":\"2023-05-31T00:00:00.000Z\",\"createdAt\":\"2023-01-24T13:32:16.693Z\",\"updatedAt\":\"2023-03-16T09:17:06.906Z\",\"bot_id\":669},{\"log_id\":111,\"log_status\":\"active\",\"log_description\":\"minus 5 jds\",\"fixed_value\":5,\"log_type\":\"Fixed\",\"log_icon\":\"\",\"log_percentage\":0,\"log_date_start\":\"2023-03-16T09:17:08.908Z\",\"log_date_end\":\"2023-03-31T00:00:00.000Z\",\"createdAt\":\"2023-03-16T09:17:28.861Z\",\"updatedAt\":\"2023-03-16T09:17:28.861Z\",\"bot_id\":669}]"

interface ILog {
  log_id: number;
  user_id: number;
  log: string;
  category: string;
  bot_designer_user:{email: string};
  action_type: string;
  id: number;
  createdAt: string;
  updatedAt: string;
  bot_id: number;
}

interface LogSlice {
  LOG: ILog;
  LOGS: ILog[];

  message?: string;
  loading?: boolean;

  get_all_logs: (bot_id: number) => Promise<void>;
}

export type { ILog, LogSlice };
