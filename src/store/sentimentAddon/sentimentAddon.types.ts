interface ISentimentAddon {
  sentiment_addon_id: number;
  active: boolean;
  generated_by: string;
  bot_id: number;
}

interface SentimentAddonSlice {
  sentimentAddon: ISentimentAddon;
  message?: string;
  loading?: boolean;
  fetchLoading?: boolean;

  getSentimentAddon: (bot_id: number) => Promise<void>;
  createSentimentAddon: (
    sentimentAddon: Partial<ISentimentAddon>
  ) => Promise<void>;
  updateSentimentAddon: (
    sentimentAddon: Partial<ISentimentAddon>
  ) => Promise<void>;
}

export type { ISentimentAddon, SentimentAddonSlice };
