import * as api from "apis/sentimentAddon.api";
import { create } from "zustand";

import { persist } from "zustand/middleware";

import toast from "react-hot-toast";
import SentimentAddonState from "./sentimentAddon.state";
import { ISentimentAddon, SentimentAddonSlice } from "./sentimentAddon.types";

const SentimentAddonStore = (set, get) => ({
  ...SentimentAddonState,
  message: "",
  loading: false,
  fetchLoading: false,

  getSentimentAddon: async (bot_id: number) => {
    set({ fetchLoading: true });
    api.getOne(bot_id).then((data) => {
      if (data && data?.message) {
        set({
          message: data?.message,
          sentimentAddon: SentimentAddonState.sentimentAddon,
        });
      } else {
        set({
          sentimentAddon: data,
        });
      }
      set({ fetchLoading: false });
    });
  },

  createSentimentAddon: async (sentimentAddon: Partial<ISentimentAddon>) => {
    set({ loading: true })
    api.createOne(sentimentAddon).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
        toast.error(data?.message);
      } else {
        set({
          sentimentAddon: data,
        });
      }
      set({ loading: false });
    });
  },

  updateSentimentAddon: async (sentimentAddon: Partial<ISentimentAddon>) => {
    set({ loading: true })
    api.updateOne(sentimentAddon).then((data) => {
      if (data && data?.message) {
        set({ message: data?.message });
      } else {
        set({
          sentimentAddon: data,
        });
      }
      set({ loading: false });
    });
  },
});

const useSentimentAddonStore = create(
  persist<SentimentAddonSlice>(SentimentAddonStore, {
    name: "sentiment_addon",
    partialize: (state) => {
      const { message, loading, fetchLoading, ...partialState } = state;
      return partialState;
    },
  })
);

export default useSentimentAddonStore;
