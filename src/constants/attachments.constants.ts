import generateStorageId from "helpers/generateStorageId";

const attachmentTypes = [
  {
    label: "image",
    options: [
      {
        value: "image",
        label: "Image",
      },
    ],
  },
  {
    label: "File",
    options: [
      {
        value: "file",
        label: "File",
      },
    ],
  },
];

const initialAttachmentsConditions = [
  {
    criteria: "attachment",
    title: "matched",
    storageType: {
      storage_type: "i2i-storage" as "i2i-storage" | "live-storage",
      url: "",
      fieldName: "file",
      entityName: "file",
    },
    connector_id: generateStorageId(),
    redirect: "",
    label: "label",
    size: 100,
    attachmentsConditions: [
      {
        criteria: "image",
        title: "image/jpeg",
        label: "jpeg",
      },
    ],
  },
  {
    connector_id: "0",
    criteria: "default",
    redirect: "",
    title: "not matched",
    attachmentsConditions: null,
    size: 0,
  },
];

type TAttachmentsConditions = {
  size: number;
  attachment_type: "file" | "image";
  entity: string;
  saveAs: string;
};

export type { TAttachmentsConditions };

export { attachmentTypes, initialAttachmentsConditions };
